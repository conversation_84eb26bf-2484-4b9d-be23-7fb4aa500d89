package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 事件记录
 * <AUTHOR>
 * @date  2025-04-08 18:32
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "EVENT_RECORD", indexes = {@Index(name = "EVENT_RECORD_ID_IDX", columnList = "ID"), @Index(name = "EVENT_RECORD_CRT_IDX", columnList = "CREATE_TIME"), @Index(name = "EVENT_RECORD_UPT_IDX", columnList = "UPDATE_TIME")})
public class EventRecord extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件编号
     */
    @Column(name = "event_code", length = 50)
    private String eventCode;


    /**
     * 事件类型名称
     */
    @Column(name = "event_type", length = 100)
    private String eventType;

    /**
     * 点位id
     */
    @Column(name = "point_id", length = 100)
    private String pointId;

    /**
     * 点位名称
     */
    @Column(name = "point_name", length = 100)
    private String pointName;

    /**
     * 提示词
     */
    @Column(name = "prompt", length = 100)
    private String prompt;


    /**
     * 文件名称
     */
    @Column(name = "file_name", length = 100)
    private String fileName;

    /**
     * 事件时间
     */
    @Column(name = "event_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date eventTime;





}

