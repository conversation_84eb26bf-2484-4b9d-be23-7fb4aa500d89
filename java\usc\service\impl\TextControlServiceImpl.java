package com.zkteco.mars.usc.service.impl;


import com.zkteco.framework.base.bean.BaseItem;
import com.zkteco.framework.base.bean.Pager;
import com.zkteco.framework.core.utils.CollectionUtil;
import com.zkteco.framework.core.utils.ModelUtil;
import com.zkteco.framework.core.utils.SQLUtil;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.usc.dao.ControlRulesDao;
import com.zkteco.mars.usc.dao.EventRecordDao;
import com.zkteco.mars.usc.dao.EventTypeDao;
import com.zkteco.mars.usc.dao.PointControlRulesDao;
import com.zkteco.mars.usc.model.ControlRules;
import com.zkteco.mars.usc.model.EventType;
import com.zkteco.mars.usc.model.PointControlRules;
import com.zkteco.mars.usc.service.TextControlService;
import com.zkteco.mars.usc.vo.ControlRulesItem;
import com.zkteco.mars.usc.vo.EventTypeItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 点位管理
 *
 * <AUTHOR>
 * @date 2024-12-31 14:36
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TextControlServiceImpl implements TextControlService {

    @Autowired
    private EventTypeDao eventTypeDao;
    @Autowired
    private ControlRulesDao controlRulesDao;

    @Autowired
    private PointControlRulesDao pointControlRulesDao;

    @Autowired
    private EventRecordDao eventRecordDao;


    @Override
    public Pager getEventTypeItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        return eventTypeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public void insertEventTypeData(List<EventType> eventTypeList) {
        eventTypeDao.saveAll(eventTypeList);
    }

    @Override
    public Pager getControlRulesItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        return controlRulesDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public ApiResultMessage editControlRule(ControlRulesItem controlRule) {
        ControlRules rule = new ControlRules();
        if (StringUtils.isNotEmpty(controlRule.getId())) {
            //更新
            rule = controlRulesDao.findById(controlRule.getId()).orElse(null);
            BeanUtils.copyProperties(controlRule, rule);
        } else {
            BeanUtils.copyProperties(controlRule, rule);
        }
        controlRulesDao.save(rule);
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage getAllEventType() {
        List<EventType> eventTypeList = eventTypeDao.findAll();
        return ApiResultMessage.successMessage(ModelUtil.copyListProperties(eventTypeList, EventTypeItem.class));
    }

    @Override
    public ApiResultMessage getAllControlRules() {
        List<ControlRules> controlRules = controlRulesDao.findAll();
        return ApiResultMessage.successMessage(ModelUtil.copyListProperties(controlRules, ControlRulesItem.class));
    }

    @Override
    public ApiResultMessage delControlRules(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            list.forEach(id -> {
                controlRulesDao.deleteById(id);
            });
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage startControl(String pointId, String ruleIds) {
        if (StringUtils.isNotEmpty(ruleIds)) {
            // 转为 Set 方便查找
            Set<String> inputRuleIdSet = new HashSet<>(CollectionUtil.strToList(ruleIds));

            // 查询数据库中已存在的规则
            List<PointControlRules> existingRules = pointControlRulesDao.findByPointId(pointId);
            Map<String, PointControlRules> existingRuleMap = existingRules.stream()
                    .collect(Collectors.toMap(PointControlRules::getRuleId, Function.identity()));

            // 插入或保留
            for (String ruleId : inputRuleIdSet) {
                if (!existingRuleMap.containsKey(ruleId)) {
                    // 不存在，插入
                    PointControlRules newRule = new PointControlRules();
                    newRule.setPointId(pointId);
                    newRule.setRuleId(ruleId);
                    pointControlRulesDao.save(newRule);
                }
            }

            // 删除多余的记录（数据库中有，但 ruleIds 中没有）
            for (PointControlRules existing : existingRules) {
                if (!inputRuleIdSet.contains(existing.getRuleId())) {
                    pointControlRulesDao.delete(existing);
                }
            }
        } else {
            // ruleIds 是空，删除该 pointId 下的所有规则
            pointControlRulesDao.deleteByPointId(pointId);
        }

        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage getPointControls(String pointId) {

        List<PointControlRules> existingRules = pointControlRulesDao.findByPointId(pointId);

        List<String> collect = existingRules.stream().map(PointControlRules::getRuleId).toList();

        return ApiResultMessage.successMessage(collect);
    }

    @Override
    public Pager getEventRecordsItemsByPage(BaseItem condition, int page, int size) {
        return eventRecordDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public ApiResultMessage getUsedEventCodes() {
        List<String> usedCodes = controlRulesDao.findUsedEventCodes();
        return ApiResultMessage.successMessage(usedCodes);
    }

    @Override
    public ApiResultMessage checkRuleIsControl(String pointId) {
        if (pointControlRulesDao.existsByRuleId(pointId)) {
            return ApiResultMessage.successMessage();
        } else {
            return ApiResultMessage.failedMessage(-1);
        }
    }
}
