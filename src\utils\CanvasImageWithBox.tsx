/**
 * Canvas图片组件 - 绘制带坐标框的图片
 */
import React, { useRef, useEffect } from 'react';
import { drawImageWithBox, createCanvasClickHandler, CanvasImageWithBoxProps } from './imageUtils';

interface CanvasImageWithBoxComponentProps extends CanvasImageWithBoxProps {
  fetchImage: (imageName: string, imageType: string) => Promise<string | null>;
}

export const CanvasImageWithBox: React.FC<CanvasImageWithBoxComponentProps> = ({ 
  src, 
  coordinate, 
  width, 
  height, 
  name, 
  time, 
  address, 
  score, 
  onImageClick,
  fetchImage
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 使用公共方法绘制带坐标框的图片
    drawImageWithBox(canvas, src, coordinate, width, height).catch((error: any) => {
      console.error('绘制图片失败:', error);
    });
  }, [src, coordinate, width, height]);

  // 使用公共方法创建点击处理函数
  const handleCanvasClick = createCanvasClickHandler(fetchImage, onImageClick);

  const onCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasClick(e, canvasRef, name, src, coordinate, time, address, score);
  };

  return (
    <canvas
      ref={canvasRef}
      style={{
        width: '100%',
        height: '100%',
        display: 'block',
        cursor: 'pointer'
      }}
      onClick={onCanvasClick}
    />
  );
};

export default CanvasImageWithBox;
