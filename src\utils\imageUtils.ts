/**
 * 图片处理工具类
 */

export interface CanvasImageWithBoxProps {
  src: string;
  coordinate: string;
  width: number;
  height: number;
  name?: string;
  time?: string;
  address?: string;
  score?: number | string;
  onImageClick?: (data: {
    name?: string;
    thumbUrl: string;
    imageUrl: string;
    coordinate: string;
    time?: string;
    address?: string;
    score?: number | string;
  }) => void;
}

/**
 * 简化的坐标映射函数
 * 直接从原图坐标映射到显示坐标，忽略中间的缩略图步骤
 */
export const mapCoordinates = (
  originalCoords: [number, number, number, number],
  displayWidth: number,
  displayHeight: number,
  originalWidth: number = 1280,
  originalHeight: number = 720
) => {
  const [x1, y1, x2, y2] = originalCoords;

  // 直接按比例映射
  const scaleX = displayWidth / originalWidth;
  const scaleY = displayHeight / originalHeight;

  return {
    left: Math.min(x1, x2) * scaleX,
    top: Math.min(y1, y2) * scaleY,
    width: Math.abs(x2 - x1) * scaleX,
    height: Math.abs(y2 - y1) * scaleY
  };
};

/**
 * 在canvas上绘制带坐标框的图片
 * @param canvas - canvas元素
 * @param src - 图片源
 * @param coordinate - 坐标字符串
 * @param width - canvas宽度
 * @param height - canvas高度
 * @returns Promise<void>
 */
export const drawImageWithBox = async (
  canvas: HTMLCanvasElement,
  src: string,
  coordinate: string,
  width: number,
  height: number
): Promise<void> => {
  const ctx = canvas.getContext('2d');
  if (!ctx || !coordinate || !src) return;

  const img = new window.Image();
  img.crossOrigin = 'anonymous';
  
  return new Promise((resolve, reject) => {
    img.onload = () => {
      try {
        // 设置canvas尺寸
        canvas.width = width;
        canvas.height = height;
        
        // 清除画布
        ctx.clearRect(0, 0, width, height);
        
        // 计算缩放比例
        const scaleX = width / img.width;
        const scaleY = height / img.height;
        const scale = Math.max(scaleX, scaleY); // 使用cover模式
        
        // 计算居中裁剪的偏移量
        const offsetX = (width - img.width * scale) / 2;
        const offsetY = (height - img.height * scale) / 2;
        
        // 绘制图片，但需要考虑实际的缩略图尺寸和显示方式
        ctx.drawImage(img, offsetX, offsetY, img.width * scale, img.height * scale);

        try {
          // 解析坐标并绘制框
          const coords = JSON.parse(coordinate);
          if (Array.isArray(coords) && coords.length === 4) {
            const [x1, y1, x2, y2] = coords;

            // 重新分析坐标映射：
            // 1. 坐标是基于原图 1280*720
            // 2. 后端生成缩略图 200*200，但实际内容可能不是200*200
            // 3. 前端canvas显示 width*height

            // 关键：需要考虑图片在canvas中的实际显示区域
            // 因为我们使用了cover模式绘制图片

            // 计算图片在canvas中的实际显示区域
            const imgDisplayWidth = img.width * scale;
            const imgDisplayHeight = img.height * scale;

            // 计算坐标映射比例
            // 如果缩略图是200*200，但原图是1280*720，那么：
            // - 如果按宽度缩放：200/1280 = 0.15625，高度变成 720*0.15625 = 112.5
            // - 如果按高度缩放：200/720 = 0.278，宽度变成 1280*0.278 = 355.6 (会被裁剪到200)

            // 假设后端是按比例缩放并居中裁剪的
            const thumbAspect = img.width / img.height; // 实际缩略图的宽高比

            let coordScaleX, coordScaleY;

            if (Math.abs(thumbAspect - 1) < 0.1) {
              // 缩略图接近正方形，说明是200*200
              // 原图1280*720被缩放到200*200时，会按较小的缩放比例
              const thumbScale = Math.min(200 / 1280, 200 / 720); // 取较小值 200/1280 = 0.15625
              coordScaleX = thumbScale * (imgDisplayWidth / 200);
              coordScaleY = thumbScale * (imgDisplayHeight / 200);
            } else {
              // 缩略图保持了原始宽高比
              coordScaleX = imgDisplayWidth / 1280;
              coordScaleY = imgDisplayHeight / 720;
            }

            // 计算框的位置和大小，考虑图片在canvas中的偏移
            const boxLeft = Math.min(x1, x2) * coordScaleX + offsetX;
            const boxTop = Math.min(y1, y2) * coordScaleY + offsetY;
            const boxWidth = Math.abs(x2 - x1) * coordScaleX;
            const boxHeight = Math.abs(y2 - y1) * coordScaleY;

            // 绘制红色框
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 1;
            ctx.strokeRect(boxLeft, boxTop, boxWidth, boxHeight);

            // console.log('坐标映射调试信息:', {
            //   原始坐标: [x1, y1, x2, y2],
            //   图片实际尺寸: [img.width, img.height],
            //   图片显示尺寸: [imgDisplayWidth, imgDisplayHeight],
            //   canvas尺寸: [width, height],
            //   图片偏移: [offsetX, offsetY],
            //   坐标缩放: [coordScaleX, coordScaleY],
            //   最终框位置: [boxLeft, boxTop, boxWidth, boxHeight]
            // });
          }
        } catch (error) {
          console.error('绘制坐标框失败:', error);
        }
        
        resolve();
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => {
      // 图片加载失败时使用默认图片
      const defaultImg = new window.Image();
      defaultImg.src = '/icons/search/default-noImage.png';
      defaultImg.onload = () => {
        canvas.width = width;
        canvas.height = height;
        ctx.clearRect(0, 0, width, height);
        ctx.drawImage(defaultImg, 0, 0, width, height);
        resolve();
      };
      defaultImg.onerror = () => reject(new Error('默认图片加载失败'));
    };
    
    img.src = src;
  });
};

/**
 * 获取原图URL并通过canvas处理后转成blob流
 * @param fetchImage - 获取图片的方法
 * @param name - 图片名称
 * @param coordinate - 坐标信息
 * @returns Promise<string> - 返回处理后的blob URL
 */
export const getProcessedImageBlob = async (
  fetchImage: (imageName: string, imageType: string) => Promise<string | null>,
  name: string,
  coordinate?: string
): Promise<string> => {
  try {
    // 获取原图URL
    const imageUrl = await fetchImage(name, "snap");
    console.log('获取到原图URL:', imageUrl);
    
    if (!imageUrl) {
      throw new Error('获取原图URL失败');
    }

    // 创建canvas处理图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('创建canvas上下文失败');
    }

    const img = new window.Image();
    img.crossOrigin = 'anonymous';

    return new Promise((resolve, reject) => {
      img.onload = () => {
        try {
          // 设置canvas尺寸为原图尺寸
          canvas.width = img.width;
          canvas.height = img.height;
          
          // 绘制原图
          ctx.drawImage(img, 0, 0);
          
          // 如果有坐标信息，绘制坐标框
          if (coordinate) {
            try {
              const coords = JSON.parse(coordinate);
              if (Array.isArray(coords) && coords.length === 4) {
                const [x1, y1, x2, y2] = coords;
                
                // 计算框的位置和大小
                const boxLeft = Math.min(x1, x2);
                const boxTop = Math.min(y1, y2);
                const boxWidth = Math.abs(x2 - x1);
                const boxHeight = Math.abs(y2 - y1);
                
                // 绘制红色框
                ctx.strokeStyle = '#ff0000';
                ctx.lineWidth = 2;
                ctx.strokeRect(boxLeft, boxTop, boxWidth, boxHeight);
              }
            } catch (error) {
              console.error('绘制坐标框失败:', error);
            }
          }
          
          // 转换为blob
          canvas.toBlob((blob) => {
            if (blob) {
              const blobUrl = URL.createObjectURL(blob);
              resolve(blobUrl);
            } else {
              reject(new Error('转换blob失败'));
            }
          }, 'image/jpeg', 0.9);
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('原图加载失败'));
      };
      
      img.src = imageUrl;
    });
  } catch (error) {
    console.error('处理图片失败:', error);
    throw error;
  }
};

/**
 * 创建带坐标框的canvas图片组件的点击处理函数
 * @param fetchImage - 获取图片的方法
 * @param onImageClick - 点击回调函数
 * @returns 点击处理函数
 */
export const createCanvasClickHandler = (
  fetchImage: (imageName: string, imageType: string) => Promise<string | null>,
  onImageClick?: (data: any) => void
) => {
  return async (
    e: React.MouseEvent<HTMLCanvasElement>,
    canvasRef: React.RefObject<HTMLCanvasElement>,
    name?: string,
    src?: string,
    coordinate?: string,
    time?: string,
    address?: string,
    score?: number | string
  ) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !name) return;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    console.log('点击位置:', x, y);
    
    try {
      // 获取处理后的图片blob URL
      const processedImageUrl = await getProcessedImageBlob(fetchImage, name, coordinate);
      console.log('获取到处理后的图片URL:', processedImageUrl);
      
      // 触发onImageClick回调，传递处理后的图片URL
      if (onImageClick) {
        onImageClick({
          name: name,
          imageUrl: processedImageUrl, // 这里传递的是处理后的blob URL
          thumbUrl: src || '',
          coordinate: coordinate || '',
          time: time,
          address: address,
          score: score
        });
      }
    } catch (error) {
      console.error('获取处理后的图片失败:', error);
    }
  };
};


