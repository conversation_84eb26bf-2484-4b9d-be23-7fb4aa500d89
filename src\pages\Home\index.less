.search-Image .ant-image {
    display: block !important;
}


.search-Image .ant-image .ant-image-mask {
    cursor: none !important;
}

.img_video_preview {
    width: 24px;
    content: url(/icons/search/preview.svg);
}

.img_video_preview:hover {
    content: url(/icons/search/preview_hover.svg);
}

.img_video_playback {
    width: 24px;
    content: url(/icons/search/playback.svg);
}

.img_video_playback:hover {
    content: url(/icons/search/playback_hover.svg);
}

#video-play::-webkit-media-controls-volume-control-container {
    display: none !important;
}

#video-play::-internal-media-controls-overflow-button {
    display: none !important;
}


#video-play::-webkit-media-controls-fullscreen-button {
    display: none !important;
}


#video-play::-webkit-media-controls-timeline {
    display: none !important;
}


.eventInfo-modal .ant-modal-close {
    top: 15px;
    margin: 10px 20px 10px 10px;
}

.condition-select .ant-select-outlined .ant-select-selector {
    border: 1px solid #d9d9d9 !important;
    background: #ffffff !important;
    border-radius: 80px !important;
  }


  .similarity-slider {
    .ant-slider-rail {
      height: 7px !important;
      background: rgba(230, 230, 230, 1) !important;
      border-radius: 20px !important;
      border: 1px solid rgba(217, 218, 220, 1) !important;
    }
    .ant-slider-track {
      height: 7px !important;
      border-radius: 20px !important;
    }
    .ant-slider-handle {
      width: 11px !important;
      height: 11px !important;
      //border: 1px solid rgba(6, 217, 89, 1) !important;
      //background: #fff !important;
      margin-top: 1px !important;
      box-shadow: none !important;
    }
  }