import {
    ProCard,
    ProForm,
    ProFormGroup,
    ProFormSelect,
    ProFormInstance,
    ProFormTextArea,
    ProFormText,
    ProList,
} from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { Descriptions, Typography, Divider, Card } from 'antd';
import ArrangeList from './ArrangeList';
import dayjs from 'dayjs';

import { InfoCircleOutlined, MessageOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;


type infoItem = {
    id: string;
    name: string;
    description?: string;
    icon_url?: string;
};

interface LeftData {
    modelName: string;
    tool_info_list: infoItem[];
    knowledge_info_list: infoItem[];
    workflow_info_list: infoItem[];
}

interface LeftDataProps {
    leftData: LeftData;

}


const Left: React.FC<LeftDataProps> = (props) => {
    const promptContent = props.leftData.prompt?.trim();
    const createdAt = props.leftData.created_at;
    const formattedCreatedAt = createdAt
        ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')
        : '-';
    return (
        <div
            style={{
                height: '100%',
                overflowY: 'auto',
                paddingRight: '8px',
                paddingBottom: '16px',
                //  background: '#fafafa',
                borderRadius: '8px',
            }}
        >
            <ProCard
                title={
                    <span style={{ fontSize: 16, fontWeight: 500 }}>
                        <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                        基础信息
                    </span>
                }
                bordered
                headerBordered
                layout="center"
                direction="column"
                style={{
                    minHeight: '100%',
                    // backgroundColor: '#fff',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                    borderRadius: 8,
                }}
            >
                {/* 名称 & 描述区域 */}
                <Descriptions
                    column={1}
                    size="middle"
                    labelStyle={{ fontWeight: 500, width: 80, textAlign: 'right' }}
                    contentStyle={{ textAlign: 'left' }}
                >
                    <Descriptions.Item label="类型">
                        <Text>
                            {props.leftData.type === 2
                                ? 'Coze'
                                : props.leftData.type === 3
                                    ? 'Dify'
                                    : '-'}
                        </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                        <Text>{formattedCreatedAt}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="描述">
                        <Paragraph
                            type="secondary"
                            ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                        >
                            {props.leftData.description || '-'}
                        </Paragraph>
                    </Descriptions.Item>
                </Descriptions>

                <Divider />
                {promptContent ? (
                    <Card
                        title="提示词"
                        size="small"
                        bordered={false}
                        styles={{
                            body: {
                                padding: '12px 16px',
                                background: '#f9f9f9',
                                minHeight: 80,
                            },
                        }}
                    >
                        <Paragraph
                            style={{ whiteSpace: 'pre-wrap', marginBottom: 0 }}
                            type="secondary"
                            ellipsis={{ rows: 15, expandable: true, symbol: '展开' }}
                        >
                            {promptContent}
                        </Paragraph>
                    </Card>
                ) : null}
            </ProCard>
        </div>
    );
};

export default Left;