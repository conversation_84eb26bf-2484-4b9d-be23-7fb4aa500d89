package com.zkteco.mars.usc.controller;


import com.zkteco.framework.base.bean.Pager;
import com.zkteco.framework.core.utils.I18nUtil;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.usc.constant.USCConstants;
import com.zkteco.mars.usc.service.DmcPointService;
import com.zkteco.mars.usc.vo.DmcPointItem;
import com.zkteco.mars.usc.vo.GetChannelItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 点位管理
 *
 * <AUTHOR>
 * @date 2024-12-31 9:38
 * @since 1.0.0
 */
@RestController
@RequestMapping("/v1/dmc_Point")
public class DmcPointController {

    @Autowired
    private DmcPointService dmcPointService;




    /**
     *  添加点位信息
     *
     * @param dmcPointList:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-01-10 13:53
     * @since 1.0.0
     */
    @PostMapping("/addPointInfo")
    @ResponseBody
    public ApiResultMessage addPointInfo(@RequestBody List<DmcPointItem> dmcPointList) {
        return dmcPointService.addPointInfo(dmcPointList);
    }

    /**
     *  添加点位信息
     *
     * @param dmcPointItemList:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-01-10 13:53
     * @since 1.0.0
     */
    @PostMapping("/addDeviceInfos")
    @ResponseBody
    public ApiResultMessage addDeviceInfos(@RequestBody List<DmcPointItem> dmcPointItemList) {
        return dmcPointService.addDeviceInfos(dmcPointItemList);
    }

    /**
     *  获取通道信息
     *
     * @param getChannelItem:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-01-10 13:53
     * @since 1.0.0
     */
    @PostMapping("/getChannelInfo")
    @ResponseBody
    public ApiResultMessage getChannelInfo(@RequestBody GetChannelItem getChannelItem) {
        return dmcPointService.getChannelInfo(getChannelItem);
    }


    /**
    * 分页查询点位列表
    *
    * @param dmcPointItem:
    * @param pageNo:
    * @param pageSize:
    * @return com.zkteco.framework.vo.ApiResultMessage
    * <AUTHOR>
    * @throws
    * @date  2025-01-11 10:18
    * @since 1.0.0
    */
    @PostMapping("/getPointInfoList")
    @ResponseBody
    public ApiResultMessage getPointInfoList(@RequestBody DmcPointItem dmcPointItem, @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(USCConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(USCConstants.API_PAGE_OVERSIZE, I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        try {
            Pager pager = dmcPointService.getItemsByPage(dmcPointItem, pageNo - 1, pageSize);
            List<DmcPointItem> pointInfoList = (List<DmcPointItem>) pager.getData();
            pager.setData(pointInfoList);
            return ApiResultMessage.successMessage(pager);
        } catch (Exception e) {
            return ApiResultMessage.failedMessage(-1);
        }
    }




    /**
     * 删除点位
     *
     * @param dmcPointList:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-01-15 20:57
     * @since 1.0.0
     */
    @PostMapping("/delPoints")
    @ResponseBody
    public ApiResultMessage delPoints(@RequestBody List<DmcPointItem> dmcPointList) {
        return dmcPointService.delPoints(dmcPointList);
    }


    /**
     * 在无通道的数据清空下删除流媒体设备
     *
     * @param deviceId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-05-30 17:19
     * @since 1.0.0
     */
    @PostMapping("/deleteDevice")
    @ResponseBody
    public ApiResultMessage deleteDevice(@RequestParam(name = "deviceId") String deviceId) {
        return dmcPointService.deleteDevice(deviceId);
    }



    /**
     * 编辑点位名称
     *
     * @param dmcPointItem:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-01-15 20:57
     * @since 1.0.0
     */
    @PostMapping("/editPointName")
    @ResponseBody
    public ApiResultMessage editChannel(@RequestBody DmcPointItem dmcPointItem) {
        return dmcPointService.editPointName(dmcPointItem);
    }


    /**
     * 编辑点位信息
     *
     * @param dmcPointItem:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-02-21 16:36
     * @since 1.0.0
     */
    @PostMapping("/editPointInfo")
    @ResponseBody
    public ApiResultMessage editPointInfo(@RequestBody DmcPointItem dmcPointItem) {
        return dmcPointService.editPointInfo(dmcPointItem);
    }


    /**
     * 获取所有的点位名称
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-28 16:46
     * @since 1.0.0
     */
    @PostMapping("/getAllPointName")
    @ResponseBody
    public ApiResultMessage getAllPointName() {
        return dmcPointService.getAllPointName();
    }

    /**
     * 获取点位ID和名称集合
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-12-31 10:00
     * @since 1.0.0
     */
    @PostMapping("/getPointIdAndNameList")
    @ResponseBody
    public ApiResultMessage getPointIdAndNameList() {
        return dmcPointService.getPointIdAndNameList();
    }




}
