import { Modal, Button, message, Form, Steps, Spin, Input, Select, FormInstance } from "antd"
import { useState, useRef, useEffect } from "react"
import { useIntl } from 'umi'
import { ProForm, ProFormText, ProFormDigit, ProFormSelect, ProTable, ProFormUploadButton, ProFormTextArea } from '@ant-design/pro-components';
import './index.less'
import { addDeviceInfos, getChannelInfo, addAgentInfo, editPointName } from "@/services/ant-design-pro/api";

const contentStyle: React.CSSProperties = {
};

const EditAgent: React.FC = (props: any, ref: any) => {

  /** 国际化 */
  const intl = useIntl();

  const [showPlatApiKey, setShowPlatApiKey] = useState(false);

  const formRef = useRef<FormInstance>();
  useEffect(() => {
    // 避免多次添加时数据没有清空
    // props.editData && form.resetFields()
  }, [props.visible])

  return (
    <>
      <Modal
        width={500}
        destroyOnClose
        styles={{
          body: { padding: '24px', height: '60vh', overflow: 'auto' }
        }}
        title={intl.formatMessage({ id: 'pages.agent.create', defaultMessage: '创建智能体' })}
        open={props.visible}
        onOk={async () => {
          try {
            const values = await formRef.current?.validateFields();
            if (values.logo && values.logo.length > 0) {
              values.logo = values.logo[0].response?.data?.url || values.logo[0].url;
            }
            const rs = await addAgentInfo(values);
            console.log("addAgentInfo rs->", rs)
            if (rs.code === 0) {
              props.setManualEditVisible(false);
              formRef.current?.resetFields();
              message.success(intl.formatMessage({ id: 'pages.pointManage.addSuccess', defaultMessage: '添加成功' }));
              props.loadAgentInfo({});
            } else {
              message.error(rs.code);
            }
          } catch (err) {
            console.error('提交失败', err);
            message.error(intl.formatMessage({ id: 'pages.agent.submitFailed', defaultMessage: '提交失败，请检查表单数据' }));
          }
        }}
        onCancel={() => {
          formRef.current?.resetFields();
          setShowPlatApiKey(false)
          props.setManualEditVisible(false);
        }}
      >
        <ProForm<{
          name: string;
        }>
          formRef={formRef}
          layout="vertical"
          style={{ width: '100%' }}
          labelAlign="left" // 设置 label 左对齐
          labelCol={{ span: 24 }} // 设置 label 的宽度
          wrapperCol={{ span: 24 }} // 设置输入框的宽度
          submitter={{
            resetButtonProps: {
              hidden: true,
            },
            submitButtonProps: {
              hidden: true,
            },
            searchConfig: {
            }
          }}
          onValuesChange={(changedValues, allValues) => {
            if ('type' in changedValues) {
              setShowPlatApiKey(changedValues.type === 3);
            }
          }}
        >
          <ProFormText
            width="md"
            name="name"
            initialValue={props.editData?.name}
            rules={[
              {
                required: true
              },
              {
                max: 200,
                message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 200"
              },
            ]}
            label={intl.formatMessage({ id: 'pages.agent.name', defaultMessage: '智能体名称' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.name', defaultMessage: '智能体名称' })}
            // maxLength={64}  // 限制最大输入64个字符
            tooltip={intl.formatMessage({ id: 'pages.agent.nameLimit', defaultMessage: '最多可输入64个字符' })}
            fieldProps={{
              maxLength: 64  // 将 maxLength 放在 fieldProps 中
            }}
          />
          <ProFormTextArea
            name="description"
            label={intl.formatMessage({ id: 'pages.agent.description', defaultMessage: '智能体功能介绍' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.descriptionTip', defaultMessage: '介绍智能体的功能，将会展示给智能体的用户' })}
            initialValue={props.editData?.description}
            rules={[
              {
                required: true
              },
              {
                max: 64,
                message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 64"
              },
            ]}
          />
          <ProFormSelect
            name="type"
            label={intl.formatMessage({ id: 'pages.agent.type', defaultMessage: '类型' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.type.placeholder', defaultMessage: '请选择智能体类型' })}
            options={[
              { label: 'Coze', value: 2 },
              { label: 'Dify', value: 3 },
            ]}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.type.required', defaultMessage: '请选择类型' }) }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 20 }}
          />

          <ProFormText
            name="platBotId"
            label={intl.formatMessage({ id: 'pages.agent.id', defaultMessage: '智能体 ID' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.id.placeholder', defaultMessage: '请输入智能体 ID' })}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.id.required', defaultMessage: '请输入智能体 ID' }) }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 20 }}
            extra={intl.formatMessage({ id: 'pages.agent.botId.tip', defaultMessage: '请前往对应平台（如 Coze、Dify）创建智能体后复制其 ID 粘贴到此处' })}
          />
          {showPlatApiKey && (
            <ProFormText
              name="platApiKey"
              label="API Key"
              placeholder="API Key"
              rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.apiKey.required', defaultMessage: 'API Key 为必填项' }) }]}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 20 }}
              extra={intl.formatMessage({ id: 'pages.agent.apiKey.tip', defaultMessage: '请前往 Dify 平台，复制其 API Key 粘贴到此处' })}
            />
          )}
          <ProFormUploadButton
            width="md"
            name="logo"
            label={intl.formatMessage({ id: 'pages.agent.icon', defaultMessage: '图标' })}
            max={1}
            fieldProps={{
              name: 'file',
              listType: 'picture-card',
              beforeUpload: (file) => {
                // 限制文件类型和大小
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                  message.error(intl.formatMessage({ id: 'pages.agent.imageOnly', defaultMessage: '只能上传图片文件' }));
                  return false;
                }
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                  message.error(intl.formatMessage({ id: 'pages.agent.imageSizeLimit', defaultMessage: '图片大小不能超过2MB' }));
                  return false;
                }
                return true;
              }
            }}
            action={process.env.API_UPLOAD_URL || "/api/upload"}
            extra={intl.formatMessage({ id: 'pages.agent.imageFormatLimit', defaultMessage: '支持jpg/png格式，大小不超过2MB' })}
            onChange={(info) => {
              if (info.file.status === 'done') {
                const { url } = info.file.response.data;
                console.log("logo:", url)
                formRef.current?.setFieldsValue({ logo: url });
              }
            }}
          />

        </ProForm>
      </Modal>
    </>
  )
}
export default EditAgent
