"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[970],{94737:function(ge,B){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"};B.Z=e},51042:function(ge,B,e){var p=e(1413),v=e(67294),w=e(42110),d=e(91146),k=function(F,A){return v.createElement(d.Z,(0,p.Z)((0,p.Z)({},F),{},{ref:A,icon:w.Z}))},Z=v.forwardRef(k);B.Z=Z},64317:function(ge,B,e){var p=e(1413),v=e(45987),w=e(22270),d=e(67294),k=e(66758),Z=e(27577),te=e(85893),F=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],A=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],S=function(I,n){var q=I.fieldProps,a=I.children,s=I.params,o=I.proFieldProps,h=I.mode,L=I.valueEnum,K=I.request,W=I.showSearch,O=I.options,N=(0,v.Z)(I,F),$=(0,d.useContext)(k.Z);return(0,te.jsx)(Z.Z,(0,p.Z)((0,p.Z)({valueEnum:(0,w.h)(L),request:K,params:s,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,p.Z)({options:O,mode:h,showSearch:W,getPopupContainer:$.getPopupContainer},q),ref:n,proFieldProps:o},N),{},{children:a}))},le=d.forwardRef(function(E,I){var n=E.fieldProps,q=E.children,a=E.params,s=E.proFieldProps,o=E.mode,h=E.valueEnum,L=E.request,K=E.options,W=(0,v.Z)(E,A),O=(0,p.Z)({options:K,mode:o||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},n),N=(0,d.useContext)(k.Z);return(0,te.jsx)(Z.Z,(0,p.Z)((0,p.Z)({valueEnum:(0,w.h)(h),request:L,params:a,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,p.Z)({getPopupContainer:N.getPopupContainer},O),ref:I,proFieldProps:s},W),{},{children:q}))}),ie=d.forwardRef(S),U=le,b=ie;b.SearchSelect=U,b.displayName="ProFormComponent",B.Z=b},5966:function(ge,B,e){var p=e(97685),v=e(1413),w=e(45987),d=e(21770),k=e(53025),Z=e(55241),te=e(97435),F=e(67294),A=e(27577),S=e(85893),le=["fieldProps","proFieldProps"],ie=["fieldProps","proFieldProps"],U="text",b=function(a){var s=a.fieldProps,o=a.proFieldProps,h=(0,w.Z)(a,le);return(0,S.jsx)(A.Z,(0,v.Z)({valueType:U,fieldProps:s,filedConfig:{valueType:U},proFieldProps:o},h))},E=function(a){var s=(0,d.Z)(a.open||!1,{value:a.open,onChange:a.onOpenChange}),o=(0,p.Z)(s,2),h=o[0],L=o[1];return(0,S.jsx)(k.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(W){var O,N=W.getFieldValue(a.name||[]);return(0,S.jsx)(Z.Z,(0,v.Z)((0,v.Z)({getPopupContainer:function(T){return T&&T.parentNode?T.parentNode:T},onOpenChange:function(T){return L(T)},content:(0,S.jsxs)("div",{style:{padding:"4px 0"},children:[(O=a.statusRender)===null||O===void 0?void 0:O.call(a,N),a.strengthText?(0,S.jsx)("div",{style:{marginTop:10},children:(0,S.jsx)("span",{children:a.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},a.popoverProps),{},{open:h,children:a.children}))}})},I=function(a){var s=a.fieldProps,o=a.proFieldProps,h=(0,w.Z)(a,ie),L=(0,F.useState)(!1),K=(0,p.Z)(L,2),W=K[0],O=K[1];return s!=null&&s.statusRender&&h.name?(0,S.jsx)(E,{name:h.name,statusRender:s==null?void 0:s.statusRender,popoverProps:s==null?void 0:s.popoverProps,strengthText:s==null?void 0:s.strengthText,open:W,onOpenChange:O,children:(0,S.jsx)("div",{children:(0,S.jsx)(A.Z,(0,v.Z)({valueType:"password",fieldProps:(0,v.Z)((0,v.Z)({},(0,te.Z)(s,["statusRender","popoverProps","strengthText"])),{},{onBlur:function($){var T;s==null||(T=s.onBlur)===null||T===void 0||T.call(s,$),O(!1)},onClick:function($){var T;s==null||(T=s.onClick)===null||T===void 0||T.call(s,$),O(!0)}}),proFieldProps:o,filedConfig:{valueType:U}},h))})}):(0,S.jsx)(A.Z,(0,v.Z)({valueType:"password",fieldProps:s,proFieldProps:o,filedConfig:{valueType:U}},h))},n=b;n.Password=I,n.displayName="ProFormComponent",B.Z=n},2236:function(ge,B,e){e.d(B,{S:function(){return a}});var p=e(1413),v=e(4942),w=e(71002),d=e(45987),k=e(12044),Z=e(21532),te=e(93967),F=e.n(te),A=e(97435),S=e(67294),le=e(73935),ie=e(76509),U=e(98082),b=function(o){return(0,v.Z)({},o.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,U.uK)(o.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(o.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:o.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:o.colorText},"&-right":{color:o.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function E(s){return(0,U.Xj)("ProLayoutFooterToolbar",function(o){var h=(0,p.Z)((0,p.Z)({},o),{},{componentCls:".".concat(s)});return[b(h)]})}function I(s,o){var h=o.stylish;return(0,U.Xj)("ProLayoutFooterToolbarStylish",function(L){var K=(0,p.Z)((0,p.Z)({},L),{},{componentCls:".".concat(s)});return h?[(0,v.Z)({},"".concat(K.componentCls),h==null?void 0:h(K))]:[]})}var n=e(85893),q=["children","className","extra","portalDom","style","renderContent"],a=function(o){var h=o.children,L=o.className,K=o.extra,W=o.portalDom,O=W===void 0?!0:W,N=o.style,$=o.renderContent,T=(0,d.Z)(o,q),Ee=(0,S.useContext)(Z.ZP.ConfigContext),Me=Ee.getPrefixCls,Pe=Ee.getTargetContainer,De=o.prefixCls||Me("pro"),H="".concat(De,"-footer-bar"),Ze=E(H),Fe=Ze.wrapSSR,pe=Ze.hashId,l=(0,S.useContext)(ie.X),ce=(0,S.useMemo)(function(){var de=l.hasSiderMenu,be=l.isMobile,se=l.siderWidth;if(de)return se?be?"100%":"calc(100% - ".concat(se,"px)"):"100%"},[l.collapsed,l.hasSiderMenu,l.isMobile,l.siderWidth]),Ie=(0,S.useMemo)(function(){return(typeof window=="undefined"?"undefined":(0,w.Z)(window))===void 0||(typeof document=="undefined"?"undefined":(0,w.Z)(document))===void 0?null:(Pe==null?void 0:Pe())||document.body},[]),fe=I("".concat(H,".").concat(H,"-stylish"),{stylish:o.stylish}),je=(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"".concat(H,"-left ").concat(pe).trim(),children:K}),(0,n.jsx)("div",{className:"".concat(H,"-right ").concat(pe).trim(),children:h})]});(0,S.useEffect)(function(){return!l||!(l!=null&&l.setHasFooterToolbar)?function(){}:(l==null||l.setHasFooterToolbar(!0),function(){var de;l==null||(de=l.setHasFooterToolbar)===null||de===void 0||de.call(l,!1)})},[]);var Te=(0,n.jsx)("div",(0,p.Z)((0,p.Z)({className:F()(L,pe,H,(0,v.Z)({},"".concat(H,"-stylish"),!!o.stylish)),style:(0,p.Z)({width:ce},N)},(0,A.Z)(T,["prefixCls"])),{},{children:$?$((0,p.Z)((0,p.Z)((0,p.Z)({},o),l),{},{leftWidth:ce}),je):je})),Ae=!(0,k.j)()||!O||!Ie?Te:(0,le.createPortal)(Te,Ie,H);return fe.wrapSSR(Fe((0,n.jsx)(S.Fragment,{children:Ae},H)))}},76509:function(ge,B,e){e.d(B,{X:function(){return v}});var p=e(67294),v=(0,p.createContext)({})},94278:function(ge,B,e){e.r(B),e.d(B,{default:function(){return be}});var p=e(97857),v=e.n(p),w=e(15009),d=e.n(w),k=e(99289),Z=e.n(k),te=e(5574),F=e.n(te),A=e(2618),S=e(51042),le=e(53199),ie=e(2236),U=e(34994),b=e(5966),E=e(1413),I=e(45987),n=e(67294),q=e(27577),a=e(85893),s=["fieldProps","proFieldProps"],o=function(c,r){var X=c.fieldProps,C=c.proFieldProps,g=(0,I.Z)(c,s);return(0,a.jsx)(q.Z,(0,E.Z)({ref:r,valueType:"textarea",fieldProps:X,proFieldProps:C},g))},h=n.forwardRef(o),L=e(64317),K=e(87462),W=e(94737),O=e(57080),N=function(c,r){return n.createElement(O.Z,(0,K.Z)({},c,{ref:r,icon:W.Z}))},$=n.forwardRef(N),T=$,Ee=e(11550),Me=e(28036),Pe=e(9105),De=e(90789),H=["fieldProps","action","accept","listType","title","max","icon","buttonProps","disabled","proFieldProps"],Ze=function(c,r){var X,C=c.fieldProps,g=c.action,m=c.accept,_=c.listType,ne=c.title,ue=ne===void 0?"\u5355\u51FB\u4E0A\u4F20":ne,G=c.max,ee=c.icon,y=ee===void 0?(0,a.jsx)(T,{}):ee,R=c.buttonProps,j=c.disabled,ae=c.proFieldProps,re=(0,I.Z)(c,H),oe=(0,n.useMemo)(function(){var me;return(me=re.fileList)!==null&&me!==void 0?me:re.value},[re.fileList,re.value]),D=(0,n.useContext)(Pe.A),Oe=(ae==null?void 0:ae.mode)||D.mode||"edit",Be=(G===void 0||!oe||(oe==null?void 0:oe.length)<G)&&Oe!=="read",ve=(_!=null?_:C==null?void 0:C.listType)==="picture-card";return(0,a.jsx)(Ee.Z,(0,E.Z)((0,E.Z)({action:g,accept:m,ref:r,listType:_||"picture",fileList:oe},C),{},{name:(X=C==null?void 0:C.name)!==null&&X!==void 0?X:"file",onChange:function(Re){var V;C==null||(V=C.onChange)===null||V===void 0||V.call(C,Re)},children:Be&&(ve?(0,a.jsxs)("span",{children:[y," ",ue]}):(0,a.jsxs)(Me.ZP,(0,E.Z)((0,E.Z)({disabled:j||(C==null?void 0:C.disabled)},R),{},{children:[y,ue]})))}))},Fe=(0,De.G)(n.forwardRef(Ze),{getValueFromEvent:function(c){return c.fileList}}),pe=Fe,l=e(2453),ce=e(17788),Ie=e(66309),fe=e(71551),je={},Te=function(c,r){var X,C,g=(0,fe.useIntl)(),m=(0,n.useState)(!1),_=F()(m,2),ne=_[0],ue=_[1],G=(0,n.useRef)();return(0,n.useEffect)(function(){},[c.visible]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(ce.Z,{width:500,destroyOnClose:!0,styles:{body:{padding:"24px",height:"60vh",overflow:"auto"}},title:g.formatMessage({id:"pages.agent.create",defaultMessage:"\u521B\u5EFA\u667A\u80FD\u4F53"}),open:c.visible,onOk:Z()(d()().mark(function ee(){var y,R,j,ae,re;return d()().wrap(function(D){for(;;)switch(D.prev=D.next){case 0:return D.prev=0,D.next=3,(y=G.current)===null||y===void 0?void 0:y.validateFields();case 3:return R=D.sent,R.logo&&R.logo.length>0&&(R.logo=((j=R.logo[0].response)===null||j===void 0||(j=j.data)===null||j===void 0?void 0:j.url)||R.logo[0].url),D.next=7,(0,A.tL)(R);case 7:ae=D.sent,console.log("addAgentInfo rs->",ae),ae.code===0?(c.setManualEditVisible(!1),(re=G.current)===null||re===void 0||re.resetFields(),l.ZP.success(g.formatMessage({id:"pages.pointManage.addSuccess",defaultMessage:"\u6DFB\u52A0\u6210\u529F"})),c.loadAgentInfo({})):l.ZP.error(ae.code),D.next=16;break;case 12:D.prev=12,D.t0=D.catch(0),console.error("\u63D0\u4EA4\u5931\u8D25",D.t0),l.ZP.error(g.formatMessage({id:"pages.agent.submitFailed",defaultMessage:"\u63D0\u4EA4\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u8868\u5355\u6570\u636E"}));case 16:case"end":return D.stop()}},ee,null,[[0,12]])})),onCancel:function(){var y;(y=G.current)===null||y===void 0||y.resetFields(),ue(!1),c.setManualEditVisible(!1)},children:(0,a.jsxs)(U.A,{formRef:G,layout:"vertical",style:{width:"100%"},labelAlign:"left",labelCol:{span:24},wrapperCol:{span:24},submitter:{resetButtonProps:{hidden:!0},submitButtonProps:{hidden:!0},searchConfig:{}},onValuesChange:function(y,R){"type"in y&&ue(y.type===3)},children:[(0,a.jsx)(b.Z,{width:"md",name:"name",initialValue:(X=c.editData)===null||X===void 0?void 0:X.name,rules:[{required:!0},{max:200,message:g.formatMessage({id:"pages.pointManage.charLimitExceeded"})+" 200"}],label:g.formatMessage({id:"pages.agent.name",defaultMessage:"\u667A\u80FD\u4F53\u540D\u79F0"}),placeholder:g.formatMessage({id:"pages.agent.name",defaultMessage:"\u667A\u80FD\u4F53\u540D\u79F0"}),tooltip:g.formatMessage({id:"pages.agent.nameLimit",defaultMessage:"\u6700\u591A\u53EF\u8F93\u516564\u4E2A\u5B57\u7B26"}),fieldProps:{maxLength:64}}),(0,a.jsx)(h,{name:"description",label:g.formatMessage({id:"pages.agent.description",defaultMessage:"\u667A\u80FD\u4F53\u529F\u80FD\u4ECB\u7ECD"}),placeholder:g.formatMessage({id:"pages.agent.descriptionTip",defaultMessage:"\u4ECB\u7ECD\u667A\u80FD\u4F53\u7684\u529F\u80FD\uFF0C\u5C06\u4F1A\u5C55\u793A\u7ED9\u667A\u80FD\u4F53\u7684\u7528\u6237"}),initialValue:(C=c.editData)===null||C===void 0?void 0:C.description,rules:[{required:!0},{max:64,message:g.formatMessage({id:"pages.pointManage.charLimitExceeded"})+" 64"}]}),(0,a.jsx)(L.Z,{name:"type",label:g.formatMessage({id:"pages.agent.type",defaultMessage:"\u7C7B\u578B"}),placeholder:g.formatMessage({id:"pages.agent.type.placeholder",defaultMessage:"\u8BF7\u9009\u62E9\u667A\u80FD\u4F53\u7C7B\u578B"}),options:[{label:"Coze",value:2},{label:"Dify",value:3}],rules:[{required:!0,message:g.formatMessage({id:"pages.agent.type.required",defaultMessage:"\u8BF7\u9009\u62E9\u7C7B\u578B"})}],labelCol:{span:6},wrapperCol:{span:20}}),(0,a.jsx)(b.Z,{name:"platBotId",label:g.formatMessage({id:"pages.agent.id",defaultMessage:"\u667A\u80FD\u4F53 ID"}),placeholder:g.formatMessage({id:"pages.agent.id.placeholder",defaultMessage:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53 ID"}),rules:[{required:!0,message:g.formatMessage({id:"pages.agent.id.required",defaultMessage:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53 ID"})}],labelCol:{span:6},wrapperCol:{span:20},extra:g.formatMessage({id:"pages.agent.botId.tip",defaultMessage:"\u8BF7\u524D\u5F80\u5BF9\u5E94\u5E73\u53F0\uFF08\u5982 Coze\u3001Dify\uFF09\u521B\u5EFA\u667A\u80FD\u4F53\u540E\u590D\u5236\u5176 ID \u7C98\u8D34\u5230\u6B64\u5904"})}),ne&&(0,a.jsx)(b.Z,{name:"platApiKey",label:"API Key",placeholder:"API Key",rules:[{required:!0,message:g.formatMessage({id:"pages.agent.apiKey.required",defaultMessage:"API Key \u4E3A\u5FC5\u586B\u9879"})}],labelCol:{span:6},wrapperCol:{span:20},extra:g.formatMessage({id:"pages.agent.apiKey.tip",defaultMessage:"\u8BF7\u524D\u5F80 Dify \u5E73\u53F0\uFF0C\u590D\u5236\u5176 API Key \u7C98\u8D34\u5230\u6B64\u5904"})}),(0,a.jsx)(pe,{width:"md",name:"logo",label:g.formatMessage({id:"pages.agent.icon",defaultMessage:"\u56FE\u6807"}),max:1,fieldProps:{name:"file",listType:"picture-card",beforeUpload:function(y){var R=y.type.startsWith("image/");if(!R)return l.ZP.error(g.formatMessage({id:"pages.agent.imageOnly",defaultMessage:"\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6"})),!1;var j=y.size/1024/1024<2;return j?!0:(l.ZP.error(g.formatMessage({id:"pages.agent.imageSizeLimit",defaultMessage:"\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC72MB"})),!1)}},action:{NODE_ENV:"production",PUBLIC_PATH:"/"}.API_UPLOAD_URL||"/api/upload",extra:g.formatMessage({id:"pages.agent.imageFormatLimit",defaultMessage:"\u652F\u6301jpg/png\u683C\u5F0F\uFF0C\u5927\u5C0F\u4E0D\u8D85\u8FC72MB"}),onChange:function(y){if(y.file.status==="done"){var R,j=y.file.response.data.url;console.log("logo:",j),(R=G.current)===null||R===void 0||R.setFieldsValue({logo:j})}}})]})})})},Ae=Te,de=function(c){var r=(0,fe.useIntl)(),X=(0,n.useRef)(),C=(0,n.useState)(),g=F()(C,2),m=g[0],_=g[1],ne=(0,n.useRef)(),ue=(0,n.useState)(!1),G=F()(ue,2),ee=G[0],y=G[1],R=(0,n.useState)(),j=F()(R,2),ae=j[0],re=j[1],oe=(0,n.useState)([]),D=F()(oe,2),Oe=D[0],Be=D[1],ve=(0,n.useRef)(),me=(0,n.useState)({name:"",pageSize:10,pageNo:1,total:0}),Re=F()(me,2),V=Re[0],Le=Re[1],Xe=(0,n.useState)(!1),We=F()(Xe,2),Ge=We[0],Ke=We[1],Je=(0,n.useState)([]),$e=F()(Je,2),xe=$e[0],Ve=$e[1],Qe=(0,n.useState)(!1),ze=F()(Qe,2),Ye=ze[0],Ue=ze[1],na=(0,n.useRef)(),Ne=(0,fe.useNavigate)(),ke=function(i,t){return(0,a.jsxs)("div",{id:"operate",children:[(0,a.jsx)("a",{onClick:function(){return aa([t])},children:(0,a.jsx)("img",{className:"img_arrange",title:r.formatMessage({id:"pages.agent.orchestration",defaultMessage:"\u7F16\u6392"})})}),(0,a.jsx)("a",{onClick:function(){return ta(t)},children:(0,a.jsx)("img",{className:"img_edit",title:r.formatMessage({id:"pages.pointManage.edit",defaultMessage:"\u7F16\u8F91"})})}),(0,a.jsx)("a",{onClick:function(){return He([t])},children:(0,a.jsx)("img",{className:"img_del",title:r.formatMessage({id:"pages.pointManage.delete",defaultMessage:"\u5220\u9664"})})})]})},qe=function(){var P=Z()(d()().mark(function i(){return d()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:y(!0);case 1:case"end":return u.stop()}},i)}));return function(){return P.apply(this,arguments)}}(),_e=function(){var P=Z()(d()().mark(function i(t,u,f,x){return d()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:console.log("page change",t,u,f,x),Le(v()(v()({},V),{},{pageSize:t.pageSize,pageNo:t.current})),Ce({current:t.current,size:t.pageSize});case 3:case"end":return z.stop()}},i)}));return function(t,u,f,x){return P.apply(this,arguments)}}(),ea=function(){var P=Z()(d()().mark(function i(t){return d()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:console.log("point search",t),Le(v()(v()({},V),{},{pageNo:1,name:t.name})),Ce({current:1,name:t.name?t.name:"no&input"});case 3:case"end":return f.stop()}},i)}));return function(t){return P.apply(this,arguments)}}(),aa=function(){var P=Z()(d()().mark(function i(t){var u;return d()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:if(console.log("rows",t),t[0].id){x.next=4;break}return l.ZP.error("\u65E0\u6CD5\u8DF3\u8F6C\uFF1A\u7F3A\u5C11ID"),x.abrupt("return");case 4:return x.next=6,(0,A.OV)({id:t[0].id});case 6:u=x.sent,u.code===0&&u.data?Ne("/agent/agentManage/agentArrange/".concat(t[0].id),{state:{data:u.data}}):Ne("/agent/agentManage/agentArrange/".concat(t[0].id),{state:{data:{agent_item:{name:t[0].name}}}});case 8:case"end":return x.stop()}},i)}));return function(t){return P.apply(this,arguments)}}(),ta=function(){var P=Z()(d()().mark(function i(t){return d()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:Ue(!0),console.log("editRecord->",t),_(t),t.type===3?Ke(!0):Ke(!1);case 4:case"end":return f.stop()}},i)}));return function(t){return P.apply(this,arguments)}}(),He=function(){var P=Z()(d()().mark(function i(t){return d()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:console.log("del data:",t),ce.Z.confirm({title:r.formatMessage({id:"pages.pointManage.deleteRecord",defaultMessage:"\u5220\u9664\u8BB0\u5F55"}),content:r.formatMessage({id:"pages.pointManage.confirmDeleteRecord",defaultMessage:"\u786E\u5B9A\u5220\u9664\u8BB0\u5F55\u5417\uFF1F"}),okText:r.formatMessage({id:"pages.pointManage.confirm",defaultMessage:"\u786E\u8BA4"}),cancelText:r.formatMessage({id:"pages.pointManage.cancel",defaultMessage:"\u53D6\u6D88"}),onOk:function(){var x=Z()(d()().mark(function z(){var M,Q,Y,ye,we;return d()().wrap(function(Se){for(;;)switch(Se.prev=Se.next){case 0:return M=l.ZP.loading(r.formatMessage({id:"pages.pointManage.deleting",defaultMessage:"\u6B63\u5728\u5220\u9664"})),Q=0,Y=0,ye=[],Se.next=6,(0,A.pI)(t);case 6:we=Se.sent,we.code===0?l.ZP.success(r.formatMessage({id:"pages.pointManage.deleteSuccess",defaultMessage:"\u5220\u9664\u6210\u529F\uFF0C\u81EA\u52A8\u5237\u65B0"})):l.ZP.error(r.formatMessage({id:"pages.pointManage.deleteFailure",defaultMessage:"\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"})),Ve([]),Ce({}),M();case 11:case"end":return Se.stop()}},z)}));function J(){return x.apply(this,arguments)}return J}()});case 2:case"end":return f.stop()}},i)}));return function(t){return P.apply(this,arguments)}}(),ra=function(i,t){var u=t.status,f=[{label:"\u5DF2\u53D1\u5E03",value:2},{label:"\u672A\u53D1\u5E03",value:1}];if(!u)return null;var x=typeof u=="string"?u.split(",").map(Number):[Number(u)],J=x.map(function(M){var Q;return(Q=f.find(function(Y){return Y.value===M}))===null||Q===void 0?void 0:Q.label}).filter(Boolean),z=J.slice(0,3);return(0,a.jsx)("div",{style:{whiteSpace:"pre-wrap"},children:z.map(function(M,Q){return(0,a.jsx)(Ie.Z,{color:"rgb(11, 211, 87)",children:M},Q)})})},sa=[{title:"id",dataIndex:"id",hideInSearch:!0,ellipsis:!0,hideInTable:!0},{title:r.formatMessage({id:"pages.config.name",defaultMessage:"\u540D\u79F0"}),dataIndex:"name",width:300,hideInSearch:!1,ellipsis:!0},{title:r.formatMessage({id:"pages.agent.functionIntroduction",defaultMessage:"\u529F\u80FD\u4ECB\u7ECD"}),dataIndex:"description",hideInSearch:!0,ellipsis:!0},{title:r.formatMessage({id:"pages.agent.icon",defaultMessage:"\u56FE\u6807"}),dataIndex:"logo",width:120,hideInSearch:!0,ellipsis:!0,hideInTable:!0},{title:r.formatMessage({id:"pages.agent.publishStatus",defaultMessage:"\u53D1\u5E03\u72B6\u6001"}),dataIndex:"status",width:200,hideInSearch:!0,ellipsis:!0,render:ra},{title:r.formatMessage({id:"pages.agent.creationTime",defaultMessage:"\u521B\u5EFA\u65F6\u95F4"}),dataIndex:"createTime",width:200,hideInSearch:!0,ellipsis:!0},{title:r.formatMessage({id:"pages.pointManage.operation",defaultMessage:"\u64CD\u4F5C"}),width:200,dataIndex:"option",valueType:"option",render:ke}],Ce=function(){var P=Z()(d()().mark(function i(t){var u,f,x,J,z,M,Q,Y,ye;return d()().wrap(function(he){for(;;)switch(he.prev=he.next){case 0:return console.log("load agents",ve.current,(u=ve.current)===null||u===void 0?void 0:u.getFieldsValue()),J=(f=ve.current)===null||f===void 0?void 0:f.getFieldsValue(),z=t.current?t.current:V.pageNo,M=t.size?t.size:V.pageSize,Q=t.name?t.name:J.name,t.name==="no&input"&&(Q=void 0),he.next=8,(0,A.he)({name:Q},z,M);case 8:Y=he.sent,console.log("getAgentInfoList rs->",Y),Y.code===0&&Y.data&&((x=Y.data)===null||x===void 0?void 0:x.data.length)>=0&&(Be((ye=Y.data)===null||ye===void 0?void 0:ye.data),Le(v()(v()({},V),{},{pageSize:M,pageNo:z,total:Y.data.total})));case 11:case"end":return he.stop()}},i)}));return function(t){return P.apply(this,arguments)}}();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(le.Z,{rowKey:"id",headerTitle:r.formatMessage({id:"pages.agent.agentDisplay",defaultMessage:"\u667A\u80FD\u4F53\u5C55\u793A"}),actionRef:X,formRef:ve,pagination:{current:V.pageNo,pageSize:V.pageSize,showQuickJumper:!0,showSizeChanger:!0,showPrevNextJumpers:!0,showTitle:!0,pageSizeOptions:["10","20","50","100"],total:V.total},onChange:_e,rowSelection:{onChange:function(i,t){console.log("selectedRows-> ",t),Ve(t)}},beforeSearchSubmit:ea,columns:sa,tableAlertRender:!1,options:{density:!1,setting:!1},dataSource:Oe,toolBarRender:function(){return[(0,a.jsxs)(Me.ZP,{type:"primary",onClick:qe,children:[(0,a.jsx)(S.Z,{}),r.formatMessage({id:"pages.agent.create",defaultMessage:"\u521B\u5EFA\u667A\u80FD\u4F53"})]},"primary")]}}),(0,a.jsx)("div",{children:(0,a.jsx)(Ae,{visible:ee,setManualEditVisible:y,loadAgentInfo:Ce,editData:ae})}),(xe==null?void 0:xe.length)>0&&(0,a.jsx)(ie.S,{extra:(0,a.jsxs)("div",{children:[r.formatMessage({id:"pages.pointManage.selected",defaultMessage:"\u5DF2\u9009\u62E9"}),(0,a.jsx)("a",{style:{fontWeight:600,color:"rgb(11, 211, 87)"},children:xe.length}),r.formatMessage({id:"pages.pointManage.item",defaultMessage:"\u9879"})]}),children:(0,a.jsx)(Me.ZP,{onClick:function(){return He(xe)},children:r.formatMessage({id:"pages.pointManage.batchDelete",defaultMessage:"\u6279\u91CF\u5220\u9664"})})}),(0,a.jsx)(ce.Z,{width:500,destroyOnClose:!0,styles:{body:{padding:"24px",height:"60vh",overflow:"auto"}},title:r.formatMessage({id:"pages.pointManage.edit",defaultMessage:"\u7F16\u8F91"}),open:Ye,onOk:Z()(d()().mark(function P(){var i,t,u,f,x,J;return d()().wrap(function(M){for(;;)switch(M.prev=M.next){case 0:return M.prev=0,M.next=3,(i=ne.current)===null||i===void 0?void 0:i.validateFields();case 3:return t=M.sent,u=m==null?void 0:m.id,t.id=u,console.log("addAgentInfo values->",t),t.logo&&t.logo.length>0&&(t.logo=((f=t.logo[0].response)===null||f===void 0||(f=f.data)===null||f===void 0?void 0:f.url)||t.logo[0].url),M.next=10,(0,A.tL)(t);case 10:x=M.sent,console.log("addAgentInfo rs->",x),x.code===0?(Ue(!1),(J=ne.current)===null||J===void 0||J.resetFields(),l.ZP.success(r.formatMessage({id:"pages.pointManage.editSuccess",defaultMessage:"\u7F16\u8F91\u6210\u529F"})),Ce({})):l.ZP.error(x.code),M.next=19;break;case 15:M.prev=15,M.t0=M.catch(0),console.error("\u63D0\u4EA4\u5931\u8D25",M.t0),l.ZP.error(r.formatMessage({id:"pages.agent.submitFailed",defaultMessage:"\u63D0\u4EA4\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u8868\u5355\u6570\u636E"}));case 19:_({});case 20:case"end":return M.stop()}},P,null,[[0,15]])})),onCancel:function(){return Ue(!1)},children:(0,a.jsxs)(U.A,{formRef:ne,layout:"vertical",style:{width:"100%"},labelAlign:"left",labelCol:{span:24},wrapperCol:{span:24},submitter:{resetButtonProps:{hidden:!0},submitButtonProps:{hidden:!0},searchConfig:{}},onValuesChange:function(i,t){"type"in i&&Ke(i.type===3)},children:[(0,a.jsx)(b.Z,{name:"id",label:r.formatMessage({id:"pages.primaryKey.id",defaultMessage:"\u4E3B\u952E ID"}),labelCol:{span:6},wrapperCol:{span:20},readonly:!0,initialValue:m==null?void 0:m.id}),(0,a.jsx)(b.Z,{width:"md",name:"name",initialValue:m==null?void 0:m.name,rules:[{required:!0},{max:64,message:r.formatMessage({id:"pages.pointManage.charLimitExceeded"})+" 64"}],label:r.formatMessage({id:"pages.agent.name",defaultMessage:"\u667A\u80FD\u4F53\u540D\u79F0"}),placeholder:r.formatMessage({id:"pages.agent.name",defaultMessage:"\u667A\u80FD\u4F53\u540D\u79F0"}),labelCol:{span:24},wrapperCol:{span:24},tooltip:r.formatMessage({id:"pages.agent.nameLimit",defaultMessage:"\u6700\u591A\u53EF\u8F93\u516564\u4E2A\u5B57\u7B26"}),fieldProps:{maxLength:64}}),(0,a.jsx)(h,{name:"description",label:r.formatMessage({id:"pages.agent.description",defaultMessage:"\u667A\u80FD\u4F53\u529F\u80FD\u4ECB\u7ECD"}),placeholder:r.formatMessage({id:"pages.agent.descriptionTip",defaultMessage:"\u4ECB\u7ECD\u667A\u80FD\u4F53\u7684\u529F\u80FD\uFF0C\u5C06\u4F1A\u5C55\u793A\u7ED9\u667A\u80FD\u4F53\u7684\u7528\u6237"}),initialValue:m==null?void 0:m.description,rules:[{required:!0},{max:200,message:r.formatMessage({id:"pages.pointManage.charLimitExceeded"})+" 200"}]}),(0,a.jsx)(L.Z,{name:"type",label:r.formatMessage({id:"pages.agent.type",defaultMessage:"\u7C7B\u578B"}),placeholder:r.formatMessage({id:"pages.agent.type.placeholder",defaultMessage:"\u8BF7\u9009\u62E9\u667A\u80FD\u4F53\u7C7B\u578B"}),options:[{label:"Coze",value:2},{label:"Dify",value:3}],rules:[{required:!0,message:r.formatMessage({id:"pages.agent.type.required",defaultMessage:"\u8BF7\u9009\u62E9\u7C7B\u578B"})}],labelCol:{span:6},wrapperCol:{span:20},initialValue:m==null?void 0:m.type}),(0,a.jsx)(b.Z,{name:"platBotId",label:r.formatMessage({id:"pages.agent.id",defaultMessage:"\u667A\u80FD\u4F53 ID"}),placeholder:r.formatMessage({id:"pages.agent.id.placeholder",defaultMessage:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53 ID"}),rules:[{required:!0,message:r.formatMessage({id:"pages.agent.id.required",defaultMessage:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53 ID"})}],labelCol:{span:6},wrapperCol:{span:20},initialValue:m==null?void 0:m.platBotId,extra:r.formatMessage({id:"pages.agent.botId.tip",defaultMessage:"\u8BF7\u524D\u5F80\u5BF9\u5E94\u5E73\u53F0\uFF08\u5982 Coze\u3001Dify\uFF09\u521B\u5EFA\u667A\u80FD\u4F53\u540E\u590D\u5236\u5176 ID \u7C98\u8D34\u5230\u6B64\u5904"})}),Ge&&(0,a.jsx)(b.Z,{name:"platApiKey",label:"API Key",placeholder:"API Key",rules:[{required:!0,message:r.formatMessage({id:"pages.agent.apiKey.required",defaultMessage:"API Key \u4E3A\u5FC5\u586B\u9879"})}],labelCol:{span:6},wrapperCol:{span:20},initialValue:m==null?void 0:m.platApiKey,extra:r.formatMessage({id:"pages.agent.apiKey.tip",defaultMessage:"\u8BF7\u524D\u5F80 Dify \u5E73\u53F0\uFF0C\u590D\u5236\u5176 API Key \u7C98\u8D34\u5230\u6B64\u5904"})}),(0,a.jsx)(pe,{width:"md",name:"logo",label:r.formatMessage({id:"pages.agent.icon",defaultMessage:"\u56FE\u6807"}),max:1,fieldProps:{name:"file",listType:"picture-card",beforeUpload:function(i){var t=i.type.startsWith("image/");if(!t)return l.ZP.error(r.formatMessage({id:"pages.agent.imageOnly",defaultMessage:"\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6"})),!1;var u=i.size/1024/1024<2;return u?!0:(l.ZP.error(r.formatMessage({id:"pages.agent.imageSizeLimit",defaultMessage:"\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC72MB"})),!1)}},extra:r.formatMessage({id:"pages.agent.imageFormatLimit",defaultMessage:"\u652F\u6301jpg/png\u683C\u5F0F\uFF0C\u5927\u5C0F\u4E0D\u8D85\u8FC72MB"})})]})})]})},be=(0,fe.connect)()(de)}}]);
