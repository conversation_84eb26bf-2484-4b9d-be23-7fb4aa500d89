import { getAuth<PERSON>ppList, delAppList, createAppKey } from "@/services/ant-design-pro/api";
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ProFormDigit,
  ProFormText,
  ProFormSelect,
  ProTable,
  ProForm
} from '@ant-design/pro-components';
import { Button, message, Modal, Space } from 'antd';
import type { ReactNode } from 'react';
import { useEffect, useRef, useState } from 'react';
import { connect, useIntl } from 'umi';
import './index.less'


const AuthManage: React.FC = () => {
  /** 国际化工具 */
  const intl = useIntl();

  const authRef = useRef()

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    name: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })

  /** Appkey 数据 */
  const [appKeyData, setAppKeyData] = useState<any[]>([])

  /** 当前行 */
  const [currentRow, setCurrentRow] = useState<any>();

  /** 选中点位集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const [createVisible, setCreateVisible] = useState<boolean>(false)

  const [editKey, setEditKey] = useState<boolean>(false)

  const [loading, setLoading] = useState<boolean>(false)

  const [modalTitle, setModalTitle] = useState<string>(intl.formatMessage({ id: 'pages.config.createApiKey', defaultMessage: '创建API KEY', }))

  /** 表单引用 */
  const editFormRef = useRef<ProFormInstance>();


  const operateRender = (dom: any, record: any): ReactNode => {
    return <div id="operate">
      <a onClick={() => editRecord(record)}>
        <img className="img_edit" title={intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', })} />
      </a>
      <a onClick={() => deleteRecord([record])}>
        <img className="img_del" title={intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除', })} />
      </a>
    </div>;
  }


  //列表渲染
  const columns: any = [
    {
      title: intl.formatMessage({ id: 'pages.config.name', defaultMessage: '名称', }),
      dataIndex: 'name',
      hideInSearch: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: "key",
      dataIndex: 'key',
      width: 400,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.config.creationDate', defaultMessage: '创建日期', }),
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      ellipsis: true
    },
    {
      title: intl.formatMessage({ id: 'pages.config.expirationTime', defaultMessage: '过期时间', }),
      dataIndex: 'expirationTime',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.operation', defaultMessage: '操作', }),
      dataIndex: 'option',
      valueType: 'option',
      render: operateRender,
    },
  ];



  /**
   * 编辑点位
   */
  const editRecord = async (rows: any[]) => {
    setEditKey(true)
    setModalTitle(intl.formatMessage({ id: 'pages.config.editApiKey', defaultMessage: '编辑API KEY', }))
    setCurrentRow(rows)
    setCreateVisible(true)
  }

  /**
   * 删除记录
   */
  const deleteRecord = async (rows: any[]) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.pointManage.deleteRecord', defaultMessage: '删除记录', }),
      content: intl.formatMessage({ id: 'pages.pointManage.confirmDeleteRecord', defaultMessage: '确定删除记录吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk: async () => {
        const hide = message.loading(intl.formatMessage({ id: 'pages.pointManage.deleting', defaultMessage: '正在删除', }));
        const rs = await delAppList(rows);
        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }));
        }
        else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.deleteFailure', defaultMessage: '删除失败，请重试', }));
        }
        setSelectedRows([]);
        loadData({});
        hide();
      },
    });
  };

  /**
   * 添加点位
   */
  const doCreateAppKey = async (data: any) => {
    if (editKey) {
      data.id = currentRow.id
    }
    const rs = await createAppKey(data)
    if (rs.code === 0) {
      message.success(editKey ? intl.formatMessage({ id: 'pages.config.editSuccess', defaultMessage: '编辑成功，自动刷新', }) : intl.formatMessage({ id: 'pages.config.createSuccess', defaultMessage: '创建成功，自动刷新', }));
    }
    else {
      message.error(editKey ? intl.formatMessage({ id: 'pages.config.editFailure', defaultMessage: '编辑失败，请重试', }) : intl.formatMessage({ id: 'pages.config.createFailure', defaultMessage: '创建失败，请重试', }));
    }
    setCreateVisible(false)
    setCurrentRow({})
    setSelectedRows([]);
    setEditKey(false)
    setModalTitle(intl.formatMessage({ id: 'pages.config.createApiKey', defaultMessage: '创建API KEY', }))
    loadData({});
  }


  /**
   * 分页
   */
  const handlePageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {

    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadData({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
   * 加载数据
   * @param page 
   */
  const loadData = async (page: any) => {

    setLoading(true)
    const searchValue = authRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let name = page.name ? page.name : searchValue.name
    if (page.name === 'no&input') {
      name = undefined
    }
    const rs = await getAuthAppList({ name: name }, currentPage, pageSize);
    setLoading(false)
    if (rs.code === 0 && rs.data && rs.data?.data.length >= 0) {
      setAppKeyData(rs.data?.data)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
    } else if (rs.code !== 0) {
      message.error(rs.msg)
    }
  }

  /**
  * 根据条件搜索
  * @param params 查询条件
  */
  const handleBeforeSearch = async (params: any) => {
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      name: params.name,
    })
    loadData({
      current: 1,
      name: params.name ? params.name : 'no&input',
    })
  }

  const cancelOpenApp = () => {
    setCreateVisible(false)
    setCurrentRow({})
    setSelectedRows([]);
    setEditKey(false)
    setModalTitle(intl.formatMessage({ id: 'pages.config.createApiKey', defaultMessage: '创建API KEY', }))
  }

  return (
    <>
      <ProTable
        rowKey="id"
        headerTitle={intl.formatMessage({ id: 'pages.config.authManagement', defaultMessage: '授权管理', })}
        formRef={authRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,          // 默认每页显示的条数
          showQuickJumper: true, // 显示跳转至某页
          showSizeChanger: true, // 显示 pageSize 切换器
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'], // 指定每页可以显示多少条
          total: searchParams.total,   // 数据总数
        }}
        onChange={handlePageChange}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        beforeSearchSubmit={handleBeforeSearch}
        columns={columns}
        loading={loading}
        tableAlertRender={false}
        options={{
          density: false,
          setting: false,
          reload: loadData
        }}
        dataSource={appKeyData}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={() => { setCreateVisible(true) }}>
            <PlusOutlined />
            {intl.formatMessage({ id: 'pages.config.createApiKey', defaultMessage: '创建API KEY', })}
          </Button>
        ]}
      />
      <Modal
        width={450}
        footer={null}
        destroyOnClose
        styles={{
          body: { padding: '0px', height: '150px', overflow: 'hidden' }
        }}
        title={modalTitle}
        open={createVisible}
        onOk={cancelOpenApp}
        onCancel={cancelOpenApp}
      >
        <ProForm <{
          name: string;
        }>
          formRef={editFormRef}
          layout={'horizontal'}
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}
          labelAlign="left" // 设置 label 左对齐
          labelCol={{ span: 6 }} // 设置 label 的宽度
          wrapperCol={{ span: 16 }} // 设置输入框的宽度
          onFinish={doCreateAppKey}
          submitter={{
            searchConfig: {
              submitText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
              resetText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', })
            },
            onReset: cancelOpenApp,
            render: (props, doms) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center', // 垂直居中
                    height: '100%', // 确保容器有高度
                  }}
                >
                  <Space>
                    {doms[0]} {/* 提交按钮 */}
                    {doms[1]} {/* 重置按钮 */}
                  </Space>
                </div>
              );
            },
          }}>
          <ProFormText
            width="md"
            name="name"
            initialValue={currentRow?.name}
            rules={[
              {
                required: true
              }
            ]}
            label={intl.formatMessage({ id: 'pages.config.name', defaultMessage: '名称', })}
            placeholder={intl.formatMessage({ id: 'pages.config.name', defaultMessage: '名称', })}
          />
        </ProForm>
      </Modal>
    </>
  )
};

export default AuthManage;
