export default {
    'pages.login.success': "Login successful!",
    'pages.login.failure': "Login failed!",
    'pages.login.retry': "Lo<PERSON> failed, please try again!",
    'pages.login.title': "Login Page",
    'pages.login.welcome': "Welcome to MarsLume Al Platform",
    'pages.login.agreementPrefix': "You have read and agree to the MarsLume",
    'pages.login.terms': " Terms of Use",
    'pages.login.and': " and",
    'pages.login.privacy': " Privacy Policy",
    'pages.login.submit': "User Login",
    'pages.login.usernamePlaceholder': "Please enter your username",
    'pages.login.passwordPlaceholder': "Please enter your password",
    'pages.search.sortByTime': "By latest time",
    'pages.search.sortBySimilarity': "By similarity",
    'pages.search.history': "History",
    'pages.search.searchHistory': "Search history",
    'pages.search.delete': "Delete",
    'pages.search.hotSearch': "Hot searches",
    'pages.search.example1': "The boy next to the motor vehicle at the door",
    'pages.search.example2': "The person in red clothes making a phone call in the elevator",
    'pages.search.example3': "The person wearing a blue hat at the parking booth",
    'pages.search.assistHint': "I can help you search for information, such as: the boy in black clothes next to the takeaway cabinet on the first floor of the park",
    'pages.search.searchButton': "Search",
    'pages.search.filter': "Search conditions",
    'pages.search.loading': "Loading...",
    'pages.search.eventDetail': "Event details",
    'pages.search.videoPlayback': "Video playback",
    'pages.search.panorama': "Panorama",
    'pages.search.description': "Description",
    'pages.search.time': "Time",
    'pages.search.location': "Location",
    'pages.search.similarity': "Similarity",
    'pages.pointManage.voiceInput': "Voice input",
    'pages.pointManage.speechRecognition': "Voice recognition",
    'pages.pointManage.stopVoiceInput': "Stop voice input",
    'pages.pointManage.operationSuccess': "Operation successful",
    'pages.pointManage.preview': "Preview",
    'pages.pointManage.edit': "Edit",
    'pages.pointManage.alreadyMonitored': "Control deployed",
    'pages.pointManage.monitor': "Control deployed",
    'pages.pointManage.delete': "Delete",
    'pages.pointManage.deleteRecord': "Delete record",
    'pages.pointManage.confirmDeleteRecord': "Are you sure to delete the record?",
    'pages.pointManage.confirm': "Confirm",
    'pages.pointManage.cancel': "Cancel",
    'pages.pointManage.deleting': "Deleting",
    'pages.pointManage.deleteSuccess': "Delete successful, auto refresh",
    'pages.pointManage.deleteFailure': "Delete failed, please try again",
    'pages.pointManage.person': "Person",
    'pages.pointManage.motorVehicle': "Motor vehicle",
    'pages.pointManage.nonMotorVehicle': "Non-motor vehicle",
    'pages.pointManage.pointName': "Point name",
    'pages.pointManage.protocolType': "Protocol type",
    'pages.pointManage.captureType': "Snapshot type",
    'pages.pointManage.captureInterval': "Snapshot interval",
    'pages.pointManage.deviceName': "Device name",
    'pages.pointManage.deviceIP': "Device IP",
    'pages.pointManage.devicePort': "Device port",
    'pages.pointManage.operation': "Operation",
    'pages.pointManage.pointManagement': "Point management",
    'pages.pointManage.add': "Add",
    'pages.pointManage.selected': "Selected",
    'pages.pointManage.batchDelete': "Batch delete",
    'pages.pointManage.item': "Item",
    'pages.pointManage.inputCaptureInterval': "Please enter the snapshot interval",
    'pages.pointManage.inputPointName': "Please enter the point name",
    'pages.pointManage.selectCaptureType': "Please select the capture type",
    'pages.pointManage.addDevice': "Add device",
    'pages.pointManage.protocol': "Protocol",
    'pages.pointManage.deviceCode': "Device code",
    'pages.pointManage.inputDeviceName': "Please enter the device name",
    'pages.pointManage.inputDeviceCode': "Please enter the device code",
    'pages.pointManage.port': "Port",
    'pages.pointManage.username': "User name",
    'pages.pointManage.password': "Password",
    'pages.pointManage.mainStream': "Main stream",
    'pages.pointManage.example': "Example",
    'pages.pointManage.subStream': "Sub stream",
    'pages.pointManage.addPoint': "Add point",
    'pages.pointManage.validIP': "Please enter a valid IP address",
    'pages.pointManage.noData': "No data",
    'pages.pointManage.selectPoint': "Please select a point",
    'pages.pointManage.addSuccess': "Added successfully",
    'pages.pointManage.prevStep': "Previous step",
    'pages.pointManage.nextStep': "Next step",
    'pages.pointManage.monitorSuccess': "Control deployment successful",
    'pages.pointManage.monitorFailure': "Control deployment failed",
    'pages.pointManage.cancelSuccess': "Cancel successful",
    'pages.pointManage.operationFailure': "Operation failed",
    'pages.pointManage.monitor': "Control deployment",
    'pages.pointManage.monitorEvent': "Control deployment event",
    'pages.pointManage.startMonitor': "Start control deployment",
    'pages.pointManage.monitorRecord': "Control deployment record",
    'pages.pointManage.noEventRecord': "No event record",
    'pages.pointManage.charLimitExceeded': "Character length exceeded, max:",
    'pages.pointManage.eventType': "Event Type",
    'pages.pointManage.prompt': "Prompt",
    'pages.pointManage.createTime': "Creation Time",
    'pages.pointManage.editSuccess': "Edit Successful",
    'pages.pointManage.monitorRule': "Monitoring Rule",
    'pages.pointManage.selectEventType': "Please select event type",
    'pages.pointManage.inputPrompt': "Please enter prompt",
    'pages.pointManage.eventRecord': "Event Record",
    'pages.pointManage.paginationInfo': "Items {range0} - {range1} / Total {total}",
    'pages.pointManage.detail': "Details",
    'pages.pointManage.eventImage': "Event Image",
    'pages.pointManage.pointInfo': "Point Information",
    'pages.pointManage.eventTime': "Event Time",
    'pages.config.name': "Name",
    'pages.config.creationDate': "Creation Date",
    'pages.config.expirationTime': "Expiration Time",
    'pages.config.editApiKey': "Edit API Key",
    'pages.config.editSuccess': "Edit Successful, Auto Refresh",
    'pages.config.createSuccess': "Creation Successful, Auto Refresh",
    'pages.config.editFailure': "Edit Failed, Please Retry",
    'pages.config.createFailure': "Creation Failed, Please Retry",
    'pages.config.createApiKey': "Create API Key",
    'pages.config.authManagement': "Authorization Management",
    'pages.config.eventCode': "Event Code",
    'pages.config.paramConfigFailure': "Failed to Retrieve Parameter Configuration",
    'pages.config.saveFailure': "Save Failed",
    'pages.config.saveSuccess': "Save Successful",
    'pages.config.save': "Save",
    'pages.config.reset': "Reset",
    'pages.config.streamConfig': "Streaming Configuration",
    'pages.config.streamServiceUrl': "Streaming Service Address",
    'pages.config.secretKey': "Secret Key",
    'pages.config.configuration': "Configuration",
    'pages.config.workspaceId': "Workspace ID",
    'pages.config.multiSearchStrategy': "Multi-dimensional Search Strategy",
    'pages.config.dataCleanStrategy': "Data Cleaning Strategy",
    'pages.config.objectDetection': "Object Detection",
    'pages.config.enableObjectDetection': "Enable Object Detection",
    'pages.config.allowObjectWhitelist': "Allow Object Detection Whitelist",
    'pages.config.sedan': "Sedan",
    'pages.config.bus': "Bus",
    'pages.config.truck': "Truck",
    'pages.config.bicycle': "Bicycle",
    'pages.config.motorcycle': "Motorcycle",
    'pages.config.enableImageDupCheck': "Enable Image Duplicate Detection",
    'pages.config.intervalSeconds': "Interval Time (Seconds)",
    'pages.config.minScoreLimitError': "Minimum Score Value Cannot Exceed 1",
    'pages.config.initialSearchStrategy': "Initial Screening Search Strategy",
    'pages.config.enhancedSearchStrategy': "Multi-dimensional Enhanced Search Strategy",
    'pages.config.multiInitialSearch': "Multi-dimensional Initial Screening",
    'pages.config.minScore': "Minimum Score Value",
    'pages.config.maxResultSet': "Maximum Result Set",
    'pages.config.topValue': "Top Value",
    'pages.config.reRanking': "Multi-dimensional Re-ranking",
    'pages.config.batchSize': "Batch Size",
    'pages.config.fineSearch': "Multi-dimensional Fine Screening",
    'pages.config.fineSearchStrategy': "Fine Screening Search Strategy",
    'pages.config.enableReId': "Enable ReId?",
    'pages.config.objectMinScore': "Object Detection Minimum Score",
    'pages.config.vectorMinScore': "Vector Similarity Minimum Score",
    'pages.config.maxResultsPerSearch': "Maximum Results per Search",
    'pages.common.logout': "Log Out",
    'pages.agent.apiCallFailed': "Failed to call the interface, please try again later",
    'pages.agent.hello': "Hello, I am",
    'pages.agent.agent': "Agent",
    'pages.agent.attachment': "Attachment",
    'pages.agent.dropFilesHere': "Put the file here",
    'pages.agent.uploadFile': "Upload file",
    'pages.agent.clickOrDragToUpload': "Click or drag the file to this area to upload",
    'pages.agent.shiftEnterNewline': "Shift+Enter to change line",
    'pages.agent.basicConfig': "Basic configuration",
    'pages.agent.llmModel': "LLM model used",
    'pages.agent.doubaoModel': "Bean bag model",
    'pages.agent.selectAnOption': "Please select an option",
    'pages.agent.memoryMessageCount': "Number of remembered messages",
    'pages.agent.skillConfig': "Skill configuration",
    'pages.agent.toolSet': "Toolset",
    'pages.agent.toolSetDescription': "The toolset enables the agent to call external tools and expand the ability boundary of the agent.",
    'pages.agent.knowledgeBase': "Knowledge base",
    'pages.agent.knowledgeBaseDescription': "When the user sends a message, the agent can refer to the content of the knowledge base to answer the user's question.",
    'pages.agent.workflow': "Workflow",
    'pages.agent.workflowDescription': "Used to handle task flows with complex logic and many steps.",
    'pages.agent.describePersona': "Please describe the personality and functions",
    'pages.agent.publishSuccess': "Publish successfully",
    'pages.agent.publishFailed': "Publish failed",
    'pages.agent.publishNotAllowed': "Sorry, the agent cannot be published",
    'pages.agent.config': "Agent configuration",
    'pages.agent.publish': "Publish",
    'pages.agent.modelCapabilityConfig': "Model capability configuration",
    'pages.agent.promptDev': "Prompt word development",
    'pages.agent.debug': "Agent debugging",
    'pages.agent.create': "Create agent",
    'pages.agent.submitFailed': "Submission failed, please check the form data",
    'pages.agent.name': "Agent name",
    'pages.agent.nameLimit': "Up to 64 characters can be entered",
    'pages.agent.description': "Agent function introduction",
    'pages.agent.descriptionTip': "Introduce the functions of the agent and will be displayed to the user of the agent",
    'pages.agent.icon': "Icon",
    'pages.agent.imageOnly': "Only image files can be uploaded",
    'pages.agent.imageSizeLimit': "Image size cannot exceed 2MB",
    'pages.agent.imageFormatLimit': "Support jpg/png format, size does not exceed 2MB",
    'pages.agent.flagship': "Flagship",
    'pages.agent.highSpeed': "High speed",
    'pages.agent.toolInvocation': "Tool call",
    'pages.agent.rolePlay': "Role play",
    'pages.agent.longText': "Long text",
    'pages.agent.imageUnderstanding': "Image understanding",
    'pages.agent.reasoning': "Reasoning ability",
    'pages.agent.videoUnderstanding': "Video understanding",
    'pages.agent.costPerformance': "Cost-effectiveness",
    'pages.agent.codeExpert': "Code expertise",
    'pages.agent.audioUnderstanding': "Audio understanding",
    'pages.agent.visualAnalysis': "Visual analysis",
    'pages.agent.running': "Running",
    'pages.agent.queuing': "Queuing",
    'pages.agent.training': "Training",
    'pages.agent.trainingFailed': "Training failed",
    'pages.agent.text': "Text",
    'pages.agent.multimodal': "Multimodal",
    'pages.agent.landongModel': "Lazy hole model",
    'pages.agent.searchModelName': "Search model name",
    'pages.agent.quotaTrial': "Limit experience",
    'pages.agent.comingOffline': "Coming offline soon",
    'pages.agent.newModelExperience': "New model experience",
    'pages.agent.advancedModel': "Advanced model",
    'pages.agent.generalModel': "General model",
    'pages.agent.modelType': "Model type",
    'pages.agent.modelFeature': "Model features",
    'pages.agent.modelProvider': "Model manufacturer",
    'pages.agent.modelSupportedFunctions': "Model support functions",
    'pages.agent.contextLength': "Context length",
    'pages.agent.userRights': "User rights",
    'pages.agent.creator': "Creator",
    'pages.agent.creationTime': "Creation time",
    'pages.agent.describeFunction': "Please describe the character and function",
    'pages.agent.orchestration': "Orchestration",
    'pages.agent.functionIntroduction': "Function introduction",
    'pages.agent.publishStatus': "Release status",
    'pages.agent.agentDisplay': "Agent display",
    'pages.agent.modelStatus': "Model status",
    'pages.search.expandir': "Expand",
    'pages.search.retirar': "Collapse",
    'pages.search.deleteConfirmWarning': "Once deleted, it cannot be restored. Are you sure you want to delete it?",
    'pages.config.applicationId': "Application ID",
    'pages.config.imageDeduplication': "Image Deduplication",
    'pages.pointManage.loadingMessage': "Loading, please do not refresh the page",
    'pages.pointManage.fetchError': "Timeout for obtaining the point, please check if the device is online",
    'pages.pointManage.deviceTimeout': "Device request timeout",
    'pages.pointManage.streamConnectFailed': "Streaming service connection failed",
    'pages.pointManage.serviceException': "Service abnormality, please try again later",
    'pages.pointManage.deleteHasControlRule': "The selected point is being monitored and cannot be deleted",
    'pages.pointManage.online': "Online",
    'pages.pointManage.offline': "Offline",
    'pages.pointManage.confirmDeleteEventType': "Are you sure to delete this event type?",
    'pages.pointManage.captureIntervalRange': "The capture interval is between 1 and 3600 seconds",
    'pages.pointManage.status': "State",
    'pages.login.terms.title': "Terms of Service",
    'pages.login.terms.check': "Please read and agree to the Terms of Service first.",
    'pages.pointManage.confirmDeletePoint': "Are you sure you want to delete the point information record?",
    'pages.pointManage.pointNameRequired': "Some point names are missing. Please fill them in before submitting.",
    'pages.pointManage.refresh': "Refresh",
    'pages.account.updateSuc': "Successfully modified",
    'pages.account.updatePwd': "Change Password",
    'pages.account.oldPassword': "Old password",
    'pages.account.newPassword': "New Password",
    'pages.account.confirmPwd': "Confirm Password",
    'pages.account.passwordmatch': "The new password you entered does not match",
    'pages.password.reset.fail': "Failed to reset password",
    'pages.password.reset.success': "Password reset successful",
    'pages.password.update': "Change Password",
    'pages.register.success': "Registration successful, please log in",
    'pages.register.fail': "Registration failed",
    'pages.login.fail': "Incorrect username or password",
    'pages.login.needRegister': "Please register an account first",
    'pages.system.check.fail': "Service check failed",
    'pages.account.maxlength': "Password can be up to 18 characters",
    'pages.login.login': "Login",
    'pages.login.register': "Register",
    'pages.login.registerTitle': "User Registration",
    'pages.search.similarity': "Similarity",
    'pages.common.sessionExpired': "Session expired, please log in again.",
    'pages.primaryKey.id': "Primary key ID",
    'pages.agent.type': "type",
    'pages.agent.type.placeholder': "Please select the type of intelligent agent",
    'pages.agent.type.required': "Please select the type",
    'pages.agent.id': "Agent ID",
    'pages.agent.id.placeholder': "Please enter the agent ID",
    'pages.agent.id.required': "Please enter the agent ID",
    'pages.agent.suggestedQuestions': "You can ask me like this:",
    'pages.agent.botId.tip': "Please go to the corresponding platform (such as Coze, Dify) to create an agent, then copy its ID and paste it here",
    'pages.agent.apiKey.tip': "Please go to the Dify platform, copy its API Key and paste it here",
    'pages.agent.apiKey.required': "API Key is required",
}
