package com.zkteco.mars.usc.dao;

import com.zkteco.framework.dao.BaseDao;
import com.zkteco.mars.usc.model.DmcPoint;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date  2024-12-31 16:09
 * @since 1.0.0
 */
public interface DmcPointDao extends BaseDao<DmcPoint, String> {

    /**
     * 通过 businessId查询
     * @param businessId
     * @return
     */
    DmcPoint findByBusinessId(String businessId);

    Integer countByDeviceId(String deviceId);

    @Query("SELECT DISTINCT c.name FROM DmcPoint c")
    List<String> getAllPointName();

    /**
     * 获取点位ID和名称集合
     * @return 点位ID和名称的列表
     */
    @Query("SELECT new map(c.id as id, c.name as name) FROM DmcPoint c")
    List<java.util.Map<String, Object>> getPointIdAndNameList();
}
