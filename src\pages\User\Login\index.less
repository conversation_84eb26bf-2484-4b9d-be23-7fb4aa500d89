.ant-pro-form-login-header {
    display: block;
}

.ant-pro-form-login-main {
    margin: 20px;
}

// :root {
//     --primary-color: rgb(11, 211, 87);
//     --hover-color: rgb(10, 200, 80);
//     --checkbox-tick-color: white;
//     --focus-color: rgba(5, 145, 255, 0.1);
// }

.submit-Button-Dom .ant-btn-variant-solid {
    border-radius: 4px;
}

// /* hover 样式也添加 !important */
// .submit-Button-Dom .ant-btn-variant-solid:hover {
//     background: var(--hover-color) !important;
//     color: #fff !important;
// }


// /* 覆盖复选框样式（未选中状态） */
// .submit-Checkbox-Dom .ant-checkbox-input {
//     background-color: var(--primary-color) !important;
//     /* 设置未选中状态的背景色 */
//     border-color: var(--primary-color) !important;
//     /* 设置未选中状态的边框色 */
// }

// /* 处理复选框 hover 时的背景色 */
// .submit-Checkbox-Dom .ant-checkbox-input:hover {
//     background-color: var(--hover-color) !important;
//     /* 设置 hover 状态下的背景色 */
//     border-color: var(--hover-color) !important;
//     /* 设置 hover 状态下的边框色 */
// }

// /* 处理复选框选中状态的背景色 */
// .submit-Checkbox-Dom .ant-checkbox-checked .ant-checkbox-inner {
//     background-color: var(--primary-color) !important;
//     /* 设置选中状态的背景色 */
//     border-color: var(--primary-color) !important;
//     /* 设置选中状态的边框色 */
// }

// /* 处理复选框选中状态的 hover 状态 */
// .submit-Checkbox-Dom .ant-checkbox-checked:hover .ant-checkbox-inner {
//     background-color: var(--hover-color) !important;
//     /* 设置 hover 状态下的背景色 */
//     border-color: var(--hover-color) !important;
//     /* 设置 hover 状态下的边框色 */
// }

// /* 处理勾选图标的样式 */
// .submit-Checkbox-Dom .ant-checkbox-checked .ant-checkbox-inner::after {
//     border-color: white !important;
//     /* 设置勾选图标的颜色 */
// }

// /* 处理未选中状态下的 hover 边框颜色 */
// .submit-Checkbox-Dom .ant-checkbox-input:not(:checked):hover {
//     border-color: var(--hover-color) !important;
//     /* 设置未选中状态下的 hover 边框颜色 */
// }


// /* 处理复选框 hover 时的边框颜色 */
// .submit-Checkbox-Dom .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner,
// .submit-Checkbox-Dom .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {
//     border-color: var(--hover-color) !important;
//     /* 设置 hover 边框颜色 */
// }

// /* 处理复选框获得焦点时的样式 */
// .submit-Checkbox-Dom .ant-form input[type='checkbox']:focus {
//     box-shadow: 0 0 0 2px var(--focus-color) !important;
//     /* 修改焦点时的 box-shadow */
// }


// .login-Form .ant-input-outlined:focus,
// .login-Form .ant-input-outlined:focus-within {
//     border-color: var(--primary-color);
// }

// .login-Form .ant-input-outlined:hover {
//     border-color: var(--primary-color);
// }

// .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-selected {
//     color: var(--primary-color);
// }