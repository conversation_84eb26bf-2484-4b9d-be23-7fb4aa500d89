package com.zkteco.mars.usc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通道查询信息
 *
 * <AUTHOR>
 * @date 2025-01-09 13:48
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class GetChannelItem implements Serializable {

    /**
     * 通道id
     */
    private String id;

    /**
     * 设备id
     */
    private String deviceId;


    /**
     * 排序  ASC
     */
    private String channelNumber;


    /**
     * pageNo
     */
    private int pageNo;

    /**
     * pageSize
     */
    private int pageSize;


}
