package com.zkteco.mars.usc.service;

import com.zkteco.framework.vo.ApiResultMessage;

import com.zkteco.mars.ai.dto.RagFilterDTO;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * 智能搜索查询
 *
 * <AUTHOR>
 * @date 2024-12-31 14:15
 * @since 1.0.0
 */
public interface SmartSearchService {

    /**
     * 获取历史记录
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 14:18
     * @since 1.0.0
     */
    ApiResultMessage getHistoryRecord();


    /**
     * 获取热门搜索
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 14:20
     * @since 1.0.0
     */
    ApiResultMessage getHotSearch();

    /**
     * 智能搜索
     *
     * @param ragFilterDTO:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 14:24
     * @since 1.0.0
     */
    ApiResultMessage smartSearch(RagFilterDTO ragFilterDTO);


    /**
     * 获取抓拍图片
     *
     * @param imageName: 图片名称
     * @param imageType: 图片类型 snap 抓拍 thumb 缩略图 target 目标抠图
     * @return org.springframework.http.ResponseEntity<org.springframework.core.io.Resource>
     * @throws
     * <AUTHOR>
     * @date 2025-02-14 12:24
     * @since 1.0.0
     */
    ResponseEntity<Resource> getImageFile(String imageName, String imageType);


    /**
     * 语音转文字
     *
     * @param multipartFile: 语音文件
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-02-13 11:56
     * @since 1.0.0
     */
    ApiResultMessage voiceToText(MultipartFile multipartFile);


    /**
     * 通过文件名称查询目标抠图
     *
     * @param imageName:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-03-19 14:55
     * @since 1.0.0
     */
    ApiResultMessage getTargetImageList(String imageName);


    /**
     * 以图搜图第一步 （抠图）
     *
     * @param multipartFile:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @date 2025/8/20 11:19
     * @since 1.0.0
     */
    ApiResultMessage imageSearch(MultipartFile multipartFile);


    /**
     * 以图搜图第二步 （相似度检索） 分页
     *
     * @param multipartFile:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @date 2025/8/20 11:19
     * @since 1.0.0
     */
    ApiResultMessage similarityFeaturePage(MultipartFile multipartFile, RagFilterDTO ragFilterDTO);


}
