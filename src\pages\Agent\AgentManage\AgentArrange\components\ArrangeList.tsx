import { ProList } from "@ant-design/pro-components";

interface CommonProListProps<T> {
    dataSource: T[];
    rowKey: string;
    avatarKey?: string;
    titleKey: string;
    descriptionKey?: string; 
  }
  
  const ArrangeList = <T extends object>({
    dataSource,
    row<PERSON>ey,
    avatar<PERSON><PERSON>,
    title<PERSON><PERSON>,
    description<PERSON>ey,
  }: CommonProListProps<T>) => {
    
    return (
      <ProList<T>
        rowKey={rowKey}
        dataSource={dataSource}
        metas={{
          avatar: avatar<PERSON>ey ? {
            dataIndex: avatar<PERSON>ey,
            render: (dom, entity) => (
              <img 
                src={(entity as Record<string, any>)[avatarKey]} 
                style={{ 
                  width: 36, 
                  height: 36,
                  borderRadius: '10%'
                }} 
              />
            ),
          } : undefined,
          title: {
            dataIndex: titleKey,
          },
        ...(descriptionKey ? { 
            description: {
              dataIndex: descriptionKey,
            }
          } : {})
        }}
        pagination={false}
        showActions="hover"
        showExtra="hover"
        search={false}
        toolBarRender={false}
      />
    );
  };
export default ArrangeList;