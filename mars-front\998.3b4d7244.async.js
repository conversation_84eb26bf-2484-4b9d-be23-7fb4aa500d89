!(function(){var rn=Object.defineProperty,an=Object.defineProperties;var on=Object.getOwnPropertyDescriptors;var Wt=Object.getOwnPropertySymbols;var en=Object.prototype.hasOwnProperty,tn=Object.prototype.propertyIsEnumerable;var Yt=(O,v)=>(v=Symbol[O])?v:Symbol.for("Symbol."+O);var Jt=(O,v,e)=>v in O?rn(O,v,{enumerable:!0,configurable:!0,writable:!0,value:e}):O[v]=e,N=(O,v)=>{for(var e in v||(v={}))en.call(v,e)&&Jt(O,e,v[e]);if(Wt)for(var e of Wt(v))tn.call(v,e)&&Jt(O,e,v[e]);return O},wt=(O,v)=>an(O,on(v));var dt=(O,v)=>{var e={};for(var l in O)en.call(O,l)&&v.indexOf(l)<0&&(e[l]=O[l]);if(O!=null&&Wt)for(var l of Wt(O))v.indexOf(l)<0&&tn.call(O,l)&&(e[l]=O[l]);return e};var ft=(O,v,e)=>Jt(O,typeof v!="symbol"?v+"":v,e);var Nt=(O,v,e)=>new Promise((l,E)=>{var t=$=>{try{f(e.next($))}catch(H){E(H)}},c=$=>{try{f(e.throw($))}catch(H){E(H)}},f=$=>$.done?l($.value):Promise.resolve($.value).then(t,c);f((e=e.apply(O,v)).next())}),Qt=function(O,v){this[0]=O,this[1]=v},nn=(O,v,e)=>{var l=(c,f,$,H)=>{try{var z=e[c](f),a=(f=z.value)instanceof Qt,X=z.done;Promise.resolve(a?f[0]:f).then(V=>a?l(c==="return"?c:"next",f[1]?{done:V.done,value:V.value}:V,$,H):$({value:V,done:X})).catch(V=>l("throw",V,$,H))}catch(V){H(V)}},E=c=>t[c]=f=>new Promise(($,H)=>l(c,f,$,H)),t={};return e=e.apply(O,v),t[Yt("asyncIterator")]=()=>t,E("next"),E("throw"),E("return"),t};var qt=(O,v,e)=>(v=O[Yt("asyncIterator")])?v.call(O):(O=O[Yt("iterator")](),v={},e=(l,E)=>(E=O[l])&&(v[l]=t=>new Promise((c,f,$)=>(t=E.call(O,t),$=t.done,Promise.resolve(t.value).then(H=>c({value:H,done:$}),f)))),e("next"),e("return"),v);(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[998],{66023:function(O,v){"use strict";var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};v.Z=e},42110:function(O,v){"use strict";var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};v.Z=e},8751:function(O,v,e){"use strict";e.d(v,{Z:function(){return z}});var l=e(1413),E=e(67294),t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},c=t,f=e(91146),$=function(X,V){return E.createElement(f.Z,(0,l.Z)((0,l.Z)({},X),{},{ref:V,icon:c}))},H=E.forwardRef($),z=H},18429:function(O,v,e){"use strict";e.d(v,{Z:function(){return z}});var l=e(1413),E=e(67294),t={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},c=t,f=e(91146),$=function(X,V){return E.createElement(f.Z,(0,l.Z)((0,l.Z)({},X),{},{ref:V,icon:c}))},H=E.forwardRef($),z=H},16596:function(O,v,e){"use strict";e.d(v,{Z:function(){return z}});var l=e(1413),E=e(67294),t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.3 459a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-upload",theme:"outlined"},c=t,f=e(91146),$=function(X,V){return E.createElement(f.Z,(0,l.Z)((0,l.Z)({},X),{},{ref:V,icon:c}))},H=E.forwardRef($),z=H},56717:function(O,v,e){"use strict";var l=e(1413),E=e(67294),t=e(93696),c=e(91146),f=function(z,a){return E.createElement(c.Z,(0,l.Z)((0,l.Z)({},z),{},{ref:a,icon:t.Z}))},$=E.forwardRef(f);v.Z=$},37653:function(O,v,e){"use strict";var l=e(1413),E=e(67294),t=e(26554),c=e(91146),f=function(z,a){return E.createElement(c.Z,(0,l.Z)((0,l.Z)({},z),{},{ref:a,icon:t.Z}))},$=E.forwardRef(f);v.Z=$},45128:function(O,v,e){"use strict";var l=e(1413),E=e(67294),t=e(90102),c=e(91146),f=function(z,a){return E.createElement(c.Z,(0,l.Z)((0,l.Z)({},z),{},{ref:a,icon:t.Z}))},$=E.forwardRef(f);v.Z=$},21450:function(O,v,e){"use strict";e.d(v,{Z:function(){return $}});var l=e(67294),t=l.createContext({});const c={classNames:{},styles:{},className:"",style:{}};var $=H=>{const z=l.useContext(t);return l.useMemo(()=>N(N({},c),z[H]),[z[H]])}},24495:function(O,v,e){"use strict";e.d(v,{Z:function(){return Vt}});var l=e(93967),E=e.n(l),t=e(67294),c=e(21450),f=e(36158),$=e(56790),H=e(73935);const z=t.createContext(null);function a(s){const{getDropContainer:I,className:M,prefixCls:h,children:re}=s,{disabled:_}=t.useContext(z),[Ze,Ne]=t.useState(),[Ie,Xe]=t.useState(null);if(t.useEffect(()=>{const Oe=I==null?void 0:I();Ze!==Oe&&Ne(Oe)},[I]),t.useEffect(()=>{if(Ze){const Oe=()=>{Xe(!0)},Qe=ee=>{ee.preventDefault()},rt=ee=>{ee.relatedTarget||Xe(!1)},qe=ee=>{Xe(!1),ee.preventDefault()};return document.addEventListener("dragenter",Oe),document.addEventListener("dragover",Qe),document.addEventListener("dragleave",rt),document.addEventListener("drop",qe),()=>{document.removeEventListener("dragenter",Oe),document.removeEventListener("dragover",Qe),document.removeEventListener("dragleave",rt),document.removeEventListener("drop",qe)}}},[!!Ze]),!(I&&Ze&&!_))return null;const me=`${h}-drop-area`;return(0,H.createPortal)(t.createElement("div",{className:E()(me,M,{[`${me}-on-body`]:Ze.tagName==="BODY"}),style:{display:Ie?"block":"none"}},re),Ze)}var X=e(87462),V=e(42110),K=e(51398),be=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:V.Z}))},oe=t.forwardRef(be),Ce=oe,ce=e(26554),w=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:ce.Z}))},y=t.forwardRef(w),D=y,T=e(50756),p=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:T.Z}))},b=t.forwardRef(p),B=b,L=e(28036),Z=e(29372),R=e(11550);function m(s,I){const{children:M,upload:h,rootClassName:re}=s,_=t.useRef(null);return t.useImperativeHandle(I,()=>_.current),t.createElement(R.Z,(0,X.Z)({},h,{showUploadList:!1,rootClassName:re,ref:_}),M)}var x=t.forwardRef(m),F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM575.34 477.84l-61.22 102.3L452.3 477.8a12 12 0 00-10.27-5.79h-38.44a12 12 0 00-6.4 1.85 12 12 0 00-3.75 16.56l82.34 130.42-83.45 132.78a12 12 0 00-1.84 6.39 12 12 0 0012 12h34.46a12 12 0 0010.21-5.7l62.7-101.47 62.3 101.45a12 12 0 0010.23 5.72h37.48a12 12 0 006.48-1.9 12 12 0 003.62-16.58l-83.83-130.55 85.3-132.47a12 12 0 001.9-6.5 12 12 0 00-12-12h-35.7a12 12 0 00-10.29 5.84z"}}]},name:"file-excel",theme:"filled"},U=F,k=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:U}))},J=t.forwardRef(k),te=J,Le={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7L639.4 73.4c-6-6-14.2-9.4-22.7-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.6-9.4-22.6zM400 402c22.1 0 40 17.9 40 40s-17.9 40-40 40-40-17.9-40-40 17.9-40 40-40zm296 294H328c-6.7 0-10.4-7.7-6.3-12.9l99.8-127.2a8 8 0 0112.6 0l41.1 52.4 77.8-99.2a8 8 0 0112.6 0l136.5 174c4.3 5.2.5 12.9-6.1 12.9zm-94-370V137.8L790.2 326H602z"}}]},name:"file-image",theme:"filled"},tt=Le,Ke=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:tt}))},ze=t.forwardRef(Ke),Ue=ze,o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM426.13 600.93l59.11 132.97a16 16 0 0014.62 9.5h24.06a16 16 0 0014.63-9.51l59.1-133.35V758a16 16 0 0016.01 16H641a16 16 0 0016-16V486a16 16 0 00-16-16h-34.75a16 16 0 00-14.67 9.62L512.1 662.2l-79.48-182.59a16 16 0 00-14.67-9.61H383a16 16 0 00-16 16v272a16 16 0 0016 16h27.13a16 16 0 0016-16V600.93z"}}]},name:"file-markdown",theme:"filled"},d=o,i=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:d}))},r=t.forwardRef(i),S=r,P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM633.22 637.26c-15.18-.5-31.32.67-49.65 2.96-24.3-14.99-40.66-35.58-52.28-65.83l1.07-4.38 1.24-5.18c4.3-18.13 6.61-31.36 7.3-44.7.52-10.07-.04-19.36-1.83-27.97-3.3-18.59-16.45-29.46-33.02-30.13-15.45-.63-29.65 8-33.28 21.37-5.91 21.62-2.45 50.07 10.08 98.59-15.96 38.05-37.05 82.66-51.2 107.54-18.89 9.74-33.6 18.6-45.96 28.42-16.3 12.97-26.48 26.3-29.28 40.3-1.36 6.49.69 14.97 5.36 21.92 5.3 7.88 13.28 13 22.85 13.74 24.15 1.87 53.83-23.03 86.6-79.26 3.29-1.1 6.77-2.26 11.02-3.7l11.9-4.02c7.53-2.54 12.99-4.36 18.39-6.11 23.4-7.62 41.1-12.43 57.2-15.17 27.98 14.98 60.32 24.8 82.1 24.8 17.98 0 30.13-9.32 34.52-23.99 3.85-12.88.8-27.82-7.48-36.08-8.56-8.41-24.3-12.43-45.65-13.12zM385.23 765.68v-.36l.13-.34a54.86 54.86 0 015.6-10.76c4.28-6.58 10.17-13.5 17.47-20.87 3.92-3.95 8-7.8 12.79-12.12 1.07-.96 7.91-7.05 9.19-8.25l11.17-10.4-8.12 12.93c-12.32 19.64-23.46 33.78-33 43-3.51 3.4-6.6 5.9-9.1 7.51a16.43 16.43 0 01-2.61 1.42c-.41.17-.77.27-1.13.3a2.2 2.2 0 01-1.12-.15 2.07 2.07 0 01-1.27-1.91zM511.17 547.4l-2.26 4-1.4-4.38c-3.1-9.83-5.38-24.64-6.01-38-.72-15.2.49-24.32 5.29-24.32 6.74 0 9.83 10.8 10.07 27.05.22 14.28-2.03 29.14-5.7 35.65zm-5.81 58.46l1.53-4.05 2.09 3.8c11.69 21.24 26.86 38.96 43.54 51.31l3.6 2.66-4.39.9c-16.33 3.38-31.54 8.46-52.34 16.85 2.17-.88-21.62 8.86-27.64 11.17l-5.25 2.01 2.8-4.88c12.35-21.5 23.76-47.32 36.05-79.77zm157.62 76.26c-7.86 3.1-24.78.33-54.57-12.39l-7.56-3.22 8.2-.6c23.3-1.73 39.8-.45 49.42 3.07 4.1 1.5 6.83 3.39 8.04 5.55a4.64 4.64 0 01-1.36 6.31 6.7 6.7 0 01-2.17 1.28z"}}]},name:"file-pdf",theme:"filled"},A=P,Q=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:A}))},j=t.forwardRef(Q),G=j,q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM468.53 760v-91.54h59.27c60.57 0 100.2-39.65 100.2-98.12 0-58.22-39.58-98.34-99.98-98.34H424a12 12 0 00-12 12v276a12 12 0 0012 12h32.53a12 12 0 0012-12zm0-139.33h34.9c47.82 0 67.19-12.93 67.19-50.33 0-32.05-18.12-50.12-49.87-50.12h-52.22v100.45z"}}]},name:"file-ppt",theme:"filled"},Ae=q,de=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:Ae}))},ue=t.forwardRef(de),Se=ue,Ye={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM512 566.1l52.81 197a12 12 0 0011.6 8.9h31.77a12 12 0 0011.6-8.88l74.37-276a12 12 0 00.4-3.12 12 12 0 00-12-12h-35.57a12 12 0 00-11.7 9.31l-45.78 199.1-49.76-199.32A12 12 0 00528.1 472h-32.2a12 12 0 00-11.64 9.1L434.6 680.01 388.5 481.3a12 12 0 00-11.68-9.29h-35.39a12 12 0 00-3.11.41 12 12 0 00-8.47 14.7l74.17 276A12 12 0 00415.6 772h31.99a12 12 0 0011.59-8.9l52.81-197z"}}]},name:"file-word",theme:"filled"},$e=Ye,Be=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:$e}))},De=t.forwardRef(Be),we=De,se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM296 136v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm0 64v160h128V584H296zm48 48h32v64h-32v-64z"}}]},name:"file-zip",theme:"filled"},Me=se,He=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:Me}))},Pe=t.forwardRef(He),ke=Pe,lt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM320 482a8 8 0 00-8 8v48a8 8 0 008 8h384a8 8 0 008-8v-48a8 8 0 00-8-8H320zm0 136a8 8 0 00-8 8v48a8 8 0 008 8h184a8 8 0 008-8v-48a8 8 0 00-8-8H320z"}}]},name:"file-text",theme:"filled"},ut=lt,Ee=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:ut}))},Te=t.forwardRef(Ee),xt=Te,je=e(1085),mt=function(I,M){return t.createElement(K.Z,(0,X.Z)({},I,{ref:M,icon:je.Z}))},ht=t.forwardRef(mt),ie=ht,ne=e(83262),Ve=e(15063),nt=e(43495),_e=s=>{const{componentCls:I,calc:M}=s,h=`${I}-list-card`,re=M(s.fontSize).mul(s.lineHeight).mul(2).add(s.paddingSM).add(s.paddingSM).equal();return{[h]:{borderRadius:s.borderRadius,position:"relative",background:s.colorFillContent,borderWidth:s.lineWidth,borderStyle:"solid",borderColor:"transparent",flex:"none",[`${h}-name,${h}-desc`]:{display:"flex",flexWrap:"nowrap",maxWidth:"100%"},[`${h}-ellipsis-prefix`]:{flex:"0 1 auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`${h}-ellipsis-suffix`]:{flex:"none"},"&-type-overview":{padding:M(s.paddingSM).sub(s.lineWidth).equal(),paddingInlineStart:M(s.padding).add(s.lineWidth).equal(),display:"flex",flexWrap:"nowrap",gap:s.paddingXS,alignItems:"flex-start",width:236,[`${h}-icon`]:{fontSize:M(s.fontSizeLG).mul(2).equal(),lineHeight:1,paddingTop:M(s.paddingXXS).mul(1.5).equal(),flex:"none"},[`${h}-content`]:{flex:"auto",minWidth:0,display:"flex",flexDirection:"column",alignItems:"stretch"},[`${h}-desc`]:{color:s.colorTextTertiary}},"&-type-preview":{width:re,height:re,lineHeight:1,[`&:not(${h}-status-error)`]:{border:0},img:{width:"100%",height:"100%",verticalAlign:"top",objectFit:"cover",borderRadius:"inherit"},[`${h}-img-mask`]:{position:"absolute",inset:0,display:"flex",justifyContent:"center",alignItems:"center",background:`rgba(0, 0, 0, ${s.opacityLoading})`,borderRadius:"inherit"},[`&${h}-status-error`]:{[`img, ${h}-img-mask`]:{borderRadius:M(s.borderRadius).sub(s.lineWidth).equal()},[`${h}-desc`]:{paddingInline:s.paddingXXS}},[`${h}-progress`]:{}},[`${h}-remove`]:{position:"absolute",top:0,insetInlineEnd:0,border:0,padding:s.paddingXXS,background:"transparent",lineHeight:1,transform:"translate(50%, -50%)",fontSize:s.fontSize,cursor:"pointer",opacity:s.opacityLoading,display:"none","&:dir(rtl)":{transform:"translate(-50%, -50%)"},"&:hover":{opacity:1},"&:active":{opacity:s.opacityLoading}},[`&:hover ${h}-remove`]:{display:"block"},"&-status-error":{borderColor:s.colorError,[`${h}-desc`]:{color:s.colorError}},"&-motion":{transition:["opacity","width","margin","padding"].map(_=>`${_} ${s.motionDurationSlow}`).join(","),"&-appear-start":{width:0,transition:"none"},"&-leave-active":{opacity:0,width:0,paddingInline:0,borderInlineWidth:0,marginInlineEnd:M(s.paddingSM).mul(-1).equal()}}}}};const st={"&, *":{boxSizing:"border-box"}},n=s=>{const{componentCls:I,calc:M,antCls:h}=s,re=`${I}-drop-area`,_=`${I}-placeholder`;return{[re]:wt(N({position:"absolute",inset:0,zIndex:s.zIndexPopupBase},st),{"&-on-body":{position:"fixed",inset:0},"&-hide-placement":{[`${_}-inner`]:{display:"none"}},[_]:{padding:0}}),"&":{[_]:wt(N({height:"100%",borderRadius:s.borderRadius,borderWidth:s.lineWidthBold,borderStyle:"dashed",borderColor:"transparent",padding:s.padding,position:"relative",backdropFilter:"blur(10px)",background:s.colorBgPlaceholderHover},st),{[`${h}-upload-wrapper ${h}-upload${h}-upload-btn`]:{padding:0},[`&${_}-drag-in`]:{borderColor:s.colorPrimaryHover},[`&${_}-disabled`]:{opacity:.25,pointerEvents:"none"},[`${_}-inner`]:{gap:M(s.paddingXXS).div(2).equal()},[`${_}-icon`]:{fontSize:s.fontSizeHeading2,lineHeight:1},[`${_}-title${_}-title`]:{margin:0,fontSize:s.fontSize,lineHeight:s.lineHeight},[`${_}-description`]:{}})}}},u=s=>{const{componentCls:I,calc:M}=s,h=`${I}-list`,re=M(s.fontSize).mul(s.lineHeight).mul(2).add(s.paddingSM).add(s.paddingSM).equal();return{[I]:wt(N({position:"relative",width:"100%"},st),{[h]:{display:"flex",flexWrap:"wrap",gap:s.paddingSM,fontSize:s.fontSize,lineHeight:s.lineHeight,color:s.colorText,paddingBlock:s.paddingSM,paddingInline:s.padding,width:"100%",background:s.colorBgContainer,scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},"&-overflow-scrollX, &-overflow-scrollY":{"&:before, &:after":{content:'""',position:"absolute",opacity:0,transition:`opacity ${s.motionDurationSlow}`,zIndex:1}},"&-overflow-ping-start:before":{opacity:1},"&-overflow-ping-end:after":{opacity:1},"&-overflow-scrollX":{overflowX:"auto",overflowY:"hidden",flexWrap:"nowrap","&:before, &:after":{insetBlock:0,width:8},"&:before":{insetInlineStart:0,background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetInlineEnd:0,background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:dir(rtl)":{"&:before":{background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"}}},"&-overflow-scrollY":{overflowX:"hidden",overflowY:"auto",maxHeight:M(re).mul(3).equal(),"&:before, &:after":{insetInline:0,height:8},"&:before":{insetBlockStart:0,background:"linear-gradient(to bottom, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetBlockEnd:0,background:"linear-gradient(to top, rgba(0,0,0,0.06), rgba(0,0,0,0));"}},"&-upload-btn":{width:re,height:re,fontSize:s.fontSizeHeading2,color:"#999"},"&-prev-btn, &-next-btn":{position:"absolute",top:"50%",transform:"translateY(-50%)",boxShadow:s.boxShadowTertiary,opacity:0,pointerEvents:"none"},"&-prev-btn":{left:{_skip_check_:!0,value:s.padding}},"&-next-btn":{right:{_skip_check_:!0,value:s.padding}},"&:dir(ltr)":{[`&${h}-overflow-ping-start ${h}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${h}-overflow-ping-end ${h}-next-btn`]:{opacity:1,pointerEvents:"auto"}},"&:dir(rtl)":{[`&${h}-overflow-ping-end ${h}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${h}-overflow-ping-start ${h}-next-btn`]:{opacity:1,pointerEvents:"auto"}}}})}},g=s=>{const{colorBgContainer:I}=s;return{colorBgPlaceholderHover:new Ve.t(I).setA(.85).toRgbString()}};var C=(0,nt.I$)("Attachments",s=>{const I=(0,ne.IX)(s,{});return[n(I),u(I),_e(I)]},g);const W=s=>s.indexOf("image/")===0,Y=200;function le(s){return new Promise(I=>{if(!s||!s.type||!W(s.type)){I("");return}const M=new Image;if(M.onload=()=>{const{width:h,height:re}=M,_=h/re,Ze=_>1?Y:Y*_,Ne=_>1?Y/_:Y,Ie=document.createElement("canvas");Ie.width=Ze,Ie.height=Ne,Ie.style.cssText=`position: fixed; left: 0; top: 0; width: ${Ze}px; height: ${Ne}px; z-index: 9999; display: none;`,document.body.appendChild(Ie),Ie.getContext("2d").drawImage(M,0,0,Ze,Ne);const xe=Ie.toDataURL();document.body.removeChild(Ie),window.URL.revokeObjectURL(M.src),I(xe)},M.crossOrigin="anonymous",s.type.startsWith("image/svg+xml")){const h=new FileReader;h.onload=()=>{h.result&&typeof h.result=="string"&&(M.src=h.result)},h.readAsDataURL(s)}else if(s.type.startsWith("image/gif")){const h=new FileReader;h.onload=()=>{h.result&&I(h.result)},h.readAsDataURL(s)}else M.src=window.URL.createObjectURL(s)})}function fe(){return t.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink"},t.createElement("title",null,"audio"),t.createElement("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M10.7315824,7.11216117 C10.7428131,7.15148751 10.7485063,7.19218979 10.7485063,7.23309113 L10.7485063,8.07742614 C10.7484199,8.27364959 10.6183424,8.44607275 10.4296853,8.50003683 L8.32984514,9.09986306 L8.32984514,11.7071803 C8.32986605,12.5367078 7.67249692,13.217028 6.84345686,13.2454634 L6.79068592,13.2463395 C6.12766108,13.2463395 5.53916361,12.8217001 5.33010655,12.1924966 C5.1210495,11.563293 5.33842118,10.8709227 5.86959669,10.4741173 C6.40077221,10.0773119 7.12636292,10.0652587 7.67042486,10.4442027 L7.67020842,7.74937024 L7.68449368,7.74937024 C7.72405122,7.59919041 7.83988806,7.48101083 7.98924584,7.4384546 L10.1880418,6.81004755 C10.42156,6.74340323 10.6648954,6.87865515 10.7315824,7.11216117 Z M9.60714286,1.31785714 L12.9678571,4.67857143 L9.60714286,4.67857143 L9.60714286,1.31785714 Z",fill:"currentColor"})))}var Fe=e(9361),pe=e(38703);function Re(s){const{percent:I}=s,{token:M}=Fe.Z.useToken();return t.createElement(pe.Z,{type:"circle",percent:I,size:M.fontSizeHeading2*2,strokeColor:"#FFF",trailColor:"rgba(255, 255, 255, 0.3)",format:h=>t.createElement("span",{style:{color:"#FFF"}},(h||0).toFixed(0),"%")})}function ge(){return t.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink"},t.createElement("title",null,"video"),t.createElement("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M12.9678571,4.67857143 L9.60714286,1.31785714 L9.60714286,4.67857143 L12.9678571,4.67857143 Z M10.5379461,10.3101106 L6.68957555,13.0059749 C6.59910784,13.0693494 6.47439406,13.0473861 6.41101953,12.9569184 C6.3874624,12.9232903 6.37482581,12.8832269 6.37482581,12.8421686 L6.37482581,7.45043999 C6.37482581,7.33998304 6.46436886,7.25043999 6.57482581,7.25043999 C6.61588409,7.25043999 6.65594753,7.26307658 6.68957555,7.28663371 L10.5379461,9.98249803 C10.6284138,10.0458726 10.6503772,10.1705863 10.5870027,10.2610541 C10.5736331,10.2801392 10.5570312,10.2967411 10.5379461,10.3101106 Z",fill:"currentColor"})))}const ve="\xA0",et="#8c8c8c",bt=["png","jpg","jpeg","gif","bmp","webp","svg"],We=[{icon:t.createElement(te,null),color:"#22b35e",ext:["xlsx","xls"]},{icon:t.createElement(Ue,null),color:et,ext:bt},{icon:t.createElement(S,null),color:et,ext:["md","mdx"]},{icon:t.createElement(G,null),color:"#ff4d4f",ext:["pdf"]},{icon:t.createElement(Se,null),color:"#ff6e31",ext:["ppt","pptx"]},{icon:t.createElement(we,null),color:"#1677ff",ext:["doc","docx"]},{icon:t.createElement(ke,null),color:"#fab714",ext:["zip","rar","7z","tar","gz"]},{icon:t.createElement(ge,null),color:"#ff4d4f",ext:["mp4","avi","mov","wmv","flv","mkv"]},{icon:t.createElement(fe,null),color:"#8c8c8c",ext:["mp3","wav","flac","ape","aac","ogg"]}];function it(s,I){return I.some(M=>s.toLowerCase()===`.${M}`)}function yt(s){let I=s;const M=["B","KB","MB","GB","TB","PB","EB"];let h=0;for(;I>=1024&&h<M.length-1;)I/=1024,h++;return`${I.toFixed(0)} ${M[h]}`}function Et(s,I){const{prefixCls:M,item:h,onRemove:re,className:_,style:Ze}=s,Ne=t.useContext(z),{disabled:Ie}=Ne||{},{name:Xe,size:xe,percent:me,status:Oe="done",description:Qe}=h,{getPrefixCls:rt}=(0,f.Z)(),qe=rt("attachment",M),ee=`${qe}-list-card`,[Mt,It,at]=C(qe),[pt,ct]=t.useMemo(()=>{const ye=Xe||"",Ge=ye.match(/^(.*)\.[^.]+$/);return Ge?[Ge[1],ye.slice(Ge[1].length)]:[ye,""]},[Xe]),Ct=t.useMemo(()=>it(ct,bt),[ct]),he=t.useMemo(()=>Qe||(Oe==="uploading"?`${me||0}%`:Oe==="error"?h.response||ve:xe?yt(xe):ve),[Oe,me]),[ot,Tt]=t.useMemo(()=>{for(const{ext:ye,icon:Ge,color:Xt}of We)if(it(ct,ye))return[Ge,Xt];return[t.createElement(xt,{key:"defaultIcon"}),et]},[ct]),[Ft,Pt]=t.useState();t.useEffect(()=>{if(h.originFileObj){let ye=!0;return le(h.originFileObj).then(Ge=>{ye&&Pt(Ge)}),()=>{ye=!1}}Pt(void 0)},[h.originFileObj]);let gt=null;const $t=h.thumbUrl||h.url||Ft,ae=Ct&&(h.originFileObj||$t);return ae?gt=t.createElement(t.Fragment,null,t.createElement("img",{alt:"preview",src:$t}),Oe!=="done"&&t.createElement("div",{className:`${ee}-img-mask`},Oe==="uploading"&&me!==void 0&&t.createElement(Re,{percent:me,prefixCls:ee}),Oe==="error"&&t.createElement("div",{className:`${ee}-desc`},t.createElement("div",{className:`${ee}-ellipsis-prefix`},he)))):gt=t.createElement(t.Fragment,null,t.createElement("div",{className:`${ee}-icon`,style:{color:Tt}},ot),t.createElement("div",{className:`${ee}-content`},t.createElement("div",{className:`${ee}-name`},t.createElement("div",{className:`${ee}-ellipsis-prefix`},pt!=null?pt:ve),t.createElement("div",{className:`${ee}-ellipsis-suffix`},ct)),t.createElement("div",{className:`${ee}-desc`},t.createElement("div",{className:`${ee}-ellipsis-prefix`},he)))),Mt(t.createElement("div",{className:E()(ee,{[`${ee}-status-${Oe}`]:Oe,[`${ee}-type-preview`]:ae,[`${ee}-type-overview`]:!ae},_,It,at),style:Ze,ref:I},gt,!Ie&&re&&t.createElement("button",{type:"button",className:`${ee}-remove`,onClick:()=>{re(h)}},t.createElement(ie,null))))}var Rt=t.forwardRef(Et);const St=1;function At(s){const{prefixCls:I,items:M,onRemove:h,overflow:re,upload:_,listClassName:Ze,listStyle:Ne,itemClassName:Ie,itemStyle:Xe}=s,xe=`${I}-list`,me=t.useRef(null),[Oe,Qe]=t.useState(!1),{disabled:rt}=t.useContext(z);t.useEffect(()=>(Qe(!0),()=>{Qe(!1)}),[]);const[qe,ee]=t.useState(!1),[Mt,It]=t.useState(!1),at=()=>{const he=me.current;he&&(re==="scrollX"?(ee(Math.abs(he.scrollLeft)>=St),It(he.scrollWidth-he.clientWidth-Math.abs(he.scrollLeft)>=St)):re==="scrollY"&&(ee(he.scrollTop!==0),It(he.scrollHeight-he.clientHeight!==he.scrollTop)))};t.useEffect(()=>{at()},[re]);const pt=he=>{const ot=me.current;ot&&ot.scrollTo({left:ot.scrollLeft+he*ot.clientWidth,behavior:"smooth"})},ct=()=>{pt(-1)},Ct=()=>{pt(1)};return t.createElement("div",{className:E()(xe,{[`${xe}-overflow-${s.overflow}`]:re,[`${xe}-overflow-ping-start`]:qe,[`${xe}-overflow-ping-end`]:Mt},Ze),ref:me,onScroll:at,style:Ne},t.createElement(Z.V4,{keys:M.map(he=>({key:he.uid,item:he})),motionName:`${xe}-card-motion`,component:!1,motionAppear:Oe,motionLeave:!0,motionEnter:!0},({key:he,item:ot,className:Tt,style:Ft})=>t.createElement(Rt,{key:he,prefixCls:I,item:ot,onRemove:h,className:E()(Tt,Ie),style:N(N({},Ft),Xe)})),!rt&&t.createElement(x,{upload:_},t.createElement(L.ZP,{className:`${xe}-upload-btn`,type:"dashed"},t.createElement(Ce,{className:`${xe}-upload-btn-icon`}))),re==="scrollX"&&t.createElement(t.Fragment,null,t.createElement(L.ZP,{size:"small",shape:"circle",className:`${xe}-prev-btn`,icon:t.createElement(D,null),onClick:ct}),t.createElement(L.ZP,{size:"small",shape:"circle",className:`${xe}-next-btn`,icon:t.createElement(B,null),onClick:Ct})))}var Bt=e(86250),Lt=e(78301);function zt(s,I){const{prefixCls:M,placeholder:h={},upload:re,className:_,style:Ze}=s,Ne=`${M}-placeholder`,Ie=h||{},{disabled:Xe}=t.useContext(z),[xe,me]=t.useState(!1),Oe=()=>{me(!0)},Qe=ee=>{ee.currentTarget.contains(ee.relatedTarget)||me(!1)},rt=()=>{me(!1)},qe=t.isValidElement(h)?h:t.createElement(Bt.Z,{align:"center",justify:"center",vertical:!0,className:`${Ne}-inner`},t.createElement(Lt.Z.Text,{className:`${Ne}-icon`},Ie.icon),t.createElement(Lt.Z.Title,{className:`${Ne}-title`,level:5},Ie.title),t.createElement(Lt.Z.Text,{className:`${Ne}-description`,type:"secondary"},Ie.description));return t.createElement("div",{className:E()(Ne,{[`${Ne}-drag-in`]:xe,[`${Ne}-disabled`]:Xe},_),onDragEnter:Oe,onDragLeave:Qe,onDrop:rt,"aria-hidden":Xe,style:Ze},t.createElement(R.Z.Dragger,(0,X.Z)({showUploadList:!1},re,{ref:I,style:{padding:0,border:0,background:"transparent"}}),qe))}var Dt=t.forwardRef(zt);function Ut(s,I){const kt=s,{prefixCls:M,rootClassName:h,rootStyle:re,className:_,style:Ze,items:Ne,children:Ie,getDropContainer:Xe,placeholder:xe,onChange:me,overflow:Oe,disabled:Qe,classNames:rt={},styles:qe={}}=kt,ee=dt(kt,["prefixCls","rootClassName","rootStyle","className","style","items","children","getDropContainer","placeholder","onChange","overflow","disabled","classNames","styles"]),{getPrefixCls:Mt,direction:It}=(0,f.Z)(),at=Mt("attachment",M),pt=(0,c.Z)("attachments"),{classNames:ct,styles:Ct}=pt,he=t.useRef(null),ot=t.useRef(null);t.useImperativeHandle(I,()=>({nativeElement:he.current,upload:vt=>{var Zt,jt;const Ot=(jt=(Zt=ot.current)==null?void 0:Zt.nativeElement)==null?void 0:jt.querySelector('input[type="file"]');if(Ot){const _t=new DataTransfer;_t.items.add(vt),Ot.files=_t.files,Ot.dispatchEvent(new Event("change",{bubbles:!0}))}}}));const[Tt,Ft,Pt]=C(at),gt=E()(Ft,Pt),[$t,ae]=(0,$.C8)([],{value:Ne}),ye=(0,$.zX)(vt=>{ae(vt.fileList),me==null||me(vt)}),Ge=wt(N({},ee),{fileList:$t,onChange:ye}),Xt=vt=>{const Ot=$t.filter(Zt=>Zt.uid!==vt.uid);ye({file:vt,fileList:Ot})};let Gt;const Kt=(vt,Ot,Zt)=>{const jt=typeof xe=="function"?xe(vt):xe;return t.createElement(Dt,{placeholder:jt,upload:Ge,prefixCls:at,className:E()(ct.placeholder,rt.placeholder),style:N(N(N({},Ct.placeholder),qe.placeholder),Ot==null?void 0:Ot.style),ref:Zt})};if(Ie)Gt=t.createElement(t.Fragment,null,t.createElement(x,{upload:Ge,rootClassName:h,ref:ot},Ie),t.createElement(a,{getDropContainer:Xe,prefixCls:at,className:E()(gt,h)},Kt("drop")));else{const vt=$t.length>0;Gt=t.createElement("div",{className:E()(at,gt,{[`${at}-rtl`]:It==="rtl"},_,h),style:N(N({},re),Ze),dir:It||"ltr",ref:he},t.createElement(At,{prefixCls:at,items:$t,onRemove:Xt,overflow:Oe,upload:Ge,listClassName:E()(ct.list,rt.list),listStyle:N(N(N({},Ct.list),qe.list),!vt&&{display:"none"}),itemClassName:E()(ct.item,rt.item),itemStyle:N(N({},Ct.item),qe.item)}),Kt("inline",vt?{style:{display:"none"}}:{},ot),t.createElement(a,{getDropContainer:Xe||(()=>he.current),prefixCls:at,className:gt},Kt("drop")))}return Tt(t.createElement(z.Provider,{value:{disabled:Qe}},Gt))}const Ht=t.forwardRef(Ut);Ht.FileCard=Rt;var Vt=Ht},37864:function(O,v,e){"use strict";e.d(v,{Z:function(){return d}});var l=e(87462),E=e(93967),t=e.n(E),c=e(67294),f=e(7134),$=e(21450),H=e(36158),z=e(8410);function a(i){return typeof i=="string"}var V=(i,r,S,P)=>{const[A,Q]=c.useState(""),[j,G]=c.useState(1),q=r&&a(i);return(0,z.Z)(()=>{Q(i),!q&&a(i)?G(i.length):a(i)&&a(A)&&i.indexOf(A)!==0&&G(1)},[i]),c.useEffect(()=>{if(q&&j<i.length){const de=setTimeout(()=>{G(ue=>ue+S)},P);return()=>{clearTimeout(de)}}},[j,r,i]),[q?i.slice(0,j):i,q&&j<i.length]};function K(i){return c.useMemo(()=>{if(!i)return[!1,0,0,null];let r={step:1,interval:50,suffix:null};return typeof i=="object"&&(r=N(N({},r),i)),[!0,r.step,r.interval,r.suffix]},[i])}var be=K,Ce=({prefixCls:i})=>c.createElement("span",{className:`${i}-dot`},c.createElement("i",{className:`${i}-dot-item`,key:"item-1"}),c.createElement("i",{className:`${i}-dot-item`,key:"item-2"}),c.createElement("i",{className:`${i}-dot-item`,key:"item-3"})),ce=e(11568),w=e(83262),y=e(43495);const D=i=>{const{componentCls:r,paddingSM:S,padding:P}=i;return{[r]:{[`${r}-content`]:{"&-filled,&-outlined,&-shadow":{padding:`${(0,ce.bf)(S)} ${(0,ce.bf)(P)}`,borderRadius:i.borderRadiusLG},"&-filled":{backgroundColor:i.colorFillContent},"&-outlined":{border:`1px solid ${i.colorBorderSecondary}`},"&-shadow":{boxShadow:i.boxShadowTertiary}}}}},T=i=>{const{componentCls:r,fontSize:S,lineHeight:P,paddingSM:A,padding:Q,calc:j}=i,G=j(S).mul(P).div(2).add(A).equal(),q=`${r}-content`;return{[r]:{[q]:{"&-round":{borderRadius:{_skip_check_:!0,value:G},paddingInline:j(Q).mul(1.25).equal()}},[`&-start ${q}-corner`]:{borderStartStartRadius:i.borderRadiusXS},[`&-end ${q}-corner`]:{borderStartEndRadius:i.borderRadiusXS}}}};var b=i=>{const{componentCls:r,padding:S}=i;return{[`${r}-list`]:{display:"flex",flexDirection:"column",gap:S,overflowY:"auto"}}};const B=new ce.E4("loadingMove",{"0%":{transform:"translateY(0)"},"10%":{transform:"translateY(4px)"},"20%":{transform:"translateY(0)"},"30%":{transform:"translateY(-4px)"},"40%":{transform:"translateY(0)"}}),L=new ce.E4("cursorBlink",{"0%":{opacity:1},"50%":{opacity:0},"100%":{opacity:1}}),Z=i=>{const{componentCls:r,fontSize:S,lineHeight:P,paddingSM:A,colorText:Q,calc:j}=i;return{[r]:{display:"flex",columnGap:A,[`&${r}-end`]:{justifyContent:"end",flexDirection:"row-reverse",[`& ${r}-content-wrapper`]:{alignItems:"flex-end"}},[`&${r}-rtl`]:{direction:"rtl"},[`&${r}-typing ${r}-content:last-child::after`]:{content:'"|"',fontWeight:900,userSelect:"none",opacity:1,marginInlineStart:"0.1em",animationName:L,animationDuration:"0.8s",animationIterationCount:"infinite",animationTimingFunction:"linear"},[`& ${r}-avatar`]:{display:"inline-flex",justifyContent:"center",alignSelf:"flex-start"},[`& ${r}-header, & ${r}-footer`]:{fontSize:S,lineHeight:P,color:i.colorText},[`& ${r}-header`]:{marginBottom:i.paddingXXS},[`& ${r}-footer`]:{marginTop:A},[`& ${r}-content-wrapper`]:{flex:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start",minWidth:0,maxWidth:"100%"},[`& ${r}-content`]:{position:"relative",boxSizing:"border-box",minWidth:0,maxWidth:"100%",color:Q,fontSize:i.fontSize,lineHeight:i.lineHeight,minHeight:j(A).mul(2).add(j(P).mul(S)).equal(),wordBreak:"break-word",[`& ${r}-dot`]:{position:"relative",height:"100%",display:"flex",alignItems:"center",columnGap:i.marginXS,padding:`0 ${(0,ce.bf)(i.paddingXXS)}`,"&-item":{backgroundColor:i.colorPrimary,borderRadius:"100%",width:4,height:4,animationName:B,animationDuration:"2s",animationIterationCount:"infinite",animationTimingFunction:"linear","&:nth-child(1)":{animationDelay:"0s"},"&:nth-child(2)":{animationDelay:"0.2s"},"&:nth-child(3)":{animationDelay:"0.4s"}}}}}}},R=()=>({});var m=(0,y.I$)("Bubble",i=>{const r=(0,w.IX)(i,{});return[Z(r),b(r),D(r),T(r)]},R);const x=c.createContext({}),F=(i,r)=>{const W=i,{prefixCls:S,className:P,rootClassName:A,style:Q,classNames:j={},styles:G={},avatar:q,placement:Ae="start",loading:de=!1,loadingRender:ue,typing:Se,content:Ye="",messageRender:$e,variant:Be="filled",shape:De,onTypingComplete:we,header:se,footer:Me}=W,He=dt(W,["prefixCls","className","rootClassName","style","classNames","styles","avatar","placement","loading","loadingRender","typing","content","messageRender","variant","shape","onTypingComplete","header","footer"]),{onUpdate:Pe}=c.useContext(x),ke=c.useRef(null);c.useImperativeHandle(r,()=>({nativeElement:ke.current}));const{direction:lt,getPrefixCls:ut}=(0,H.Z)(),Ee=ut("bubble",S),Te=(0,$.Z)("bubble"),[xt,je,mt,ht]=be(Se),[ie,ne]=V(Ye,xt,je,mt);c.useEffect(()=>{Pe==null||Pe()},[ie]);const Ve=c.useRef(!1);c.useEffect(()=>{!ne&&!de?Ve.current||(Ve.current=!0,we==null||we()):Ve.current=!1},[ne,de]);const[nt,Je,_e]=m(Ee),st=t()(Ee,A,Te.className,P,Je,_e,`${Ee}-${Ae}`,{[`${Ee}-rtl`]:lt==="rtl",[`${Ee}-typing`]:ne&&!de&&!$e&&!ht}),n=c.isValidElement(q)?q:c.createElement(f.C,q),u=$e?$e(ie):ie;let g;de?g=ue?ue():c.createElement(Ce,{prefixCls:Ee}):g=c.createElement(c.Fragment,null,u,ne&&ht);let C=c.createElement("div",{style:N(N({},Te.styles.content),G.content),className:t()(`${Ee}-content`,`${Ee}-content-${Be}`,De&&`${Ee}-content-${De}`,Te.classNames.content,j.content)},g);return(se||Me)&&(C=c.createElement("div",{className:`${Ee}-content-wrapper`},se&&c.createElement("div",{className:t()(`${Ee}-header`,Te.classNames.header,j.header),style:N(N({},Te.styles.header),G.header)},se),C,Me&&c.createElement("div",{className:t()(`${Ee}-footer`,Te.classNames.footer,j.footer),style:N(N({},Te.styles.footer),G.footer)},Me))),nt(c.createElement("div",(0,l.Z)({style:N(N({},Te.style),Q),className:st},He,{ref:ke}),q&&c.createElement("div",{style:N(N({},Te.styles.avatar),G.avatar),className:t()(`${Ee}-avatar`,Te.classNames.avatar,j.avatar)},n),C))};var k=c.forwardRef(F),J=e(56790),te=e(64217);function Le(i){const[r,S]=c.useState(i.length),P=c.useMemo(()=>i.slice(0,r),[i,r]),A=c.useMemo(()=>{const j=P[P.length-1];return j?j.key:null},[P]);c.useEffect(()=>{var j;if(!(P.length&&P.every((G,q)=>{var Ae;return G.key===((Ae=i[q])==null?void 0:Ae.key)}))){if(P.length===0)S(1);else for(let G=0;G<P.length;G+=1)if(P[G].key!==((j=i[G])==null?void 0:j.key)){S(G);break}}},[i]);const Q=(0,J.zX)(j=>{j===A&&S(r+1)});return[P,Q]}function tt(i,r){const S=c.useCallback(P=>typeof r=="function"?r(P):r?r[P.role]||{}:{},[r]);return c.useMemo(()=>(i||[]).map((P,A)=>{var j;const Q=(j=P.key)!=null?j:`preset_${A}`;return wt(N(N({},S(P)),P),{key:Q})}),[i,S])}const Ke=1,ze=(i,r)=>{const ht=i,{prefixCls:S,rootClassName:P,className:A,items:Q,autoScroll:j=!0,roles:G}=ht,q=dt(ht,["prefixCls","rootClassName","className","items","autoScroll","roles"]),Ae=(0,te.Z)(q,{attr:!0,aria:!0}),de=c.useRef(null),ue=c.useRef({}),{getPrefixCls:Se}=(0,H.Z)(),Ye=Se("bubble",S),$e=`${Ye}-list`,[Be,De,we]=m(Ye),[se,Me]=c.useState(!1);c.useEffect(()=>(Me(!0),()=>{Me(!1)}),[]);const He=tt(Q,G),[Pe,ke]=Le(He),[lt,ut]=c.useState(!0),[Ee,Te]=c.useState(0),xt=ie=>{const ne=ie.target;ut(ne.scrollHeight-Math.abs(ne.scrollTop)-ne.clientHeight<=Ke)};c.useEffect(()=>{j&&de.current&&lt&&de.current.scrollTo({top:de.current.scrollHeight})},[Ee]),c.useEffect(()=>{var ie;if(j){const ne=(ie=Pe[Pe.length-2])==null?void 0:ie.key,Ve=ue.current[ne];if(Ve){const{nativeElement:nt}=Ve,{top:Je,bottom:_e}=nt.getBoundingClientRect(),{top:st,bottom:n}=de.current.getBoundingClientRect();Je<n&&_e>st&&(Te(g=>g+1),ut(!0))}}},[Pe.length]),c.useImperativeHandle(r,()=>({nativeElement:de.current,scrollTo:({key:ie,offset:ne,behavior:Ve="smooth",block:nt})=>{if(typeof ne=="number")de.current.scrollTo({top:ne,behavior:Ve});else if(ie!==void 0){const Je=ue.current[ie];if(Je){const _e=Pe.findIndex(st=>st.key===ie);ut(_e===Pe.length-1),Je.nativeElement.scrollIntoView({behavior:Ve,block:nt})}}}}));const je=(0,J.zX)(()=>{j&&Te(ie=>ie+1)}),mt=c.useMemo(()=>({onUpdate:je}),[]);return Be(c.createElement(x.Provider,{value:mt},c.createElement("div",(0,l.Z)({},Ae,{className:t()($e,P,A,De,we,{[`${$e}-reach-end`]:lt}),ref:de,onScroll:xt}),Pe.map(Ve=>{var nt=Ve,{key:ie}=nt,ne=dt(nt,["key"]);return c.createElement(k,(0,l.Z)({},ne,{key:ie,ref:Je=>{Je?ue.current[ie]=Je:delete ue.current[ie]},typing:se?ne.typing:!1,onTypingComplete:()=>{var Je;(Je=ne.onTypingComplete)==null||Je.call(ne),ke(ie)}}))}))))};var o=c.forwardRef(ze);k.List=o;var d=k},78919:function(O,v,e){"use strict";e.d(v,{Z:function(){return ce}});var l=e(87462),E=e(78301),t=e(93967),c=e.n(t),f=e(67294),$=e(21450),H=e(36158),z=e(11568),a=e(83262),X=e(43495);const V=w=>{const{componentCls:y}=w;return{[y]:{"&, & *":{boxSizing:"border-box"},maxWidth:"100%",[`&${y}-rtl`]:{direction:"rtl"},[`& ${y}-title`]:{marginBlockStart:0,fontWeight:"normal",color:w.colorTextTertiary},[`& ${y}-list`]:{display:"flex",gap:w.paddingSM,overflowX:"scroll","&::-webkit-scrollbar":{display:"none"},listStyle:"none",paddingInlineStart:0,marginBlock:0,alignItems:"stretch","&-wrap":{flexWrap:"wrap"},"&-vertical":{flexDirection:"column",alignItems:"flex-start"}},[`${y}-item`]:{flex:"none",display:"flex",gap:w.paddingXS,height:"auto",paddingBlock:w.paddingSM,paddingInline:w.padding,alignItems:"flex-start",justifyContent:"flex-start",background:w.colorBgContainer,borderRadius:w.borderRadiusLG,transition:["border","background"].map(D=>`${D} ${w.motionDurationSlow}`).join(","),border:`${(0,z.bf)(w.lineWidth)} ${w.lineType} ${w.colorBorderSecondary}`,[`&:not(${y}-item-has-nest)`]:{"&:hover":{cursor:"pointer",background:w.colorFillTertiary},"&:active":{background:w.colorFill}},[`${y}-content`]:{flex:"auto",minWidth:0,display:"flex",gap:w.paddingXXS,flexDirection:"column",alignItems:"flex-start"},[`${y}-icon, ${y}-label, ${y}-desc`]:{margin:0,padding:0,fontSize:w.fontSize,lineHeight:w.lineHeight,textAlign:"start",whiteSpace:"normal"},[`${y}-label`]:{color:w.colorTextHeading,fontWeight:500},[`${y}-label + ${y}-desc`]:{color:w.colorTextTertiary},[`&${y}-item-disabled`]:{pointerEvents:"none",background:w.colorBgContainerDisabled,[`${y}-label, ${y}-desc`]:{color:w.colorTextTertiary}}}}}},K=w=>{const{componentCls:y}=w;return{[y]:{[`${y}-item-has-nest`]:{[`> ${y}-content`]:{[`> ${y}-label`]:{fontSize:w.fontSizeLG,lineHeight:w.lineHeightLG}}},[`&${y}-nested`]:{marginTop:w.paddingXS,alignSelf:"stretch",[`${y}-list`]:{alignItems:"stretch"},[`${y}-item`]:{border:0,background:w.colorFillQuaternary}}}}},be=()=>({});var oe=(0,X.I$)("Prompts",w=>{const y=(0,a.IX)(w,{});return[V(y),K(y)]},be);const Ce=w=>{const o=w,{prefixCls:y,title:D,className:T,items:p,onItemClick:b,vertical:B,wrap:L,rootClassName:Z,styles:R={},classNames:m={},style:x}=o,F=dt(o,["prefixCls","title","className","items","onItemClick","vertical","wrap","rootClassName","styles","classNames","style"]),{getPrefixCls:U,direction:k}=(0,H.Z)(),J=U("prompts",y),te=(0,$.Z)("prompts"),[Le,tt,Ke]=oe(J),ze=c()(J,te.className,T,Z,tt,Ke,{[`${J}-rtl`]:k==="rtl"}),Ue=c()(`${J}-list`,te.classNames.list,m.list,{[`${J}-list-wrap`]:L},{[`${J}-list-vertical`]:B});return Le(f.createElement("div",(0,l.Z)({},F,{className:ze,style:N(N({},x),te.style)}),D&&f.createElement(E.Z.Title,{level:5,className:c()(`${J}-title`,te.classNames.title,m.title),style:N(N({},te.styles.title),R.title)},D),f.createElement("div",{className:Ue,style:N(N({},te.styles.list),R.list)},p==null?void 0:p.map((d,i)=>{const r=d.children&&d.children.length>0;return f.createElement("div",{key:d.key||`key_${i}`,style:N(N({},te.styles.item),R.item),className:c()(`${J}-item`,te.classNames.item,m.item,{[`${J}-item-disabled`]:d.disabled,[`${J}-item-has-nest`]:r}),onClick:()=>{!r&&b&&b({data:d})}},d.icon&&f.createElement("div",{className:`${J}-icon`},d.icon),f.createElement("div",{className:c()(`${J}-content`,te.classNames.itemContent,m.itemContent),style:N(N({},te.styles.itemContent),R.itemContent)},d.label&&f.createElement("h6",{className:`${J}-label`},d.label),d.description&&f.createElement("p",{className:`${J}-desc`},d.description),r&&f.createElement(Ce,{className:`${J}-nested`,items:d.children,vertical:!0,onItemClick:b,classNames:{list:m.subList,item:m.subItem},styles:{list:R.subList,item:R.subItem}})))}))))};var ce=Ce},45311:function(O,v,e){"use strict";e.d(v,{Z:function(){return st}});var l=e(87462),E=e(26915),t=e(86250),c=e(93967),f=e.n(c),$=e(56790),H=e(64217),z=e(88306),a=e(67294);function X(n,u){return(0,a.useImperativeHandle)(n,()=>{const g=u(),{nativeElement:C}=g;return new Proxy(C,{get(W,Y){return g[Y]?g[Y]:Reflect.get(W,Y)}})})}var V=e(21450),K=e(36158),be=e(89503),oe=e(51398),Ce=function(u,g){return a.createElement(oe.Z,(0,l.Z)({},u,{ref:g,icon:be.Z}))},ce=a.forwardRef(Ce),w=ce,y=e(28036),D=e(29372);const T=a.createContext({}),p=()=>({height:0}),b=n=>({height:n.scrollHeight});function B(n){const{title:u,onOpenChange:g,open:C,children:W,className:Y,style:le,classNames:fe={},styles:Fe={},closable:pe,forceRender:Re}=n,{prefixCls:ge}=a.useContext(T),ve=`${ge}-header`;return a.createElement(D.ZP,{motionEnter:!0,motionLeave:!0,motionName:`${ve}-motion`,leavedClassName:`${ve}-motion-hidden`,onEnterStart:p,onEnterActive:b,onLeaveStart:b,onLeaveActive:p,visible:C,forceRender:Re},({className:et,style:bt})=>a.createElement("div",{className:f()(ve,et,Y),style:N(N({},bt),le)},(pe!==!1||u)&&a.createElement("div",{className:f()(`${ve}-header`,fe.header),style:N({},Fe.header)},a.createElement("div",{className:`${ve}-title`},u),pe!==!1&&a.createElement("div",{className:`${ve}-close`},a.createElement(y.ZP,{type:"text",icon:a.createElement(w,null),size:"small",onClick:()=>{g==null||g(!C)}}))),W&&a.createElement("div",{className:f()(`${ve}-content`,fe.content),style:N({},Fe.content)},W)))}const L=a.createContext(null);function Z(n,u){var ve;const ge=n,{className:g,action:C,onClick:W}=ge,Y=dt(ge,["className","action","onClick"]),le=a.useContext(L),{prefixCls:fe,disabled:Fe}=le,pe=le[C],Re=(ve=Fe!=null?Fe:Y.disabled)!=null?ve:le[`${C}Disabled`];return a.createElement(y.ZP,(0,l.Z)({type:"text"},Y,{ref:u,onClick:et=>{Re||(pe&&pe(),W&&W(et))},className:f()(fe,g,{[`${fe}-disabled`]:Re})}))}var R=a.forwardRef(Z),m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},x=m,F=function(u,g){return a.createElement(oe.Z,(0,l.Z)({},u,{ref:g,icon:x}))},U=a.forwardRef(F),k=U;function J(n,u){return a.createElement(R,(0,l.Z)({icon:a.createElement(k,null)},n,{action:"onClear",ref:u}))}var te=a.forwardRef(J),tt=(0,a.memo)(n=>{const{className:u}=n;return a.createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:u},a.createElement("title",null,"Stop Loading"),a.createElement("rect",{fill:"currentColor",height:"250",rx:"24",ry:"24",width:"250",x:"375",y:"375"}),a.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",opacity:"0.45"}),a.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",strokeDasharray:"600 9999999"},a.createElement("animateTransform",{attributeName:"transform",dur:"1s",from:"0 500 500",repeatCount:"indefinite",to:"360 500 500",type:"rotate"})))});function Ke(n,u){const{prefixCls:g}=a.useContext(L),{className:C}=n;return a.createElement(R,(0,l.Z)({icon:null,color:"primary",variant:"text",shape:"circle"},n,{className:f()(C,`${g}-loading-button`),action:"onCancel",ref:u}),a.createElement(tt,{className:`${g}-loading-icon`}))}var ze=a.forwardRef(Ke),Ue={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},o=Ue,d=function(u,g){return a.createElement(oe.Z,(0,l.Z)({},u,{ref:g,icon:o}))},i=a.forwardRef(d),r=i;function S(n,u){return a.createElement(R,(0,l.Z)({icon:a.createElement(r,null),type:"primary",shape:"circle"},n,{action:"onSend",ref:u}))}var P=a.forwardRef(S),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M682 455V311l-76 76v68c-.1 50.7-42 92.1-94 92a95.8 95.8 0 01-52-15l-54 55c29.1 22.4 65.9 36 106 36 93.8 0 170-75.1 170-168z"}},{tag:"path",attrs:{d:"M833 446h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254-63 0-120.7-23-165-61l-54 54a334.01 334.01 0 00179 81v102H326c-13.9 0-24.9 14.3-25 32v36c.1 4.4 2.9 8 6 8h408c3.2 0 6-3.6 6-8v-36c0-17.7-11-32-25-32H547V782c165.3-17.9 294-157.9 294-328 0-4.4-3.6-8-8-8zm13.1-377.7l-43.5-41.9a8 8 0 00-11.2.1l-129 129C634.3 101.2 577 64 511 64c-93.9 0-170 75.3-170 168v224c0 6.7.4 13.3 1.2 19.8l-68 68A252.33 252.33 0 01258 454c-.2-4.4-3.8-8-8-8h-60c-4.4 0-8 3.6-8 8 0 53 12.5 103 34.6 147.4l-137 137a8.03 8.03 0 000 11.3l42.7 42.7c3.1 3.1 8.2 3.1 11.3 0L846.2 79.8l.1-.1c3.1-3.2 3-8.3-.2-11.4zM417 401V232c0-50.6 41.9-92 94-92 46 0 84.1 32.3 92.3 74.7L417 401z"}}]},name:"audio-muted",theme:"outlined"},Q=A,j=function(u,g){return a.createElement(oe.Z,(0,l.Z)({},u,{ref:g,icon:Q}))},G=a.forwardRef(j),q=G,Ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"},de=Ae,ue=function(u,g){return a.createElement(oe.Z,(0,l.Z)({},u,{ref:g,icon:de}))},Se=a.forwardRef(ue),Ye=Se;const $e=1e3,Be=4,De=140,we=De/2,se=250,Me=500,He=.8;function Pe({className:n}){return a.createElement("svg",{color:"currentColor",viewBox:`0 0 ${$e} ${$e}`,xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:n},a.createElement("title",null,"Speech Recording"),Array.from({length:Be}).map((u,g)=>{const C=($e-De*Be)/(Be-1),W=g*(C+De),Y=$e/2-se/2,le=$e/2-Me/2;return a.createElement("rect",{fill:"currentColor",rx:we,ry:we,height:se,width:De,x:W,y:Y,key:g},a.createElement("animate",{attributeName:"height",values:`${se}; ${Me}; ${se}`,keyTimes:"0; 0.5; 1",dur:`${He}s`,begin:`${He/Be*g}s`,repeatCount:"indefinite"}),a.createElement("animate",{attributeName:"y",values:`${Y}; ${le}; ${Y}`,keyTimes:"0; 0.5; 1",dur:`${He}s`,begin:`${He/Be*g}s`,repeatCount:"indefinite"}))}))}function ke(n,u){const{speechRecording:g,onSpeechDisabled:C,prefixCls:W}=a.useContext(L);let Y=null;return g?Y=a.createElement(Pe,{className:`${W}-recording-icon`}):C?Y=a.createElement(q,null):Y=a.createElement(Ye,null),a.createElement(R,(0,l.Z)({icon:Y,color:"primary",variant:"text"},n,{action:"onSpeech",ref:u}))}var lt=a.forwardRef(ke),ut=e(11568),Ee=e(83262),Te=e(43495),je=n=>{const{componentCls:u,calc:g}=n,C=`${u}-header`;return{[u]:{[C]:{borderBottomWidth:n.lineWidth,borderBottomStyle:"solid",borderBottomColor:n.colorBorder,"&-header":{background:n.colorFillAlter,fontSize:n.fontSize,lineHeight:n.lineHeight,paddingBlock:g(n.paddingSM).sub(n.lineWidthBold).equal(),paddingInlineStart:n.padding,paddingInlineEnd:n.paddingXS,display:"flex",[`${C}-title`]:{flex:"auto"}},"&-content":{padding:n.padding},"&-motion":{transition:["height","border"].map(W=>`${W} ${n.motionDurationSlow}`).join(","),overflow:"hidden","&-enter-start, &-leave-active":{borderBottomColor:"transparent"},"&-hidden":{display:"none"}}}}}};const mt=n=>{const{componentCls:u,padding:g,paddingSM:C,paddingXS:W,lineWidth:Y,lineWidthBold:le,calc:fe}=n;return{[u]:{position:"relative",width:"100%",boxSizing:"border-box",boxShadow:`${n.boxShadowTertiary}`,transition:`background ${n.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:fe(n.borderRadius).mul(2).equal()},borderColor:n.colorBorder,borderWidth:0,borderStyle:"solid","&:after":{content:'""',position:"absolute",inset:0,pointerEvents:"none",transition:`border-color ${n.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:"inherit"},borderStyle:"inherit",borderColor:"inherit",borderWidth:Y},"&:focus-within":{boxShadow:`${n.boxShadowSecondary}`,borderColor:n.colorPrimary,"&:after":{borderWidth:le}},"&-disabled":{background:n.colorBgContainerDisabled},[`&${u}-rtl`]:{direction:"rtl"},[`${u}-content`]:{display:"flex",gap:W,width:"100%",paddingBlock:C,paddingInlineStart:g,paddingInlineEnd:C,boxSizing:"border-box",alignItems:"flex-end"},[`${u}-prefix`]:{flex:"none"},[`${u}-input`]:{padding:0,borderRadius:0,flex:"auto",alignSelf:"center",minHeight:"auto"},[`${u}-actions-list`]:{flex:"none",display:"flex","&-presets":{gap:n.paddingXS}},[`${u}-actions-btn`]:{"&-disabled":{opacity:.45},"&-loading-button":{padding:0,border:0},"&-loading-icon":{height:n.controlHeight,width:n.controlHeight,verticalAlign:"top"},"&-recording-icon":{height:"1.2em",width:"1.2em",verticalAlign:"top"}}}}},ht=()=>({});var ie=(0,Te.I$)("Sender",n=>{const{paddingXS:u,calc:g}=n,C=(0,Ee.IX)(n,{SenderContentMaxWidth:`calc(100% - ${(0,ut.bf)(g(u).add(32).equal())})`});return[mt(C),je(C)]},ht);let ne;!ne&&typeof window!="undefined"&&(ne=window.SpeechRecognition||window.webkitSpeechRecognition);function Ve(n,u){const g=(0,$.zX)(n),[C,W,Y]=a.useMemo(()=>typeof u=="object"?[u.recording,u.onRecordingChange,typeof u.recording=="boolean"]:[void 0,void 0,!1],[u]),[le,fe]=a.useState(null);a.useEffect(()=>{if(typeof navigator!="undefined"&&"permissions"in navigator){let We=null;return navigator.permissions.query({name:"microphone"}).then(it=>{fe(it.state),it.onchange=function(){fe(this.state)},We=it}),()=>{We&&(We.onchange=null)}}},[]);const Fe=ne&&le!=="denied",pe=a.useRef(null),[Re,ge]=(0,$.C8)(!1,{value:C}),ve=a.useRef(!1),et=()=>{if(Fe&&!pe.current){const We=new ne;We.onstart=()=>{ge(!0)},We.onend=()=>{ge(!1)},We.onresult=it=>{var yt,Et,Rt;if(!ve.current){const St=(Rt=(Et=(yt=it.results)==null?void 0:yt[0])==null?void 0:Et[0])==null?void 0:Rt.transcript;g(St)}ve.current=!1},pe.current=We}},bt=(0,$.zX)(We=>{We&&!Re||(ve.current=We,Y?W==null||W(!Re):(et(),pe.current&&(Re?(pe.current.stop(),W==null||W(!1)):(pe.current.start(),W==null||W(!0)))))});return[Fe,bt,Re]}function nt(n,u,g){return(0,z.Z)(n,u)||g}const _e=a.forwardRef((n,u)=>{const $t=n,{prefixCls:g,styles:C={},classNames:W={},className:Y,rootClassName:le,style:fe,defaultValue:Fe,value:pe,readOnly:Re,submitType:ge="enter",onSubmit:ve,loading:et,components:bt,onCancel:We,onChange:it,actions:yt,onKeyPress:Et,onKeyDown:Rt,disabled:St,allowSpeech:At,prefix:Bt,header:Lt,onPaste:zt,onPasteFile:Dt}=$t,Ut=dt($t,["prefixCls","styles","classNames","className","rootClassName","style","defaultValue","value","readOnly","submitType","onSubmit","loading","components","onCancel","onChange","actions","onKeyPress","onKeyDown","disabled","allowSpeech","prefix","header","onPaste","onPasteFile"]),{direction:Ht,getPrefixCls:Vt}=(0,K.Z)(),s=Vt("sender",g),I=a.useRef(null),M=a.useRef(null);X(u,()=>{var ae,ye;return{nativeElement:I.current,focus:(ae=M.current)==null?void 0:ae.focus,blur:(ye=M.current)==null?void 0:ye.blur}});const h=(0,V.Z)("sender"),re=`${s}-input`,[_,Ze,Ne]=ie(s),Ie=f()(s,h.className,Y,le,Ze,Ne,{[`${s}-rtl`]:Ht==="rtl",[`${s}-disabled`]:St}),Xe=`${s}-actions-btn`,xe=`${s}-actions-list`,[me,Oe]=(0,$.C8)(Fe||"",{value:pe}),Qe=(ae,ye)=>{Oe(ae),it&&it(ae,ye)},[rt,qe,ee]=Ve(ae=>{Qe(`${me} ${ae}`)},At),Mt=nt(bt,["input"],E.Z.TextArea),It=(0,H.Z)(Ut,{attr:!0,aria:!0,data:!0}),at=wt(N({},It),{ref:M}),pt=()=>{me&&ve&&!et&&ve(me)},ct=()=>{Qe("")},Ct=a.useRef(!1),he=()=>{Ct.current=!0},ot=()=>{Ct.current=!1},Tt=ae=>{const ye=ae.key==="Enter"&&!Ct.current;switch(ge){case"enter":ye&&!ae.shiftKey&&(ae.preventDefault(),pt());break;case"shiftEnter":ye&&ae.shiftKey&&(ae.preventDefault(),pt());break}Et&&Et(ae)},Ft=ae=>{var Ge;const ye=(Ge=ae.clipboardData)==null?void 0:Ge.files[0];ye&&Dt&&(Dt(ye),ae.preventDefault()),zt==null||zt(ae)},Pt=ae=>{var ye,Ge;ae.target!==((ye=I.current)==null?void 0:ye.querySelector(`.${re}`))&&ae.preventDefault(),(Ge=M.current)==null||Ge.focus()};let gt=a.createElement(t.Z,{className:`${xe}-presets`},At&&a.createElement(lt,null),et?a.createElement(ze,null):a.createElement(P,null));return typeof yt=="function"?gt=yt(gt,{components:{SendButton:P,ClearButton:te,LoadingButton:ze}}):yt&&(gt=yt),_(a.createElement("div",{ref:I,className:Ie,style:N(N({},h.style),fe)},Lt&&a.createElement(T.Provider,{value:{prefixCls:s}},Lt),a.createElement("div",{className:`${s}-content`,onMouseDown:Pt},Bt&&a.createElement("div",{className:f()(`${s}-prefix`,h.classNames.prefix,W.prefix),style:N(N({},h.styles.prefix),C.prefix)},Bt),a.createElement(Mt,(0,l.Z)({},at,{disabled:St,style:N(N({},h.styles.input),C.input),className:f()(re,h.classNames.input,W.input),autoSize:{maxRows:8},value:me,onChange:ae=>{Qe(ae.target.value,ae),qe(!0)},onPressEnter:Tt,onCompositionStart:he,onCompositionEnd:ot,onKeyDown:Rt,onPaste:Ft,variant:"borderless",readOnly:Re})),a.createElement("div",{className:f()(xe,h.classNames.actions,W.actions),style:N(N({},h.styles.actions),C.actions)},a.createElement(L.Provider,{value:{prefixCls:Xe,onSend:pt,onSendDisabled:!me,onClear:ct,onClearDisabled:!me,onCancel:We,onCancelDisabled:!et,onSpeech:()=>qe(!1),onSpeechDisabled:!rt,speechRecording:ee,disabled:St}},gt)))))});_e.Header=B;var st=_e},43495:function(O,v,e){"use strict";e.d(v,{I$:function(){return Ce}});var l=e(83262),E=e(36158),t=e(11568),c=e(9361),f=e(29691),$=e(1393),H=e(67294),z="1.0.5",a=z;const X=(0,t.jG)(c.Z.defaultAlgorithm),V={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},K=(y,D,T)=>{const p=T.getDerivativeToken(y),Z=D,{override:b}=Z,B=dt(Z,["override"]);let L=wt(N({},p),{override:b});return L=(0,$.Z)(L),B&&Object.entries(B).forEach(([R,m])=>{const k=m,{theme:x}=k,F=dt(k,["theme"]);let U=F;x&&(U=K(N(N({},L),F),{override:F},x)),L[R]=U}),L};function be(){const{token:y,hashed:D,theme:T=X,override:p,cssVar:b}=H.useContext(c.Z._internalContext),[B,L,Z]=(0,t.fp)(T,[c.Z.defaultSeed,y],{salt:`${a}-${D||""}`,override:p,getComputedToken:K,cssVar:b&&{prefix:b.prefix,key:b.key,unitless:f.NJ,ignore:f.ID,preserve:V}});return[T,Z,D?L:"",B,b]}function oe(){const[y,D,T]=be();return{theme:y,token:D,hashId:T}}const{genStyleHooks:Ce,genComponentStyleHook:ce,genSubStyleComponent:w}=(0,l.rb)({usePrefix:()=>{const{getPrefixCls:y,iconPrefixCls:D}=(0,E.Z)();return{iconPrefixCls:D,rootPrefixCls:y()}},useToken:()=>{const[y,D,T,p,b]=be();return{theme:y,realToken:D,hashId:T,token:p,cssVar:b}},useCSP:()=>{const{csp:y}=(0,E.Z)();return y!=null?y:{}},layer:{name:"antdx",dependencies:["antd"]}})},93461:function(O,v,e){"use strict";e.d(v,{Z:function(){return w}});var l=e(67294);const E=`

`,t=`
`,c=":",f=D=>(D!=null?D:"").trim()!=="";function $(){let D="";return new TransformStream({transform(T,p){D+=T;const b=D.split(E);b.slice(0,-1).forEach(B=>{f(B)&&p.enqueue(B)}),D=b[b.length-1]},flush(T){f(D)&&T.enqueue(D)}})}function H(){return new TransformStream({transform(D,T){const b=D.split(t).reduce((B,L)=>{const Z=L.indexOf(c);if(Z===-1)throw new Error(`The key-value separator "${c}" is not found in the sse line chunk!`);const R=L.slice(0,Z);if(!f(R))return B;const m=L.slice(Z+1);return wt(N({},B),{[R]:m})},{});Object.keys(b).length!==0&&T.enqueue(b)}})}function z(D){const{readableStream:T,transformStream:p}=D;if(!(T instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const b=new TextDecoderStream,B=p?T.pipeThrough(b).pipeThrough(p):T.pipeThrough(b).pipeThrough($()).pipeThrough(H());return B[Symbol.asyncIterator]=function(){return nn(this,null,function*(){const L=this.getReader();for(;;){const{done:Z,value:R}=yield new Qt(L.read());if(Z)break;R&&(yield R)}})},B}var a=z,V=(p,...b)=>Nt(this,[p,...b],function*(D,T={}){const x=T,{fetch:B=globalThis.fetch,middlewares:L={}}=x,Z=dt(x,["fetch","middlewares"]);if(typeof B!="function")throw new Error("The options.fetch must be a typeof fetch function!");let R=[D,Z];typeof L.onRequest=="function"&&(R=yield L.onRequest(...R));let m=yield B(...R);if(typeof L.onResponse=="function"){const F=yield L.onResponse(m);if(!(F instanceof Response))throw new Error("The options.onResponse must return a Response instance!");m=F}if(!m.ok)throw new Error(`Fetch failed with status ${m.status}`);if(!m.body)throw new Error("The response body is empty.");return m});const y=class y{constructor(T){ft(this,"baseURL");ft(this,"model");ft(this,"defaultHeaders");ft(this,"customOptions");ft(this,"create",(T,p,b)=>Nt(this,null,function*(){var L;const B={method:"POST",body:JSON.stringify(N({model:this.model},T)),headers:this.defaultHeaders};try{const Z=yield V(this.baseURL,N({fetch:this.customOptions.fetch},B));if(b){yield this.customResponseHandler(Z,p,b);return}const R=Z.headers.get("content-type")||"";switch(R.split(";")[0].trim()){case"text/event-stream":yield this.sseResponseHandler(Z,p);break;case"application/json":yield this.jsonResponseHandler(Z,p);break;default:throw new Error(`The response content-type: ${R} is not support!`)}}catch(Z){const R=Z instanceof Error?Z:new Error("Unknown error!");throw(L=p==null?void 0:p.onError)==null||L.call(p,R),R}}));ft(this,"customResponseHandler",(T,p,b)=>Nt(this,null,function*(){var L,F;const B=[];try{for(var Z=qt(a({readableStream:T.body,transformStream:b})),R,m,x;R=!(m=yield Z.next()).done;R=!1){const U=m.value;B.push(U),(L=p==null?void 0:p.onUpdate)==null||L.call(p,U)}}catch(m){x=[m]}finally{try{R&&(m=Z.return)&&(yield m.call(Z))}finally{if(x)throw x[0]}}(F=p==null?void 0:p.onSuccess)==null||F.call(p,B)}));ft(this,"sseResponseHandler",(T,p)=>Nt(this,null,function*(){var B,x;const b=[];try{for(var L=qt(a({readableStream:T.body})),Z,R,m;Z=!(R=yield L.next()).done;Z=!1){const F=R.value;b.push(F),(B=p==null?void 0:p.onUpdate)==null||B.call(p,F)}}catch(R){m=[R]}finally{try{Z&&(R=L.return)&&(yield R.call(L))}finally{if(m)throw m[0]}}(x=p==null?void 0:p.onSuccess)==null||x.call(p,b)}));ft(this,"jsonResponseHandler",(T,p)=>Nt(this,null,function*(){var B,L;const b=yield T.json();(B=p==null?void 0:p.onUpdate)==null||B.call(p,b),(L=p==null?void 0:p.onSuccess)==null||L.call(p,[b])}));const Z=T,{baseURL:p,model:b,dangerouslyApiKey:B}=Z,L=dt(Z,["baseURL","model","dangerouslyApiKey"]);this.baseURL=T.baseURL,this.model=T.model,this.defaultHeaders=N({"Content-Type":"application/json"},T.dangerouslyApiKey&&{Authorization:T.dangerouslyApiKey}),this.customOptions=L}static init(T){if(!T.baseURL||typeof T.baseURL!="string")throw new Error("The baseURL is not valid!");const p=T.fetch||T.baseURL;return y.instanceBuffer.has(p)||y.instanceBuffer.set(p,new y(T)),y.instanceBuffer.get(p)}};ft(y,"instanceBuffer",new Map);let K=y;var oe=K.init;let Ce=0;class ce{constructor(T){ft(this,"config");ft(this,"requestingMap",{});ft(this,"request",(T,p)=>{const{request:b}=this.config,{onUpdate:B,onSuccess:L,onError:Z}=p,R=Ce;Ce+=1,this.requestingMap[R]=!0,b==null||b(T,{onUpdate:m=>{this.requestingMap[R]&&B(m)},onSuccess:m=>{this.requestingMap[R]&&(L(m),this.finishRequest(R))},onError:m=>{this.requestingMap[R]&&(Z(m),this.finishRequest(R))}})});this.config=T}finishRequest(T){delete this.requestingMap[T]}isRequesting(){return Object.keys(this.requestingMap).length>0}}function w(D){const b=D,{request:T}=b,p=dt(b,["request"]);return l.useMemo(()=>[new ce(N({request:T||oe({baseURL:p.baseURL,model:p.model,dangerouslyApiKey:p.dangerouslyApiKey}).create},p))],[])}},34114:function(O,v,e){"use strict";e.d(v,{Z:function(){return f}});var l=e(56790),E=e(67294);function t($){const[,H]=E.useState(0),z=E.useRef(typeof $=="function"?$():$),a=E.useCallback(V=>{z.current=typeof V=="function"?V(z.current):V,H(K=>K+1)},[]),X=E.useCallback(()=>z.current,[]);return[z.current,a,X]}function c($){return Array.isArray($)?$:[$]}function f($){const{defaultMessages:H,agent:z,requestFallback:a,requestPlaceholder:X,parser:V}=$,K=E.useRef(0),[be,oe,Ce]=t(()=>(H||[]).map((p,b)=>N({id:`default_${b}`,status:"local"},p))),ce=(p,b)=>{const B={id:`msg_${K.current}`,message:p,status:b};return K.current+=1,B},w=E.useMemo(()=>{const p=[];return be.forEach(b=>{const B=V?V(b.message):b.message,L=c(B);L.forEach((Z,R)=>{let m=b.id;L.length>1&&(m=`${m}_${R}`),p.push({id:m,message:Z,status:b.status})})}),p},[be]),y=p=>p.filter(b=>b.status!=="loading"&&b.status!=="error").map(b=>b.message),D=()=>y(Ce());return{onRequest:(0,l.zX)(p=>{if(!z)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let b=null;oe(Z=>{let R=[...Z,ce(p,"local")];if(X){let m;typeof X=="function"?m=X(p,{messages:y(R)}):m=X;const x=ce(m,"loading");b=x.id,R=[...R,x]}return R});let B=null;const L=(Z,R)=>{let m=Ce().find(x=>x.id===B);return m?oe(x=>x.map(F=>F.id===B?wt(N({},F),{message:Z,status:R}):F)):(m=ce(Z,R),oe(x=>[...x.filter(U=>U.id!==b),m]),B=m.id),m};z.request({message:p,messages:D()},{onUpdate:Z=>{L(Z,"loading")},onSuccess:Z=>{L(Z,"success")},onError:Z=>Nt(this,null,function*(){if(a){let R;typeof a=="function"?R=yield a(p,{error:Z,messages:D()}):R=a,oe(m=>[...m.filter(x=>x.id!==b&&x.id!==B),ce(R,"error")])}else oe(R=>R.filter(m=>m.id!==b&&m.id!==B))})})}),messages:be,parsedMessages:w,setMessages:oe}}},78205:function(O,v,e){"use strict";e.d(v,{Z:function(){return ce}});var l=e(78301),E=e(86250),t=e(93967),c=e.n(t),f=e(67294),$=e(21450),H=e(36158),z=e(83262),a=e(43495);const X=w=>{const{componentCls:y,calc:D}=w,T=D(w.fontSizeHeading3).mul(w.lineHeightHeading3).equal(),p=D(w.fontSize).mul(w.lineHeight).equal();return{[y]:{gap:w.padding,[`${y}-icon`]:{height:D(T).add(p).add(w.paddingXXS).equal(),display:"flex",img:{height:"100%"}},[`${y}-content-wrapper`]:{gap:w.paddingXS,flex:"auto",minWidth:0,[`${y}-title-wrapper`]:{gap:w.paddingXS},[`${y}-title`]:{margin:0},[`${y}-extra`]:{marginInlineStart:"auto"}}}}},V=w=>{const{componentCls:y}=w;return{[y]:{"&-filled":{paddingInline:w.padding,paddingBlock:w.paddingSM,background:w.colorFillContent,borderRadius:w.borderRadiusLG},"&-borderless":{[`${y}-title`]:{fontSize:w.fontSizeHeading3,lineHeight:w.lineHeightHeading3}}}}},K=()=>({});var be=(0,a.I$)("Welcome",w=>{const y=(0,z.IX)(w,{});return[X(y),V(y)]},K);function oe(w,y){const{prefixCls:D,rootClassName:T,className:p,style:b,variant:B="filled",classNames:L={},styles:Z={},icon:R,title:m,description:x,extra:F}=w,{direction:U,getPrefixCls:k}=(0,H.Z)(),J=k("welcome",D),te=(0,$.Z)("welcome"),[Le,tt,Ke]=be(J),ze=f.useMemo(()=>{if(!R)return null;let d=R;return typeof R=="string"&&R.startsWith("http")&&(d=f.createElement("img",{src:R,alt:"icon"})),f.createElement("div",{className:c()(`${J}-icon`,te.classNames.icon,L.icon),style:Z.icon},d)},[R]),Ue=f.useMemo(()=>m?f.createElement(l.Z.Title,{level:4,className:c()(`${J}-title`,te.classNames.title,L.title),style:Z.title},m):null,[m]),o=f.useMemo(()=>F?f.createElement("div",{className:c()(`${J}-extra`,te.classNames.extra,L.extra),style:Z.extra},F):null,[F]);return Le(f.createElement(E.Z,{ref:y,className:c()(J,te.className,p,T,tt,Ke,`${J}-${B}`,{[`${J}-rtl`]:U==="rtl"}),style:b},ze,f.createElement(E.Z,{vertical:!0,className:`${J}-content-wrapper`},F?f.createElement(E.Z,{align:"flex-start",className:`${J}-title-wrapper`},Ue,o):Ue,x&&f.createElement(l.Z.Text,{className:c()(`${J}-description`,te.classNames.description,L.description),style:Z.description},x))))}var ce=f.forwardRef(oe)},36158:function(O,v,e){"use strict";var l=e(21532),E=e(67294);const t="ant";function c(){const{getPrefixCls:f,direction:$,csp:H,iconPrefixCls:z,theme:a}=E.useContext(l.ZP.ConfigContext);return{theme:a,getPrefixCls:f,direction:$,csp:H,iconPrefixCls:z}}v.Z=c},51398:function(O,v,e){"use strict";e.d(v,{Z:function(){return st}});var l=e(87462),E=e(97685),t=e(4942),c=e(45987),f=e(67294),$=e(93967),H=e.n($),z=e(15063),a=2,X=.16,V=.05,K=.05,be=.15,oe=5,Ce=4,ce=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function w(n,u,g){var C;return Math.round(n.h)>=60&&Math.round(n.h)<=240?C=g?Math.round(n.h)-a*u:Math.round(n.h)+a*u:C=g?Math.round(n.h)+a*u:Math.round(n.h)-a*u,C<0?C+=360:C>=360&&(C-=360),C}function y(n,u,g){if(n.h===0&&n.s===0)return n.s;var C;return g?C=n.s-X*u:u===Ce?C=n.s+X:C=n.s+V*u,C>1&&(C=1),g&&u===oe&&C>.1&&(C=.1),C<.06&&(C=.06),Math.round(C*100)/100}function D(n,u,g){var C;return g?C=n.v+K*u:C=n.v-be*u,C=Math.max(0,Math.min(1,C)),Math.round(C*100)/100}function T(n){for(var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},g=[],C=new z.t(n),W=C.toHsv(),Y=oe;Y>0;Y-=1){var le=new z.t({h:w(W,Y,!0),s:y(W,Y,!0),v:D(W,Y,!0)});g.push(le)}g.push(C);for(var fe=1;fe<=Ce;fe+=1){var Fe=new z.t({h:w(W,fe),s:y(W,fe),v:D(W,fe)});g.push(Fe)}return u.theme==="dark"?ce.map(function(pe){var Re=pe.index,ge=pe.amount;return new z.t(u.backgroundColor||"#141414").mix(g[Re],ge).toHexString()}):g.map(function(pe){return pe.toHexString()})}var p={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},b=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];b.primary=b[5];var B=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];B.primary=B[5];var L=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];L.primary=L[5];var Z=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];Z.primary=Z[5];var R=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];R.primary=R[5];var m=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];m.primary=m[5];var x=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];x.primary=x[5];var F=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];F.primary=F[5];var U=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];U.primary=U[5];var k=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];k.primary=k[5];var J=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];J.primary=J[5];var te=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];te.primary=te[5];var Le=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];Le.primary=Le[5];var tt=null,Ke={red:b,volcano:B,orange:L,gold:Z,yellow:R,lime:m,green:x,cyan:F,blue:U,geekblue:k,purple:J,magenta:te,grey:Le},ze=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];ze.primary=ze[5];var Ue=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];Ue.primary=Ue[5];var o=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];o.primary=o[5];var d=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];d.primary=d[5];var i=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];i.primary=i[5];var r=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];r.primary=r[5];var S=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];S.primary=S[5];var P=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];P.primary=P[5];var A=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];A.primary=A[5];var Q=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];Q.primary=Q[5];var j=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];j.primary=j[5];var G=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];G.primary=G[5];var q=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];q.primary=q[5];var Ae={red:ze,volcano:Ue,orange:o,gold:d,yellow:i,lime:r,green:S,cyan:P,blue:A,geekblue:Q,purple:j,magenta:G,grey:q},de=(0,f.createContext)({}),ue=de,Se=e(1413),Ye=e(71002),$e=e(44958),Be=e(27571),De=e(80334);function we(n){return n.replace(/-(.)/g,function(u,g){return g.toUpperCase()})}function se(n,u){(0,De.ZP)(n,"[@ant-design/icons] ".concat(u))}function Me(n){return(0,Ye.Z)(n)==="object"&&typeof n.name=="string"&&typeof n.theme=="string"&&((0,Ye.Z)(n.icon)==="object"||typeof n.icon=="function")}function He(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(n).reduce(function(u,g){var C=n[g];switch(g){case"class":u.className=C,delete u.class;break;default:delete u[g],u[we(g)]=C}return u},{})}function Pe(n,u,g){return g?f.createElement(n.tag,(0,Se.Z)((0,Se.Z)({key:u},He(n.attrs)),g),(n.children||[]).map(function(C,W){return Pe(C,"".concat(u,"-").concat(n.tag,"-").concat(W))})):f.createElement(n.tag,(0,Se.Z)({key:u},He(n.attrs)),(n.children||[]).map(function(C,W){return Pe(C,"".concat(u,"-").concat(n.tag,"-").concat(W))}))}function ke(n){return T(n)[0]}function lt(n){return n?Array.isArray(n)?n:[n]:[]}var ut={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},Ee=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Te=function(u){var g=(0,f.useContext)(ue),C=g.csp,W=g.prefixCls,Y=g.layer,le=Ee;W&&(le=le.replace(/anticon/g,W)),Y&&(le="@layer ".concat(Y,` {
`).concat(le,`
}`)),(0,f.useEffect)(function(){var fe=u.current,Fe=(0,Be.A)(fe);(0,$e.hq)(le,"@ant-design-icons",{prepend:!Y,csp:C,attachTo:Fe})},[])},xt=["icon","className","onClick","style","primaryColor","secondaryColor"],je={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function mt(n){var u=n.primaryColor,g=n.secondaryColor;je.primaryColor=u,je.secondaryColor=g||ke(u),je.calculated=!!g}function ht(){return(0,Se.Z)({},je)}var ie=function(u){var g=u.icon,C=u.className,W=u.onClick,Y=u.style,le=u.primaryColor,fe=u.secondaryColor,Fe=(0,c.Z)(u,xt),pe=f.useRef(),Re=je;if(le&&(Re={primaryColor:le,secondaryColor:fe||ke(le)}),Te(pe),se(Me(g),"icon should be icon definiton, but got ".concat(g)),!Me(g))return null;var ge=g;return ge&&typeof ge.icon=="function"&&(ge=(0,Se.Z)((0,Se.Z)({},ge),{},{icon:ge.icon(Re.primaryColor,Re.secondaryColor)})),Pe(ge.icon,"svg-".concat(ge.name),(0,Se.Z)((0,Se.Z)({className:C,onClick:W,style:Y,"data-icon":ge.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},Fe),{},{ref:pe}))};ie.displayName="IconReact",ie.getTwoToneColors=ht,ie.setTwoToneColors=mt;var ne=ie;function Ve(n){var u=lt(n),g=(0,E.Z)(u,2),C=g[0],W=g[1];return ne.setTwoToneColors({primaryColor:C,secondaryColor:W})}function nt(){var n=ne.getTwoToneColors();return n.calculated?[n.primaryColor,n.secondaryColor]:n.primaryColor}var Je=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];Ve(U.primary);var _e=f.forwardRef(function(n,u){var g=n.className,C=n.icon,W=n.spin,Y=n.rotate,le=n.tabIndex,fe=n.onClick,Fe=n.twoToneColor,pe=(0,c.Z)(n,Je),Re=f.useContext(ue),ge=Re.prefixCls,ve=ge===void 0?"anticon":ge,et=Re.rootClassName,bt=H()(et,ve,(0,t.Z)((0,t.Z)({},"".concat(ve,"-").concat(C.name),!!C.name),"".concat(ve,"-spin"),!!W||C.name==="loading"),g),We=le;We===void 0&&fe&&(We=-1);var it=Y?{msTransform:"rotate(".concat(Y,"deg)"),transform:"rotate(".concat(Y,"deg)")}:void 0,yt=lt(Fe),Et=(0,E.Z)(yt,2),Rt=Et[0],St=Et[1];return f.createElement("span",(0,l.Z)({role:"img","aria-label":C.name},pe,{ref:u,tabIndex:We,onClick:fe,className:bt}),f.createElement(ne,{icon:C,primaryColor:Rt,secondaryColor:St,style:it}))});_e.displayName="AntdIcon",_e.getTwoToneColor=nt,_e.setTwoToneColor=Ve;var st=_e},15746:function(O,v,e){"use strict";var l=e(21584);v.Z=l.Z},26412:function(O,v,e){"use strict";e.d(v,{Z:function(){return Ue}});var l=e(67294),E=e(93967),t=e.n(E),c=e(74443),f=e(53124),$=e(98675),H=e(25378),a={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},V=l.createContext({}),K=e(50344),be=function(o,d){var i={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&d.indexOf(r)<0&&(i[r]=o[r]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var S=0,r=Object.getOwnPropertySymbols(o);S<r.length;S++)d.indexOf(r[S])<0&&Object.prototype.propertyIsEnumerable.call(o,r[S])&&(i[r[S]]=o[r[S]]);return i};const oe=o=>(0,K.Z)(o).map(d=>Object.assign(Object.assign({},d==null?void 0:d.props),{key:d.key}));function Ce(o,d,i){const r=l.useMemo(()=>d||oe(i),[d,i]);return l.useMemo(()=>r.map(P=>{var{span:A}=P,Q=be(P,["span"]);return A==="filled"?Object.assign(Object.assign({},Q),{filled:!0}):Object.assign(Object.assign({},Q),{span:typeof A=="number"?A:(0,c.m9)(o,A)})}),[r,o])}var ce=function(o,d){var i={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&d.indexOf(r)<0&&(i[r]=o[r]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var S=0,r=Object.getOwnPropertySymbols(o);S<r.length;S++)d.indexOf(r[S])<0&&Object.prototype.propertyIsEnumerable.call(o,r[S])&&(i[r[S]]=o[r[S]]);return i};function w(o,d){let i=[],r=[],S=!1,P=0;return o.filter(A=>A).forEach(A=>{const{filled:Q}=A,j=ce(A,["filled"]);if(Q){r.push(j),i.push(r),r=[],P=0;return}const G=d-P;P+=A.span||1,P>=d?(P>d?(S=!0,r.push(Object.assign(Object.assign({},j),{span:G}))):r.push(j),i.push(r),r=[],P=0):r.push(j)}),r.length>0&&i.push(r),i=i.map(A=>{const Q=A.reduce((j,G)=>j+(G.span||1),0);if(Q<d){const j=A[A.length-1];return j.span=d-Q+1,A}return A}),[i,S]}var D=(o,d)=>{const[i,r]=(0,l.useMemo)(()=>w(d,o),[d,o]);return i},p=o=>{let{children:d}=o;return d};function b(o){return o!=null}var L=o=>{const{itemPrefixCls:d,component:i,span:r,className:S,style:P,labelStyle:A,contentStyle:Q,bordered:j,label:G,content:q,colon:Ae,type:de}=o,ue=i;return j?l.createElement(ue,{className:t()({[`${d}-item-label`]:de==="label",[`${d}-item-content`]:de==="content"},S),style:P,colSpan:r},b(G)&&l.createElement("span",{style:A},G),b(q)&&l.createElement("span",{style:Q},q)):l.createElement(ue,{className:t()(`${d}-item`,S),style:P,colSpan:r},l.createElement("div",{className:`${d}-item-container`},(G||G===0)&&l.createElement("span",{className:t()(`${d}-item-label`,{[`${d}-item-no-colon`]:!Ae}),style:A},G),(q||q===0)&&l.createElement("span",{className:t()(`${d}-item-content`),style:Q},q)))};function Z(o,d,i){let{colon:r,prefixCls:S,bordered:P}=d,{component:A,type:Q,showLabel:j,showContent:G,labelStyle:q,contentStyle:Ae}=i;return o.map((de,ue)=>{let{label:Se,children:Ye,prefixCls:$e=S,className:Be,style:De,labelStyle:we,contentStyle:se,span:Me=1,key:He}=de;return typeof A=="string"?l.createElement(L,{key:`${Q}-${He||ue}`,className:Be,style:De,labelStyle:Object.assign(Object.assign({},q),we),contentStyle:Object.assign(Object.assign({},Ae),se),span:Me,colon:r,component:A,itemPrefixCls:$e,bordered:P,label:j?Se:null,content:G?Ye:null,type:Q}):[l.createElement(L,{key:`label-${He||ue}`,className:Be,style:Object.assign(Object.assign(Object.assign({},q),De),we),span:1,colon:r,component:A[0],itemPrefixCls:$e,bordered:P,label:Se,type:"label"}),l.createElement(L,{key:`content-${He||ue}`,className:Be,style:Object.assign(Object.assign(Object.assign({},Ae),De),se),span:Me*2-1,component:A[1],itemPrefixCls:$e,bordered:P,content:Ye,type:"content"})]})}var m=o=>{const d=l.useContext(V),{prefixCls:i,vertical:r,row:S,index:P,bordered:A}=o;return r?l.createElement(l.Fragment,null,l.createElement("tr",{key:`label-${P}`,className:`${i}-row`},Z(S,o,Object.assign({component:"th",type:"label",showLabel:!0},d))),l.createElement("tr",{key:`content-${P}`,className:`${i}-row`},Z(S,o,Object.assign({component:"td",type:"content",showContent:!0},d)))):l.createElement("tr",{key:P,className:`${i}-row`},Z(S,o,Object.assign({component:A?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},d)))},x=e(11568),F=e(14747),U=e(83559),k=e(83262);const J=o=>{const{componentCls:d,labelBg:i}=o;return{[`&${d}-bordered`]:{[`> ${d}-view`]:{border:`${(0,x.bf)(o.lineWidth)} ${o.lineType} ${o.colorSplit}`,"> table":{tableLayout:"auto"},[`${d}-row`]:{borderBottom:`${(0,x.bf)(o.lineWidth)} ${o.lineType} ${o.colorSplit}`,"&:last-child":{borderBottom:"none"},[`> ${d}-item-label, > ${d}-item-content`]:{padding:`${(0,x.bf)(o.padding)} ${(0,x.bf)(o.paddingLG)}`,borderInlineEnd:`${(0,x.bf)(o.lineWidth)} ${o.lineType} ${o.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${d}-item-label`]:{color:o.colorTextSecondary,backgroundColor:i,"&::after":{display:"none"}}}},[`&${d}-middle`]:{[`${d}-row`]:{[`> ${d}-item-label, > ${d}-item-content`]:{padding:`${(0,x.bf)(o.paddingSM)} ${(0,x.bf)(o.paddingLG)}`}}},[`&${d}-small`]:{[`${d}-row`]:{[`> ${d}-item-label, > ${d}-item-content`]:{padding:`${(0,x.bf)(o.paddingXS)} ${(0,x.bf)(o.padding)}`}}}}}},te=o=>{const{componentCls:d,extraColor:i,itemPaddingBottom:r,itemPaddingEnd:S,colonMarginRight:P,colonMarginLeft:A,titleMarginBottom:Q}=o;return{[d]:Object.assign(Object.assign(Object.assign({},(0,F.Wf)(o)),J(o)),{"&-rtl":{direction:"rtl"},[`${d}-header`]:{display:"flex",alignItems:"center",marginBottom:Q},[`${d}-title`]:Object.assign(Object.assign({},F.vS),{flex:"auto",color:o.titleColor,fontWeight:o.fontWeightStrong,fontSize:o.fontSizeLG,lineHeight:o.lineHeightLG}),[`${d}-extra`]:{marginInlineStart:"auto",color:i,fontSize:o.fontSize},[`${d}-view`]:{width:"100%",borderRadius:o.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${d}-row`]:{"> th, > td":{paddingBottom:r,paddingInlineEnd:S},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${d}-item-label`]:{color:o.colorTextTertiary,fontWeight:"normal",fontSize:o.fontSize,lineHeight:o.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,x.bf)(A)} ${(0,x.bf)(P)}`},[`&${d}-item-no-colon::after`]:{content:'""'}},[`${d}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${d}-item-content`]:{display:"table-cell",flex:1,color:o.contentColor,fontSize:o.fontSize,lineHeight:o.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${d}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${d}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${d}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${d}-row`]:{"> th, > td":{paddingBottom:o.paddingSM}}},"&-small":{[`${d}-row`]:{"> th, > td":{paddingBottom:o.paddingXS}}}})}},Le=o=>({labelBg:o.colorFillAlter,titleColor:o.colorText,titleMarginBottom:o.fontSizeSM*o.lineHeightSM,itemPaddingBottom:o.padding,itemPaddingEnd:o.padding,colonMarginRight:o.marginXS,colonMarginLeft:o.marginXXS/2,contentColor:o.colorText,extraColor:o.colorText});var tt=(0,U.I$)("Descriptions",o=>{const d=(0,k.IX)(o,{});return te(d)},Le),Ke=function(o,d){var i={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&d.indexOf(r)<0&&(i[r]=o[r]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var S=0,r=Object.getOwnPropertySymbols(o);S<r.length;S++)d.indexOf(r[S])<0&&Object.prototype.propertyIsEnumerable.call(o,r[S])&&(i[r[S]]=o[r[S]]);return i};const ze=o=>{const{prefixCls:d,title:i,extra:r,column:S,colon:P=!0,bordered:A,layout:Q,children:j,className:G,rootClassName:q,style:Ae,size:de,labelStyle:ue,contentStyle:Se,items:Ye}=o,$e=Ke(o,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","items"]),{getPrefixCls:Be,direction:De,descriptions:we}=l.useContext(f.E_),se=Be("descriptions",d),Me=(0,H.Z)(),He=l.useMemo(()=>{var je;return typeof S=="number"?S:(je=(0,c.m9)(Me,Object.assign(Object.assign({},a),S)))!==null&&je!==void 0?je:3},[Me,S]),Pe=Ce(Me,Ye,j),ke=(0,$.Z)(de),lt=D(He,Pe),[ut,Ee,Te]=tt(se),xt=l.useMemo(()=>({labelStyle:ue,contentStyle:Se}),[ue,Se]);return ut(l.createElement(V.Provider,{value:xt},l.createElement("div",Object.assign({className:t()(se,we==null?void 0:we.className,{[`${se}-${ke}`]:ke&&ke!=="default",[`${se}-bordered`]:!!A,[`${se}-rtl`]:De==="rtl"},G,q,Ee,Te),style:Object.assign(Object.assign({},we==null?void 0:we.style),Ae)},$e),(i||r)&&l.createElement("div",{className:`${se}-header`},i&&l.createElement("div",{className:`${se}-title`},i),r&&l.createElement("div",{className:`${se}-extra`},r)),l.createElement("div",{className:`${se}-view`},l.createElement("table",null,l.createElement("tbody",null,lt.map((je,mt)=>l.createElement(m,{key:mt,index:mt,colon:P,prefixCls:se,vertical:Q==="vertical",bordered:A,row:je}))))))))};ze.Item=p;var Ue=ze},86250:function(O,v,e){"use strict";e.d(v,{Z:function(){return R}});var l=e(67294),E=e(93967),t=e.n(E),c=e(98423),f=e(98065),$=e(53124),H=e(83559),z=e(83262);const a=["wrap","nowrap","wrap-reverse"],X=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],V=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"],K=(m,x)=>{const F=x.wrap===!0?"wrap":x.wrap;return{[`${m}-wrap-${F}`]:F&&a.includes(F)}},be=(m,x)=>{const F={};return V.forEach(U=>{F[`${m}-align-${U}`]=x.align===U}),F[`${m}-align-stretch`]=!x.align&&!!x.vertical,F},oe=(m,x)=>{const F={};return X.forEach(U=>{F[`${m}-justify-${U}`]=x.justify===U}),F};function Ce(m,x){return t()(Object.assign(Object.assign(Object.assign({},K(m,x)),be(m,x)),oe(m,x)))}var ce=Ce;const w=m=>{const{componentCls:x}=m;return{[x]:{display:"flex","&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},y=m=>{const{componentCls:x}=m;return{[x]:{"&-gap-small":{gap:m.flexGapSM},"&-gap-middle":{gap:m.flexGap},"&-gap-large":{gap:m.flexGapLG}}}},D=m=>{const{componentCls:x}=m,F={};return a.forEach(U=>{F[`${x}-wrap-${U}`]={flexWrap:U}}),F},T=m=>{const{componentCls:x}=m,F={};return V.forEach(U=>{F[`${x}-align-${U}`]={alignItems:U}}),F},p=m=>{const{componentCls:x}=m,F={};return X.forEach(U=>{F[`${x}-justify-${U}`]={justifyContent:U}}),F},b=()=>({});var B=(0,H.I$)("Flex",m=>{const{paddingXS:x,padding:F,paddingLG:U}=m,k=(0,z.IX)(m,{flexGapSM:x,flexGap:F,flexGapLG:U});return[w(k),y(k),D(k),T(k),p(k)]},b,{resetStyle:!1}),L=function(m,x){var F={};for(var U in m)Object.prototype.hasOwnProperty.call(m,U)&&x.indexOf(U)<0&&(F[U]=m[U]);if(m!=null&&typeof Object.getOwnPropertySymbols=="function")for(var k=0,U=Object.getOwnPropertySymbols(m);k<U.length;k++)x.indexOf(U[k])<0&&Object.prototype.propertyIsEnumerable.call(m,U[k])&&(F[U[k]]=m[U[k]]);return F},R=l.forwardRef((m,x)=>{const{prefixCls:F,rootClassName:U,className:k,style:J,flex:te,gap:Le,children:tt,vertical:Ke=!1,component:ze="div"}=m,Ue=L(m,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:o,direction:d,getPrefixCls:i}=l.useContext($.E_),r=i("flex",F),[S,P,A]=B(r),Q=Ke!=null?Ke:o==null?void 0:o.vertical,j=t()(k,U,o==null?void 0:o.className,r,P,A,ce(r,m),{[`${r}-rtl`]:d==="rtl",[`${r}-gap-${Le}`]:(0,f.n)(Le),[`${r}-vertical`]:Q}),G=Object.assign(Object.assign({},o==null?void 0:o.style),J);return te&&(G.flex=te),Le&&!(0,f.n)(Le)&&(G.gap=Le),S(l.createElement(ze,Object.assign({ref:x,className:j,style:G},(0,c.Z)(Ue,["justify","wrap","align"])),tt))})},71230:function(O,v,e){"use strict";var l=e(92820);v.Z=l.Z},35918:function(O,v,e){"use strict";e.d(v,{Z:function(){return z}});var l=e(87462),E=e(67294),t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},c=t,f=e(93771),$=function(X,V){return E.createElement(f.Z,(0,l.Z)({},X,{ref:V,icon:c}))},H=E.forwardRef($),z=H},64599:function(O,v,e){var l=e(96263);function E(t,c){var f=typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!f){if(Array.isArray(t)||(f=l(t))||c&&t&&typeof t.length=="number"){f&&(t=f);var $=0,H=function(){};return{s:H,n:function(){return $>=t.length?{done:!0}:{done:!1,value:t[$++]}},e:function(K){throw K},f:H}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var z=!0,a=!1,X;return{s:function(){f=f.call(t)},n:function(){var K=f.next();return z=K.done,K},e:function(K){a=!0,X=K},f:function(){try{!z&&f.return!=null&&f.return()}finally{if(a)throw X}}}}O.exports=E,O.exports.__esModule=!0,O.exports.default=O.exports},68400:function(O){function v(e,l){return l||(l=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(l)}}))}O.exports=v,O.exports.__esModule=!0,O.exports.default=O.exports}}]);
}());