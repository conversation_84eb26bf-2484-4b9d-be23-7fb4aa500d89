package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点位 和 布控规则关联表
 * <AUTHOR>
 * @date  2025-04-16 10:24
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
@Entity
@Table(name = "POINT_CONTROL_RULES")
public class PointControlRules extends BaseModel implements Serializable {

	/**
	 * 点位id
	 */
	@Column(name="POINT_ID", length = 50)
	private String pointId;

	/**
	 * 规则id
	 */
	@Column(name="RULE_ID", length = 50)
	private String ruleId;
}
