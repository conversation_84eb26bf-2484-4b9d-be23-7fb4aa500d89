html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-layout {
  min-height: 100% !important;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead>tr,
    &-tbody>tr {

      >th,
      >td {
        white-space: pre;

        >span {
          display: block;
        }
      }
    }
  }
}

.ant-design-pro.ant-pro-layout {
  height: 100%;
}



.ant-menu-light>.ant-menu .ant-menu-item {
  color: black !important;
}

.ant-menu-light .ant-menu-item-selected,
.ant-menu-light>.ant-menu .ant-menu-item-selected {
  color: #ffffff !important;
}

.ant-menu-light>.ant-menu .ant-menu-item a:hover {
  color: #ffffff !important;
}

.ant-menu-submenu-popup.ant-menu-submenu {
  height: 1px;
}

.ant-pro-top-nav-header-menu {
  line-height: 35px;
  padding-left: 90px;
}

.ant-pro-top-nav-header-menu .ant-menu-submenu {
  margin-left: 5px;
}

.ant-menu-light.ant-menu-horizontal>.ant-menu-item {
  margin-left: 5px;
}

.ant-menu-light .ant-menu-item,
.ant-menu-light>.ant-menu .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title,
.ant-menu-light>.ant-menu .ant-menu-submenu-title {
  height: 35px;
  line-height: 35px;
}

.ant-btn {
  background-color: #ffffff !important;
  color: rgb(18, 18, 18) !important;
  border: 1px solid rgb(220, 226, 235) !important;
  border-radius: 5px !important;

  &:focus {
    background-color: #ffffff !important;
    color: rgb(18, 18, 18) !important;
    border: 1px solid rgb(220, 226, 235) !important;
  }

  &:hover {
    border: 1px solid rgb(220, 226, 235) !important;
    background-color: #0BD357 !important;
    color: #ffffff !important;
    /* hover 时字体变白 */
  }
}

#logo h1 {
  display: none;
}

.ant-pro-global-header-header-actions-avatar {
  padding-inline-start: 0px;
}

.ant-table-body {
  max-height: 530px !important;
  overflow-y: auto !important;
}