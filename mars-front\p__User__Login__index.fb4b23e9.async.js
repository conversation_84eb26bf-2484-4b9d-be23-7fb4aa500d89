"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[366],{63434:function(ye,J,e){var M=e(1413),h=e(45987),z=e(22270),x=e(84567),Y=e(67294),K=e(90789),Q=e(27577),C=e(85893),H=["options","fieldProps","proFieldProps","valueEnum"],f=Y.forwardRef(function(b,A){var B=b.options,m=b.fieldProps,G=b.proFieldProps,s=b.valueEnum,n=(0,h.Z)(b,H);return(0,C.jsx)(Q.Z,(0,M.Z)({ref:A,valueType:"checkbox",valueEnum:(0,z.h)(s,void 0),fieldProps:(0,M.Z)({options:B},m),lightProps:(0,M.Z)({labelFormatter:function(){return(0,C.jsx)(Q.Z,(0,M.Z)({ref:A,valueType:"checkbox",mode:"read",valueEnum:(0,z.h)(s,void 0),filedConfig:{customLightMode:!0},fieldProps:(0,M.Z)({options:B},m),proFieldProps:G},n))}},n.lightProps),proFieldProps:G},n))}),I=Y.forwardRef(function(b,A){var B=b.fieldProps,m=b.children;return(0,C.jsx)(x.Z,(0,M.Z)((0,M.Z)({ref:A},B),{},{children:m}))}),ne=(0,K.G)(I,{valuePropName:"checked"}),D=ne;D.Group=f,J.Z=D},5966:function(ye,J,e){var M=e(97685),h=e(1413),z=e(45987),x=e(21770),Y=e(53025),K=e(55241),Q=e(97435),C=e(67294),H=e(27577),f=e(85893),I=["fieldProps","proFieldProps"],ne=["fieldProps","proFieldProps"],D="text",b=function(s){var n=s.fieldProps,T=s.proFieldProps,L=(0,z.Z)(s,I);return(0,f.jsx)(H.Z,(0,h.Z)({valueType:D,fieldProps:n,filedConfig:{valueType:D},proFieldProps:T},L))},A=function(s){var n=(0,x.Z)(s.open||!1,{value:s.open,onChange:s.onOpenChange}),T=(0,M.Z)(n,2),L=T[0],t=T[1];return(0,f.jsx)(Y.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(ie){var F,X=ie.getFieldValue(s.name||[]);return(0,f.jsx)(K.Z,(0,h.Z)((0,h.Z)({getPopupContainer:function(d){return d&&d.parentNode?d.parentNode:d},onOpenChange:function(d){return t(d)},content:(0,f.jsxs)("div",{style:{padding:"4px 0"},children:[(F=s.statusRender)===null||F===void 0?void 0:F.call(s,X),s.strengthText?(0,f.jsx)("div",{style:{marginTop:10},children:(0,f.jsx)("span",{children:s.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},s.popoverProps),{},{open:L,children:s.children}))}})},B=function(s){var n=s.fieldProps,T=s.proFieldProps,L=(0,z.Z)(s,ne),t=(0,C.useState)(!1),le=(0,M.Z)(t,2),ie=le[0],F=le[1];return n!=null&&n.statusRender&&L.name?(0,f.jsx)(A,{name:L.name,statusRender:n==null?void 0:n.statusRender,popoverProps:n==null?void 0:n.popoverProps,strengthText:n==null?void 0:n.strengthText,open:ie,onOpenChange:F,children:(0,f.jsx)("div",{children:(0,f.jsx)(H.Z,(0,h.Z)({valueType:"password",fieldProps:(0,h.Z)((0,h.Z)({},(0,Q.Z)(n,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(p){var d;n==null||(d=n.onBlur)===null||d===void 0||d.call(n,p),F(!1)},onClick:function(p){var d;n==null||(d=n.onClick)===null||d===void 0||d.call(n,p),F(!0)}}),proFieldProps:T,filedConfig:{valueType:D}},L))})}):(0,f.jsx)(H.Z,(0,h.Z)({valueType:"password",fieldProps:n,proFieldProps:T,filedConfig:{valueType:D}},L))},m=b;m.Password=B,m.displayName="ProFormComponent",J.Z=m},63569:function(ye,J,e){e.r(J),e.d(J,{default:function(){return Ve}});var M=e(15009),h=e.n(M),z=e(97857),x=e.n(z),Y=e(99289),K=e.n(Y),Q=e(5574),C=e.n(Q),H=e(80567),f=e(2618),I=e(1413),ne=e(45987),D=e(10915),b=e(21532),A=e(93967),B=e.n(A),m=e(67294),G=e(34994),s=e(4942),n=e(98082),T=function(a){return(0,s.Z)((0,s.Z)({},a.componentCls,{"&-container":{display:"flex",flex:"1",flexDirection:"column",height:"100%",paddingInline:32,paddingBlock:24,overflow:"auto",background:"inherit"},"&-top":{textAlign:"center"},"&-header":{display:"flex",alignItems:"center",justifyContent:"center",height:"44px",lineHeight:"44px",a:{textDecoration:"none"}},"&-title":{position:"relative",insetBlockStart:"2px",color:"@heading-color",fontWeight:"600",fontSize:"33px"},"&-logo":{width:"44px",height:"44px",marginInlineEnd:"16px",verticalAlign:"top",img:{width:"100%"}},"&-desc":{marginBlockStart:"12px",marginBlockEnd:"40px",color:a.colorTextSecondary,fontSize:a.fontSize},"&-main":{minWidth:"328px",maxWidth:"580px",margin:"0 auto","&-other":{marginBlockStart:"24px",lineHeight:"22px",textAlign:"start"}}}),"@media (min-width: @screen-md-min)",(0,s.Z)({},"".concat(a.componentCls,"-container"),{paddingInline:0,paddingBlockStart:32,paddingBlockEnd:24,backgroundRepeat:"no-repeat",backgroundPosition:"center 110px",backgroundSize:"100%"}))};function L(o){return(0,n.Xj)("LoginForm",function(a){var l=(0,I.Z)((0,I.Z)({},a),{},{componentCls:".".concat(o)});return[T(l)]})}var t=e(85893),le=["logo","message","contentStyle","title","subTitle","actions","children","containerStyle","otherStyle"];function ie(o){var a,l=o.logo,P=o.message,j=o.contentStyle,W=o.title,_=o.subTitle,re=o.actions,ge=o.children,se=o.containerStyle,q=o.otherStyle,g=(0,ne.Z)(o,le),O=(0,D.YB)(),ee=g.submitter===!1?!1:(0,I.Z)((0,I.Z)({searchConfig:{submitText:O.getMessage("loginForm.submitText","\u767B\u5F55")}},g.submitter),{},{submitButtonProps:(0,I.Z)({size:"large",style:{width:"100%"}},(a=g.submitter)===null||a===void 0?void 0:a.submitButtonProps),render:function(V,r){var te,de=r.pop();if(typeof(g==null||(te=g.submitter)===null||te===void 0?void 0:te.render)=="function"){var ae,R;return g==null||(ae=g.submitter)===null||ae===void 0||(R=ae.render)===null||R===void 0?void 0:R.call(ae,V,r)}return de}}),ce=(0,m.useContext)(b.ZP.ConfigContext),$=ce.getPrefixCls("pro-form-login"),w=L($),oe=w.wrapSSR,i=w.hashId,S=function(V){return"".concat($,"-").concat(V," ").concat(i)},N=(0,m.useMemo)(function(){return l?typeof l=="string"?(0,t.jsx)("img",{src:l}):l:null},[l]);return oe((0,t.jsxs)("div",{className:B()(S("container"),i),style:se,children:[(0,t.jsxs)("div",{className:"".concat(S("top")," ").concat(i).trim(),children:[W||N?(0,t.jsxs)("div",{className:"".concat(S("header")),children:[N?(0,t.jsx)("span",{className:S("logo"),children:N}):null,W?(0,t.jsx)("span",{className:S("title"),children:W}):null]}):null,_?(0,t.jsx)("div",{className:S("desc"),children:_}):null]}),(0,t.jsxs)("div",{className:S("main"),style:(0,I.Z)({width:328},j),children:[(0,t.jsxs)(G.A,(0,I.Z)((0,I.Z)({isKeyPressSubmit:!0},g),{},{submitter:ee,children:[P,ge]})),re?(0,t.jsx)("div",{className:S("main-other"),style:q,children:re}):null]})]}))}var F=e(63434),X=e(5966),p=e(71551),d=e(2453),Re=e(53025),Ze=e(67610),De=e(73935),Fe=e(28846),Ne=e(9783),Ae=e.n(Ne),Be=e(13769),be=e.n(Be),We=e(85418),me=e(67159),pe=e(50136),Oe=["overlayClassName"],ke=["globalIconClassName","postLocalesData","onItemClick","icon","style","reload"],Ue=function(a){var l=a.overlayClassName,P=be()(a,Oe);return(0,t.jsx)(We.Z,x()({overlayClassName:l},P))},ze=function(a){return a.reduce(function(l,P){return P.lang?x()(x()({},l),{},Ae()({},P.lang,P)):l},{})},Ke={"ar-EG":{lang:"ar-EG",label:"\u0627\u0644\u0639\u0631\u0628\u064A\u0629",icon:"\u{1F1EA}\u{1F1EC}",title:"\u0644\u063A\u0629"},"az-AZ":{lang:"az-AZ",label:"Az\u0259rbaycan dili",icon:"\u{1F1E6}\u{1F1FF}",title:"Dil"},"bg-BG":{lang:"bg-BG",label:"\u0411\u044A\u043B\u0433\u0430\u0440\u0441\u043A\u0438 \u0435\u0437\u0438\u043A",icon:"\u{1F1E7}\u{1F1EC}",title:"\u0435\u0437\u0438\u043A"},"bn-BD":{lang:"bn-BD",label:"\u09AC\u09BE\u0982\u09B2\u09BE",icon:"\u{1F1E7}\u{1F1E9}",title:"\u09AD\u09BE\u09B7\u09BE"},"ca-ES":{lang:"ca-ES",label:"Catal\xE1",icon:"\u{1F1E8}\u{1F1E6}",title:"llengua"},"cs-CZ":{lang:"cs-CZ",label:"\u010Ce\u0161tina",icon:"\u{1F1E8}\u{1F1FF}",title:"Jazyk"},"da-DK":{lang:"da-DK",label:"Dansk",icon:"\u{1F1E9}\u{1F1F0}",title:"Sprog"},"de-DE":{lang:"de-DE",label:"Deutsch",icon:"\u{1F1E9}\u{1F1EA}",title:"Sprache"},"el-GR":{lang:"el-GR",label:"\u0395\u03BB\u03BB\u03B7\u03BD\u03B9\u03BA\u03AC",icon:"\u{1F1EC}\u{1F1F7}",title:"\u0393\u03BB\u03CE\u03C3\u03C3\u03B1"},"en-GB":{lang:"en-GB",label:"English",icon:"\u{1F1EC}\u{1F1E7}",title:"Language"},"en-US":{lang:"en-US",label:"English",icon:"\u{1F1FA}\u{1F1F8}",title:"Language"},"es-ES":{lang:"es-ES",label:"Espa\xF1ol",icon:"\u{1F1EA}\u{1F1F8}",title:"Idioma"},"et-EE":{lang:"et-EE",label:"Eesti",icon:"\u{1F1EA}\u{1F1EA}",title:"Keel"},"fa-IR":{lang:"fa-IR",label:"\u0641\u0627\u0631\u0633\u06CC",icon:"\u{1F1EE}\u{1F1F7}",title:"\u0632\u0628\u0627\u0646"},"fi-FI":{lang:"fi-FI",label:"Suomi",icon:"\u{1F1EB}\u{1F1EE}",title:"Kieli"},"fr-BE":{lang:"fr-BE",label:"Fran\xE7ais",icon:"\u{1F1E7}\u{1F1EA}",title:"Langue"},"fr-FR":{lang:"fr-FR",label:"Fran\xE7ais",icon:"\u{1F1EB}\u{1F1F7}",title:"Langue"},"ga-IE":{lang:"ga-IE",label:"Gaeilge",icon:"\u{1F1EE}\u{1F1EA}",title:"Teanga"},"he-IL":{lang:"he-IL",label:"\u05E2\u05D1\u05E8\u05D9\u05EA",icon:"\u{1F1EE}\u{1F1F1}",title:"\u05E9\u05E4\u05D4"},"hi-IN":{lang:"hi-IN",label:"\u0939\u093F\u0928\u094D\u0926\u0940, \u0939\u093F\u0902\u0926\u0940",icon:"\u{1F1EE}\u{1F1F3}",title:"\u092D\u093E\u0937\u093E: \u0939\u093F\u0928\u094D\u0926\u0940"},"hr-HR":{lang:"hr-HR",label:"Hrvatski jezik",icon:"\u{1F1ED}\u{1F1F7}",title:"Jezik"},"hu-HU":{lang:"hu-HU",label:"Magyar",icon:"\u{1F1ED}\u{1F1FA}",title:"Nyelv"},"hy-AM":{lang:"hu-HU",label:"\u0540\u0561\u0575\u0565\u0580\u0565\u0576",icon:"\u{1F1E6}\u{1F1F2}",title:"\u053C\u0565\u0566\u0578\u0582"},"id-ID":{lang:"id-ID",label:"Bahasa Indonesia",icon:"\u{1F1EE}\u{1F1E9}",title:"Bahasa"},"it-IT":{lang:"it-IT",label:"Italiano",icon:"\u{1F1EE}\u{1F1F9}",title:"Linguaggio"},"is-IS":{lang:"is-IS",label:"\xCDslenska",icon:"\u{1F1EE}\u{1F1F8}",title:"Tungum\xE1l"},"ja-JP":{lang:"ja-JP",label:"\u65E5\u672C\u8A9E",icon:"\u{1F1EF}\u{1F1F5}",title:"\u8A00\u8A9E"},"ku-IQ":{lang:"ku-IQ",label:"\u06A9\u0648\u0631\u062F\u06CC",icon:"\u{1F1EE}\u{1F1F6}",title:"Ziman"},"kn-IN":{lang:"kn-IN",label:"\u0C95\u0CA8\u0CCD\u0CA8\u0CA1",icon:"\u{1F1EE}\u{1F1F3}",title:"\u0CAD\u0CBE\u0CB7\u0CC6"},"ko-KR":{lang:"ko-KR",label:"\uD55C\uAD6D\uC5B4",icon:"\u{1F1F0}\u{1F1F7}",title:"\uC5B8\uC5B4"},"lv-LV":{lang:"lv-LV",label:"Latvie\u0161u valoda",icon:"\u{1F1F1}\u{1F1EE}",title:"Kalba"},"mk-MK":{lang:"mk-MK",label:"\u043C\u0430\u043A\u0435\u0434\u043E\u043D\u0441\u043A\u0438 \u0458\u0430\u0437\u0438\u043A",icon:"\u{1F1F2}\u{1F1F0}",title:"\u0408\u0430\u0437\u0438\u043A"},"mn-MN":{lang:"mn-MN",label:"\u041C\u043E\u043D\u0433\u043E\u043B \u0445\u044D\u043B",icon:"\u{1F1F2}\u{1F1F3}",title:"\u0425\u044D\u043B"},"ms-MY":{lang:"ms-MY",label:"\u0628\u0647\u0627\u0633 \u0645\u0644\u0627\u064A\u0648\u200E",icon:"\u{1F1F2}\u{1F1FE}",title:"Bahasa"},"nb-NO":{lang:"nb-NO",label:"Norsk",icon:"\u{1F1F3}\u{1F1F4}",title:"Spr\xE5k"},"ne-NP":{lang:"ne-NP",label:"\u0928\u0947\u092A\u093E\u0932\u0940",icon:"\u{1F1F3}\u{1F1F5}",title:"\u092D\u093E\u0937\u093E"},"nl-BE":{lang:"nl-BE",label:"Vlaams",icon:"\u{1F1E7}\u{1F1EA}",title:"Taal"},"nl-NL":{lang:"nl-NL",label:"Nederlands",icon:"\u{1F1F3}\u{1F1F1}",title:"Taal"},"pl-PL":{lang:"pl-PL",label:"Polski",icon:"\u{1F1F5}\u{1F1F1}",title:"J\u0119zyk"},"pt-BR":{lang:"pt-BR",label:"Portugu\xEAs",icon:"\u{1F1E7}\u{1F1F7}",title:"Idiomas"},"pt-PT":{lang:"pt-PT",label:"Portugu\xEAs",icon:"\u{1F1F5}\u{1F1F9}",title:"Idiomas"},"ro-RO":{lang:"ro-RO",label:"Rom\xE2n\u0103",icon:"\u{1F1F7}\u{1F1F4}",title:"Limba"},"ru-RU":{lang:"ru-RU",label:"\u0420\u0443\u0441\u0441\u043A\u0438\u0439",icon:"\u{1F1F7}\u{1F1FA}",title:"\u044F\u0437\u044B\u043A"},"sk-SK":{lang:"sk-SK",label:"Sloven\u010Dina",icon:"\u{1F1F8}\u{1F1F0}",title:"Jazyk"},"sr-RS":{lang:"sr-RS",label:"\u0441\u0440\u043F\u0441\u043A\u0438 \u0458\u0435\u0437\u0438\u043A",icon:"\u{1F1F8}\u{1F1F7}",title:"\u0408\u0435\u0437\u0438\u043A"},"sl-SI":{lang:"sl-SI",label:"Sloven\u0161\u010Dina",icon:"\u{1F1F8}\u{1F1F1}",title:"Jezik"},"sv-SE":{lang:"sv-SE",label:"Svenska",icon:"\u{1F1F8}\u{1F1EA}",title:"Spr\xE5k"},"ta-IN":{lang:"ta-IN",label:"\u0BA4\u0BAE\u0BBF\u0BB4\u0BCD",icon:"\u{1F1EE}\u{1F1F3}",title:"\u0BAE\u0BCA\u0BB4\u0BBF"},"th-TH":{lang:"th-TH",label:"\u0E44\u0E17\u0E22",icon:"\u{1F1F9}\u{1F1ED}",title:"\u0E20\u0E32\u0E29\u0E32"},"tr-TR":{lang:"tr-TR",label:"T\xFCrk\xE7e",icon:"\u{1F1F9}\u{1F1F7}",title:"Dil"},"uk-UA":{lang:"uk-UA",label:"\u0423\u043A\u0440\u0430\u0457\u043D\u0441\u044C\u043A\u0430",icon:"\u{1F1FA}\u{1F1F0}",title:"\u041C\u043E\u0432\u0430"},"vi-VN":{lang:"vi-VN",label:"Ti\u1EBFng Vi\u1EC7t",icon:"\u{1F1FB}\u{1F1F3}",title:"Ng\xF4n ng\u1EEF"},"zh-CN":{lang:"zh-CN",label:"\u7B80\u4F53\u4E2D\u6587",icon:"\u{1F1E8}\u{1F1F3}",title:"\u8BED\u8A00"},"zh-TW":{lang:"zh-TW",label:"\u7E41\u9AD4\u4E2D\u6587",icon:"\u{1F1ED}\u{1F1F0}",title:"\u8A9E\u8A00"}},Se=function(a){var l,P=a.globalIconClassName,j=a.postLocalesData,W=a.onItemClick,_=a.icon,re=a.style,ge=a.reload,se=be()(a,ke),q=(0,m.useState)(function(){return(0,p.getLocale)()}),g=C()(q,2),O=g[0],ee=g[1],ce=function(te){var de=te.key;(0,p.setLocale)(de,ge),ee((0,p.getLocale)())},$=(0,p.getAllLocales)().map(function(r){return Ke[r]||{lang:r,label:r,icon:"\u{1F310}",title:r}}),w=(j==null?void 0:j($))||$,oe=W?function(r){return W(r)}:ce,i={minWidth:"160px"},S={marginRight:"8px"},N={selectedKeys:[O],onClick:oe,items:w.map(function(r){return{key:r.lang||r.key,style:i,label:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{role:"img","aria-label":(r==null?void 0:r.label)||"en-US",style:S,children:(r==null?void 0:r.icon)||"\u{1F310}"}),(r==null?void 0:r.label)||"en-US"]})}})},k;me.Z.startsWith("5.")||me.Z.startsWith("4.24.")?k={menu:N}:me.Z.startsWith("3.")?k={overlay:(0,t.jsx)(pe.Z,{children:N.items.map(function(r){return(0,t.jsx)(pe.Z.Item,{onClick:r.onClick,children:r.label},r.key)})})}:k={overlay:(0,t.jsx)(pe.Z,x()({},N))};var V=x()({cursor:"pointer",padding:"12px",display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:18,verticalAlign:"middle"},re);return(0,t.jsx)(Ue,x()(x()(x()({},k),{},{placement:"bottomRight"},se),{},{children:(0,t.jsxs)("span",{className:P,style:V,children:[(0,t.jsx)("img",{src:"/icons/login/language.svg",alt:"Language Icon",style:{width:"14px",height:"14px",verticalAlign:"middle"}}),(0,t.jsx)("span",{className:"language-title",style:{marginLeft:"8px",fontSize:"14px",verticalAlign:"middle",marginRight:"8px",lineHeight:"14px"},children:(l=ze(w)[O])===null||l===void 0?void 0:l.label}),(0,t.jsx)("img",{src:"/icons/login/dropDown.svg",alt:"Language Icon",style:{width:"12px",height:"12px",verticalAlign:"middle"}})]})}))},He=e(61615),ve=(0,Fe.kc)(function(o){var a=o.token;return{action:{marginLeft:"8px",color:"rgba(0, 0, 0, 0.2)",fontSize:"24px",verticalAlign:"middle",cursor:"pointer",transition:"color 0.3s","&:hover":{color:a.colorPrimaryActive}},lang:{position:"relative",right:16,borderRadius:a.borderRadius,":hover":{backgroundColor:a.colorBgTextHover},marginTop:20},container:{display:"flex",flexDirection:"column",height:"100vh",overflow:"auto",backgroundImage:"url('/icons/login/background.png')",backgroundSize:"100% 100%"},langIcon:{marginLeft:"8px",display:"flex",alignItems:"center",paddingLeft:16,paddingTop:20},langWrapper:{display:"flex",alignItems:"center",justifyContent:"space-between",position:"fixed",width:"100%",right:0,height:42,lineHeight:"42px"}}}),Qe=function(){var a=ve(),l=a.styles;return _jsxs(_Fragment,{children:[_jsx(AlipayCircleOutlined,{className:l.action},"AlipayCircleOutlined"),_jsx(TaobaoCircleOutlined,{className:l.action},"TaobaoCircleOutlined"),_jsx(WeiboCircleOutlined,{className:l.action},"WeiboCircleOutlined")]})},Ge=function(){var a=ve(),l=a.styles,P=(0,p.useIntl)(),j=P.locale;return(0,t.jsxs)("div",{className:l.langWrapper,children:[(0,t.jsx)("div",{className:l.langIcon,children:(0,t.jsx)("img",{style:{paddingLeft:"5px"},src:j==="zh-CN"||j==="zh-TW"?"/icons/login/logo_black_CN.svg":"/icons/login/logo_black_EN.svg"})}),(0,t.jsx)("div",{className:l.lang,children:Se&&(0,t.jsx)(Se,{})})]})},Xe=function(a){var l=a.content;return _jsx(Alert,{style:{marginBottom:24},message:l,type:"error",showIcon:!0})};function $e(){var o=localStorage.getItem("agreed_terms");if(!o)return!1;try{var a=JSON.parse(o),l=a.value,P=a.expire;if(l===1&&P>Date.now())return!0}catch(j){}return!1}var we=function(){var a=(0,m.useState)({}),l=C()(a,2),P=l[0],j=l[1],W=(0,m.useState)("account"),_=C()(W,2),re=_[0],ge=_[1],se=(0,m.useState)(!0),q=C()(se,2),g=q[0],O=q[1],ee=(0,p.useModel)("@@initialState"),ce=ee.initialState,$=ee.setInitialState,w=ve(),oe=w.styles,i=(0,p.useIntl)(),S=i.locale,N=S==="zh-CN"||S==="zh-TW"?"/icons/login/zh_combination.png":"/icons/login/us_combination.png",k=function(){var Z=K()(h()().mark(function v(c){return h()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:c&&(c.name=c.name,(0,De.flushSync)(function(){$(function(y){return x()(x()({},y),{},{currentUser:c})})}));case 1:case"end":return E.stop()}},v)}));return function(c){return Z.apply(this,arguments)}}(),V=function(){var Z=K()(h()().mark(function v(c){var U,E,y,fe,he,xe,Ee,ue,Pe,je,Te,Le;return h()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(u.prev=0,console.log("11111"),c.terms){u.next=5;break}return d.ZP.warning(i.formatMessage({id:"pages.login.terms.check",defaultMessage:"\u8BF7\u5148\u9605\u8BFB\u5E76\u540C\u610F\u300A\u4EA7\u54C1\u4F7F\u7528\u534F\u8BAE\u300B"})),u.abrupt("return");case 5:if(U=(0,He.m)(c.pass),g){u.next=12;break}return u.next=9,(0,f.a$)({name:c.account,password:U});case 9:return E=u.sent,E.code===0?(d.ZP.success(i.formatMessage({id:"pages.register.success",defaultMessage:"\u6CE8\u518C\u6210\u529F\uFF0C\u8BF7\u767B\u5F55"})),O(!0)):d.ZP.error(i.formatMessage({id:"pages.serverCode."+E.code,defaultMessage:i.formatMessage({id:"pages.register.fail",defaultMessage:"\u6CE8\u518C\u5931\u8D25"})})),u.abrupt("return");case 12:return u.next=14,(0,f.x4)(x()(x()({},c),{},{pass:U}));case 14:if(y=u.sent,!(y.code===0&&y.data)){u.next=30;break}return Ee=i.formatMessage({id:"pages.login.success",defaultMessage:"\u767B\u5F55\u6210\u529F\uFF01"}),d.ZP.success(Ee),ue=y==null||(fe=y.data)===null||fe===void 0?void 0:fe.token,Pe={name:y==null||(he=y.data)===null||he===void 0?void 0:he.userName,username:y==null||(xe=y.data)===null||xe===void 0?void 0:xe.userName,accessToken:ue,outTime:!1},(0,f.M8)(ue),je=(0,p.getDvaApp)(),je._store.dispatch({type:"user/changeUserInfo",payload:Pe}),u.next=25,k(Pe,ue);case 25:return Te=new URL(window.location.href).searchParams,p.history.push(Te.get("redirect")||"/"),u.abrupt("return");case 30:R.setFields([{name:"pass",errors:[i.formatMessage({id:"pages.login.fail",defaultMessage:"\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF"})]}]),d.ZP.error(i.formatMessage({id:"pages.login.failure",defaultMessage:"\u767B\u5F55\u5931\u8D25\uFF01"}));case 32:j(y),u.next=40;break;case 35:u.prev=35,u.t0=u.catch(0),Le=i.formatMessage({id:"pages.login.retry",defaultMessage:"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01"}),console.log(u.t0),d.ZP.error(Le);case 40:case"end":return u.stop()}},v,null,[[0,35]])}));return function(c){return Z.apply(this,arguments)}}(),r=P.status,te=P.type,de=Re.Z.useForm(),ae=C()(de,1),R=ae[0],Je=m.useState($e()),Me=C()(Je,2),Ce=Me[0],Ie=Me[1],Ye=function(v){if(v.target.checked){var c=Date.now()+1728e6;localStorage.setItem("agreed_terms",JSON.stringify({value:1,expire:c})),Ie(!0)}else localStorage.removeItem("agreed_terms"),Ie(!1)};return(0,m.useEffect)(function(){var Z="admin";(0,f.kV)(Z).then(function(v){v&&v.code!==0&&d.ZP.warning(v.msg||i.formatMessage({id:"pages.login.needRegister",defaultMessage:"\u8BF7\u5148\u6CE8\u518C\u8D26\u53F7"})),v.data?O(!0):O(!1)}).catch(function(){d.ZP.warning(i.formatMessage({id:"pages.system.check.fail",defaultMessage:"\u670D\u52A1\u68C0\u6D4B\u5931\u8D25"}))})},[]),(0,m.useEffect)(function(){g||R.setFieldsValue({account:"admin"}),R.setFieldsValue({terms:Ce})},[g,Ce,R]),(0,t.jsx)("div",{className:oe.container,children:(0,t.jsxs)(b.ZP,{theme:{components:{Button:{colorPrimary:"rgb(11, 211, 87)",algorithm:!0},Input:{colorPrimary:"rgb(11, 211, 87)",algorithm:!0},Checkbox:{colorPrimary:"rgb(11, 211, 87)",algorithm:!0},Dropdown:{colorPrimary:"rgb(11, 211, 87)",algorithm:!0}}},children:[(0,t.jsx)(p.Helmet,{children:(0,t.jsxs)("title",{children:[i.formatMessage({id:"pages.login.title",defaultMessage:"\u767B\u5F55\u9875"}),"- ",Ze.Z.title]})}),(0,t.jsx)(Ge,{}),(0,t.jsxs)("div",{style:{display:"flex",justifyContent:"space-evenly",alignItems:"center",flexWrap:"nowrap",padding:"32px 0",width:"100%",height:"100vh"},children:[(0,t.jsx)("div",{style:{flex:"0 0 auto",maxWidth:"694px",width:"100%",height:"100%",maxHeight:"601px",textAlign:"center",backgroundImage:"url("+N+")",backgroundSize:"contain",backgroundRepeat:"no-repeat",backgroundPosition:"center"}}),(0,t.jsx)("div",{style:{flex:"0 0 auto",maxWidth:"476px",width:"100%",maxHeight:"480px",borderRadius:"16px",boxShadow:"0px 3px 10px 0px rgba(178, 177, 177, 0.25)",background:"rgb(255, 255, 255)",overflow:"hidden"},children:(0,t.jsxs)(ie,{form:R,className:"login-Form",contentStyle:{minWidth:"372px",maxWidth:"75vw",height:"100vh",overflowY:"auto"},style:{color:"rgb(0, 0, 0)"},title:(0,t.jsx)("span",{style:{color:"rgb(0, 0, 0)",fontSize:"24px",fontWeight:400,lineHeight:"35px",letterSpacing:"0px",textAlign:"left",display:"block",margin:"20px"},children:i.formatMessage({id:"pages.login.welcome",defaultMessage:"\u6B22\u8FCE\u6765\u5230\u706B\u661F\u6167\u77E5"})}),contentRender:function(v,c){return(0,t.jsxs)(t.Fragment,{children:[v,(0,t.jsx)("div",{className:"submit-Button-Dom",style:{textAlign:"center",marginTop:"16px"},children:(0,t.jsx)("button",{type:"button",onClick:function(){R.submit()},className:"ant-btn ant-btn-primary ant-btn-lg",style:{width:"100%",height:"40px",borderRadius:"4px",background:"rgb(11, 211, 87)",border:"none",color:"white",fontSize:"16x",fontWeight:"400",cursor:"pointer",lineHeight:"1.5"},children:g?i.formatMessage({id:"pages.login.login",defaultMessage:"\u767B \u5F55"}):i.formatMessage({id:"pages.login.register",defaultMessage:"\u6CE8 \u518C"})})}),(0,t.jsx)("div",{className:"submit-Checkbox-Dom",style:{textAlign:"left",marginTop:"25px"},children:(0,t.jsxs)(F.Z,{name:"terms",onChange:Ye,fieldProps:{style:{fontSize:12,fontWeight:400,lineHeight:"17px",textAlign:"left"}},children:[i.formatMessage({id:"pages.login.agreementPrefix",defaultMessage:"\u60A8\u5DF2\u9605\u8BFB\u5E76\u540C\u610F\u706B\u661F\u6167\u77E5"})," ",(0,t.jsx)("a",{href:"/terms.html",style:{color:"rgb(11, 211, 87)"},children:i.formatMessage({id:"pages.login.terms.title",defaultMessage:"\u300A\u4EA7\u54C1\u4F7F\u7528\u534F\u8BAE\u300B"})})]})})]})},onFinish:function(){var Z=K()(h()().mark(function v(c){return h()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return g||(c.account="admin"),E.next=3,V(c);case 3:case"end":return E.stop()}},v)}));return function(v){return Z.apply(this,arguments)}}(),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{style:{color:"rgb(0, 0, 0)",fontSize:"16px",fontWeight:400,lineHeight:"23px",letterSpacing:"0px",textAlign:"left",display:"flex",paddingBottom:"32px"},children:g?i.formatMessage({id:"pages.login.submit",defaultMessage:"\u7528\u6237\u767B\u5F55"}):i.formatMessage({id:"pages.login.registerTitle",defaultMessage:"\u7528\u6237\u6CE8\u518C"})}),(0,t.jsx)(X.Z,{name:"account",placeholder:i.formatMessage({id:"pages.login.usernamePlaceholder",defaultMessage:"\u8BF7\u8F93\u5165\u7528\u6237\u540D"}),disabled:!g,rules:[{required:!0,message:(0,t.jsx)(p.FormattedMessage,{id:"pages.login.usernamePlaceholder",defaultMessage:"\u8BF7\u8F93\u5165\u7528\u6237\u540D"})}],fieldProps:{className:"login-disabled-input",style:{height:"40px",borderRadius:"4px",background:"rgb(255, 255, 255)",fontSize:"13px",fontWeight:"400",lineHeight:"19px",letterSpacing:"0px",textAlign:"left"}}}),(0,t.jsx)(X.Z.Password,{name:"pass",placeholder:i.formatMessage({id:"pages.login.passwordPlaceholder",defaultMessage:"\u8BF7\u8F93\u5165\u5BC6\u7801"}),rules:[{required:!0,message:(0,t.jsx)(p.FormattedMessage,{id:"pages.login.passwordPlaceholder",defaultMessage:"\u8BF7\u8F93\u5165\u5BC6\u7801"})},{max:18,message:i.formatMessage({id:"pages.account.maxlength",defaultMessage:"\u5BC6\u7801\u6700\u591A18\u4F4D"})},{validator:function(v,c){if(!c||g)return Promise.resolve();var U=/^(?=.*[a-z])(?=.*[A-Z])(?=.*[\d\W]).{1,18}$/;return U.test(c)?Promise.resolve():Promise.reject(i.formatMessage({id:"pages.register.password.rule",defaultMessage:"\u5BC6\u7801\u9700\u5305\u542B\u5927\u5199\u5B57\u6BCD\u3001\u5C0F\u5199\u5B57\u6BCD\u548C\u6570\u5B57/\u7B26\u53F7\uFF0C\u6700\u591A18\u4F4D"}))}}],fieldProps:{style:{height:"40px",borderRadius:"4px",background:"rgb(255, 255, 255)",fontSize:"13px",fontWeight:"400",lineHeight:"19px",letterSpacing:"0px",textAlign:"left"}}})]}),(0,t.jsx)("div",{style:{marginBottom:60}})]})})]}),(0,t.jsx)(H.$_,{})]})})},Ve=we}}]);
