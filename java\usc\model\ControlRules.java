package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 布控规则
 * <AUTHOR>
 * @date  2025-04-09 15:32
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "CONTROL_RULES", indexes = {@Index(name = "CONTROL_RULES_ID_IDX", columnList = "ID"), @Index(name = "CONTROL_RULES_CRT_IDX", columnList = "CREATE_TIME"), @Index(name = "CONTROL_RULES_UPT_IDX", columnList = "UPDATE_TIME")})
public class ControlRules extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件编码
     */
    @Column(name = "event_code", length = 50)
    private String eventCode;

    /**
     * 事件类型
     */
    @Column(name = "event_type", length = 100)
    private String eventType;



    /**
     * 提示词
     */
    @Column(name = "prompt", length = 256)
    private String prompt;


}

