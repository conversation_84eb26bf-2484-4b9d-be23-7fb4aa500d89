package com.zkteco.mars.usc.vo;


import com.zkteco.framework.base.annotation.Column;
import com.zkteco.framework.base.annotation.From;
import com.zkteco.framework.base.annotation.OrderBy;
import com.zkteco.framework.base.bean.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点位信息
 *
 * <AUTHOR>
 * @date 2025-01-09 13:48
 * @since 1.0.0
 */
@From(after = "DMC_POINT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
public class DmcPointItem extends BaseItem implements Serializable {


    /**
     * id
     */
    @Column(name = "t.ID")
    private String id;

    /**
     * 点位名称
     */
    @Column(name = "t.name")
    private String name;

    /**
     * 协议类型【字典值】:1.RTSP(默认，poc界面不需要支持选择);2.ONVIF;3.GB28181
     */
    @Column(name = "t.protocol_type")
    private String protocolType;

    /**
     * 码流类型
     */
    @Column(name = "t.main_stream")
    private String mainStream;

    /**
     * 码流类型
     */
    @Column(name = "t.second_Stream")
    private String secondStream;



    /**
     * 抓拍间隔（秒）0:不抓拍;大于等于1:抓拍的时间间隔关联
     */
    @Column(name = "t.capture_interval")
    private String captureInterval;

    /**
     * 第三方业务系统类型【字典值】:1:zk流媒体 2:万傲瑞达
     */
    @Column(name = "t.system_type")
    private String systemType;

    /**
     * 设备id
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * 设备ip
     */
    @Column(name = "t.device_ip")
    private String deviceIp;

    /**
     * 设备名称
     */
    @Column(name = "t.device_name")
    private String deviceName;

    /**
     * 设备端口
     */
    @Column(name = "t.device_port")
    private String devicePort;

    /**
     * 设备用户名
     */
    @Column(name = "t.device_username")
    private String deviceUsername;

    /**
     * 设备密码
     */
    @Column(name = "t.device_userpass")
    private String deviceUserpass;

    /**
     * 设备厂商
     */
    @Column(name = "t.manufacturer")
    private String manufacturer;

    /**
     * 检测类型
     */
    @Column(name = "t.detect_type")
    private String detectType;

    /**
     * 第三方点位业务id
     */
    @Column(name = "t.business_id")
    private String businessId;





}
