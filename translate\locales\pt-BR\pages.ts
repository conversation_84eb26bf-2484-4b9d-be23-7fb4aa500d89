export default {
    'pages.login.success': "Login bem-sucedido!",
    'pages.login.failure': "Falha no login!",
    'pages.login.retry': "O login falhou, por favor, tente novamente!",
    'pages.login.title': "Página de login",
    'pages.login.welcome': "Bem-vindo à MarsLume",
    'pages.login.agreementPrefix': "Você leu e concorda com os Termos de Uso da MarsLume",
    'pages.login.terms': "Termos de Uso",
    'pages.login.and': " e",
    'pages.login.privacy': "Política de Privacidade",
    'pages.login.submit': "Login do usuário",
    'pages.login.usernamePlaceholder': "Digite seu nome de usuário",
    'pages.login.passwordPlaceholder': "Digite sua senha",
    'pages.search.sortByTime': "Por hora mais recente",
    'pages.search.sortBySimilarity': "Por similaridade",
    'pages.search.history': "Histórico",
    'pages.search.searchHistory': "Histórico de pesquisa",
    'pages.search.delete': "Excluir",
    'pages.search.hotSearch': "Pesquisas populares",
    'pages.search.example1': "O menino ao lado do veículo na porta",
    'pages.search.example2': "A pessoa de roupa vermelha fazendo uma ligação no elevador",
    'pages.search.example3': "A pessoa de chapéu azul na vaga de estacionamento",
    'pages.search.assistHint': "Posso ajudar você a pesquisar informações, como: o menino de roupa preta ao lado da barraca de comida para viagem no primeiro andar do parque",
    'pages.search.searchButton': "Pesquisar",
    'pages.search.filter': "Condições de pesquisa",
    'pages.search.loading': "Carregando...",
    'pages.search.eventDetail': "Detalhes do evento",
    'pages.search.videoPlayback': "Reprodução de vídeo",
    'pages.search.panorama': "Panorama",
    'pages.search.description': "Descrição",
    'pages.search.time': "Horário",
    'pages.search.location': "Local",
    'pages.search.similarity': "Similaridade",
    'pages.pointManage.voiceInput': "Entrada de voz",
    'pages.pointManage.speechRecognition': "Reconhecimento de voz",
    'pages.pointManage.stopVoiceInput': "Parar entrada de voz",
    'pages.pointManage.operationSuccess': "Operação bem-sucedida",
    'pages.pointManage.preview': "Visualizar",
    'pages.pointManage.edit': "Editar",
    'pages.pointManage.alreadyMonitored': "Controle implantado",
    'pages.pointManage.monitor': "Controle implantado",
    'pages.pointManage.delete': "Excluir",
    'pages.pointManage.deleteRecord': "Excluir registro",
    'pages.pointManage.confirmDeleteRecord': "Tem certeza de que deseja excluir o registro?",
    'pages.pointManage.confirm': "Confirmar",
    'pages.pointManage.cancel': "Cancelar",
    'pages.pointManage.deleting': "Excluindo",
    'pages.pointManage.deleteSuccess': "Exclusão bem-sucedida, atualização automática",
    'pages.pointManage.deleteFailure': "Falha na exclusão, tente novamente",
    'pages.pointManage.person': "Pessoa",
    'pages.pointManage.motorVehicle': "Veículo motorizado",
    'pages.pointManage.nonMotorVehicle': "Veículo não motorizado",
    'pages.pointManage.pointName': "Nome do ponto",
    'pages.pointManage.protocolType': "Tipo de protocolo",
    'pages.pointManage.captureType': "Tipo de snapshot",
    'pages.pointManage.captureInterval': "Intervalo do snapshot",
    'pages.pointManage.deviceName': "Nome do dispositivo",
    'pages.pointManage.deviceIP': "IP do dispositivo",
    'pages.pointManage.devicePort': "Porta do dispositivo",
    'pages.pointManage.operation': "Operação",
    'pages.pointManage.pointManagement': "Gerenciamento de pontos",
    'pages.pointManage.add': "Adicionar",
    'pages.pointManage.selected': "Selecionado",
    'pages.pointManage.batchDelete': "Exclusão em lote",
    'pages.pointManage.item': "Item",
    'pages.pointManage.inputCaptureInterval': "Insira o intervalo do snapshot",
    'pages.pointManage.inputPointName': "Insira o nome do ponto",
    'pages.pointManage.selectCaptureType': "Selecione o tipo de captura",
    'pages.pointManage.addDevice': "Adicionar dispositivo",
    'pages.pointManage.protocol': "Protocolo",
    'pages.pointManage.deviceCode': "Código do dispositivo",
    'pages.pointManage.inputDeviceName': "Insira o nome do dispositivo",
    'pages.pointManage.inputDeviceCode': "Insira o código do dispositivo",
    'pages.pointManage.port': "Porta",
    'pages.pointManage.username': "Nome de usuário",
    'pages.pointManage.password': "Senha",
    'pages.pointManage.mainStream': "Fluxo principal",
    'pages.pointManage.example': "Exemplo",
    'pages.pointManage.subStream': "Subfluxo",
    'pages.pointManage.addPoint': "Adicionar ponto",
    'pages.pointManage.validIP': "Insira um endereço IP válido",
    'pages.pointManage.noData': "Sem dados",
    'pages.pointManage.selectPoint': "Selecione um ponto",
    'pages.pointManage.addSuccess': "Adicionado com sucesso",
    'pages.pointManage.prevStep': "Etapa anterior",
    'pages.pointManage.nextStep': "Próxima etapa",
    'pages.pointManage.monitorSuccess': "Implantação de controle bem-sucedida",
    'pages.pointManage.monitorFailure': "Implantação de controle falhou",
    'pages.pointManage.cancelSuccess': "Cancelamento bem-sucedido",
    'pages.pointManage.operationFailure': "Falha na operação",
    'pages.pointManage.monitor': "Implantação de controle",
    'pages.pointManage.monitorEvent': "Evento de implantação de controle",
    'pages.pointManage.startMonitor': "Iniciar implantação de controle",
    'pages.pointManage.monitorRecord': "Registro de implantação de controle",
    'pages.pointManage.noEventRecord': "Nenhum registro de evento",
    'pages.pointManage.charLimitExceeded': "O comprimento do caractere excede o limite, máximo:",
    'pages.pointManage.eventType': "Tipo de Evento",
    'pages.pointManage.prompt': "Palavra de Prompt",
    'pages.pointManage.createTime': "Hora de Criação",
    'pages.pointManage.editSuccess': "Edição Bem-sucedida",
    'pages.pointManage.monitorRule': "Regra de Monitoramento",
    'pages.pointManage.selectEventType': "Por favor, selecione o tipo de evento",
    'pages.pointManage.inputPrompt': "Por favor, insira a palavra de prompt",
    'pages.pointManage.eventRecord': "Registro de Evento",
    'pages.pointManage.paginationInfo': "Itens {range0} - {range1} de um total de {total}",
    'pages.pointManage.detail': "Detalhes",
    'pages.pointManage.eventImage': "Imagem do Evento",
    'pages.pointManage.pointInfo': "Informações do Ponto",
    'pages.pointManage.eventTime': "Hora d",
    'pages.config.name': "Nome",
    'pages.config.creationDate': "Data de Criação",
    'pages.config.expirationTime': "Data de Expiração",
    'pages.config.editApiKey': "Editar Chave API",
    'pages.config.editSuccess': "Edição Bem-sucedida, Atualização Automática",
    'pages.config.createSuccess': "Criação Bem-sucedida, Atualização Automática",
    'pages.config.editFailure': "Falha na Edição, Por Favor, Tente Novamente",
    'pages.config.createFailure': "Falha na Criação, Por Favor, Tente Novamente",
    'pages.config.createApiKey': "Criar Chave API",
    'pages.config.authManagement': "Gerenciamento de Autorização",
    'pages.config.eventCode': "Código do Evento",
    'pages.config.paramConfigFailure': "Falha ao Obter Configuração de Parâmetro",
    'pages.config.saveFailure': "Falha ao Salvar",
    'pages.config.saveSuccess': "Salvo com Sucesso",
    'pages.config.save': "Salvar",
    'pages.config.reset': "Redefinir",
    'pages.config.streamConfig': "Configuração de Streaming",
    'pages.config.streamServiceUrl': "Endereço do Serviço de Streaming",
    'pages.config.secretKey': "Chave Secreta",
    'pages.config.configuration': "Configuração",
    'pages.config.workspaceId': "ID do Espaço de Trabalho",
    'pages.config.multiSearchStrategy': "Estratégia de Pesquisa Multidimensional",
    'pages.config.dataCleanStrategy': "Estratégia de Limpeza de Dados",
    'pages.config.objectDetection': "Detecção de Objetos",
    'pages.config.enableObjectDetection': "Ativar Detecção de Objetos?",
    'pages.config.allowObjectWhitelist': "Permitir Lista Branca de Detecção de Objetos",
    'pages.config.sedan': "Sedan",
    'pages.config.bus': "Ônibus",
    'pages.config.truck': "Caminhão",
    'pages.config.bicycle': "Bicicleta",
    'pages.config.motorcycle': "Motocicleta",
    'pages.config.enableImageDupCheck': "Ativar Detecção de Imagem Duplicada?",
    'pages.config.intervalSeconds': "Tempo de Intervalo (Segundos)",
    'pages.config.minScoreLimitError': "Valor Mínimo de Pontuação Não Pode Exceder 1",
    'pages.config.initialSearchStrategy': "Estratégia de Pesquisa de Triagem Inicial",
    'pages.config.enhancedSearchStrategy': "Estratégia de Pesquisa Aprimorada Multidimensional",
    'pages.config.multiInitialSearch': "Triagem Inicial Multidimensional",
    'pages.config.minScore': "Valor Mínimo de Pontuação",
    'pages.config.maxResultSet': "Conjunto Máximo de Resultados",
    'pages.config.topValue': "Valor Superior",
    'pages.config.reRanking': "Reclassificação Multidimensional",
    'pages.config.batchSize': "Tamanho do Lote",
    'pages.config.fineSearch': "Triagem Fina Multidimensional",
    'pages.config.fineSearchStrategy': "Estratégia de Pesquisa de Triagem Fina",
    'pages.config.enableReId': "Ativar ReId?",
    'pages.config.objectMinScore': "Pontuação Mínima de Detecção de Objeto",
    'pages.config.vectorMinScore': "Pontuação Mínima de Similaridade Vetorial",
    'pages.config.maxResultsPerSearch': "Máximo de Resultados por Pesquisa",
    'pages.common.logout': "Sair",
    'pages.agent.apiCallFailed': "A interface de chamada falhou, por favor tente de novo mais tarde",
    'pages.agent.hello': "Olá, eu sou",
    'pages.agent.agent': "Agente inteligente",
    'pages.agent.attachment': "anexo",
    'pages.agent.dropFilesHere': "Coloque o arquivo aqui",
    'pages.agent.uploadFile': "Upload files",
    'pages.agent.clickOrDragToUpload': "Clique ou arraste o ficheiro para esta área para enviar",
    'pages.agent.shiftEnterNewline': "Shift+Enter Line Break",
    'pages.agent.basicConfig': "configuração básica",
    'pages.agent.llmModel': "Modelo LLM usado",
    'pages.agent.doubaoModel': "Modelo de cano de feijão",
    'pages.agent.selectAnOption': "Por favor escolha uma opção",
    'pages.agent.memoryMessageCount': "Contação de mensagens de memória",
    'pages.agent.skillConfig': "Configuração das habilidades",
    'pages.agent.toolSet': "Ferramentas",
    'pages.agent.toolSetDescription': "O conjunto de ferramentas permite aos agentes chamar ferramentas externas e expandir os limites de suas capacidades.",
    'pages.agent.knowledgeBase': "base de conhecimento",
    'pages.agent.knowledgeBaseDescription': "Quando os usuários enviam mensagens, o agente inteligente pode referir o conteúdo da base de conhecimento para responder às suas perguntas.",
    'pages.agent.workflow': "Fluxo de trabalho",
    'pages.agent.workflowDescription': "Costumava lidar com fluxos de tarefas com lógica complexa e passos múltiplos.",
    'pages.agent.describePersona': "Por favor descreva o design de personagem e a funcionalidade",
    'pages.agent.publishSuccess': "Publicado com sucesso",
    'pages.agent.publishFailed': "A publicação falhou",
    'pages.agent.publishNotAllowed': "Desculpe, este agente inteligente não pode ser publicado",
    'pages.agent.config': "Configuração inteligente do agente",
    'pages.agent.publish': "libertar",
    'pages.agent.modelCapabilityConfig': "Configuração da capacidade do modelo",
    'pages.agent.promptDev': "Promover o desenvolvimento de palavras",
    'pages.agent.debug': "Depuração inteligente de agentes",
    'pages.agent.create': "Criar um agente inteligente",
    'pages.agent.submitFailed': "Submission failed, please check the form data",
    'pages.agent.name': "Nome de agente inteligente",
    'pages.agent.nameLimit': "Até 64 caracteres podem ser introduzidos",
    'pages.agent.description': "Introdução às Funções de Agentes Inteligentes",
    'pages.agent.descriptionTip': "Introduz as funções do agente inteligente, que será apresentado aos seus usuários",
    'pages.agent.icon': "ícone",
    'pages.agent.imageOnly': "Só os ficheiros de imagem podem ser enviados",
    'pages.agent.imageSizeLimit': "O tamanho da imagem não pode exceder 2MB",
    'pages.agent.imageFormatLimit': "Suporta o formato jpg/png, com um tamanho não superior a 2MB",
    'pages.agent.flagship': "nave-pavilhão",
    'pages.agent.highSpeed': "alta velocidade",
    'pages.agent.toolInvocation': "Chamada de ferramentas",
    'pages.agent.rolePlay': "jogo de papel",
    'pages.agent.longText': "Texto longo",
    'pages.agent.imageUnderstanding': "Compreensão da imagem",
    'pages.agent.reasoning': "habilidade de raciocínio",
    'pages.agent.videoUnderstanding': "Video Understanding",
    'pages.agent.costPerformance': "custo-eficácia",
    'pages.agent.codeExpert': "Especialização do código",
    'pages.agent.audioUnderstanding': "Compreensão do som",
    'pages.agent.visualAnalysis': "Análise Visual",
    'pages.agent.running': "Em operação",
    'pages.agent.queuing': "em fila",
    'pages.agent.training': "Durante o treinamento",
    'pages.agent.trainingFailed': "Training failed",
    'pages.agent.text': "texto",
    'pages.agent.multimodal': "Multimodal",
    'pages.agent.landongModel': "Modelo de Buraco Lazy",
    'pages.agent.searchModelName': "Procurar o nome do modelo",
    'pages.agent.quotaTrial': "Limitar a experiência",
    'pages.agent.comingOffline': "Desconectando em breve",
    'pages.agent.newModelExperience': "Nova experiência modelo",
    'pages.agent.advancedModel': "Modelo Avançado",
    'pages.agent.generalModel': "modelo geral",
    'pages.agent.modelType': "Tipo de modelo",
    'pages.agent.modelFeature': "Características do Modelo",
    'pages.agent.modelProvider': "Fabricante de modelos",
    'pages.agent.modelSupportedFunctions': "Função de suporte modelo",
    'pages.agent.contextLength': "Longitude do contexto",
    'pages.agent.userRights': "Direitos do Utilizador",
    'pages.agent.creator': "criador",
    'pages.agent.creationTime': "Tempo de criação",
    'pages.agent.describeFunction': "Por favor descreva o design de personagem e a funcionalidade",
    'pages.agent.orchestration': "arranjar",
    'pages.agent.functionIntroduction': "Função Introdução",
    'pages.agent.publishStatus': "Publicar o Estado",
    'pages.agent.agentDisplay': "Visualização inteligente de agentes",
    'pages.agent.modelStatus': "Estado do modelo",
    'pages.search.expandir': "Expandir",
    'pages.search.retirar': "Recolher",
    'pages.search.deleteConfirmWarning': "Após ser excluído, não será possível restaurá-lo. Tem certeza de que deseja excluí-lo?",
    'pages.config.applicationId': "ID do aplicativo",
    'pages.config.imageDeduplication': "Desduplicação de imagem",
    'pages.pointManage.loadingMessage': "Carregando, por favor, não atualize a página",
    'pages.pointManage.fetchError': "Tempo limite para obtenção do ponto, verifique se o dispositivo está online",
    'pages.pointManage.deviceTimeout': "Tempo limite para solicitação do dispositivo",
    'pages.pointManage.streamConnectFailed': "Falha na conexão do serviço de streaming",
    'pages.pointManage.serviceException': "Anormalidade no serviço, tente novamente mais tarde",
    'pages.pointManage.deleteHasControlRule': "O ponto selecionado está sendo monitorado e não pode ser excluído",
    'pages.pointManage.online': "Online",
    'pages.pointManage.offline': "Offline",
    'pages.pointManage.confirmDeleteEventType': "Tem certeza de que deseja excluir este tipo de evento?",
    'pages.pointManage.captureIntervalRange': "O intervalo de captura é entre 1 e 3600 segundos",
    'pages.pointManage.status': "estado",
    'pages.login.terms.title': "Termos de Serviço",
    'pages.login.terms.check': "Por favor, leia e concorde com os Termos de Serviço primeiro.",
    'pages.pointManage.confirmDeletePoint': "Tem certeza de que deseja excluir o registro do ponto?",
    'pages.pointManage.pointNameRequired': "Alguns nomes de pontos não foram preenchidos. Preencha-os antes de enviar.",
    'pages.pointManage.refresh': "Atualizar",
    'pages.account.updateSuc': "Modificado com sucesso",
    'pages.account.updatePwd': "alterar a senha",
    'pages.account.oldPassword': "Senha Antiga",
    'pages.account.newPassword': "Nova Senha",
    'pages.account.confirmPwd': "Confirme sua senha",
    'pages.account.passwordmatch': "A nova senha que você digitou não corresponde",
    'pages.password.reset.fail': "Falha ao redefinir a senha",
    'pages.password.reset.success': "Senha redefinida com sucesso",
    'pages.password.update': "Alterar Senha",
    'pages.register.success': "Registro bem-sucedido, faça login",
    'pages.register.fail': "Falha no registro",
    'pages.login.fail': "Nome de usuário ou senha incorretos",
    'pages.login.needRegister': "Registre uma conta primeiro",
    'pages.system.check.fail': "Falha na verificação do serviço",
    'pages.account.maxlength': "A senha pode ter no máximo 18 caracteres",
    'pages.login.login': "Entrar",
    'pages.login.register': "Registrar",
    'pages.login.registerTitle': "Registro de Usuário",
    'pages.search.similarity': "Similaridade",
    'pages.common.sessionExpired': "Sessão expirada. Por favor, faça login novamente.",
    'pages.primaryKey.id': "ID da chave primária",
    'pages.agent.type': "tipo",
    'pages.agent.type.placeholder': "Por favor, escolha o tipo de agente inteligente",
    'pages.agent.type.required': "Por favor seleccione o tipo",
    'pages.agent.id': "ID do agente inteligente",
    'pages.agent.id.placeholder': "Por favor, introduz o ID do agente inteligente",
    'pages.agent.id.required': "Por favor, introduz o ID do agente inteligente",
    'pages.agent.suggestedQuestions': "Você pode me perguntar isto:",
    'pages.agent.botId.tip': "Por favor, vá para a plataforma correspondente (como Coze, Dify) para criar um agente inteligente, copiar seu ID e colocá-lo aqui",
    'pages.agent.apiKey.tip': "Por favor, vá para a plataforma Dify, copie sua chave API e coloque aqui",
    'pages.agent.apiKey.required': "A chave API é um campo requerido",
}
