export default {
    'pages.login.success': "Login berhasil!",
    'pages.login.failure': "Login gagal!",
    'pages.login.retry': "Login gagal, silakan coba lagi!",
    'pages.login.title': "<PERSON><PERSON> login",
    'pages.login.welcome': "Selamat datang di Mars Wisdom",
    'pages.login.agreementPrefix': "Anda telah membaca dan menyetujui",
    'pages.login.terms': "Persyaratan Penggunaan Pengguna",
    'pages.login.and': "dan",
    'pages.login.privacy': "Kebijakan Privasi",
    'pages.login.submit': "Login pengguna",
    'pages.login.usernamePlaceholder': "Silakan masukkan nama pengguna Anda",
    'pages.login.passwordPlaceholder': "Silakan masukkan kata sandi Anda",
    'pages.search.sortByTime': "Berdasarkan waktu terkini",
    'pages.search.sortBySimilarity': "Berdasarkan kemiripan",
    'pages.search.history': "Riwayat",
    'pages.search.searchHistory': "Riwayat penelusuran",
    'pages.search.delete': "Hapus",
    'pages.search.hotSearch': "Penelusuran terpopuler",
    'pages.search.example1': "Anak laki-laki di samping kendaraan bermotor di pintu",
    'pages.search.example2': "Orang berpakaian merah menelepon di lift",
    'pages.search.example3': "Orang mengenakan topi biru di tempat parkir",
    'pages.search.assistHint': "Saya dapat membantu Anda menelusuri informasi, seperti: anak laki-laki berpakaian hitam di samping lemari makanan di lantai pertama taman",
    'pages.search.searchButton': "Penelusuran",
    'pages.search.filter': "Ketentuan penelusuran",
    'pages.search.loading': "Memuat...",
    'pages.search.eventDetail': "Detail acara",
    'pages.search.videoPlayback': "Pemutaran video",
    'pages.search.panorama': "Panorama",
    'pages.search.description': "Deskripsi",
    'pages.search.time': "Waktu",
    'pages.search.location': "Lokasi",
    'pages.search.similarity': "Kemiripan",
    'pages.pointManage.voiceInput': "Input suara",
    'pages.pointManage.speechRecognition': "Pengenalan suara",
    'pages.pointManage.stopVoiceInput': "Hentikan input suara",
    'pages.pointManage.operationSuccess': "Operasi berhasil",
    'pages.pointManage.preview': "Pratinjau",
    'pages.pointManage.edit': "Edit",
    'pages.pointManage.alreadyMonitored': "Kontrol diterapkan",
    'pages.pointManage.monitor': "Kontrol diterapkan",
    'pages.pointManage.delete': "Hapus",
    'pages.pointManage.deleteRecord': "Hapus rekaman",
    'pages.pointManage.confirmDeleteRecord': "Anda yakin ingin menghapus rekaman?",
    'pages.pointManage.confirm': "Konfirmasi",
    'pages.pointManage.cancel': "Batal",
    'pages.pointManage.deleting': "Menghapus",
    'pages.pointManage.deleteSuccess': "Hapus berhasil, penyegaran otomatis",
    'pages.pointManage.deleteFailure': "Penghapusan gagal, silakan coba lagi",
    'pages.pointManage.person': "Orang",
    'pages.pointManage.motorVehicle': "Kendaraan bermotor",
    'pages.pointManage.nonMotorVehicle': "Kendaraan non-bermotor",
    'pages.pointManage.pointName': "Nama titik",
    'pages.pointManage.protocolType': "Jenis protokol",
    'pages.pointManage.captureType': "Jenis snapshot",
    'pages.pointManage.captureInterval': "Interval snapshot",
    'pages.pointManage.deviceName': "Nama perangkat",
    'pages.pointManage.deviceIP': "IP perangkat",
    'pages.pointManage.devicePort': "Port perangkat",
    'pages.pointManage.operation': "Operasi",
    'pages.pointManage.pointManagement': "Manajemen titik",
    'pages.pointManage.add': "Tambah",
    'pages.pointManage.selected': "Dipilih",
    'pages.pointManage.batchDelete': "Hapus batch",
    'pages.pointManage.item': "Item",
    'pages.pointManage.inputCaptureInterval': "Silakan masukkan interval snapshot",
    'pages.pointManage.inputPointName': "Harap masukkan nama titik",
    'pages.pointManage.selectCaptureType': "Harap pilih jenis penangkapan",
    'pages.pointManage.addDevice': "Tambahkan perangkat",
    'pages.pointManage.protocol': "Protokol",
    'pages.pointManage.deviceCode': "Kode perangkat",
    'pages.pointManage.inputDeviceName': "Harap masukkan nama perangkat",
    'pages.pointManage.inputDeviceCode': "Harap masukkan kode perangkat",
    'pages.pointManage.port': "Port",
    'pages.pointManage.username': "Nama pengguna",
    'pages.pointManage.password': "Kata sandi",
    'pages.pointManage.mainStream': "Aliran utama",
    'pages.pointManage.example': "Contoh",
    'pages.pointManage.subStream': "Subaliran",
    'pages.pointManage.addPoint': "Tambahkan titik",
    'pages.pointManage.validIP': "Harap masukkan alamat IP yang valid",
    'pages.pointManage.noData': "Tidak ada data",
    'pages.pointManage.selectPoint': "Harap pilih titik",
    'pages.pointManage.addSuccess': "Berhasil ditambahkan",
    'pages.pointManage.prevStep': "Langkah sebelumnya",
    'pages.pointManage.nextStep': "Langkah berikutnya",
    'pages.pointManage.monitorSuccess': "Penerapan kontrol berhasil",
    'pages.pointManage.monitorFailure': "Penerapan kontrol gagal",
    'pages.pointManage.cancelSuccess': "Pembatalan berhasil",
    'pages.pointManage.operationFailure': "Operasi gagal",
    'pages.pointManage.monitor': "Penerapan kontrol",
    'pages.pointManage.monitorEvent': "Peristiwa penerapan kontrol",
    'pages.pointManage.startMonitor': "Mulai penerapan kontrol",
    'pages.pointManage.monitorRecord': "Rekaman penerapan kontrol",
    'pages.pointManage.noEventRecord': "Tidak ada rekaman kejadian",
    'pages.pointManage.charLimitExceeded': "Panjang karakter melebihi batas, maksimum:",
    'pages.pointManage.eventType': "Jenis Acara",
    'pages.pointManage.prompt': "Petunjuk",
    'pages.pointManage.createTime': "Waktu Pembuatan",
    'pages.pointManage.editSuccess': "Edit Berhasil",
    'pages.pointManage.monitorRule': "Aturan Pemantauan",
    'pages.pointManage.selectEventType': "Silakan pilih jenis acara",
    'pages.pointManage.inputPrompt': "Silakan masukkan petunjuk",
    'pages.pointManage.eventRecord': "Catatan Acara",
    'pages.pointManage.paginationInfo': "Item {range0} - {range1} dari total {total}",
    'pages.pointManage.detail': "Rincian",
    'pages.pointManage.eventImage': "Gambar Acara",
    'pages.pointManage.pointInfo': "Informasi Titik",
    'pages.pointManage.eventTime': "Waktu Acara",
    'pages.config.name': "Nama",
    'pages.config.creationDate': "Tanggal Pembuatan",
    'pages.config.expirationTime': "Waktu Kedaluwarsa",
    'pages.config.editApiKey': "Edit Kunci API",
    'pages.config.editSuccess': "Edit Berhasil, Segarkan Otomatis",
    'pages.config.createSuccess': "Pembuatan Berhasil, Segarkan Otomatis",
    'pages.config.editFailure': "Edit Gagal, Silakan Coba Lagi",
    'pages.config.createFailure': "Pembuatan Gagal, Silakan Coba Lagi",
    'pages.config.createApiKey': "Buat Kunci API",
    'pages.config.authManagement': "Manajemen Otorisasi",
    'pages.config.eventCode': "Kode Acara",
    'pages.config.paramConfigFailure': "Gagal Mendapatkan Konfigurasi Parameter",
    'pages.config.saveFailure': "Gagal Menyimpan",
    'pages.config.saveSuccess': "Berhasil Menyimpan",
    'pages.config.save': "Simpan",
    'pages.config.reset': "Atur Ulang",
    'pages.config.streamConfig': "Konfigurasi Streaming",
    'pages.config.streamServiceUrl': "Alamat Layanan Streaming",
    'pages.config.secretKey': "Kunci Rahasia",
    'pages.config.configuration': "Konfigurasi",
    'pages.config.workspaceId': "ID Ruang Kerja",
    'pages.config.multiSearchStrategy': "Strategi Pencarian Multidimensi",
    'pages.config.dataCleanStrategy': "Strategi Pembersihan Data",
    'pages.config.objectDetection': "Deteksi Objek",
    'pages.config.enableObjectDetection': "Aktifkan Deteksi Objek?",
    'pages.config.allowObjectWhitelist': "Izinkan Daftar Putih Deteksi Objek",
    'pages.config.sedan': "Sedan",
    'pages.config.bus': "Bus",
    'pages.config.truck': "Truk",
    'pages.config.bicycle': "Sepeda",
    'pages.config.motorcycle': "Sepeda Motor",
    'pages.config.enableImageDupCheck': "Aktifkan Deteksi Duplikat Gambar?",
    'pages.config.intervalSeconds': "Waktu Interval (Detik)",
    'pages.config.minScoreLimitError': "Nilai Skor Minimum Tidak Boleh Lebih dari 1",
    'pages.config.initialSearchStrategy': "Strategi Pencarian Penyaringan Awal",
    'pages.config.enhancedSearchStrategy': "Strategi Pencarian Ditingkatkan Multidimensi",
    'pages.config.multiInitialSearch': "Penyaringan Awal Multidimensi",
    'pages.config.minScore': "Nilai Skor Minimum",
    'pages.config.maxResultSet': "Set Hasil Maksimum",
    'pages.config.topValue': "Nilai Teratas",
    'pages.config.reRanking': "Peringkat Ulang Multidimensi",
    'pages.config.batchSize': "Ukuran Batch",
    'pages.config.fineSearch': "Penyaringan Halus Multidimensi",
    'pages.config.fineSearchStrategy': "Strategi Pencarian Penyaringan Halus",
    'pages.config.enableReId': "Aktifkan ReId?",
    'pages.config.objectMinScore': "Skor Minimum Deteksi Objek",
    'pages.config.vectorMinScore': "Skor Minimum Kemiripan Vektor",
    'pages.config.maxResultsPerSearch': "Hasil Maksimum per Pencarian",
    'pages.common.logout': "Keluar",
    'pages.agent.apiCallFailed': "Panggilan antarmuka gagal, silakan coba lagi nanti",
    'pages.agent.hello': "Halo, aku.",
    'pages.agent.agent': "Tubuh cerdas",
    'pages.agent.attachment': "Aksesoris",
    'pages.agent.dropFilesHere': "Tempatkan file di sini",
    'pages.agent.uploadFile': "Mengunggah File",
    'pages.agent.clickOrDragToUpload': "Klik atau seret file ke area ini untuk mengunggah",
    'pages.agent.shiftEnterNewline': "Shift + Enter untuk mengubah",
    'pages.agent.basicConfig': "Konfigurasi dasar",
    'pages.agent.llmModel': "Model LLM yang digunakan",
    'pages.agent.doubaoModel': "Model Bungkus Kacang",
    'pages.agent.selectAnOption': "Silakan pilih pilihan",
    'pages.agent.memoryMessageCount': "Jumlah pesan memori",
    'pages.agent.skillConfig': "Konfigurasi keterampilan",
    'pages.agent.toolSet': "Paket alat",
    'pages.agent.toolSetDescription': "Paket alat memungkinkan badan cerdas memanggil alat eksternal untuk memperluas batas kemampuan badan cerdas.",
    'pages.agent.knowledgeBase': "Pangkalan pengetahuan",
    'pages.agent.knowledgeBaseDescription': "Ketika pengguna mengirim pesan, cerdas dapat mengutip isi dari basis pengetahuan untuk menjawab pertanyaan pengguna.",
    'pages.agent.workflow': "Aliran kerja",
    'pages.agent.workflowDescription': "Aliran tugas yang digunakan untuk menangani logika yang rumit dan memiliki beberapa langkah.",
    'pages.agent.describePersona': "Silakan jelaskan perawatan, fungsi",
    'pages.agent.publishSuccess': "Publikasi berhasil",
    'pages.agent.publishFailed': "Publikasi Gagal",
    'pages.agent.publishNotAllowed': "Maaf, cerdas ini tidak dapat dipublikasikan.",
    'pages.agent.config': "Konfigurasi Smart Body",
    'pages.agent.publish': "Publikasi",
    'pages.agent.modelCapabilityConfig': "Konfigurasi Kapasitas Model",
    'pages.agent.promptDev': "Pengembangan Kata Tip",
    'pages.agent.debug': "Uji Badan Cerdas",
    'pages.agent.create': "Membuat Smart Body",
    'pages.agent.submitFailed': "Pengiriman gagal, silakan periksa data formulir",
    'pages.agent.name': "Nama badan cerdas",
    'pages.agent.nameLimit': "Bisa memasukkan hingga 64 karakter",
    'pages.agent.description': "Pengenalan Fungsi Smart Body",
    'pages.agent.descriptionTip': "Pengenalan fungsi Smart Body, akan ditampilkan kepada pengguna Smart Body",
    'pages.agent.icon': "Ikon",
    'pages.agent.imageOnly': "Hanya dapat mengunggah file gambar",
    'pages.agent.imageSizeLimit': "Ukuran gambar tidak boleh lebih dari 2MB.",
    'pages.agent.imageFormatLimit': "Mendukung format jpg / png, ukuran tidak lebih dari 2MB",
    'pages.agent.flagship': "Kapal Udara",
    'pages.agent.highSpeed': "kecepatan tinggi",
    'pages.agent.toolInvocation': "Panggilan Alat",
    'pages.agent.rolePlay': "Pemain peran",
    'pages.agent.longText': "Teks Panjang",
    'pages.agent.imageUnderstanding': "Pemahaman gambar",
    'pages.agent.reasoning': "Kemampuan penalaran",
    'pages.agent.videoUnderstanding': "Pemahaman Video",
    'pages.agent.costPerformance': "Perbandingan Harga",
    'pages.agent.codeExpert': "Spesialisasi Kode",
    'pages.agent.audioUnderstanding': "Pemahaman Audio",
    'pages.agent.visualAnalysis': "Analisis visual",
    'pages.agent.running': "Jalankan",
    'pages.agent.queuing': "Dalam antrian",
    'pages.agent.training': "Dalam pelatihan",
    'pages.agent.trainingFailed': "Pelatihan Gagal",
    'pages.agent.text': "Teks",
    'pages.agent.multimodal': "Multimode",
    'pages.agent.landongModel': "Model malas",
    'pages.agent.searchModelName': "Cari nama model",
    'pages.agent.quotaTrial': "Pengalaman Batas",
    'pages.agent.comingOffline': "Akan berakhir",
    'pages.agent.newModelExperience': "Pengalaman Model Baru",
    'pages.agent.advancedModel': "Model Lanjutan",
    'pages.agent.generalModel': "Model umum",
    'pages.agent.modelType': "Jenis Model",
    'pages.agent.modelFeature': "Fitur Model",
    'pages.agent.modelProvider': "Produsen Model",
    'pages.agent.modelSupportedFunctions': "Fungsi dukungan model",
    'pages.agent.contextLength': "Panjang Konteks",
    'pages.agent.userRights': "Keuntungan Pengguna",
    'pages.agent.creator': "Pembuat",
    'pages.agent.creationTime': "Waktu pembuatan",
    'pages.agent.describeFunction': "Jelaskan karakter dan fungsinya",
    'pages.agent.orchestration': "Orkestrasi",
    'pages.agent.functionIntroduction': "Pengenalan fungsi",
    'pages.agent.publishStatus': "Status rilis",
    'pages.agent.agentDisplay': "Tampilan agen",
    'pages.agent.modelStatus': "Status model",
    'pages.search.expandir': "Perluas",
    'pages.search.retirar': "Tutup",
    'pages.search.deleteConfirmWarning': "Setelah dihapus, aplikasi tidak dapat dikembalikan. Apakah Anda yakin ingin menghapusnya?",
    'pages.config.applicationId': "ID Aplikasi",
    'pages.config.imageDeduplication': "Deduplikasi Gambar",
    'pages.pointManage.loadingMessage': "Sedang memuat, mohon jangan menyegarkan halaman",
    'pages.pointManage.fetchError': "Waktu habis untuk memperoleh titik, mohon periksa apakah perangkat sedang online",
    'pages.pointManage.deviceTimeout': "Waktu habis permintaan perangkat",
    'pages.pointManage.streamConnectFailed': "Koneksi layanan streaming gagal",
    'pages.pointManage.serviceException': "Ketidaknormalan layanan, mohon coba lagi nanti",
    'pages.pointManage.deleteHasControlRule': "Titik yang dipilih sedang dipantau dan tidak dapat dihapus",
    'pages.pointManage.online': "Online",
    'pages.pointManage.offline': "Offline",
    'pages.pointManage.confirmDeleteEventType': "Apakah Anda yakin ingin menghapus jenis peristiwa ini?",
    'pages.pointManage.captureIntervalRange': "Interval pengambilan antara 1 dan 3600 detik",
    'pages.pointManage.status': "negara",
    'pages.login.terms.title': "	Ketentuan Layanan",
    'pages.login.terms.check': "Harap baca dan setujui Ketentuan Layanan terlebih dahulu.",
    'pages.pointManage.confirmDeletePoint': "Apakah Anda yakin ingin menghapus data titik ini?",
    'pages.pointManage.pointNameRequired': "Beberapa nama titik belum diisi. Silakan lengkapi sebelum mengirim.",
    'pages.pointManage.refresh': "Segarkan",
    'pages.account.updateSuc': "Berhasil dimodifikasi",
    'pages.account.updatePwd': "ganti kata sandi",
    'pages.account.oldPassword': "password lama",
    'pages.account.newPassword': "kata sandi baru",
    'pages.account.confirmPwd': "konfirmasi sandi",
    'pages.account.passwordmatch': "Kata sandi baru yang Anda masukkan tidak cocok",
    'pages.password.reset.fail': "Gagal mengatur ulang kata sandi",
    'pages.password.reset.success': "Kata sandi berhasil direset",
    'pages.password.update': "Ubah Kata Sandi",
    'pages.register.success': "Pendaftaran berhasil, silakan login",
    'pages.register.fail': "Pendaftaran gagal",
    'pages.login.fail': "Nama pengguna atau kata sandi salah",
    'pages.login.needRegister': "Silakan daftar akun terlebih dahulu",
    'pages.system.check.fail': "Pemeriksaan layanan gagal",
    'pages.account.maxlength': "Kata sandi maksimal 18 karakter",
    'pages.login.login': "Masuk",
    'pages.login.register': "Daftar",
    'pages.login.registerTitle': "Pendaftaran Pengguna",
    'pages.search.similarity': "Kemiripan",
    'pages.common.sessionExpired': "Sesi telah kedaluwarsa, silakan login lagi.",
    'pages.primaryKey.id': "ID kunci utama",
    'pages.agent.type': "Jenis",
    'pages.agent.type.placeholder': "Silakan pilih jenis bodi cerdas",
    'pages.agent.type.required': "Silakan pilih jenis",
    'pages.agent.id': "ID Badan Cerdas",
    'pages.agent.id.placeholder': "Masukkan ID Smart Body",
    'pages.agent.id.required': "Masukkan ID Smart Body",
    'pages.agent.suggestedQuestions': "Anda bisa bertanya seperti ini:",
    'pages.agent.botId.tip': "Silakan pergi ke platform yang sesuai (misalnya Coze, Dify) untuk membuat smartphone dan salin ID mereka dan paste di sini",
    'pages.agent.apiKey.tip': "Silakan pergi ke platform Dify dan salin kunci API mereka dan paste ke sini",
    'pages.agent.apiKey.required': "API Key diperlukan",
}
