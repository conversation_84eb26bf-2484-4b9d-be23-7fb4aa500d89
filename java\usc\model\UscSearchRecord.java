package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 搜索记录表
 *
 * <AUTHOR>
 * @date 2024-12-31 16:07
 * @since 1.0.0
 */
@Entity
@Table(name = "USC_SEARCH_RECORD", indexes = {@Index(name = "USC_SEARCH_RECORD_ID_IDX", columnList = "ID"), @Index(name = "USC_SEARCH_RECORD_CRT_IDX", columnList = "CREATE_TIME"), @Index(name = "USC_SEARCH_RECORD_UPT_IDX", columnList = "UPDATE_TIME")})
@Getter
@Setter
@Accessors(chain = true)
public class UscSearchRecord extends BaseModel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联用户id
     */
    @Column(name = "user_id", length = 50)
    private String userId;

    /**
     * 搜索信息
     */
    @Column(name = "search_info", length = 255)
    private String searchInfo;


    /**
     * 搜索类型  1:文本搜索;2:图片搜索;3:语音搜索
     */
    @Column(name = "search_type", length = 2)
    private String searchType;

    /**
     * 搜索来源 1:PC端;2:移动端
     */
    @Column(name = "search_origin", length = 2)
    private String searchOrigin;

}

