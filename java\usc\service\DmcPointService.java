package com.zkteco.mars.usc.service;

import com.zkteco.framework.base.service.BaseService;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.usc.vo.DmcPointItem;
import com.zkteco.mars.usc.vo.GetChannelItem;

import java.util.List;

/**
 * 智能搜索查询
 *
 * <AUTHOR>
 * @date 2024-12-31 14:15
 * @since 1.0.0
 */
public interface DmcPointService extends BaseService {

    /**
     * 获取点位信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
//    Pager getItemsByPage(PointInfoItem pointInfoItem,pageNo);

    /**
     * 编辑点位信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
    ApiResultMessage editPointInfo();


    /**
     * 添加点位信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
    ApiResultMessage addPointInfo(List<DmcPointItem> dmcPointList);

    /**
     * 添加设备信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
    ApiResultMessage addDeviceInfos(List<DmcPointItem> dmcPointItemList);


    /**
     * 获取通道信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
    ApiResultMessage getChannelInfo(GetChannelItem getChannelItem);


    /**
     * 删除点位信息
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 11:07
     * @since 1.0.0
     */
    ApiResultMessage delPointInfo();


    /**
     * 删除点位
     *
     * @param dmcPointList:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-15 20:58
     * @since 1.0.0
     */
    ApiResultMessage delPoints(List<DmcPointItem> dmcPointList);

    /**
     * 在无通道的数据清空下删除流媒体设备
     *
     * @param deviceId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-05-30 17:20
     * @since 1.0.0
     */
    ApiResultMessage deleteDevice(String deviceId);


    /**
     * 编辑点位名称
     *
     * @param dmcPointItem:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-15 20:58
     * @since 1.0.0
     */
    ApiResultMessage editPointName(DmcPointItem dmcPointItem);

    /**
     * 编辑点位信息
     *
     * @param dmcPointItem:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-01-15 20:58
     * @since 1.0.0
     */
    ApiResultMessage editPointInfo(DmcPointItem dmcPointItem);


    /**
     * 获取所有的点位名称
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-28 16:47
     * @since 1.0.0
     */
    ApiResultMessage getAllPointName();

    /**
     * 获取点位ID和名称集合
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-12-31 10:00
     * @since 1.0.0
     */
    ApiResultMessage getPointIdAndNameList();


}
