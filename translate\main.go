package main

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
)

var rootCmd = &cobra.Command{
	Use:          "translate",
	Short:        "translate",
	SilenceUsage: true,
	Long:         `translate`,
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) < 1 {
			tip()
			return errors.New(fmt.Sprintf("missing required argument"))
		}
		return nil
	},
	PersistentPreRunE: func(*cobra.Command, []string) error { return nil },
	Run: func(cmd *cobra.Command, args []string) {
		tip()
	},
}
var aliKey string

func tip() {
	usageStr := `欢迎使用, 可以使用 -h 查看命令`
	fmt.Printf("%s\n", usageStr)
}
func init() {
	rootCmd.PersistentFlags().StringVar(&ali<PERSON>ey, "alikey", "", "阿里翻译的key")
}

func writeErrorCodesToFile(sheet *excelize.File, colIdx int, langCode, filePath string) {
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Printf("Error creating file: %s\n", err)
		return
	}
	defer file.Close()

	file.WriteString("export default {\n")

	rows, _ := sheet.GetRows(sheet.GetSheetName(0))
	for _, row := range rows[1:] {
		errorCode := row[0]
		message := row[colIdx]
		if message == "" {
			message = " "
		}
		content := fmt.Sprintf("    '%s': \"%s\",\n", errorCode, message)
		file.WriteString(content)
	}
	file.WriteString("}\n")
}

func removeDirectory(directory string) {
	filepath.Walk(directory, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			os.Remove(path)
		}
		return nil
	})
	os.Remove(directory)
}

func translate(file fs.DirEntry) {
	baseDir := "locales"
	workbook, err := excelize.OpenFile("./excel/" + file.Name())
	if err != nil {
		fmt.Printf("Error opening file: %s\n", err)
		return
	}

	sheetName := workbook.GetSheetName(0)
	languages, _ := workbook.GetRows(sheetName)
	for colIdx, language := range languages[0][1:] {
		if language == "" {
			continue
		}

		startIdx := strings.Index(language, "(")
		endIdx := strings.Index(language, ")")
		if startIdx != -1 && endIdx != -1 && startIdx < endIdx {
			langCode := language[startIdx+1 : endIdx]
			directory := filepath.Join(baseDir, langCode)

			// if _, err := os.Stat(directory); !os.IsNotExist(err) {
			// 	removeDirectory(directory)
			// }
			if _, err := os.Stat(directory); os.IsNotExist(err) {
				os.MkdirAll(directory, os.ModePerm)
			}

			filePath := filepath.Join(directory, strings.Split(file.Name(), ".")[0]+".ts")
			writeErrorCodesToFile(workbook, colIdx+1, langCode, filePath)
		}
	}
}

func run() {
	baseDir := "locales"
	// fileName := "code.ts"
	removeDirectory(baseDir)
	files, _ := os.ReadDir("./excel")
	for _, f := range files {
		fmt.Println(f.Name())
		if f.IsDir() {
			fmt.Printf("file is dicory: %s\n", f.Name())
			continue
		}
		translate(f)
	}
}
