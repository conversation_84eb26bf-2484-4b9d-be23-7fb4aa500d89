import { Footer } from '@/components';
import { login, setAccessToken, checkRegistered, registerUser, currentUser } from '@/services/ant-design-pro/api';
import { getFakeCaptcha } from '@/services/ant-design-pro/login';
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import { FormattedMessage, history, SelectLang, useIntl, useModel, Helmet, getDvaApp } from '@umijs/max';
import { Alert, message, Tabs, ConfigProvider, Form } from 'antd';
import Settings from '../../../../config/defaultSettings';
import React, { useState, useEffect } from 'react';
import { flushSync } from 'react-dom';
import { createStyles } from 'antd-style';
import { CustomSelectLang } from '@/components/CustomSelectLang';
import './index.less'
import { encryptAES } from '@/utils/encrypt';

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      position: 'relative',
      right: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
      marginTop: 20
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('/icons/login/background.png')",
      backgroundSize: '100% 100%',
    },
    langIcon: {
      marginLeft: '8px',
      display: 'flex',
      alignItems: 'center',
      paddingLeft: 16,
      paddingTop: 20
    },
    langWrapper: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      position: 'fixed',
      width: '100%',
      right: 0,
      height: 42,
      lineHeight: '42px',
    },
  };
});

const ActionIcons = () => {
  const { styles } = useStyles();

  return (
    <>
      <AlipayCircleOutlined key="AlipayCircleOutlined" className={styles.action} />
      <TaobaoCircleOutlined key="TaobaoCircleOutlined" className={styles.action} />
      <WeiboCircleOutlined key="WeiboCircleOutlined" className={styles.action} />
    </>
  );
};

const Lang = () => {
  const { styles } = useStyles();
  const intl = useIntl();
  const locale = intl.locale;

  return (
    <div className={styles.langWrapper}>
      <div className={styles.langIcon}>
        <img style={{ paddingLeft: '5px' }} src={locale === 'zh-CN' || locale === 'zh-TW' ? "/icons/login/logo_black_CN.svg" : "/icons/login/logo_black_EN.svg"}></img>
      </div>
      <div className={styles.lang}>
        {CustomSelectLang && <CustomSelectLang />}
      </div>
    </div>

  );
};

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

function getAgreedFromStorage() {
  const data = localStorage.getItem('agreed_terms');
  if (!data) return false;
  try {
    const { value, expire } = JSON.parse(data);
    if (value === 1 && expire > Date.now()) {
      return true;
    }
  } catch {
    // 解析失败视为未同意
  }
  return false;
}

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});
  const [type, setType] = useState<string>('account');
  const [isRegistered, setIsRegistered] = useState<boolean>(true);
  const { initialState, setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();

  const intl = useIntl();
  const locale = intl.locale;
  //console.log(locale)

  const bgImageUrl =
    locale === 'zh-CN' || locale === 'zh-TW'
      ? "/icons/login/zh_combination.png"
      : "/icons/login/us_combination.png";

  const fetchUserInfo = async (user: any) => {
    if (user) {
      user.name = user.name
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: user,
        }));
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      console.log("11111")
      if (!values.terms) {
        message.warning(
          intl.formatMessage({
            id: 'pages.login.terms.check',
            defaultMessage: '请先阅读并同意《产品使用协议》',
          })
        );
        return;
      }

      const encryptedPass = encryptAES(values.pass);
      if (!isRegistered) {
        // 注册流程
        const regRes = await registerUser({
          name: values.account,
          password: encryptedPass,
        });
        if (regRes.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.register.success', defaultMessage: '注册成功，请登录' }));
          setIsRegistered(true); // 注册成功后切换到登录
        } else {
          message.error(
            intl.formatMessage({
              id: 'pages.serverCode.' + regRes.code,
              defaultMessage: intl.formatMessage({
                id: 'pages.register.fail',
                defaultMessage: '注册失败',
              }),
            })
          );
        }
        return;
      }

      // 登录流程
      const msg = await login({
        ...values,
        pass: encryptedPass,
      });
      if (msg.code === 0 && msg.data) {
        const defaultLoginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        message.success(defaultLoginSuccessMessage);
        const accessToken: string = msg?.data?.token
        const payload = {
          name: msg?.data?.userName,
          username: msg?.data?.userName,
          accessToken: accessToken,
          outTime: false,
        };
        setAccessToken(accessToken)
        const dvaApp = getDvaApp()
        dvaApp._store.dispatch({
          type: 'user/changeUserInfo',
          payload: payload
        })
        await fetchUserInfo(payload, accessToken);
        const urlParams = new URL(window.location.href).searchParams;
        history.push(urlParams.get('redirect') || '/');
        return;
      } else {
        // 登录失败，设置输入框下方的错误提示
        loginForm.setFields([
          // { name: 'account', errors: [intl.formatMessage({ id: 'pages.login.fail', defaultMessage: '用户名或密码错误' })]},
          { name: 'pass', errors: [intl.formatMessage({ id: 'pages.login.fail', defaultMessage: '用户名或密码错误' })] },
        ]);
        message.error(intl.formatMessage({ id: 'pages.login.failure', defaultMessage: '登录失败！' }));
      }
      setUserLoginState(msg);
    } catch (error) {
      const defaultLoginFailureMessage = intl.formatMessage({
        id: 'pages.login.retry',
        defaultMessage: '登录失败，请重试！',
      });
      console.log(error);
      message.error(defaultLoginFailureMessage);
    }
  };
  const { status, type: loginType } = userLoginState;

  const [loginForm] = Form.useForm();

  const [agreed, setAgreed] = React.useState(getAgreedFromStorage());

  const handleAgreementChange = (e) => {
    if (e.target.checked) {
      const expireTime = Date.now() + 20 * 24 * 60 * 60 * 1000; // 20天
      localStorage.setItem('agreed_terms', JSON.stringify({ value: 1, expire: expireTime }));
      setAgreed(true);
    } else {
      localStorage.removeItem('agreed_terms');
      setAgreed(false);
    }
  };

  useEffect(() => {
    const account = 'admin';
    checkRegistered(account)
      .then(res => {
        if (res && res.code !== 0) {
          message.warning(res.msg || intl.formatMessage({ id: 'pages.login.needRegister', defaultMessage: '请先注册账号', }));
        }

        if (res.data) {
          // 用户已注册，显示登录界面
          setIsRegistered(true);
        } else {
          // 用户未注册，显示注册界面
          setIsRegistered(false);
        }
      })
      .catch(() => {
        message.warning(intl.formatMessage({ id: 'pages.system.check.fail', defaultMessage: '服务检测失败', }));
      });
  }, []);

  useEffect(() => {
    if (!isRegistered) {
      loginForm.setFieldsValue({ account: 'admin' });
    }
    loginForm.setFieldsValue({ terms: agreed });
  }, [isRegistered, agreed, loginForm]);

  return (
    <div className={styles.container}>

      <ConfigProvider theme={{
        components: {
          Button: {
            colorPrimary: 'rgb(11, 211, 87)',
            algorithm: true,
          },
          Input: {
            colorPrimary: 'rgb(11, 211, 87)',
            algorithm: true,
          },
          Checkbox:
          {
            colorPrimary: 'rgb(11, 211, 87)',
            algorithm: true,
          },
          Dropdown: {
            colorPrimary: 'rgb(11, 211, 87)',
            algorithm: true,
          }
        }
      }}>
        <Helmet>
          <title>
            {intl.formatMessage({
              id: 'pages.login.title',
              defaultMessage: '登录页',
            })}
            - {Settings.title}
          </title>
        </Helmet>
        <Lang />
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-evenly',
            alignItems: 'center',
            flexWrap: 'nowrap',
            padding: '32px 0',
            width: '100%',
            height: '100vh',
          }}
        >
          <div
            style={{
              flex: '0 0 auto',
              maxWidth: '694px',
              width: '100%',
              height: '100%',
              maxHeight: '601px',
              textAlign: 'center',
              backgroundImage: "url(" + bgImageUrl + ")",
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
            }}
          >
          </div>
          <div
            style={{
              flex: '0 0 auto',
              maxWidth: '476px',
              width: '100%',
              // height: '100%',
              maxHeight: '480px',
              //textAlign: 'center',
              borderRadius: '16px',
              boxShadow: '0px 3px 10px 0px rgba(178, 177, 177, 0.25)',
              background: 'rgb(255, 255, 255)',
              overflow: 'hidden',
            }}
          >

            <LoginForm
              form={loginForm}
              className='login-Form'
              contentStyle={{
                minWidth: '372px',
                maxWidth: '75vw',
                height: '100vh',
                overflowY: 'auto',
              }}
              style={{
                color: 'rgb(0, 0, 0)',
              }}
              //logo={<img alt="logo" src="/logo.svg" />}
              title={
                <span
                  style={{
                    color: 'rgb(0, 0, 0)',
                    fontSize: '24px',
                    fontWeight: 400,
                    lineHeight: '35px',
                    letterSpacing: '0px',
                    textAlign: 'left',
                    display: 'block',
                    margin: '20px',
                  }}
                >
                  {intl.formatMessage({
                    id: 'pages.login.welcome',
                    defaultMessage: '欢迎来到火星慧知',
                  })}
                </span>
              }
              contentRender={(formDom: any, submitButtonDom: any) => (
                <>
                  {formDom}
                  <div className="submit-Button-Dom" style={{ textAlign: 'center', marginTop: '16px' }}>
                    <button
                      type="button"
                      onClick={() => {
                        loginForm.submit();
                      }}
                      className="ant-btn ant-btn-primary ant-btn-lg"
                      style={{
                        width: '100%',
                        height: '40px',
                        borderRadius: '4px',
                        background: 'rgb(11, 211, 87)',
                        border: 'none',
                        color: 'white',
                        fontSize: '16x',
                        fontWeight: '400',
                        cursor: 'pointer',
                        lineHeight: '1.5'
                      }}
                    >
                      {isRegistered ? intl.formatMessage({ id: 'pages.login.login', defaultMessage: '登 录' }) : intl.formatMessage({ id: 'pages.login.register', defaultMessage: '注 册' })}
                    </button>
                  </div>
                  <div className="submit-Checkbox-Dom" style={{ textAlign: 'left', marginTop: '25px' }}>
                    <ProFormCheckbox
                      name="terms"
                      onChange={handleAgreementChange}
                      fieldProps={{
                        style: {
                          fontSize: 12,
                          fontWeight: 400,
                          lineHeight: '17px',
                          textAlign: 'left',
                        }
                      }}
                    >
                      {intl.formatMessage({
                        id: 'pages.login.agreementPrefix',
                        defaultMessage: '您已阅读并同意火星慧知',
                      })}
                      {' '}
                      <a href="/terms.html" style={{ color: 'rgb(11, 211, 87)' }}>
                        {intl.formatMessage({
                          id: 'pages.login.terms.title',
                          defaultMessage: '《产品使用协议》',
                        })}
                      </a>
                    </ProFormCheckbox>
                  </div>
                </>
              )}
              // initialValues={{
              //   autoLogin: true,
              // }}
              // actions={[
              //   <FormattedMessage
              //     key="loginWith"
              //     id="pages.login.loginWith"
              //     defaultMessage="其他登录方式"
              //   />,
              //   <ActionIcons key="icons" />,
              // ]}
              onFinish={async (values) => {
                // 未注册时强制用户名为 admin
                if (!isRegistered) {
                  values.account = 'admin';
                }
                await handleSubmit(values as API.LoginParams);
              }}
            >
              {/* <Tabs
              activeKey={type}
              onChange={setType}
              centered
              items={[
                {
                  key: 'account',
                  label: intl.formatMessage({
                    id: 'pages.login.accountLogin.tab',
                    defaultMessage: '账户密码登录',
                  }),
                },
                {
                  key: 'mobile',
                  label: intl.formatMessage({
                    id: 'pages.login.phoneLogin.tab',
                    defaultMessage: '手机号登录',
                  }),
                },
              ]}
            /> */}

              {/* {status === 'error' && loginType === 'account' && (
              <LoginMessage
                content={intl.formatMessage({
                  id: 'pages.login.accountLogin.errorMessage',
                  defaultMessage: '账户或密码错误(admin/ant.design)',
                })}
              />
            )} */}
              <div>
                <span style={{
                  color: 'rgb(0, 0, 0)',
                  fontSize: '16px',
                  fontWeight: 400,
                  lineHeight: '23px',
                  letterSpacing: '0px',
                  textAlign: 'left',
                  display: 'flex',
                  paddingBottom: '32px'
                }}>
                  {isRegistered ? (
                    intl.formatMessage({
                      id: 'pages.login.submit',
                      defaultMessage: '用户登录',
                    })
                  ) : (
                    intl.formatMessage({
                      id: 'pages.login.registerTitle',
                      defaultMessage: '用户注册',
                    })
                  )}
                </span>
                <ProFormText
                  name="account"
                  placeholder={intl.formatMessage({
                    id: 'pages.login.usernamePlaceholder',
                    defaultMessage: '请输入用户名',
                  })}
                  disabled={!isRegistered}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.usernamePlaceholder"
                          defaultMessage="请输入用户名"
                        />
                      ),
                    }
                  ]}
                  fieldProps={{
                    className: 'login-disabled-input',
                    style: {
                      height: '40px',
                      //border: '1px solid rgb(221, 226, 233)',
                      borderRadius: '4px',
                      background: 'rgb(255, 255, 255)',
                      fontSize: '13px',
                      fontWeight: '400',
                      lineHeight: '19px',
                      letterSpacing: '0px',
                      textAlign: 'left'
                    }
                  }}
                />
                <ProFormText.Password
                  name="pass"
                  // fieldProps={{
                  //   size: 'large',
                  //   prefix: <LockOutlined />,
                  // }}
                  placeholder={intl.formatMessage({
                    id: 'pages.login.passwordPlaceholder',
                    defaultMessage: '请输入密码',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.passwordPlaceholder"
                          defaultMessage="请输入密码"
                        />
                      ),
                    },
                    { max: 18, message: intl.formatMessage({ id: 'pages.account.maxlength', defaultMessage: '密码最多18位' }) },
                    {
                      validator: (_, value) => {
                        if (!value || isRegistered) {
                          return Promise.resolve();
                        }
                        // 必须包含大小写字母和数字/符号
                        const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[\d\W]).{1,18}$/;
                        if (!reg.test(value)) {
                          return Promise.reject(
                            intl.formatMessage({
                              id: 'pages.register.password.rule',
                              defaultMessage: '密码需包含大写字母、小写字母和数字/符号，最多18位',
                            })
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  fieldProps={{
                    style: {
                      height: '40px',
                      //border: '1px solid rgb(221, 226, 233)',
                      borderRadius: '4px',
                      background: 'rgb(255, 255, 255)',
                      fontSize: '13px',
                      fontWeight: '400',
                      lineHeight: '19px',
                      letterSpacing: '0px',
                      textAlign: 'left',
                    }

                  }}
                />
              </div>

              {/* {status === 'error' && loginType === 'mobile' && <LoginMessage content="验证码错误" />}
            {type === 'mobile' && (
              <>
                <ProFormText
                  fieldProps={{
                    size: 'large',
                    prefix: <MobileOutlined />,
                  }}
                  name="mobile"
                  placeholder={intl.formatMessage({
                    id: 'pages.login.phoneNumber.placeholder',
                    defaultMessage: '手机号',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.phoneNumber.required"
                          defaultMessage="请输入手机号！"
                        />
                      ),
                    },
                    {
                      pattern: /^1\d{10}$/,
                      message: (
                        <FormattedMessage
                          id="pages.login.phoneNumber.invalid"
                          defaultMessage="手机号格式错误！"
                        />
                      ),
                    },
                  ]}
                />
                <ProFormCaptcha
                  fieldProps={{
                    size: 'large',
                    prefix: <LockOutlined />,
                  }}
                  captchaProps={{
                    size: 'large',
                  }}
                  placeholder={intl.formatMessage({
                    id: 'pages.login.captcha.placeholder',
                    defaultMessage: '请输入验证码',
                  })}
                  captchaTextRender={(timing, count) => {
                    if (timing) {
                      return `${count} ${intl.formatMessage({
                        id: 'pages.getCaptchaSecondText',
                        defaultMessage: '获取验证码',
                      })}`;
                    }
                    return intl.formatMessage({
                      id: 'pages.login.phoneLogin.getVerificationCode',
                      defaultMessage: '获取验证码',
                    });
                  }}
                  name="captcha"
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.captcha.required"
                          defaultMessage="请输入验证码！"
                        />
                      ),
                    },
                  ]}
                  onGetCaptcha={async (phone) => {
                    const result = await getFakeCaptcha({
                      phone,
                    });
                    if (!result) {
                      return;
                    }
                    message.success('获取验证码成功！验证码为：1234');
                  }}
                />
              </>
            )} */}
              {/* <div
              style={{
                marginBottom: 24,
              }}
            >
              <ProFormCheckbox noStyle name="autoLogin">
                <FormattedMessage id="pages.login.rememberMe" defaultMessage="自动登录" />
              </ProFormCheckbox>
              <a
                style={{
                  float: 'right',
                }}
              >
                <FormattedMessage id="pages.login.forgotPassword" defaultMessage="忘记密码" />
              </a>
            </div> */}
              <div
                style={{
                  marginBottom: 60,
                }}
              >
              </div>

            </LoginForm>

          </div>
        </div >
        <Footer />
      </ConfigProvider>
    </div >
  );
};

export default Login;
