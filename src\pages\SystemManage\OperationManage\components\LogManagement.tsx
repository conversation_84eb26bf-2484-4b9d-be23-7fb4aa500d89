import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, Typography, Space, Tabs, Input, Select, message, Tooltip, Row, Col, Pagination, Alert, Spin } from 'antd';
import { DownloadOutlined, EyeOutlined, PlayCircleOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { useIntl } from 'umi';
import { getLogFiles, getRealTimeLog, downloadLog, getLogsByPage, getLogFileInfo } from '@/services/ant-design-pro/api';
import '../index.less';

const { Text } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

interface LogManagementProps {
  // 组件现在完全独立，不需要外部props
}

const LogManagement: React.FC<LogManagementProps> = () => {
  const [realTimeLog, setRealTimeLog] = useState<string[]>([]);
  const [logLoading, setLogLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('realtime');
  const [isAutoRefresh, setIsAutoRefresh] = useState(false);
  const [logLevel, setLogLevel] = useState<string>('ALL');
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const logContainerRef = useRef<HTMLDivElement>(null);
  const intl = useIntl();

  // 日志文件列表相关状态
  const [logFiles, setLogFiles] = useState<string[]>([]);
  const [filteredLogFiles, setFilteredLogFiles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [fileSearchKeyword, setFileSearchKeyword] = useState('');

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [totalLines, setTotalLines] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPrevPage, setHasPrevPage] = useState(false);

  // 文件信息
  const [fileInfo, setFileInfo] = useState<any>(null);

  /**
   * 加载日志文件列表
   */
  const loadLogFiles = useCallback(async () => {
    setLoading(true);
    try {
      const rs = await getLogFiles();
      if (rs.code === 0 && rs.data) {
        const files = rs.data.map((file: string) => file.replace(/"/g, ''));
        setLogFiles(files);
        setFilteredLogFiles(files);
        setFileSearchKeyword(''); // 重置搜索关键词
      }
    } catch (error) {
      console.error('加载日志文件失败', error);
      message.error('加载日志文件失败，请稍后重试');
    }
    setLoading(false);
  }, [selectedFile, setSelectedFile]);

  /**
   * 加载分页日志
   */
  const loadPagedLogs = useCallback(async (fileName: string, page: number = 1, size: number = 100) => {
    setLogLoading(true);
    try {
      const response = await getLogsByPage({
        fileName,
        page,
        size,
        level: logLevel !== 'ALL' ? logLevel : undefined,
        keyword: searchKeyword || undefined
      });

      if (response.code === 0 && response.data) {
        setRealTimeLog(response.data.lines || []);
        setCurrentPage(response.data.page || 1);
        setTotalLines(response.data.total || 0);
        setHasNextPage(response.data.hasNext || false);
        setHasPrevPage(response.data.hasPrev || false);
      } else {
        message.error(response.message || '加载日志失败');
      }
    } catch (error) {
      console.error('加载分页日志失败:', error);
      message.error('加载日志失败');
    } finally {
      setLogLoading(false);
    }
  }, [logLevel, searchKeyword]);

  /**
   * 获取文件信息
   */
  const loadFileInfo = useCallback(async (fileName: string) => {
    try {
      const response = await getLogFileInfo(fileName);
      if (response.code === 0 && response.data) {
        console.log('文件信息:', response.data);
        setFileInfo(response.data);
      }
    } catch (error) {
      console.error('获取文件信息失败:', error);
    }
  }, []);

  /**
   * 防抖的日志更新函数，减少界面闪烁
   */
  const debouncedSetRealTimeLog = useCallback((newLogs: string[]) => {
    setRealTimeLog(prevLogs => {
      // 快速检查：如果长度相同且最后一条相同，则认为是相同数据
      if (prevLogs.length === newLogs.length &&
        prevLogs[prevLogs.length - 1] === newLogs[newLogs.length - 1]) {
        return prevLogs;
      }

      // 深度检查：内容是否相同
      const prevHash = prevLogs.join('\n');
      const newHash = newLogs.join('\n');
      if (prevHash === newHash) {
        return prevLogs;
      }

      return newLogs;
    });
  }, []);

  /**
   * 加载实时日志 - 无感更新版本
   */
  const loadRealTimeLog = useCallback(async (fileName: string) => {
    try {
      const response = await getRealTimeLog(fileName);
      if (response.code === 0 && response.data) {
        debouncedSetRealTimeLog(response.data || []);
        await loadFileInfo(fileName);
      }
    } catch (error) {
      console.error('加载实时日志失败:', error);
    }
  }, [loadFileInfo, debouncedSetRealTimeLog]);

  /**
   * 下载日志文件
   */
  const handleDownloadLog = async (fileName: string) => {
    try {
      const result = await downloadLog(fileName);
      if (result.success) {
        message.success('日志文件下载成功');
      } else {
        message.error(result.message || '下载失败');
      }
    } catch (error) {
      console.error('下载日志文件失败:', error);
      message.error('下载失败，请重试');
    }
  };

  /**
   * 处理Tab切换
   */
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  /**
   * 过滤日志内容
   */
  const filterLogs = (logs: string[]) => {
    let filteredLogs = logs;

    // 按日志级别过滤
    if (logLevel !== 'ALL') {
      filteredLogs = filteredLogs.filter(log =>
        log.toLowerCase().includes(logLevel.toLowerCase())
      );
    }

    // 按关键字搜索
    if (searchKeyword) {
      filteredLogs = filteredLogs.filter(log =>
        log.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }

    return filteredLogs;
  };

  /**
   * 自动滚动到最新日志
   */
  useEffect(() => {
    if (logContainerRef.current && activeTab === 'realtime') {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [realTimeLog, activeTab]);

  /**
   * 实时日志自动刷新 - 优化频率和稳定性
   */
  useEffect(() => {
    let interval: NodeJS.Timeout;
    let lastContentHash = '';

    const checkForUpdates = async () => {
      if (!selectedFile) return;

      try {
        const response = await getRealTimeLog(selectedFile);
        if (response.code === 0 && response.data) {
          const newContent = response.data || [];
          const contentHash = newContent.join('\n');

          // 只有当内容真正变化时才更新
          if (contentHash !== lastContentHash) {
            debouncedSetRealTimeLog(newContent);
            lastContentHash = contentHash;
          }
        }
      } catch (error) {
        console.error('实时日志检查失败:', error);
      }
    };

    if (activeTab === 'realtime' && selectedFile && isAutoRefresh) {
      // 立即检查一次
      checkForUpdates();

      // 设置更智能的定时检查（从3秒改为5秒，减少频率）
      interval = setInterval(checkForUpdates, 5000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [activeTab, selectedFile, isAutoRefresh, debouncedSetRealTimeLog]);

  /**
   * 当activeTab切换时加载文件信息
   */
  useEffect(() => {
    if (selectedFile) {
      loadFileInfo(selectedFile);
    }
  }, [selectedFile, activeTab, loadFileInfo]);

  /**
   * 当搜索条件改变时，重新加载分页数据
   */
  useEffect(() => {
    if (activeTab === 'paged' && selectedFile) {
      setCurrentPage(1); // 重置到第一页
      loadPagedLogs(selectedFile, 1, pageSize);
    }
  }, [logLevel, searchKeyword, activeTab, selectedFile, pageSize, loadPagedLogs]);

  /**
   * 组件挂载时加载日志文件列表
   */
  useEffect(() => {
    loadLogFiles();
  }, []);


  return (
    <Row gutter={16} style={{ marginTop: 16 }}>
      <Col span={4}>
        <div className="log-files-submenu">
          <div className="submenu-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
            <Text strong>日志文件</Text>
            <Button
                type="text"
                icon={<ReloadOutlined />}
                size="small"
                onClick={loadLogFiles}
                loading={loading}
                title="刷新日志文件列表"
              />
          </div>
          <div style={{ marginBottom: 8 }}>
              <Input.Search
                 placeholder="搜索文件名"
                 allowClear
                 size="small"
                 value={fileSearchKeyword}
                 onChange={(e) => {
                   const keyword = e.target.value.toLowerCase();
                   setFileSearchKeyword(keyword);
                   if (keyword.trim()) {
                     setFilteredLogFiles(logFiles.filter(f => f.toLowerCase().includes(keyword.trim())));
                   } else {
                     setFilteredLogFiles(logFiles);
                   }
                 }}
               />
            </div>

          {loading ? (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <Text type="secondary">加载中...</Text>
            </div>
          ) : filteredLogFiles.length > 0 ? (
            <div className="log-file-list" style={{ maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' }}>
              {filteredLogFiles.map((item) => (
                <div
                  key={item}
                  className={`log-file-item ${selectedFile === item ? 'active' : ''}`}
                  onClick={() => {
                    setSelectedFile(item);
                    if (activeTab === 'realtime') {
                      loadRealTimeLog(item);
                    } else if (activeTab === 'paged') {
                      loadPagedLogs(item, 1, pageSize);
                    }
                  }}
                >
                  <div className="log-file-icon">
                    <Text style={{ fontSize: '20px' }}>📄</Text>
                  </div>
                  <div className="log-file-info">
                    <Tooltip title={item} placement="right">
                      <Text className="log-file-name" ellipsis>
                        {item}
                      </Text>
                    </Tooltip>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      日志文件
                    </Text>
                  </div>
                  {selectedFile === item && (
                    <div className="log-file-indicator">
                      <div className="active-indicator"></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <Text type="secondary">{fileSearchKeyword ? '未找到匹配的日志文件' : '暂无日志文件'}</Text>
              </div>
            )}
        </div>
      </Col>

      <Col span={20}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="实时日志" key="realtime">
            <div className="log-controls">
              <Space style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => selectedFile && loadRealTimeLog(selectedFile)}
                  disabled={!selectedFile}
                >
                  刷新
                </Button>
                <Button
                  type={isAutoRefresh ? 'primary' : 'default'}
                  icon={<PlayCircleOutlined />}
                  onClick={() => setIsAutoRefresh(!isAutoRefresh)}
                  disabled={!selectedFile}
                >
                  {isAutoRefresh ? '停止自动刷新' : '开始自动刷新'}
                </Button>
                <Select
                  value={logLevel}
                  onChange={setLogLevel}
                  style={{ width: 120 }}
                  placeholder="日志级别"
                >
                  <Option value="ALL">全部</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="WARN">WARN</Option>
                  <Option value="ERROR">ERROR</Option>
                  <Option value="DEBUG">DEBUG</Option>
                </Select>
                <Search
                  placeholder="搜索关键字"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
              </Space>
            </div>

            <Card
              title={
                <Space>
                  {intl.formatMessage({ id: 'pages.operation.realTimeLog', defaultMessage: '实时日志' })}
                  {selectedFile && (
                    <Text code style={{ marginLeft: 8 }}>{selectedFile}</Text>
                  )}

                  {fileInfo && (
                    <Tooltip title={`文件大小: ${(fileInfo.size / 1024 / 1024).toFixed(2)}MB | 总行数: ${fileInfo.lineCount}`}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {fileInfo.lineCount} 行
                      </Text>
                    </Tooltip>
                  )}
                </Space>
              }
              size="small"
              loading={logLoading}
              className="log-viewer-card"
            >
              <div className="log-content" ref={logContainerRef}>
                {selectedFile ? (
                  realTimeLog.length > 0 ? (
                    <div className="log-lines">
                      {filterLogs(realTimeLog).map((line, index) => (
                        <div key={index} className="log-line">
                          <Text style={{ color: '#fff', fontSize: 12, fontFamily: 'Monaco, Consolas, monospace' }}>
                            {line}
                          </Text>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                      {intl.formatMessage({ id: 'pages.operation.noLogContent', defaultMessage: '暂无日志内容' })}
                    </div>
                  )
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                    请先选择左侧的日志文件
                  </div>
                )}
              </div>
            </Card>
          </TabPane>

          <TabPane tab="分页日志" key="paged">
            <div className="log-controls">
              <Space style={{ marginBottom: 16 }}>
                <Select
                  value={logLevel}
                  onChange={setLogLevel}
                  style={{ width: 120 }}
                  placeholder="日志级别"
                >
                  <Option value="ALL">全部</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="WARN">WARN</Option>
                  <Option value="ERROR">ERROR</Option>
                  <Option value="DEBUG">DEBUG</Option>
                </Select>
                <Search
                  placeholder="搜索关键字"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => selectedFile && loadPagedLogs(selectedFile, currentPage, pageSize)}
                  disabled={!selectedFile}
                >
                  刷新
                </Button>
              </Space>
            </div>

            <Card
              title={
                <Space>
                  分页日志
                  {selectedFile && (
                    <Text code style={{ marginLeft: 8 }}>{selectedFile}</Text>
                  )}
                  {fileInfo && (
                    <Tooltip title={`文件大小: ${(fileInfo.size / 1024 / 1024).toFixed(2)}MB | 总行数: ${fileInfo.lineCount}`}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        总计 {fileInfo.lineCount} 行
                      </Text>
                    </Tooltip>
                  )}
                </Space>
              }
              size="small"
              loading={logLoading}
              className="log-viewer-card"
              extra={
                totalLines > 0 && (
                  <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={totalLines}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => `第 ${range[0]}-${range[1]} 行，共 ${total} 行`}
                    onChange={(page, size) => {
                      setCurrentPage(page);
                      setPageSize(size || pageSize);
                      selectedFile && loadPagedLogs(selectedFile, page, size || pageSize);
                    }}
                    pageSizeOptions={['50', '100', '200', '500']}
                    size="small"
                  />
                )
              }
            >
              <div className="log-content" ref={logContainerRef}>
                {realTimeLog.length > 0 ? (
                  realTimeLog.map((line, index) => (
                    <div key={`${currentPage}-${index}`} className="log-line">
                      <span className="log-line-number">{(currentPage - 1) * pageSize + index + 1}</span>
                      <span className="log-line-content">{line}</span>
                    </div>
                  ))
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                    {selectedFile ? '暂无日志数据' : '请先选择左侧的日志文件'}
                  </div>
                )}
              </div>
            </Card>
          </TabPane>

          <TabPane tab="日志下载" key="download">
            <Card
              title={intl.formatMessage({ id: 'pages.operation.logDownload', defaultMessage: '日志下载' })}
              size="small"
              className="log-download-card"
            >
              {selectedFile ? (
                <div>
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <Alert
                        message="文件信息"
                        description={
                          <div>
                            <p><strong>文件名：</strong><Text code>{selectedFile}</Text></p>
                            {fileInfo && (
                              <>
                                <p><strong>文件大小：</strong>{(fileInfo.size / 1024 / 1024).toFixed(2)} MB</p>
                                <p><strong>总行数：</strong>{fileInfo.lineCount.toLocaleString()} 行</p>
                                <p><strong>最后修改：</strong>{new Date(fileInfo.lastModified).toLocaleString()}</p>
                              </>
                            )}
                          </div>
                        }
                        type="info"
                        showIcon
                      />
                    </Col>
                    <Col span={24}>
                      <Space>
                        <Button
                          type="primary"
                          icon={<DownloadOutlined />}
                          onClick={() => handleDownloadLog(selectedFile)}
                          size="large"
                        >
                          下载完整日志文件
                        </Button>
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => {
                            setActiveTab('paged');
                            loadPagedLogs(selectedFile, 1, pageSize);
                          }}
                        >
                          查看日志内容
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  请先选择左侧的日志文件进行下载
                </div>
              )}
            </Card>
          </TabPane>
        </Tabs>
      </Col>
    </Row>
  );
};

export default LogManagement;