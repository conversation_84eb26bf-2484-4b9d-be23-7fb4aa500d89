export default {
    'pages.login.success': "ورود موفقیت‌آمیز بود!",
    'pages.login.failure': "ورود ناموفق بود!",
    'pages.login.retry': "ورود ناموفق بود، لطفاً دوباره امتحان کنید!",
    'pages.login.title': "صفحه ورود",
    'pages.login.welcome': "به Mars Wisdom خوش آمدید",
    'pages.login.agreementPrefix': "شما Mars Wisdom را خوانده و با آن موافقت کرده‌اید",
    'pages.login.terms': "شرایط استفاده کاربر",
    'pages.login.and': "و",
    'pages.login.privacy': "سیاست حفظ حریم خصوصی",
    'pages.login.submit': "ورود کاربر",
    'pages.login.usernamePlaceholder': "لطفاً نام کاربری خود را وارد کنید",
    'pages.login.passwordPlaceholder': "لطفاً رمز عبور خود را وارد کنید",
    'pages.search.sortByTime': "بر اساس آخرین زمان",
    'pages.search.sortBySimilarity': "بر اساس شباهت",
    'pages.search.history': "تاریخچه",
    'pages.search.searchHistory': "تاریخچه جستجو",
    'pages.search.delete': "حذف",
    'pages.search.hotSearch': "جستجوهای داغ",
    'pages.search.example1': "پسر کنار وسیله نقلیه موتوری دم در",
    'pages.search.example2': "فردی با لباس قرمز در حال تماس تلفنی در آسانسور",
    'pages.search.example3': "فردی با کلاه آبی در غرفه پارکینگ",
    'pages.search.assistHint': "من می‌توانم به شما در جستجوی اطلاعات کمک کنم، مانند: پسری با لباس مشکی در کنار کابینت غذای بیرون‌بر در طبقه اول پارک",
    'pages.search.searchButton': "جستجو",
    'pages.search.filter': "شرایط جستجو",
    'pages.search.loading': "در حال بارگیری...",
    'pages.search.eventDetail': "جزئیات رویداد",
    'pages.search.videoPlayback': "پخش ویدیو",
    'pages.search.panorama': "پانوراما",
    'pages.search.description': "توضیحات",
    'pages.search.time': "زمان",
    'pages.search.location': "مکان",
    'pages.search.similarity': "شباهت",
    'pages.pointManage.voiceInput': "ورودی صوتی",
    'pages.pointManage.speechRecognition': "تشخیص صدا",
    'pages.pointManage.stopVoiceInput': "توقف ورودی صوتی",
    'pages.pointManage.operationSuccess': "عملیات موفقیت‌آمیز بود",
    'pages.pointManage.preview': "پیش‌نمایش",
    'pages.pointManage.edit': "ویرایش",
    'pages.pointManage.alreadyMonitored': "کنترل مستقر شد",
    'pages.pointManage.monitor': "کنترل مستقر شد",
    'pages.pointManage.delete': "حذف",
    'pages.pointManage.deleteRecord': "حذف رکورد",
    'pages.pointManage.confirmDeleteRecord': "آیا مطمئنید که رکورد را حذف می‌کنید؟",
    'pages.pointManage.confirm': "تایید",
    'pages.pointManage.cancel': "لغو",
    'pages.pointManage.deleting': "حذف",
    'pages.pointManage.deleteSuccess': "حذف موفق، به‌روزرسانی خودکار",
    'pages.pointManage.deleteFailure': "حذف ناموفق بود، لطفاً دوباره امتحان کنید",
    'pages.pointManage.person': "شخص",
    'pages.pointManage.motorVehicle': "وسیله نقلیه موتوری",
    'pages.pointManage.nonMotorVehicle': "وسیله نقلیه غیر موتوری",
    'pages.pointManage.pointName': "نام نقطه",
    'pages.pointManage.protocolType': "نوع پروتکل",
    'pages.pointManage.captureType': "نوع عکس فوری",
    'pages.pointManage.captureInterval': "فاصله زمانی عکس فوری",
    'pages.pointManage.deviceName': "نام دستگاه",
    'pages.pointManage.deviceIP': "IP دستگاه",
    'pages.pointManage.devicePort': "پورت دستگاه",
    'pages.pointManage.operation': "عملیات",
    'pages.pointManage.pointManagement': "مدیریت نقطه",
    'pages.pointManage.add': "اضافه کردن",
    'pages.pointManage.selected': "انتخاب شده",
    'pages.pointManage.batchDelete': "حذف دسته‌ای",
    'pages.pointManage.item': "مورد",
    'pages.pointManage.inputCaptureInterval': "لطفاً فاصله زمانی عکس فوری را وارد کنید",
    'pages.pointManage.inputPointName': "لطفاً نام نقطه را وارد کنید",
    'pages.pointManage.selectCaptureType': "لطفاً نوع ضبط را انتخاب کنید",
    'pages.pointManage.addDevice': "افزودن دستگاه",
    'pages.pointManage.protocol': "پروتکل",
    'pages.pointManage.deviceCode': "کد دستگاه",
    'pages.pointManage.inputDeviceName': "لطفاً نام دستگاه را وارد کنید",
    'pages.pointManage.inputDeviceCode': "لطفاً کد دستگاه را وارد کنید",
    'pages.pointManage.port': "پورت",
    'pages.pointManage.username': "نام کاربری",
    'pages.pointManage.password': "رمز عبور",
    'pages.pointManage.mainStream': "جریان اصلی",
    'pages.pointManage.example': "مثال",
    'pages.pointManage.subStream': "جریان فرعی",
    'pages.pointManage.addPoint': "افزودن نقطه",
    'pages.pointManage.validIP': "لطفاً یک آدرس IP معتبر وارد کنید",
    'pages.pointManage.noData': "داده‌ای وجود ندارد",
    'pages.pointManage.selectPoint': "لطفاً یک نقطه انتخاب کنید",
    'pages.pointManage.addSuccess': "با موفقیت اضافه شد",
    'pages.pointManage.prevStep': "مرحله قبل",
    'pages.pointManage.nextStep': "مرحله بعد",
    'pages.pointManage.monitorSuccess': "استقرار کنترل موفقیت‌آمیز",
    'pages.pointManage.monitorFailure': "استقرار کنترل ناموفق",
    'pages.pointManage.cancelSuccess': "لغو موفقیت‌آمیز",
    'pages.pointManage.operationFailure': "عملیات ناموفق",
    'pages.pointManage.monitor': "استقرار کنترل",
    'pages.pointManage.monitorEvent': "رویداد استقرار کنترل",
    'pages.pointManage.startMonitor': "شروع استقرار کنترل",
    'pages.pointManage.monitorRecord': "رکورد استقرار کنترل",
    'pages.pointManage.noEventRecord': "بدون رکورد رویداد",
    'pages.pointManage.charLimitExceeded': "طول کاراکتر از حد مجاز فراتر رفته است، حداکثر طول:",
    'pages.pointManage.eventType': "نوع رویداد",
    'pages.pointManage.prompt': "کلمه راهنما",
    'pages.pointManage.createTime': "زمان ایجاد",
    'pages.pointManage.editSuccess': "ویرایش با موفقیت انجام شد",
    'pages.pointManage.monitorRule': "قانون نظارت",
    'pages.pointManage.selectEventType': "لطفاً نوع رویداد را انتخاب کنید",
    'pages.pointManage.inputPrompt': "لطفاً کلمه راهنما را وارد کنید",
    'pages.pointManage.eventRecord': "سابقه رویداد",
    'pages.pointManage.paginationInfo': "{range0} - {range1} مورد از مجموع {total} مورد",
    'pages.pointManage.detail': "جزئیات",
    'pages.pointManage.eventImage': "تصویر رویداد",
    'pages.pointManage.pointInfo': "اطلاعات نقطه",
    'pages.pointManage.eventTime': "زمان رویداد",
    'pages.config.name': "نام",
    'pages.config.creationDate': "تاریخ ایجاد",
    'pages.config.expirationTime': "زمان انقضا",
    'pages.config.editApiKey': "ویرایش کلید API",
    'pages.config.editSuccess': "ویرایش موفقیت‌آمیز، تازه‌سازی خودکار",
    'pages.config.createSuccess': "ایجاد موفقیت‌آمیز، تازه‌سازی خودکار",
    'pages.config.editFailure': "ویرایش ناموفق، لطفاً دوباره تلاش کنید",
    'pages.config.createFailure': "ایجاد ناموفق، لطفاً دوباره تلاش کنید",
    'pages.config.createApiKey': "ایجاد کلید API",
    'pages.config.authManagement': "مدیریت مجوزها",
    'pages.config.eventCode': "کد رویداد",
    'pages.config.paramConfigFailure': "دریافت پیکربندی پارامتر ناموفق بود",
    'pages.config.saveFailure': "ذخیره‌سازی ناموفق",
    'pages.config.saveSuccess': "ذخیره‌سازی موفقیت‌آمیز",
    'pages.config.save': "ذخیره‌سازی",
    'pages.config.reset': "بازنشانی",
    'pages.config.streamConfig': "پیکربندی جریان رسانه‌ای",
    'pages.config.streamServiceUrl': "آدرس سرویس جریان رسانه‌ای",
    'pages.config.secretKey': "کلید مخفی",
    'pages.config.configuration': "پیکربندی",
    'pages.config.workspaceId': "شناسه فضای کاری",
    'pages.config.multiSearchStrategy': "استراتژی جستجوی چندبعدی",
    'pages.config.dataCleanStrategy': "استراتژی پاک‌سازی داده",
    'pages.config.objectDetection': "تشخیص اشیاء",
    'pages.config.enableObjectDetection': "فعال‌سازی تشخیص اشیاء؟",
    'pages.config.allowObjectWhitelist': "اجازه دادن به لیست سفید تشخیص اشیاء",
    'pages.config.sedan': "سدان",
    'pages.config.bus': "اتوبوس",
    'pages.config.truck': "کامیون",
    'pages.config.bicycle': "دوچرخه",
    'pages.config.motorcycle': "موتورسیکلت",
    'pages.config.enableImageDupCheck': "فعال‌سازی تشخیص تصویر تکراری؟",
    'pages.config.intervalSeconds': "زمان فاصله (ثانیه)",
    'pages.config.minScoreLimitError': "مقدار حداقل امتیاز نمی‌تواند بیش از 1 باشد",
    'pages.config.initialSearchStrategy': "استراتژی جستجوی غربالگری اولیه",
    'pages.config.enhancedSearchStrategy': "استراتژی جستجوی پیشرفته چندبعدی",
    'pages.config.multiInitialSearch': "غربالگری اولیه چندبعدی",
    'pages.config.minScore': "مقدار حداقل امتیاز",
    'pages.config.maxResultSet': "مجموعه نتایج حداکثر",
    'pages.config.topValue': "مقدار بالا",
    'pages.config.reRanking': "رتبه‌بندی مجدد چندبعدی",
    'pages.config.batchSize': "اندازه دسته",
    'pages.config.fineSearch': "غربالگری دقیق چندبعدی",
    'pages.config.fineSearchStrategy': "استراتژی جستجوی غربالگری دقیق",
    'pages.config.enableReId': "فعال‌سازی ReId؟",
    'pages.config.objectMinScore': "حداقل امتیاز تشخیص شیء",
    'pages.config.vectorMinScore': "حداقل امتیاز شباهت برداری",
    'pages.config.maxResultsPerSearch': "حداکثر نتایج در هر جستجو",
    'pages.common.logout': "خروج از حساب",
    'pages.agent.apiCallFailed': "فراخوانی رابط ناموفق بود، لطفاً بعداً دوباره امتحان کنید",
    'pages.agent.hello': "سلام، من",
    'pages.agent.agent': "عامل هستم",
    'pages.agent.attachment': "پیوست",
    'pages.agent.dropFilesHere': "فایل را اینجا قرار دهید",
    'pages.agent.uploadFile': "فایل را آپلود کنید",
    'pages.agent.clickOrDragToUpload': "برای آپلود، روی فایل کلیک کنید یا آن را به این قسمت بکشید",
    'pages.agent.shiftEnterNewline': "برای تغییر خط، Shift+Enter را فشار دهید",
    'pages.agent.basicConfig': "پیکربندی پایه",
    'pages.agent.llmModel': "مدل LLM استفاده شده",
    'pages.agent.doubaoModel': "مدل Bean bag",
    'pages.agent.selectAnOption': "لطفاً یک گزینه را انتخاب کنید",
    'pages.agent.memoryMessageCount': "تعداد پیام‌های ذخیره شده",
    'pages.agent.skillConfig': "پیکربندی مهارت",
    'pages.agent.toolSet': "مجموعه ابزارها",
    'pages.agent.toolSetDescription': "مجموعه ابزارها به عامل امکان می‌دهد ابزارهای خارجی را فراخوانی کند و مرز توانایی عامل را گسترش دهد.",
    'pages.agent.knowledgeBase': "پایگاه دانش",
    'pages.agent.knowledgeBaseDescription': "وقتی کاربر پیامی ارسال می‌کند، عامل می‌تواند برای پاسخ به سوال کاربر به محتوای پایگاه دانش مراجعه کند.",
    'pages.agent.workflow': "گردش کار",
    'pages.agent.workflowDescription': "برای مدیریت جریان‌های کاری با منطق پیچیده و مراحل زیاد استفاده می‌شود.",
    'pages.agent.describePersona': "لطفا شخصیت و عملکردها را شرح دهید",
    'pages.agent.publishSuccess': "انتشار با موفقیت",
    'pages.agent.publishFailed': "انتشار ناموفق",
    'pages.agent.publishNotAllowed': "متاسفیم، عامل قابل انتشار نیست",
    'pages.agent.config': "پیکربندی عامل",
    'pages.agent.publish': "انتشار",
    'pages.agent.modelCapabilityConfig': "پیکربندی قابلیت مدل",
    'pages.agent.promptDev': "توسعه سریع کلمه",
    'pages.agent.debug': "اشکال‌زدایی عامل",
    'pages.agent.create': "ایجاد عامل",
    'pages.agent.submitFailed': "ارسال ناموفق بود، لطفا داده‌های فرم را بررسی کنید",
    'pages.agent.name': "نام عامل",
    'pages.agent.nameLimit': "تا 64 کاراکتر می‌توان وارد کرد",
    'pages.agent.description': "معرفی تابع عامل",
    'pages.agent.descriptionTip': "عملکردهای عامل را معرفی کنید و به کاربر عامل نمایش داده می‌شود",
    'pages.agent.icon': "آیکون",
    'pages.agent.imageOnly': "فقط فایل‌های تصویری قابل آپلود هستند",
    'pages.agent.imageSizeLimit': "اندازه تصویر نمی‌تواند از 2 مگابایت بیشتر باشد",
    'pages.agent.imageFormatLimit': "پشتیبانی از فرمت jpg/png، اندازه از 2 مگابایت بیشتر نیست",
    'pages.agent.flagship': "پرچمدار",
    'pages.agent.highSpeed': "سرعت بالا",
    'pages.agent.toolInvocation': "فراخوانی ابزار",
    'pages.agent.rolePlay': "نقش‌آفرینی",
    'pages.agent.longText': "متن طولانی",
    'pages.agent.imageUnderstanding': "درک تصویر",
    'pages.agent.reasoning': "توانایی استدلال",
    'pages.agent.videoUnderstanding': "درک ویدیو",
    'pages.agent.costPerformance': "مقرون به صرفه بودن",
    'pages.agent.codeExpert': "تخصص کد",
    'pages.agent.audioUnderstanding': "درک صدا",
    'pages.agent.visualAnalysis': "تحلیل بصری",
    'pages.agent.running': "در حال اجرا",
    'pages.agent.queuing': "صف‌بندی",
    'pages.agent.training': "آموزش",
    'pages.agent.trainingFailed': "آموزش ناموفق",
    'pages.agent.text': "متن",
    'pages.agent.multimodal': "چندوجهی",
    'pages.agent.landongModel': "مدل Lazy hole",
    'pages.agent.searchModelName': "جستجوی نام مدل",
    'pages.agent.quotaTrial': "تجربه محدود",
    'pages.agent.comingOffline': "به زودی آفلاین می‌شود",
    'pages.agent.newModelExperience': "تجربه مدل جدید",
    'pages.agent.advancedModel': "پیشرفته مدل",
    'pages.agent.generalModel': "مدل عمومی",
    'pages.agent.modelType': "نوع مدل",
    'pages.agent.modelFeature': "ویژگی‌های مدل",
    'pages.agent.modelProvider': "سازنده مدل",
    'pages.agent.modelSupportedFunctions': "توابع پشتیبانی مدل",
    'pages.agent.contextLength': "طول متن",
    'pages.agent.userRights': "حقوق کاربر",
    'pages.agent.creator': "ایجادکننده",
    'pages.agent.creationTime': "زمان ایجاد",
    'pages.agent.describeFunction': "لطفا شخصیت و عملکرد را شرح دهید",
    'pages.agent.orchestration': "ارکستراسیون",
    'pages.agent.functionIntroduction': "معرفی عملکرد",
    'pages.agent.publishStatus': "وضعیت انتشار",
    'pages.agent.agentDisplay': "نمایش عامل",
    'pages.agent.modelStatus': "وضعیت مدل",
    'pages.search.expandir': "باز کردن",
    'pages.search.retirar': "بستن",
    'pages.search.deleteConfirmWarning': "پس از حذف، قابل بازیابی نیست. آیا مطمئن هستید که می‌خواهید آن را حذف کنید؟",
    'pages.config.applicationId': "شناسه برنامه",
    'pages.config.imageDeduplication': "حذف داده‌های تکراری از تصویر",
    'pages.pointManage.loadingMessage': "در حال بارگیری، لطفاً صفحه را رفرش نکنید",
    'pages.pointManage.fetchError': "زمان دریافت امتیاز تمام شد، لطفاً بررسی کنید که آیا دستگاه آنلاین است یا خیر.",
    'pages.pointManage.deviceTimeout': "زمان درخواست دستگاه تمام شد.",
    'pages.pointManage.streamConnectFailed': "اتصال سرویس پخش جریانی ناموفق بود.",
    'pages.pointManage.serviceException': "اختلال در سرویس، لطفاً بعداً دوباره امتحان کنید.",
    'pages.pointManage.deleteHasControlRule': "نقطه انتخاب شده در حال نظارت است و قابل حذف نیست.",
    'pages.pointManage.online': "آنلاین",
    'pages.pointManage.offline': "آفلاین",
    'pages.pointManage.confirmDeleteEventType': "آیا مطمئن هستید که این نوع رویداد را حذف می‌کنید؟",
    'pages.pointManage.captureIntervalRange': "فاصله زمانی ثبت بین ۱ تا ۳۶۰۰ ثانیه است.",
    'pages.pointManage.status': "ایالت",
    'pages.login.terms.title': "شرایط استفاده از خدمات",
    'pages.login.terms.check': "لطفاً ابتدا شرایط استفاده از خدمات را مطالعه و قبول کنید.",
    'pages.pointManage.confirmDeletePoint': "آیا از حذف اطلاعات نقطه‌ای مطمئن هستید؟",
    'pages.pointManage.pointNameRequired': "برخی از نام‌های نقطه وارد نشده‌اند. لطفاً قبل از ارسال، آن‌ها را تکمیل کنید.",
    'pages.pointManage.refresh': "بازآوری",
    'pages.account.updateSuc': "با موفقیت اصلاح شد",
    'pages.account.updatePwd': "تغییر رمز عبور",
    'pages.account.oldPassword': "رمز قدیمی",
    'pages.account.newPassword': "رمز عبور جدید",
    'pages.account.confirmPwd': "رمز عبور را تایید کنید",
    'pages.account.passwordmatch': "رمز عبور جدیدی که وارد کردید مطابقت ندارد",
    'pages.password.reset.fail': "بازنشانی رمز عبور ناموفق بود",
    'pages.password.reset.success': "بازنشانی رمز عبور با موفقیت انجام شد",
    'pages.password.update': "تغییر رمز عبور",
    'pages.register.success': "ثبت‌نام با موفقیت انجام شد، لطفاً وارد شوید",
    'pages.register.fail': "ثبت‌نام ناموفق بود",
    'pages.login.fail': "نام کاربری یا رمز عبور اشتباه است",
    'pages.login.needRegister': "لطفاً ابتدا ثبت‌نام کنید",
    'pages.system.check.fail': "بررسی سرویس ناموفق بود",
    'pages.account.maxlength': "رمز عبور می‌تواند حداکثر ۱۸ نویسه باشد",
    'pages.login.login': "ورود",
    'pages.login.register': "ثبت‌نام",
    'pages.login.registerTitle': "ثبت‌نام کاربر",
    'pages.search.similarity': "شباهت",
    'pages.common.sessionExpired': "نشست شما منقضی شده است، لطفاً دوباره وارد شوید.",
    'pages.primaryKey.id': "شناسه کلید اصلی",
    'pages.agent.type': "نوع",
    'pages.agent.type.placeholder': "لطفا نوع جسم هوشمند را انتخاب کنید",
    'pages.agent.type.required': "لطفا نوع را انتخاب کنید",
    'pages.agent.id': "هوشمند ID",
    'pages.agent.id.placeholder': "لطفا ID هوشمند را وارد کنید",
    'pages.agent.id.required': "لطفا ID هوشمند را وارد کنید",
    'pages.agent.suggestedQuestions': "می توانید از من این سوال را بپرسید:",
    'pages.agent.botId.tip': "لطفاً به پلتفرم مربوطه (مانند Coze، Dify) بروید و یک هوشمند ایجاد کنید و ID آن را کپی کنید و در اینجا چسبانید.",
    'pages.agent.apiKey.tip': "به پلتفرم Dify بروید و کلید API خود را کپی کنید و در اینجا چسبانید",
    'pages.agent.apiKey.required': "کلید API مورد نیاز است",
}
