(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[832],{47356:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};a.default=e},44149:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};a.default=e},97832:function(o,a,e){"use strict";e.d(a,{m:function(){return G}});var r=e(4942),c=e(97685),t=e(1413),i=e(3770),s=e.n(i),f=e(77059),v=e.n(f),C=e(85673),I=e(7134),j=e(42075),T=e(21532),M=e(93967),V=e.n(M),H=e(9220),W=e(80334),k=e(67294),Z=e(98082),_=function(){return{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}},S=function(l){var b;return(0,r.Z)({},l.componentCls,(0,t.Z)((0,t.Z)({},Z.Wf===null||Z.Wf===void 0?void 0:(0,Z.Wf)(l)),{},(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({position:"relative",backgroundColor:l.colorWhite,paddingBlock:l.pageHeaderPaddingVertical+2,paddingInline:l.pageHeaderPadding,"&&-ghost":{backgroundColor:l.pageHeaderBgGhost},"&-no-children":{height:(b=l.layout)===null||b===void 0||(b=b.pageContainer)===null||b===void 0?void 0:b.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:l.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,r.Z)({marginInlineEnd:l.margin,fontSize:16,lineHeight:1,"&-button":(0,t.Z)((0,t.Z)({fontSize:16},Z.Nd===null||Z.Nd===void 0?void 0:(0,Z.Nd)(l)),{},{color:l.pageHeaderColorBack,cursor:"pointer"})},"".concat(l.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:l.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:l.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:l.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,t.Z)((0,t.Z)({marginInlineEnd:l.marginSM,marginBlockEnd:0,color:l.colorTextHeading,fontWeight:600,fontSize:l.pageHeaderFontSizeHeaderTitle,lineHeight:l.controlHeight+"px"},_()),{},(0,r.Z)({},"".concat(l.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:l.marginSM})),"&-avatar":(0,r.Z)({marginInlineEnd:l.marginSM},"".concat(l.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:l.marginSM}),"&-tags":(0,r.Z)({},"".concat(l.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,t.Z)((0,t.Z)({marginInlineEnd:l.marginSM,color:l.colorTextSecondary,fontSize:l.pageHeaderFontSizeHeaderSubTitle,lineHeight:l.lineHeight},_()),{},(0,r.Z)({},"".concat(l.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,r.Z)((0,r.Z)({marginBlock:l.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,r.Z)({"white-space":"unset"},"".concat(l.componentCls,"-rlt &"),{marginInlineEnd:l.marginSM,marginInlineStart:0})},"".concat(l.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,r.Z)({},"".concat(l.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:l.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:l.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};function h(L){return(0,Z.Xj)("ProLayoutPageHeader",function(l){var b=(0,t.Z)((0,t.Z)({},l),{},{componentCls:".".concat(L),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:l.paddingSM,pageHeaderColorBack:l.colorTextHeading,pageHeaderFontSizeHeaderTitle:l.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:l.paddingSM});return[S(b)]})}var d=e(85893),x=function(l,b,F,$){return!F||!$?null:(0,d.jsx)("div",{className:"".concat(l,"-back ").concat(b).trim(),children:(0,d.jsx)("div",{role:"button",onClick:function(m){$==null||$(m)},className:"".concat(l,"-back-button ").concat(b).trim(),"aria-label":"back",children:F})})},w=function(l,b){var F;return(F=l.items)!==null&&F!==void 0&&F.length?(0,d.jsx)(C.Z,(0,t.Z)((0,t.Z)({},l),{},{className:V()("".concat(b,"-breadcrumb"),l.className)})):null},O=function(l){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return l.backIcon!==void 0?l.backIcon:b==="rtl"?(0,d.jsx)(v(),{}):(0,d.jsx)(s(),{})},z=function(l,b){var F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",$=arguments.length>3?arguments[3]:void 0,n=b.title,m=b.avatar,g=b.subTitle,u=b.tags,p=b.extra,X=b.onBack,B="".concat(l,"-heading"),Q=n||g||u||p;if(!Q)return null;var J=O(b,F),q=x(l,$,J,X),D=q||m||Q;return(0,d.jsxs)("div",{className:B+" "+$,children:[D&&(0,d.jsxs)("div",{className:"".concat(B,"-left ").concat($).trim(),children:[q,m&&(0,d.jsx)(I.C,(0,t.Z)({className:V()("".concat(B,"-avatar"),$,m.className)},m)),n&&(0,d.jsx)("span",{className:"".concat(B,"-title ").concat($).trim(),title:typeof n=="string"?n:void 0,children:n}),g&&(0,d.jsx)("span",{className:"".concat(B,"-sub-title ").concat($).trim(),title:typeof g=="string"?g:void 0,children:g}),u&&(0,d.jsx)("span",{className:"".concat(B,"-tags ").concat($).trim(),children:u})]}),p&&(0,d.jsx)("span",{className:"".concat(B,"-extra ").concat($).trim(),children:(0,d.jsx)(j.Z,{children:p})})]})},U=function(l,b,F){return b?(0,d.jsx)("div",{className:"".concat(l,"-footer ").concat(F).trim(),children:b}):null},A=function(l,b,F){return(0,d.jsx)("div",{className:"".concat(l,"-content ").concat(F).trim(),children:b})},K=function L(l){return l==null?void 0:l.map(function(b){var F;return(0,W.ET)(!!b.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,t.Z)((0,t.Z)({},b),{},{breadcrumbName:void 0,children:void 0,title:b.title||b.breadcrumbName},(F=b.children)!==null&&F!==void 0&&F.length?{menu:{items:L(b.children)}}:{})})},G=function(l){var b,F=k.useState(!1),$=(0,c.Z)(F,2),n=$[0],m=$[1],g=function(me){var ve=me.width;return m(ve<768)},u=k.useContext(T.ZP.ConfigContext),p=u.getPrefixCls,X=u.direction,B=l.prefixCls,Q=l.style,J=l.footer,q=l.children,D=l.breadcrumb,ee=l.breadcrumbRender,ce=l.className,P=l.contentWidth,R=l.layout,E=l.ghost,y=E===void 0?!0:E,N=p("page-header",B),ne=h(N),ie=ne.wrapSSR,Y=ne.hashId,le=function(){return D&&!(D!=null&&D.items)&&D!==null&&D!==void 0&&D.routes&&((0,W.ET)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),D.items=K(D.routes)),D!=null&&D.items?w(D,N):null},ae=le(),de=D&&"props"in D,oe=(b=ee==null?void 0:ee((0,t.Z)((0,t.Z)({},l),{},{prefixCls:N}),ae))!==null&&b!==void 0?b:ae,re=de?D:oe,te=V()(N,Y,ce,(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({},"".concat(N,"-has-breadcrumb"),!!re),"".concat(N,"-has-footer"),!!J),"".concat(N,"-rtl"),X==="rtl"),"".concat(N,"-compact"),n),"".concat(N,"-wide"),P==="Fixed"&&R=="top"),"".concat(N,"-ghost"),y)),ue=z(N,l,X,Y),fe=q&&A(N,q,Y),se=U(N,J,Y);return!re&&!ue&&!se&&!fe?(0,d.jsx)("div",{className:V()(Y,["".concat(N,"-no-children")])}):ie((0,d.jsx)(H.Z,{onResize:g,children:(0,d.jsxs)("div",{className:te,style:Q,children:[re,ue,fe,se]})}))}},87646:function(o,a,e){"use strict";e.r(a),e.d(a,{blue:function(){return A},blueDark:function(){return Q},cyan:function(){return U},cyanDark:function(){return B},geekblue:function(){return K},geekblueDark:function(){return J},generate:function(){return Z},gold:function(){return x},goldDark:function(){return g},gray:function(){return b},green:function(){return z},greenDark:function(){return X},grey:function(){return l},greyDark:function(){return ee},lime:function(){return O},limeDark:function(){return p},magenta:function(){return L},magentaDark:function(){return D},orange:function(){return d},orangeDark:function(){return m},presetDarkPalettes:function(){return ce},presetPalettes:function(){return F},presetPrimaryColors:function(){return _},purple:function(){return G},purpleDark:function(){return q},red:function(){return S},redDark:function(){return $},volcano:function(){return h},volcanoDark:function(){return n},yellow:function(){return w},yellowDark:function(){return u}});var r=e(86500),c=e(1350),t=2,i=.16,s=.05,f=.05,v=.15,C=5,I=4,j=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function T(P){var R=P.r,E=P.g,y=P.b,N=(0,r.py)(R,E,y);return{h:N.h*360,s:N.s,v:N.v}}function M(P){var R=P.r,E=P.g,y=P.b;return"#".concat((0,r.vq)(R,E,y,!1))}function V(P,R,E){var y=E/100,N={r:(R.r-P.r)*y+P.r,g:(R.g-P.g)*y+P.g,b:(R.b-P.b)*y+P.b};return N}function H(P,R,E){var y;return Math.round(P.h)>=60&&Math.round(P.h)<=240?y=E?Math.round(P.h)-t*R:Math.round(P.h)+t*R:y=E?Math.round(P.h)+t*R:Math.round(P.h)-t*R,y<0?y+=360:y>=360&&(y-=360),y}function W(P,R,E){if(P.h===0&&P.s===0)return P.s;var y;return E?y=P.s-i*R:R===I?y=P.s+i:y=P.s+s*R,y>1&&(y=1),E&&R===C&&y>.1&&(y=.1),y<.06&&(y=.06),Number(y.toFixed(2))}function k(P,R,E){var y;return E?y=P.v+f*R:y=P.v-v*R,y>1&&(y=1),Number(y.toFixed(2))}function Z(P){for(var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=[],y=(0,c.uA)(P),N=C;N>0;N-=1){var ne=T(y),ie=M((0,c.uA)({h:H(ne,N,!0),s:W(ne,N,!0),v:k(ne,N,!0)}));E.push(ie)}E.push(M(y));for(var Y=1;Y<=I;Y+=1){var le=T(y),ae=M((0,c.uA)({h:H(le,Y),s:W(le,Y),v:k(le,Y)}));E.push(ae)}return R.theme==="dark"?j.map(function(de){var oe=de.index,re=de.opacity,te=M(V((0,c.uA)(R.backgroundColor||"#141414"),(0,c.uA)(E[oe]),re*100));return te}):E}var _={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},S=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];S.primary=S[5];var h=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];h.primary=h[5];var d=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];d.primary=d[5];var x=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];x.primary=x[5];var w=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];w.primary=w[5];var O=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];O.primary=O[5];var z=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];z.primary=z[5];var U=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];U.primary=U[5];var A=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];A.primary=A[5];var K=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];K.primary=K[5];var G=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];G.primary=G[5];var L=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];L.primary=L[5];var l=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];l.primary=l[5];var b=l,F={red:S,volcano:h,orange:d,gold:x,yellow:w,lime:O,green:z,cyan:U,blue:A,geekblue:K,purple:G,magenta:L,grey:l},$=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];$.primary=$[5];var n=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];n.primary=n[5];var m=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];m.primary=m[5];var g=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];g.primary=g[5];var u=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];u.primary=u[5];var p=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];p.primary=p[5];var X=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];X.primary=X[5];var B=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];B.primary=B[5];var Q=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];Q.primary=Q[5];var J=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];J.primary=J[5];var q=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];q.primary=q[5];var D=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];D.primary=D[5];var ee=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];ee.primary=ee[5];var ce={red:$,volcano:n,orange:m,gold:g,yellow:u,lime:p,green:X,cyan:B,blue:Q,geekblue:J,purple:q,magenta:D,grey:ee}},3770:function(o,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;const r=c(e(27863));function c(i){return i&&i.__esModule?i:{default:i}}const t=r;a.default=t,o.exports=t},77059:function(o,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;const r=c(e(21379));function c(i){return i&&i.__esModule?i:{default:i}}const t=r;a.default=t,o.exports=t},33046:function(o,a,e){"use strict";"use client";var r=e(64836).default,c=e(75263).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t=r(e(10434)),i=r(e(27424)),s=r(e(38416)),f=r(e(70215)),v=c(e(67294)),C=r(e(93967)),I=e(87646),j=r(e(61711)),T=r(e(27727)),M=e(26814),V=e(72014),H=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,M.setTwoToneColor)(I.blue.primary);var W=v.forwardRef(function(Z,_){var S=Z.className,h=Z.icon,d=Z.spin,x=Z.rotate,w=Z.tabIndex,O=Z.onClick,z=Z.twoToneColor,U=(0,f.default)(Z,H),A=v.useContext(j.default),K=A.prefixCls,G=K===void 0?"anticon":K,L=A.rootClassName,l=(0,C.default)(L,G,(0,s.default)((0,s.default)({},"".concat(G,"-").concat(h.name),!!h.name),"".concat(G,"-spin"),!!d||h.name==="loading"),S),b=w;b===void 0&&O&&(b=-1);var F=x?{msTransform:"rotate(".concat(x,"deg)"),transform:"rotate(".concat(x,"deg)")}:void 0,$=(0,V.normalizeTwoToneColors)(z),n=(0,i.default)($,2),m=n[0],g=n[1];return v.createElement("span",(0,t.default)({role:"img","aria-label":h.name},U,{ref:_,tabIndex:b,onClick:O,className:l}),v.createElement(T.default,{icon:h,primaryColor:m,secondaryColor:g,style:F}))});W.displayName="AntdIcon",W.getTwoToneColor=M.getTwoToneColor,W.setTwoToneColor=M.setTwoToneColor;var k=a.default=W},61711:function(o,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=e(67294),c=(0,r.createContext)({}),t=a.default=c},27727:function(o,a,e){"use strict";var r=e(64836).default,c=e(75263).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t=r(e(70215)),i=r(e(42122)),s=c(e(67294)),f=e(72014),v=["icon","className","onClick","style","primaryColor","secondaryColor"],C={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function I(V){var H=V.primaryColor,W=V.secondaryColor;C.primaryColor=H,C.secondaryColor=W||(0,f.getSecondaryColor)(H),C.calculated=!!W}function j(){return(0,i.default)({},C)}var T=function(H){var W=H.icon,k=H.className,Z=H.onClick,_=H.style,S=H.primaryColor,h=H.secondaryColor,d=(0,t.default)(H,v),x=s.useRef(),w=C;if(S&&(w={primaryColor:S,secondaryColor:h||(0,f.getSecondaryColor)(S)}),(0,f.useInsertStyles)(x),(0,f.warning)((0,f.isIconDefinition)(W),"icon should be icon definiton, but got ".concat(W)),!(0,f.isIconDefinition)(W))return null;var O=W;return O&&typeof O.icon=="function"&&(O=(0,i.default)((0,i.default)({},O),{},{icon:O.icon(w.primaryColor,w.secondaryColor)})),(0,f.generate)(O.icon,"svg-".concat(O.name),(0,i.default)((0,i.default)({className:k,onClick:Z,style:_,"data-icon":O.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},d),{},{ref:x}))};T.displayName="IconReact",T.getTwoToneColors=j,T.setTwoToneColors=I;var M=a.default=T},26814:function(o,a,e){"use strict";var r=e(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.getTwoToneColor=f,a.setTwoToneColor=s;var c=r(e(27424)),t=r(e(27727)),i=e(72014);function s(v){var C=(0,i.normalizeTwoToneColors)(v),I=(0,c.default)(C,2),j=I[0],T=I[1];return t.default.setTwoToneColors({primaryColor:j,secondaryColor:T})}function f(){var v=t.default.getTwoToneColors();return v.calculated?[v.primaryColor,v.secondaryColor]:v.primaryColor}},27863:function(o,a,e){"use strict";var r=e(75263).default,c=e(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t=c(e(10434)),i=r(e(67294)),s=c(e(47356)),f=c(e(33046)),v=function(T,M){return i.createElement(f.default,(0,t.default)({},T,{ref:M,icon:s.default}))},C=i.forwardRef(v),I=a.default=C},21379:function(o,a,e){"use strict";var r=e(75263).default,c=e(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t=c(e(10434)),i=r(e(67294)),s=c(e(44149)),f=c(e(33046)),v=function(T,M){return i.createElement(f.default,(0,t.default)({},T,{ref:M,icon:s.default}))},C=i.forwardRef(v),I=a.default=C},72014:function(o,a,e){"use strict";var r=e(75263).default,c=e(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.generate=W,a.getSecondaryColor=k,a.iconStyles=void 0,a.isIconDefinition=V,a.normalizeAttrs=H,a.normalizeTwoToneColors=Z,a.useInsertStyles=a.svgBaseProps=void 0,a.warning=M;var t=c(e(42122)),i=c(e(18698)),s=e(87646),f=e(93399),v=e(63298),C=c(e(45520)),I=r(e(67294)),j=c(e(61711));function T(d){return d.replace(/-(.)/g,function(x,w){return w.toUpperCase()})}function M(d,x){(0,C.default)(d,"[@ant-design/icons] ".concat(x))}function V(d){return(0,i.default)(d)==="object"&&typeof d.name=="string"&&typeof d.theme=="string"&&((0,i.default)(d.icon)==="object"||typeof d.icon=="function")}function H(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(d).reduce(function(x,w){var O=d[w];switch(w){case"class":x.className=O,delete x.class;break;default:delete x[w],x[T(w)]=O}return x},{})}function W(d,x,w){return w?I.default.createElement(d.tag,(0,t.default)((0,t.default)({key:x},H(d.attrs)),w),(d.children||[]).map(function(O,z){return W(O,"".concat(x,"-").concat(d.tag,"-").concat(z))})):I.default.createElement(d.tag,(0,t.default)({key:x},H(d.attrs)),(d.children||[]).map(function(O,z){return W(O,"".concat(x,"-").concat(d.tag,"-").concat(z))}))}function k(d){return(0,s.generate)(d)[0]}function Z(d){return d?Array.isArray(d)?d:[d]:[]}var _=a.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},S=a.iconStyles=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,h=a.useInsertStyles=function(x){var w=(0,I.useContext)(j.default),O=w.csp,z=w.prefixCls,U=S;z&&(U=U.replace(/anticon/g,z)),(0,I.useEffect)(function(){var A=x.current,K=(0,v.getShadowRoot)(A);(0,f.updateCSS)(U,"@ant-design-icons",{prepend:!0,csp:O,attachTo:K})},[])}},85673:function(o,a,e){"use strict";e.d(a,{Z:function(){return $}});var r=e(67294),c=e(93967),t=e.n(c),i=e(50344),s=e(64217),f=e(96159),v=e(53124),C=e(13622),I=e(7743);const j=n=>{let{children:m}=n;const{getPrefixCls:g}=r.useContext(v.E_),u=g("breadcrumb");return r.createElement("li",{className:`${u}-separator`,"aria-hidden":"true"},m===""?m:m||"/")};j.__ANT_BREADCRUMB_SEPARATOR=!0;var T=j,M=function(n,m){var g={};for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&m.indexOf(u)<0&&(g[u]=n[u]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(n);p<u.length;p++)m.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(n,u[p])&&(g[u[p]]=n[u[p]]);return g};function V(n,m){if(n.title===void 0||n.title===null)return null;const g=Object.keys(m).join("|");return typeof n.title=="object"?n.title:String(n.title).replace(new RegExp(`:(${g})`,"g"),(u,p)=>m[p]||u)}function H(n,m,g,u){if(g==null)return null;const{className:p,onClick:X}=m,B=M(m,["className","onClick"]),Q=Object.assign(Object.assign({},(0,s.Z)(B,{data:!0,aria:!0})),{onClick:X});return u!==void 0?r.createElement("a",Object.assign({},Q,{className:t()(`${n}-link`,p),href:u}),g):r.createElement("span",Object.assign({},Q,{className:t()(`${n}-link`,p)}),g)}function W(n,m){return(u,p,X,B,Q)=>{if(m)return m(u,p,X,B);const J=V(u,p);return H(n,u,J,Q)}}var k=function(n,m){var g={};for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&m.indexOf(u)<0&&(g[u]=n[u]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(n);p<u.length;p++)m.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(n,u[p])&&(g[u[p]]=n[u[p]]);return g};const Z=n=>{const{prefixCls:m,separator:g="/",children:u,menu:p,overlay:X,dropdownProps:B,href:Q}=n,q=(D=>{if(p||X){const ee=Object.assign({},B);if(p){const ce=p||{},{items:P}=ce,R=k(ce,["items"]);ee.menu=Object.assign(Object.assign({},R),{items:P==null?void 0:P.map((E,y)=>{var{key:N,title:ne,label:ie,path:Y}=E,le=k(E,["key","title","label","path"]);let ae=ie!=null?ie:ne;return Y&&(ae=r.createElement("a",{href:`${Q}${Y}`},ae)),Object.assign(Object.assign({},le),{key:N!=null?N:y,label:ae})})})}else X&&(ee.overlay=X);return r.createElement(I.Z,Object.assign({placement:"bottom"},ee),r.createElement("span",{className:`${m}-overlay-link`},D,r.createElement(C.Z,null)))}return D})(u);return q!=null?r.createElement(r.Fragment,null,r.createElement("li",null,q),g&&r.createElement(T,null,g)):null},_=n=>{const{prefixCls:m,children:g,href:u}=n,p=k(n,["prefixCls","children","href"]),{getPrefixCls:X}=r.useContext(v.E_),B=X("breadcrumb",m);return r.createElement(Z,Object.assign({},p,{prefixCls:B}),H(B,p,g,u))};_.__ANT_BREADCRUMB_ITEM=!0;var S=_,h=e(11568),d=e(14747),x=e(83559),w=e(83262);const O=n=>{const{componentCls:m,iconCls:g,calc:u}=n;return{[m]:Object.assign(Object.assign({},(0,d.Wf)(n)),{color:n.itemColor,fontSize:n.fontSize,[g]:{fontSize:n.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:n.linkColor,transition:`color ${n.motionDurationMid}`,padding:`0 ${(0,h.bf)(n.paddingXXS)}`,borderRadius:n.borderRadiusSM,height:n.fontHeight,display:"inline-block",marginInline:u(n.marginXXS).mul(-1).equal(),"&:hover":{color:n.linkHoverColor,backgroundColor:n.colorBgTextHover}},(0,d.Qy)(n)),"li:last-child":{color:n.lastItemColor},[`${m}-separator`]:{marginInline:n.separatorMargin,color:n.separatorColor},[`${m}-link`]:{[`
          > ${g} + span,
          > ${g} + a
        `]:{marginInlineStart:n.marginXXS}},[`${m}-overlay-link`]:{borderRadius:n.borderRadiusSM,height:n.fontHeight,display:"inline-block",padding:`0 ${(0,h.bf)(n.paddingXXS)}`,marginInline:u(n.marginXXS).mul(-1).equal(),[`> ${g}`]:{marginInlineStart:n.marginXXS,fontSize:n.fontSizeIcon},"&:hover":{color:n.linkHoverColor,backgroundColor:n.colorBgTextHover,a:{color:n.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${n.componentCls}-rtl`]:{direction:"rtl"}})}},z=n=>({itemColor:n.colorTextDescription,lastItemColor:n.colorText,iconFontSize:n.fontSize,linkColor:n.colorTextDescription,linkHoverColor:n.colorText,separatorColor:n.colorTextDescription,separatorMargin:n.marginXS});var U=(0,x.I$)("Breadcrumb",n=>{const m=(0,w.IX)(n,{});return O(m)},z),A=function(n,m){var g={};for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&m.indexOf(u)<0&&(g[u]=n[u]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(n);p<u.length;p++)m.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(n,u[p])&&(g[u[p]]=n[u[p]]);return g};function K(n){const{breadcrumbName:m,children:g}=n,u=A(n,["breadcrumbName","children"]),p=Object.assign({title:m},u);return g&&(p.menu={items:g.map(X=>{var{breadcrumbName:B}=X,Q=A(X,["breadcrumbName"]);return Object.assign(Object.assign({},Q),{title:B})})}),p}function G(n,m){return(0,r.useMemo)(()=>n||(m?m.map(K):null),[n,m])}var L=function(n,m){var g={};for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&m.indexOf(u)<0&&(g[u]=n[u]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(n);p<u.length;p++)m.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(n,u[p])&&(g[u[p]]=n[u[p]]);return g};const l=(n,m)=>{if(m===void 0)return m;let g=(m||"").replace(/^\//,"");return Object.keys(n).forEach(u=>{g=g.replace(`:${u}`,n[u])}),g},b=n=>{const{prefixCls:m,separator:g="/",style:u,className:p,rootClassName:X,routes:B,items:Q,children:J,itemRender:q,params:D={}}=n,ee=L(n,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:ce,direction:P,breadcrumb:R}=r.useContext(v.E_);let E;const y=ce("breadcrumb",m),[N,ne,ie]=U(y),Y=G(Q,B),le=W(y,q);if(Y&&Y.length>0){const oe=[],re=Q||B;E=Y.map((te,ue)=>{const{path:fe,key:se,type:ge,menu:me,overlay:ve,onClick:Ce,className:xe,separator:Se,dropdownProps:Oe}=te,pe=l(D,fe);pe!==void 0&&oe.push(pe);const he=se!=null?se:ue;if(ge==="separator")return r.createElement(T,{key:he},Se);const be={},Pe=ue===Y.length-1;me?be.menu=me:ve&&(be.overlay=ve);let{href:ye}=te;return oe.length&&pe!==void 0&&(ye=`#/${oe.join("/")}`),r.createElement(Z,Object.assign({key:he},be,(0,s.Z)(te,{data:!0,aria:!0}),{className:xe,dropdownProps:Oe,href:ye,separator:Pe?"":g,onClick:Ce,prefixCls:y}),le(te,D,re,oe,ye))})}else if(J){const oe=(0,i.Z)(J).length;E=(0,i.Z)(J).map((re,te)=>{if(!re)return re;const ue=te===oe-1;return(0,f.Tm)(re,{separator:ue?"":g,key:te})})}const ae=t()(y,R==null?void 0:R.className,{[`${y}-rtl`]:P==="rtl"},p,X,ne,ie),de=Object.assign(Object.assign({},R==null?void 0:R.style),u);return N(r.createElement("nav",Object.assign({className:ae,style:de},ee),r.createElement("ol",null,E)))};b.Item=S,b.Separator=T;var F=b,$=F},19158:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=e;function e(){return!!(typeof window!="undefined"&&window.document&&window.document.createElement)}},32191:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=e;function e(r,c){if(!r)return!1;if(r.contains)return r.contains(c);for(var t=c;t;){if(t===r)return!0;t=t.parentNode}return!1}},93399:function(o,a,e){"use strict";var r=e(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.clearContainerCache=Z,a.injectCSS=V,a.removeCSS=W,a.updateCSS=_;var c=r(e(42122)),t=r(e(19158)),i=r(e(32191)),s="data-rc-order",f="data-rc-priority",v="rc-util-key",C=new Map;function I(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},h=S.mark;return h?h.startsWith("data-")?h:"data-".concat(h):v}function j(S){if(S.attachTo)return S.attachTo;var h=document.querySelector("head");return h||document.body}function T(S){return S==="queue"?"prependQueue":S?"prepend":"append"}function M(S){return Array.from((C.get(S)||S).children).filter(function(h){return h.tagName==="STYLE"})}function V(S){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!(0,t.default)())return null;var d=h.csp,x=h.prepend,w=h.priority,O=w===void 0?0:w,z=T(x),U=z==="prependQueue",A=document.createElement("style");A.setAttribute(s,z),U&&O&&A.setAttribute(f,"".concat(O)),d!=null&&d.nonce&&(A.nonce=d==null?void 0:d.nonce),A.innerHTML=S;var K=j(h),G=K.firstChild;if(x){if(U){var L=(h.styles||M(K)).filter(function(l){if(!["prepend","prependQueue"].includes(l.getAttribute(s)))return!1;var b=Number(l.getAttribute(f)||0);return O>=b});if(L.length)return K.insertBefore(A,L[L.length-1].nextSibling),A}K.insertBefore(A,G)}else K.appendChild(A);return A}function H(S){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},d=j(h);return(h.styles||M(d)).find(function(x){return x.getAttribute(I(h))===S})}function W(S){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},d=H(S,h);if(d){var x=j(h);x.removeChild(d)}}function k(S,h){var d=C.get(S);if(!d||!(0,i.default)(document,d)){var x=V("",h),w=x.parentNode;C.set(S,w),S.removeChild(x)}}function Z(){C.clear()}function _(S,h){var d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},x=j(d),w=M(x),O=(0,c.default)((0,c.default)({},d),{},{styles:w});k(x,O);var z=H(h,O);if(z){var U,A;if((U=O.csp)!==null&&U!==void 0&&U.nonce&&z.nonce!==((A=O.csp)===null||A===void 0?void 0:A.nonce)){var K;z.nonce=(K=O.csp)===null||K===void 0?void 0:K.nonce}return z.innerHTML!==S&&(z.innerHTML=S),z}var G=V(S,O);return G.setAttribute(I(O),h),G}},63298:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.getShadowRoot=c,a.inShadow=r;function e(t){var i;return t==null||(i=t.getRootNode)===null||i===void 0?void 0:i.call(t)}function r(t){return e(t)instanceof ShadowRoot}function c(t){return r(t)?e(t):null}},45520:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.call=f,a.default=void 0,a.note=i,a.noteOnce=C,a.preMessage=void 0,a.resetWarned=s,a.warning=t,a.warningOnce=v;var e={},r=[],c=a.preMessage=function(T){r.push(T)};function t(j,T){if(0)var M}function i(j,T){if(0)var M}function s(){e={}}function f(j,T,M){!T&&!e[M]&&(j(!1,M),e[M]=!0)}function v(j,T){f(t,j,T)}function C(j,T){f(i,j,T)}v.preMessage=c,v.resetWarned=s,v.noteOnce=C;var I=a.default=v},73897:function(o){function a(e,r){(r==null||r>e.length)&&(r=e.length);for(var c=0,t=Array(r);c<r;c++)t[c]=e[c];return t}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},85372:function(o){function a(e){if(Array.isArray(e))return e}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},38416:function(o,a,e){var r=e(64062);function c(t,i,s){return(i=r(i))in t?Object.defineProperty(t,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[i]=s,t}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},10434:function(o){function a(){return o.exports=a=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var c=arguments[r];for(var t in c)({}).hasOwnProperty.call(c,t)&&(e[t]=c[t])}return e},o.exports.__esModule=!0,o.exports.default=o.exports,a.apply(null,arguments)}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},64836:function(o){function a(e){return e&&e.__esModule?e:{default:e}}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},75263:function(o,a,e){var r=e(18698).default;function c(i){if(typeof WeakMap!="function")return null;var s=new WeakMap,f=new WeakMap;return(c=function(C){return C?f:s})(i)}function t(i,s){if(!s&&i&&i.__esModule)return i;if(i===null||r(i)!="object"&&typeof i!="function")return{default:i};var f=c(s);if(f&&f.has(i))return f.get(i);var v={__proto__:null},C=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var I in i)if(I!=="default"&&{}.hasOwnProperty.call(i,I)){var j=C?Object.getOwnPropertyDescriptor(i,I):null;j&&(j.get||j.set)?Object.defineProperty(v,I,j):v[I]=i[I]}return v.default=i,f&&f.set(i,v),v}o.exports=t,o.exports.__esModule=!0,o.exports.default=o.exports},68872:function(o){function a(e,r){var c=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(c!=null){var t,i,s,f,v=[],C=!0,I=!1;try{if(s=(c=c.call(e)).next,r===0){if(Object(c)!==c)return;C=!1}else for(;!(C=(t=s.call(c)).done)&&(v.push(t.value),v.length!==r);C=!0);}catch(j){I=!0,i=j}finally{try{if(!C&&c.return!=null&&(f=c.return(),Object(f)!==f))return}finally{if(I)throw i}}return v}}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},12218:function(o){function a(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},42122:function(o,a,e){var r=e(38416);function c(i,s){var f=Object.keys(i);if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(i);s&&(v=v.filter(function(C){return Object.getOwnPropertyDescriptor(i,C).enumerable})),f.push.apply(f,v)}return f}function t(i){for(var s=1;s<arguments.length;s++){var f=arguments[s]!=null?arguments[s]:{};s%2?c(Object(f),!0).forEach(function(v){r(i,v,f[v])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(f)):c(Object(f)).forEach(function(v){Object.defineProperty(i,v,Object.getOwnPropertyDescriptor(f,v))})}return i}o.exports=t,o.exports.__esModule=!0,o.exports.default=o.exports},70215:function(o,a,e){var r=e(7071);function c(t,i){if(t==null)return{};var s,f,v=r(t,i);if(Object.getOwnPropertySymbols){var C=Object.getOwnPropertySymbols(t);for(f=0;f<C.length;f++)s=C[f],i.includes(s)||{}.propertyIsEnumerable.call(t,s)&&(v[s]=t[s])}return v}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},7071:function(o){function a(e,r){if(e==null)return{};var c={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(r.includes(t))continue;c[t]=e[t]}return c}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},27424:function(o,a,e){var r=e(85372),c=e(68872),t=e(86116),i=e(12218);function s(f,v){return r(f)||c(f,v)||t(f,v)||i()}o.exports=s,o.exports.__esModule=!0,o.exports.default=o.exports},95036:function(o,a,e){var r=e(18698).default;function c(t,i){if(r(t)!="object"||!t)return t;var s=t[Symbol.toPrimitive];if(s!==void 0){var f=s.call(t,i||"default");if(r(f)!="object")return f;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(t)}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},64062:function(o,a,e){var r=e(18698).default,c=e(95036);function t(i){var s=c(i,"string");return r(s)=="symbol"?s:s+""}o.exports=t,o.exports.__esModule=!0,o.exports.default=o.exports},86116:function(o,a,e){var r=e(73897);function c(t,i){if(t){if(typeof t=="string")return r(t,i);var s={}.toString.call(t).slice(8,-1);return s==="Object"&&t.constructor&&(s=t.constructor.name),s==="Map"||s==="Set"?Array.from(t):s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?r(t,i):void 0}}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports}}]);
