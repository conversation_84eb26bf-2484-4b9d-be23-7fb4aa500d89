import React, { useState } from 'react';
import { Modal, Upload, Button, message, Row, Col, Divider, Progress } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import { useIntl } from 'umi';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { addDeviceInfos, addPointInfo, deleteDevice, getChannelInfos } from '@/services/ant-design-pro/api';
import pLimit from 'p-limit';

export interface ImportDeviceProps {
  visiable: boolean;
  handleImportDeviceVisibleVisible: (visible: boolean) => void;
  onSuccess?: () => void;
  loadPointInfo: (params?: any) => Promise<any>; // 加这一行
}

const fieldKeys = ['deviceName', 'protocolType', 'mainStream', 'secondStream'];

// 使用 xlsx 生成简单模板（无美化样式）
const exportTemplate = (intl: any) => {
  const headers = [
    intl.formatMessage({ id: 'pages.device.deviceName', defaultMessage: '名称' }),
    intl.formatMessage({ id: 'pages.device.protocolType', defaultMessage: '协议' }),
    intl.formatMessage({ id: 'pages.device.mainStream', defaultMessage: '主码流' }),
    intl.formatMessage({ id: 'pages.device.secondStream', defaultMessage: '子码流' }),
  ];
  const data = [
    {
      [headers[0]]: fieldKeys[0],
      [headers[1]]: fieldKeys[1],
      [headers[2]]: fieldKeys[2],
      [headers[3]]: fieldKeys[3],
    }
  ];
  const worksheet = XLSX.utils.json_to_sheet(data, { header: headers });
  worksheet['!cols'] = [
    { wch: 18 },
    { wch: 12 },
    { wch: 32 },
    { wch: 32 },
  ];
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'RTSP设备模板');
  const wbout = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'rtsp_import_template.xlsx');
};

// 支持超时的通道查询
const CONCURRENCY = 5; // 最多并发 5 个设备
const CHANNEL_TIMEOUT = 90 * 1000;

async function fetchChannelInfoWithRetry(
  deviceId: string,
  {
    maxRetry = 5,
    interval = 2000,
    timeoutPerRequest = 90 * 1000,
  } = {}
): Promise<any[]> {
  console.log(`[fetchChannelInfoWithRetry] 开始，设备: ${deviceId}, 最大重试: ${maxRetry}`);

  for (let attempt = 1; attempt <= maxRetry; attempt++) {
    console.log(`[fetchChannelInfoWithRetry] 尝试第 ${attempt} 次查询，设备: ${deviceId}`);

    try {
      const controller = new AbortController();
      const signal = controller.signal;
      const timeoutId = setTimeout(() => controller.abort(), timeoutPerRequest);

      const res = await getChannelInfos({
        deviceId,
        channelNumber: 'ASC',
        pageNo: 1,
        pageSize: 10,
        signal,
      });

      clearTimeout(timeoutId);

      if (res.code === 0 && res?.data && res?.data.list.length > 0) {
        console.log(`[fetchChannelInfoWithRetry] 第 ${attempt} 次查询成功，设备: ${deviceId}, 通道数: ${res.data.list.length}`);
        return res.data.list;
      }

      console.warn(`[fetchChannelInfoWithRetry] 第 ${attempt} 次查询无数据，设备: ${deviceId}`);
    } catch (err: any) {
      if (err.name === 'AbortError') {
        console.warn(`[fetchChannelInfoWithRetry] 第 ${attempt} 次查询超时，设备: ${deviceId}`);
      } else {
        console.warn(`[fetchChannelInfoWithRetry] 第 ${attempt} 次查询异常，设备: ${deviceId}`, err.message);
      }
    }

    if (attempt < maxRetry) {
      console.log(`[fetchChannelInfoWithRetry] 等待 ${interval}ms 后进行第 ${attempt + 1} 次重试，设备: ${deviceId}`);
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }

  console.error(`[fetchChannelInfoWithRetry] 超过最大重试次数(${maxRetry})，设备: ${deviceId} 未查询到通道`);
  return [];
}



const ImportDevice: React.FC<ImportDeviceProps> = ({ visiable, handleImportDeviceVisibleVisible, onSuccess, loadPointInfo }) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressVisible, setProgressVisible] = useState(false);
  const [progressText, setProgressText] = useState('');
  const intl = useIntl();

  // 字段映射表
  const headers = [
    intl.formatMessage({ id: 'pages.device.deviceName', defaultMessage: '名称' }),
    intl.formatMessage({ id: 'pages.device.protocolType', defaultMessage: '协议' }),
    intl.formatMessage({ id: 'pages.device.mainStream', defaultMessage: '主码流' }),
    intl.formatMessage({ id: 'pages.device.secondStream', defaultMessage: '子码流' }),
  ];
  const headerMap: Record<string, string> = {
    [headers[0]]: fieldKeys[0],
    [headers[1]]: fieldKeys[1],
    [headers[2]]: fieldKeys[2],
    [headers[3]]: fieldKeys[3],
  };

  // 批量导入优化
  const batchImport = async (devices: any[]) => {
    setProgressVisible(true);
    setProgress(0)
    setUploading(true);

    const total = devices.length;
    let finished = 0;
    let errorList: any[] = [];

    // 按 10 个设备一组添加
    for (let i = 0; i < total; i += 10) {
      const group = devices.slice(i, i + 10);

      let addRes;
      try {
        addRes = await addDeviceInfos(group); // 一次添加 10 个设备
      } catch (err) {
        errorList.push(...group.map(dev => ({ ...dev, error_msg: '设备添加失败' })));
        finished += group.length;
        setProgress(Math.round((finished / total) * 100));
        setProgressText(`已处理 ${finished}/${total} 台设备`);
        continue;
      }

      if (!addRes || addRes.code !== 0 || !Array.isArray(addRes.data)) {
        errorList.push(...group.map(dev => ({ ...dev, error_msg: addRes?.msg || '设备添加失败' })));
        finished += group.length;
        setProgress(Math.round((finished / total) * 100));
        setProgressText(`已处理 ${finished}/${total} 台设备`);
        continue;
      }

      const limit = pLimit(CONCURRENCY);
      const groupTasks = group.map((dev, idx) =>
        limit(async () => {
          const devRes = addRes.data[idx];
          if (!devRes || devRes.error_code !== 0 || !devRes.id) {
            errorList.push({ ...dev, error_msg: devRes?.error_msg || '设备添加失败' });
            finished++;
            setProgress(Math.round((finished / total) * 100));
            setProgressText(`已处理 ${finished}/${total} 台设备`);
            return;
          }

          const deviceId = devRes.id;

          let channelList: any[] = [];
          try {
            channelList = await fetchChannelInfoWithRetry(deviceId);
            console.log("channelList->", channelList)
          } catch (err) {
            console.warn(`设备 ${deviceId} 通道查询失败`, err);
          }

          if (!channelList.length) {
            await deleteDevice(deviceId);
            errorList.push({ ...dev, error_msg: '通道获取失败，设备已删除' });
            finished++;
            setProgress(Math.round((finished / total) * 100));
            setProgressText(`已处理 ${finished}/${total} 台设备`);
            return;
          }

          const pointList = channelList.map((ch: any) => ({
            id: ch.id,
            name: dev.deviceName,
            detectType: 1,
            captureInterval: 5,
            businessId: ch.id,
            protocolType: dev.protocolType,
            deviceName: dev.deviceName,
            deviceId: deviceId,
          }));

          try {
            const pointRes = await addPointInfo(pointList);
            if (!pointRes || pointRes.code !== 0) {
              errorList.push({ ...dev, error_msg: pointRes?.msg || '点位添加失败' });
            }
          } catch (err) {
            errorList.push({ ...dev, error_msg: '点位添加异常' });
          }

          finished++;
          setProgress(Math.round((finished / total) * 100));
          setProgressText(`已处理 ${finished}/${total} 台设备`);
        })
      );

      // 等当前组的设备都处理完
      await Promise.all(groupTasks);
    }

    setUploading(false);
    setTimeout(() => setProgressVisible(false), 1000);

    if (errorList.length === 0) {
      message.success('全部导入成功！');
      loadPointInfo({})
      if (onSuccess) onSuccess();
    } else {
      message.warning(`部分导入失败，共 ${errorList.length} 个失败`);
      console.log('导入失败列表:', errorList);
      // TODO: 可展示 Modal/Table
    }
  };

  // 前端解析文件为JSON数组并做字段映射
  const handleFile = async (file: File): Promise<false> => {
    setUploading(true);
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'array' });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
          // 字段映射
          const mapped = jsonData.map((row: any) => {
            const obj: any = {};
            Object.keys(headerMap).forEach(cn => {
              obj[headerMap[cn]] = row[cn];
            });
            return obj;
          });
          await batchImport(mapped);
          resolve(false); // 阻止自动上传
        } catch (err) {
          message.error('文件解析失败');
          setUploading(false);
          setProgressVisible(false);
          reject(err);
        }
      };
      reader.onerror = (err) => {
        message.error('文件读取失败');
        setUploading(false);
        setProgressVisible(false);
        reject(err);
      };
      reader.readAsArrayBuffer(file);
    });
  };

  const uploadProps = {
    name: 'file',
    accept: '.xlsx,.xls,.csv',
    showUploadList: false,
    beforeUpload: handleFile,
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'rtsp设备批量导入', defaultMessage: 'RTSP设备批量导入' })}
      open={visiable}
      onCancel={() => handleImportDeviceVisibleVisible(false)}
      footer={null}
      destroyOnClose
      width={600}
      bodyStyle={{ padding: 32 }}
    >
      <Row gutter={32} align="middle" justify="center">
        <Col span={12} style={{ textAlign: 'center' }}>
          <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />} loading={uploading} size="large" style={{ width: 180 }}>
              {intl.formatMessage({ id: '选择文件导入', defaultMessage: '选择文件导入' })}
            </Button>
          </Upload>
        </Col>
        <Col span={12} style={{ textAlign: 'center' }}>
          <Button
            type="default"
            icon={<DownloadOutlined />}
            size="large"
            style={{ width: 180 }}
            onClick={() => exportTemplate(intl)}
          >
            {intl.formatMessage({ id: '模板下载', defaultMessage: '模板下载' })}
          </Button>
        </Col>
      </Row>
      {progressVisible && (
        <div style={{ margin: '20px 0' }}>
          <Progress percent={progress} />
          <div style={{ textAlign: 'center', marginTop: 8 }}>{progressText}</div>
        </div>
      )}
      <Divider />
      <div style={{ color: '#888', fontSize: 14, textAlign: 'center', marginTop: 8 }}>
        支持Excel或CSV格式，建议先下载模板后批量导入。
      </div>
    </Modal>
  );
};

export default ImportDevice;
