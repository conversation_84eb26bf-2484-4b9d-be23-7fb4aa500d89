import React, { useState, useEffect } from 'react';
import { Breadcrumb, Spin, Empty, message, Row, Col, Image, Card, Pagination, Select } from 'antd';
import { LeftCircleOutlined } from '@ant-design/icons';
import { useIntl } from 'umi';
import dayjs from 'dayjs';


import { imageSearch, getVLMInfo, similarityFeaturePage } from '@/services/ant-design-pro/api';
import { CanvasImageWithBox } from '@/utils/CanvasImageWithBox';
import SearchConditions from '@/components/SearchConditions';
import './ImageSearchPage.less';


const infoTextStyle: React.CSSProperties = {
  margin: '3px 0 4px',
  fontSize: '12px',
  color: 'rgb(33, 33, 33)',
  fontWeight: 400,
  lineHeight: '17px',
  textAlign: 'left',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
};

const iconStyle = {
  width: '16px',
  height: '16px',
};


interface SearchResult {
  thumbUrl: string;
  name: string;
  time: string;
  address: string;
  score: string;
  targetImages: string[];
  content: string;
  embeddingId: string;
  imageUrl?: string;
  metadata?: {
    name?: string;
    originTime?: string;
    pointName?: string;
    coordinate?: string;
  };
}

interface DetectionBox {
  label: string;
  score: number;
  coordinate: [number, number, number, number];
  // 扩展字段用于存储处理后的数据
  id?: string;
  cropLeft?: number;
  cropTop?: number;
  cropWidth?: number;
  cropHeight?: number;
  displayIndex?: number;
  croppedImage?: string;
  blob?: Blob;
}


interface ImageSearchPageProps {
  uploadedImages: string[];
  // searchValue: string;
  // searchResults: SearchResult[];
  // hasMore: boolean;
  // total: number;
  // currentPage: number;
  onBack: () => void;
  fetchImage: (imageName: string, imageType: string) => Promise<string | null>;
  onImageClick: (item: any) => void;
}

const ImageSearchPage: React.FC<ImageSearchPageProps> = ({
  uploadedImages,
  onBack,
  fetchImage,
  onImageClick,
}) => {
  const intl = useIntl();

  // 搜索结果
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);

  // 轨迹结果集
  const [trajectoryLoading, setTrajectoryLoading] = useState<boolean>(false);
  const [trajectoryResults, setTrajectoryResults] = useState<SearchResult[]>([]);
  const [trajectoryPagination, setTrajectoryPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0,
  });
  const [selectedBox, setSelectedBox] = useState<DetectionBox | null>(null);
  const [sortCondition, setSortCondition] = useState<'time' | 'score'>('time');
  const [selectedPointId, setSelectedPointId] = useState<string[]>([]);
  const [startTime, setStartTime] = useState<number>();
  const [endTime, setEndTime] = useState<number>();

  // 图片预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 目标检测最低分
  const [reidOdMinScore, setReidOdMinScore] = useState<number>(0.75);



  useEffect(() => {
    // 设置默认时间范围：今天往前推3个月
    const now = dayjs();
    const endTime = Math.floor(now.valueOf() / 1000); // 当前时间戳（秒）

    const threeMonthsAgo = now.subtract(3, 'month');
    const startTime = Math.floor(threeMonthsAgo.valueOf() / 1000); // 3个月前时间戳（秒）

    setStartTime(startTime);
    setEndTime(endTime);
  }, []);


  const loadImage = async () => {
    if (uploadedImages.length > 0) {
      setSearchLoading(true);

      const fileUrl = uploadedImages[0];
      console.log('开始图片搜索:', fileUrl);

      if (fileUrl) {
        try {
          const response = await fetch(fileUrl);
          const blob = await response.blob();
          const fileName = `image_${Date.now()}.jpg`;
          const file = new File([blob], fileName, { type: blob.type });

          const res = await imageSearch(file);
          console.log('搜索结果:', res);

          // 将搜索结果传递给父组件处理
          if (res && res.code === 0) {
            console.log('搜索成功，检测到人物:', res.data?.boxes?.length || 0);

            // 解析坐标并存储抠图结果 - 使用blob流优化性能
            const processedBoxes = await Promise.all(
              (res.data?.boxes || [])
                .filter((box: DetectionBox) => (box.label === 'person' || box.label === 'car') && box.score >= reidOdMinScore)
                .map(async (box: DetectionBox, index: number) => {
                  const [x1, y1, x2, y2] = box.coordinate;
                  const boxWidth = x2 - x1;
                  const boxHeight = y2 - y1;

                  // 创建canvas进行抠图
                  const canvas = document.createElement('canvas');
                  const ctx = canvas.getContext('2d');

                  const img = new window.Image();

                  // 使用blob URL创建临时URL，提高性能
                  const blobUrl = URL.createObjectURL(blob);

                  const croppedBlob = await new Promise<Blob>((resolve, reject) => {
                    img.onload = () => {
                      canvas.width = boxWidth;
                      canvas.height = boxHeight;
                      ctx?.drawImage(
                        img,
                        x1, y1, boxWidth, boxHeight,
                        0, 0, boxWidth, boxHeight
                      );

                      canvas.toBlob((blob) => {
                        // 清理blob URL
                        URL.revokeObjectURL(blobUrl);
                        if (blob) {
                          resolve(blob);
                        } else {
                          reject(new Error('转换blob失败'));
                        }
                      }, 'image/jpeg', 0.9);
                    };
                    img.onerror = () => {
                      URL.revokeObjectURL(blobUrl);
                      reject(new Error('图片加载失败'));
                    };
                    img.src = blobUrl;
                  });

                  // 同时生成base64用于显示
                  const croppedBase64 = await new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result as string);
                    reader.readAsDataURL(croppedBlob);
                  });

                  return {
                    ...box,
                    id: `person_${index}_${Date.now()}`,
                    cropLeft: x1,
                    cropTop: y1,
                    cropWidth: boxWidth,
                    cropHeight: boxHeight,
                    displayIndex: index + 1,
                    croppedImage: croppedBase64,
                    blob: croppedBlob
                  };
                })
            );
            console.log('处理后的坐标:', processedBoxes);

            setSearchResults(processedBoxes);
          } else {
            message.error(res?.msg || '搜索失败，请重试');
          }
        } catch (error) {
          console.error('转换图片失败:', error);
          message.error('图片处理失败，请重试');
        } finally {
          setSearchLoading(false);
        }
      } else {
        message.warning('请先上传图片');
      }
      setSearchLoading(false);
    }
  }


  const loadParamConfig = async () => {
    const rs = await getVLMInfo("retrieval_strategy");
    if (rs.code !== 0 || !rs.data) {
      message.error(intl.formatMessage({ id: 'pages.config.paramConfigFailure', defaultMessage: '获取参数配置失败', }))
      return;
    }
    setReidOdMinScore(rs.data.reid_od_min_score)
  };

  const processTrajectoryData = async (data: any[]) => {
    return Promise.all(data.map(async item => {
      let thumbUrl = item.thumbUrl;
      if (!thumbUrl && item.metadata?.name) {
        try {
          thumbUrl = await fetchImage(item.metadata.name, "thumb") || '/icons/search/default-noImage.png';
        } catch (error) {
          thumbUrl = '/icons/search/default-noImage.png';
        }
      }
      return {
        ...item,
        thumbUrl: thumbUrl || '/icons/search/default-noImage.png',
        time: item.time || (item.metadata?.originTime ? new Date(parseInt(item.metadata.originTime)).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\//g, '-') : '--'),
        address: item.address || item.metadata?.pointName || '--',
        score: typeof item.score === 'number' ? `${(item.score * 100).toFixed(5)}%` : item.score || '0%',
      };
    }));
  };

  // 统一的RAG过滤参数构建函数
  const buildRagFilterDTO = (
    page: number,
    pageSize?: number,
    sortBy?: string,
    pointIds?: string[],
    startTimeParam?: number,
    endTimeParam?: number
  ) => {
    const dto: any = {
      pageNo: page,
      pageSize: pageSize || trajectoryPagination.pageSize,
      sortedBy: sortBy || sortCondition,
    };

    // 使用传入的时间参数或组件状态中的时间
    const effectiveStartTime = startTimeParam ?? startTime;
    const effectiveEndTime = endTimeParam ?? endTime;

    // 只有当有开始时间时才添加
    if (effectiveStartTime) {
      dto.startTime = effectiveStartTime * 1000;
    }

    // 只有当有结束时间时才添加
    if (effectiveEndTime) {
      dto.endTime = effectiveEndTime * 1000;
    }

    // 只有当有点位ID时才添加
    const effectivePointIds = pointIds ?? selectedPointId;
    if (effectivePointIds?.length) {
      dto.pointId = effectivePointIds.join(',');
    }

    return dto;
  };

  const handleTrajectoryListBySelectBox = async (params: {
    box: DetectionBox;
    pointIds?: string[];
    sortBy?: string;
    startTime?: number;
    endTime?: number;
  }) => {
    const { box, pointIds, sortBy, startTime: startTimeParam, endTime: endTimeParam } = params;
    console.log('开始查询轨迹:', box);

    if (!uploadedImages[0]) {
      message.warning('请先上传图片');
      return;
    }

    if (!box.blob) {
      message.error('图片数据不完整');
      return;
    }

    setSelectedBox(box);
    setTrajectoryLoading(true);
    setTrajectoryResults([]);

    try {
      const fileName = `image_${Date.now()}.jpg`;
      const file = new File([box.blob], fileName, { type: box.blob.type });

      const ragFilterDTO = buildRagFilterDTO(1, undefined, sortBy, pointIds, startTimeParam, endTimeParam);

      console.log('ragFilterDTO:', ragFilterDTO);
      console.log('使用box.blob:', box.blob);
      const res = await similarityFeaturePage(file, ragFilterDTO);

      if (res && res.code === 0) {
        const processedData = await processTrajectoryData(res.data.data || []);
        setTrajectoryResults(processedData);
        setTrajectoryPagination({
          current: 1,
          pageSize: trajectoryPagination.pageSize,
          total: res.data?.total || 0,
        });
      } else {
        message.error(res?.msg || '轨迹查询失败');
        setTrajectoryResults([]);
      }
    } catch (error) {
      console.error('轨迹查询失败:', error);
      message.error('轨迹查询失败，请重试');
    } finally {
      setTrajectoryLoading(false);
    }
  };

  const handleTrajectoryPageChange = async (page: number, pageSize?: number) => {
    if (!uploadedImages[0] || !selectedBox || !selectedBox.blob) return;

    setTrajectoryLoading(true);
    setTrajectoryResults([]);

    try {
      const fileName = `image_${Date.now()}.jpg`;
      const file = new File([selectedBox.blob], fileName, { type: selectedBox.blob.type });

      const ragFilterDTO = buildRagFilterDTO(page, pageSize, undefined, undefined, startTime, endTime);
      console.log('ragFilterDTO:', ragFilterDTO);
      const res = await similarityFeaturePage(file, ragFilterDTO);

      if (res && res.code === 0) {
        const processedData = await processTrajectoryData(res.data?.data || []);
        setTrajectoryResults(processedData);
        setTrajectoryPagination({
          current: page,
          pageSize: pageSize || trajectoryPagination.pageSize,
          total: res.data?.total || 0,
        });
      }
    } catch (error) {
      console.error('分页查询失败:', error);
    } finally {
      setTrajectoryLoading(false);
    }
  };

  const handleSortConditionChange = (value: 'time' | 'score') => {
    setSortCondition(value);
    // 如果已选择了检测框，重新加载轨迹结果
    if (selectedBox) {
      handleTrajectoryListBySelectBox({
        box: selectedBox,
        pointIds: selectedPointId,
        sortBy: value,
        startTime: startTime,
        endTime: endTime
      });
    }
  };



  const searchConditionOptions = [
    {
      value: 'time',
      label: intl.formatMessage({
        id: 'pages.search.sortByTime',
        defaultMessage: '按最新时间',
      })
    },
    {
      value: 'score',
      label: intl.formatMessage({
        id: 'pages.search.sortBySimilarity',
        defaultMessage: '按相似度',
      })
    },
  ];


  useEffect(() => {
    loadImage();

    // 加载目标检测最低分
    loadParamConfig();
  }, []);

  return (
    <div style={{ height: '100%', display: 'flex', padding: '12px', flexDirection: 'column' }}>
      {/* 面包屑导航 */}
      <div style={{ padding: '16px 24px', borderRadius: '8px 8px 0 0 ', background: '#fff' }}>
        <Breadcrumb>
          <Breadcrumb.Item>
            <LeftCircleOutlined onClick={onBack} style={{ marginRight: 8, cursor: 'pointer', fontSize: '17px' }} />
            <span style={{ color: 'rgba(0, 0, 0, 1)', fontSize: '16px', fontWeight: 500, lineHeight: '24px', letterSpacing: '0px' }}>以图搜图</span>
          </Breadcrumb.Item>

        </Breadcrumb>
      </div>

      {/* 搜索区域 */}
      <div style={{ height: '100%', borderRadius: '0 0 8px 8px', padding: '0px 12px 12px 12px', background: '#fff', borderBottom: '1px solid #f0f0f0' }}>
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {/* 左侧3等分区域 */}
          <Col span={5}>
            <div style={{
              borderRadius: '8px',
              padding: '16px',
              height: '100%',
              minHeight: '200px',
              overflow: "hidden",
              background: 'rgba(248, 250, 253, 1)'
            }}>
              <div style={{
                color: 'rgba(0, 0, 0, 1)',
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: '24px',
                letterSpacing: '0px',
                marginBottom: '12px'
              }}>
                目标图片
              </div>


              {/* 上传图片预览区域 */}
              {/* {uploadedImages.length > 0 && (
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                  marginTop: '12px',
                }}>
                  {uploadedImages.map((image, index) => (
                    <div key={index} style={{
                      position: 'relative',
                      width: '60px',
                      height: '60px',
                      borderRadius: '4px',
                      overflow: 'hidden',
                      border: '1px solid #d9d9d9',
                    }}>
                      <img
                        src={image}
                        alt={`upload-${index}`}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                      />

                    </div>
                  ))}
                </div>
              )} */}

              {/* 搜索结果预览区域 */}
              {searchLoading && (
                <div style={{
                  marginTop: '16px',
                  padding: '20px',
                  textAlign: 'center',
                  color: '#666',
                  height: '100%',
                }}>
                  <Spin size="small" /> 正在识别人物...
                </div>
              )}

              {searchResults.length > 0 && (
                <div style={{
                  marginTop: '16px',
                  // borderTop: '1px solid #e8e8e8',
                  paddingTop: '12px',
                }}>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gap: '12px',
                    maxHeight: 'calc(100vh - 285px)',
                    overflowY: 'auto',
                    paddingRight: '8px',
                    paddingTop: '4px',
                    // height: '100%',
                  }}>
                    {searchResults.map((box) => (
                      <div title="双击放大" key={box.id} style={{
                        position: 'relative',
                        width: '100%',
                        height: '200px',
                        borderRadius: '8px',
                        overflow: 'hidden',
                        border: selectedBox?.id === box.id ? '2px solid #aaaaaa' : '1px solid transparent',
                        cursor: 'pointer',
                        backgroundColor: 'rgb(238, 238, 238)',
                        transition: 'all 0.3s ease',
                        // boxShadow: selectedBox?.id === box.id ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : 'none',
                      }}
                        onMouseEnter={(e) => {
                          if (selectedBox?.id !== box.id) {
                            e.currentTarget.style.border = '1px solid #aaaaaa';
                            e.currentTarget.style.transform = 'translateY(-2px)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (selectedBox?.id !== box.id) {
                            e.currentTarget.style.border = '1px solid transparent';
                            e.currentTarget.style.transform = 'translateY(0)';
                          }
                        }}
                        onClick={() => {
                          // 触发点击事件 左侧单机，右侧显示查询
                          handleTrajectoryListBySelectBox({
                            box: box,
                            pointIds: selectedPointId,
                            sortBy: sortCondition,
                            startTime: startTime,
                            endTime: endTime
                          });
                        }}
                        onDoubleClick={() => {
                          setPreviewImage(box.croppedImage);
                          setPreviewVisible(true);
                        }}
                      >
                        <img
                          src={box.croppedImage}
                          alt={`person-${box.displayIndex}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                          }}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/icons/search/default-noImage.png';
                          }}
                        />
                        <div style={{
                          position: 'absolute',
                          top: '4px',
                          right: '4px',
                          background: 'rgba(0, 0, 0, 0.7)',
                          color: 'white',
                          fontSize: '12px',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontWeight: 'bold',
                        }}>
                          {Math.round(box.score * 100)}%
                        </div>

                      </div>
                    ))}
                  </div>
                </div>
              )}

              {(searchResults.length === 0) && (
                <div style={{
                  marginTop: '16px',
                  padding: '20px',
                  textAlign: 'center',
                  color: '#666',
                  fontSize: '14px',
                }}>
                  未检测到人物，请尝试其他图片
                </div>
              )}
            </div>
          </Col>

          {/* 右侧7等分区域 */}
          <Col span={19}>
            <div style={{
              borderRadius: '8px',
              padding: '16px',
              background: 'rgba(248, 250, 253, 1)',
              height: '100%',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}>
              <div style={{
                color: 'rgba(0, 0, 0, 1)',
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: '24px',
                letterSpacing: '0px',
                marginBottom: '12px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  轨迹结果集
                  {trajectoryResults.length > 0 && (
                    <span style={{ fontSize: '14px', color: '#666', fontWeight: 'normal' }}>
                      (共 {trajectoryPagination.total} 条记录)
                    </span>
                  )}
                </div>

                {/* 检索条件选择框 */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexWrap: 'wrap' }}>
                  {/* 使用公共的时间和点位选择组件 */}
                  <SearchConditions
                    startTime={startTime}
                    endTime={endTime}
                    onTimeChange={(newStartTime, newEndTime) => {
                      setStartTime(newStartTime);
                      setEndTime(newEndTime);
                      if (selectedBox) {
                        handleTrajectoryListBySelectBox({
                          box: selectedBox,
                          pointIds: selectedPointId,
                          sortBy: sortCondition,
                          startTime: newStartTime,
                          endTime: newEndTime
                        });
                      }
                    }}
                    selectedPointIds={selectedPointId}
                    onPointChange={(pointIds) => {
                      setSelectedPointId(pointIds);
                      if (selectedBox) {
                        handleTrajectoryListBySelectBox({
                          box: selectedBox,
                          pointIds: pointIds,
                          sortBy: sortCondition,
                          startTime: startTime,
                          endTime: endTime
                        });
                      }
                    }}
                    showTimeSelector={true}
                    showPointSelector={true}
                    compact={false}
                  />

                  {/* 排序选择器 */}
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label style={{ marginRight: '8px', fontSize: '14px', color: '#666' }}>
                      {intl.formatMessage({ id: 'pages.search.filter', defaultMessage: '检索条件', })}：
                    </label>
                    <Select
                      value={sortCondition}
                      style={{ width: 160 }}
                      options={searchConditionOptions}
                      onChange={handleSortConditionChange}
                      className="condition-select"
                      popupClassName="condition-select-dropdown"
                    />
                  </div>
                </div>
              </div>

              {trajectoryLoading && (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <Spin size="large" />
                </div>
              )}

              {!trajectoryLoading && trajectoryResults.length === 0 && (
                <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                  <Empty
                    description="暂无轨迹数据，请在左侧选择人物进行查询"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              )}

              {trajectoryResults.length > 0 && (
                <>
                  <div style={{
                    height: 'calc(100vh - 250px)', // 面包屑(68px) + 搜索padding(48px) + 标题(52px) + 分页(48px) + 边距(24px)
                    maxHeight: 'calc(100vh - 250px)',
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden',
                    minHeight: '300px', // 确保最小高度
                  }}>
                    <div
                      className="trajectory-grid-container"
                      style={{
                        flex: 1,
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fill, minmax(min(248px, 100%), 1fr))',
                        gridAutoRows: 'max-content',
                        gap: '16px',
                        overflowY: 'auto',
                        overflowX: 'hidden',
                        alignContent: 'start',
                        // padding: '2px 8px 2px 2px',
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#c1c1c1 transparent',
                        // 确保网格容器有明确的高度
                        minHeight: 0, // 允许flex子项收缩
                      }}>
                      {trajectoryResults.map((item, index) => {

                        return (
                          <Card
                            key={item.embeddingId || index}
                            hoverable
                            style={{
                              borderRadius: '8px',
                              overflow: 'hidden',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              cursor: 'pointer',
                            }}
                            styles={{ body: { padding: 0, height: '75px' } }}
                            cover={
                              <div style={{ position: 'relative', width: '100%', height: '115px', overflow: 'hidden' }}>
                                <CanvasImageWithBox
                                  src={item.thumbUrl || '/icons/search/default-noImage.png'}
                                  coordinate={item.metadata?.coordinate || ''}
                                  width={248}
                                  height={115}
                                  name={item.metadata?.name}
                                  time={item.time}
                                  address={item.address}
                                  score={typeof item.score === 'string' ? parseFloat(item.score.replace('%', '')) / 100 : item.score}
                                  fetchImage={fetchImage}
                                  onImageClick={(data: any) => {
                                    // 调用父组件的onImageClick，传递处理后的图片URL
                                    if (onImageClick) {
                                      onImageClick({
                                        name: data.name,
                                        imageUrl: data.imageUrl, // 这里使用处理后的blob URL
                                        thumbUrl: data.thumbUrl,
                                        coordinate: data.coordinate,
                                        time: data.time,
                                        address: data.address,
                                        score: data.score
                                      });
                                    }
                                  }}
                                />
                              </div>
                            }
                          >
                            <div style={{ padding: '5px 12px 12px 12px' }}>
                              <p style={infoTextStyle}>
                                <img src="/icons/search/date.svg" style={iconStyle} />
                                {item.time}
                              </p>
                              <p style={infoTextStyle}>
                                <img src="/icons/search/local.svg" style={iconStyle} />
                                {item.address}
                              </p>
                              <p style={infoTextStyle}>
                                <img src="/icons/search/score.svg" style={iconStyle} />
                                {item.score}
                              </p>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
                      <Pagination
                        current={trajectoryPagination.current}
                        pageSize={trajectoryPagination.pageSize}
                        total={trajectoryPagination.total}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total) => `共 ${total} 条记录`}
                        onChange={handleTrajectoryPageChange}
                        onShowSizeChange={handleTrajectoryPageChange}
                      />
                    </div>

                  </div>
                </>
              )}

            </div>
          </Col>
        </Row>
      </div>

      {/* 图片预览组件 */}
      <Image
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => setPreviewVisible(visible),
        }}
        style={{ display: 'none' }}
      />

    </div>
  );
};

export default ImageSearchPage;