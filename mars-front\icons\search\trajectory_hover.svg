<svg viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30.000000" height="30.000000" fill="none" customFrame="#000000">
	<defs>
		<g id="pixso_custom_effect_0">
			<effect x="0.000000" y="1.000000" visibility="visible" fill="rgb(0,0,0)" fill-opacity="0.100000001" effectType="dropShadow" stdDeviation="2" radius="0" />
		</g>
		<filter id="filter_0" width="30.000000" height="30.000000" x="0.000000" y="0.000000" filterUnits="userSpaceOnUse" customEffect="url(#pixso_custom_effect_0)" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feColorMatrix result="hardAlpha" in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0 " />
			<feOffset dx="0.000000" dy="1.000000" in="hardAlpha" />
			<feGaussianBlur stdDeviation="0.666666687" />
			<feComposite k2="-1" k3="1" in2="hardAlpha" operator="out" />
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0 " />
			<feBlend result="effect_dropShadow_1" in2="BackgroundImageFix" mode="normal" />
			<feBlend result="shape" in="SourceGraphic" in2="effect_dropShadow_1" mode="normal" />
		</filter>
		<clipPath id="clipPath_0">
			<rect width="18.000000" height="18.000000" x="6.000000" y="5.000000" fill="rgb(255,255,255)" />
		</clipPath>
	</defs>
	<g id="轨迹icon">
		<g filter="url(#filter_0)">
			<circle id="椭圆 9" cx="15" cy="14" r="13" fill="rgb(255,255,255)" fill-opacity="0.899999976" />
		</g>
		<g id="查看轨迹" clip-path="url(#clipPath_0)" customFrame="url(#clipPath_0)">
			<rect id="查看轨迹" width="18.000000" height="18.000000" x="6.000000" y="5.000000" />
			<path id="矢量 43" d="M20.5271 17.2127C21.9561 17.2442 23.099 18.3861 23.099 19.7837C23.099 21.1822 21.9561 22.3242 20.5271 22.3547L15.7468 22.3547C15.4498 22.8587 14.8424 23.1053 14.2673 22.9541C14.1985 22.9367 14.1316 22.9139 14.0666 22.8855C14.0015 22.8571 13.9393 22.8236 13.8798 22.7849C13.8202 22.7463 13.7643 22.703 13.7119 22.6551C13.6595 22.6073 13.6114 22.5554 13.5676 22.4996C13.5238 22.4438 13.4848 22.3848 13.4507 22.3226C13.4166 22.2604 13.3878 22.1958 13.3644 22.1288C13.3409 22.0618 13.3231 21.9934 13.3109 21.9235C13.2987 21.8536 13.2924 21.7832 13.2918 21.7122C13.2918 21.13 13.6923 20.6206 14.2673 20.4703C14.3364 20.452 14.4066 20.4393 14.4777 20.4322C14.5489 20.4252 14.6201 20.4239 14.6915 20.4284C14.7629 20.4329 14.8334 20.443 14.9031 20.4589C14.9728 20.4748 15.0408 20.4961 15.1071 20.523C15.1734 20.5498 15.2371 20.5818 15.2982 20.619C15.3593 20.6561 15.417 20.6979 15.4714 20.7443C15.5257 20.7908 15.576 20.8413 15.6222 20.8959C15.6684 20.9505 15.7099 21.0084 15.7468 21.0697L20.5271 21.0697C21.2533 21.0697 21.8419 20.4937 21.8419 19.7828C21.8419 19.0737 21.2533 18.4977 20.5271 18.4977L16.8105 18.4977C17.2334 18.0658 17.624 17.6365 17.983 17.2118L20.5271 17.2127L20.5271 17.2127ZM14.6084 5C17.3873 5 19.6875 7.11209 19.8603 9.82351L19.8675 9.9828L19.8675 10.3284C19.7928 12.6339 18.325 15.1951 15.4642 18.0109L15.0736 18.3933C15.0456 18.4199 15.0154 18.4438 14.9831 18.4651C14.9508 18.4864 14.917 18.5047 14.8815 18.5201C14.846 18.5354 14.8095 18.5476 14.7719 18.5565C14.7343 18.5655 14.6962 18.5711 14.6576 18.5734C14.619 18.5757 14.5805 18.5746 14.5421 18.5702C14.5037 18.5657 14.466 18.558 14.429 18.5469C14.3919 18.5358 14.3561 18.5217 14.3216 18.5043C14.287 18.487 14.2542 18.4668 14.2232 18.4437L14.162 18.3933L13.973 18.2134C11.0492 15.3904 9.5158 12.822 9.36911 10.5074L9.36011 10.3149L9.36011 10.1439C9.35741 7.30917 11.7062 5.0072 14.6075 5L14.6084 5ZM8.69058 13.3566L8.73557 13.3566C8.92636 13.7975 9.14323 14.2277 9.38711 14.6434L8.69058 14.6434C7.96345 14.6434 7.37491 15.2185 7.37491 15.9285C7.37491 16.6394 7.96345 17.2145 8.69058 17.2145L11.2283 17.2145C11.591 17.6383 11.9815 18.0676 12.4009 18.5004L8.69058 18.5004C8.57477 18.5034 8.45927 18.4988 8.34406 18.4866C8.22886 18.4744 8.11495 18.4547 8.00234 18.4275C7.88972 18.4004 7.77937 18.3659 7.67128 18.3243C7.56318 18.2826 7.45828 18.234 7.35657 18.1786C7.25486 18.1231 7.15722 18.0612 7.06364 17.993C6.97006 17.9247 6.88134 17.8505 6.7975 17.7706C6.71366 17.6907 6.63541 17.6056 6.56275 17.5153C6.49009 17.4251 6.42365 17.3305 6.36341 17.2316C5.88016 16.427 5.88016 15.4308 6.36341 14.6263C6.42365 14.5274 6.49009 14.4328 6.56275 14.3425C6.63541 14.2523 6.71366 14.1672 6.7975 14.0873C6.88135 14.0073 6.97006 13.9332 7.06364 13.8649C7.15722 13.7967 7.25486 13.7348 7.35657 13.6793C7.45828 13.6239 7.56318 13.5753 7.67128 13.5336C7.77937 13.492 7.88972 13.4575 8.00234 13.4304C8.11495 13.4032 8.22886 13.3835 8.34406 13.3713C8.45927 13.3591 8.57477 13.3545 8.69058 13.3575L8.69058 13.3566ZM14.6084 8.21448C13.8102 8.21448 13.0911 8.68423 12.7852 9.40506C12.4792 10.1259 12.6493 10.9556 13.2135 11.5072C13.283 11.5746 13.357 11.6365 13.4354 11.6932C13.5138 11.7498 13.5959 11.8005 13.6817 11.8452C13.7675 11.89 13.856 11.9283 13.9473 11.9603C14.0387 11.9922 14.1318 12.0174 14.2268 12.0359C14.3217 12.0544 14.4175 12.0659 14.5142 12.0706C14.6108 12.0752 14.7073 12.0728 14.8035 12.0635C14.8998 12.0542 14.995 12.038 15.0889 12.0149C15.1829 11.9918 15.2747 11.9621 15.3643 11.9257C15.4532 11.8905 15.539 11.8488 15.6216 11.8008C15.7043 11.7528 15.783 11.699 15.8577 11.6393C15.9324 11.5796 16.0022 11.5147 16.0672 11.4446C16.1322 11.3745 16.1917 11.3 16.2457 11.221C16.2996 11.1421 16.3474 11.0596 16.3891 10.9736C16.4308 10.8875 16.4658 10.7989 16.4943 10.7076C16.5228 10.6163 16.5444 10.5235 16.5591 10.429C16.5737 10.3345 16.5813 10.2395 16.5819 10.1439C16.5819 9.07839 15.6982 8.21448 14.6084 8.21448L14.6084 8.21448Z" fill="rgb(11,211,87)" fill-rule="nonzero" />
		</g>
	</g>
</svg>
