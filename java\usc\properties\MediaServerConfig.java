package com.zkteco.mars.usc.properties;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 流媒体配置文件
 * <AUTHOR>
 * @date  2025-01-09 11:35
 * @since 1.0.0
 */
@Component
@ConfigurationProperties
@Data
public class MediaServerConfig {

    /**
     * 流媒体地址
     */
    @Value("${mediaServer.url:}")
    private String url;

    /**
     * 流媒体密钥
     */
    @Value("${mediaServer.secret:}")
    private String secret;

    /**
     * 流媒体抓拍路径
     */
    @Value("${mediaServer.snapPath:}")
    private String snapPath;

    /**
     * 流媒体抓拍文件前缀
     */
    @Value("${mediaServer.snapPrefix:}")
    private String snapPrefix;
}