import { Button, message, Tag } from "antd"
import { useState, useRef, useEffect } from "react"
import { connect, useIntl, useNavigate, useLocation } from 'umi';
import { ProForm, ProCard, ProFormInstance } from '@ant-design/pro-components';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { setAgentStatus } from "@/services/ant-design-pro/api";
import Chat from "./components/Chat";
import { LeftOutlined } from "@ant-design/icons";
import RcResizeObserver from 'rc-resize-observer';
import Middle from "./components/Middle";
import Left from "./components/Left";

const contentStyle: React.CSSProperties = {
};

const AgentArrange: React.FC = (props) => {
    const [responsive, setResponsive] = useState(false);
    const formRef = useRef<ProFormInstance>();

    const navigate = useNavigate();

    /** 国际化 */
    const intl = useIntl();

    const location = useLocation();
    // 接收传递的状态值
    const agentData = location.state;
    console.log(" agentData:", agentData);
    //  左侧数据
    const leftData = {
        name: agentData?.data?.agent_item?.name || '',
        description: agentData?.data?.agent_item?.description || '',
        prompt: agentData?.data?.agent_item?.prompt || '',
        created_at: agentData?.data?.agent_item?.created_at || '',
        type: agentData?.data?.agent_item?.type || '',
    }
    //  对话数据
    const chatData = {
        name: agentData?.data?.agent_item.name || '',
        opening_statement: agentData?.data?.agent_item.opening_statement || '',
        suggested_questions: agentData?.data?.agent_item.suggested_questions || [],
        agent_id: agentData?.data?.id || '',
        external_user_id: Math.floor(Math.random() * 100000) + '',
        type: agentData?.data?.agent_item?.type || '',
    }

    const [status, setStatus] = useState<number>(agentData?.data?.agent_item?.status || 1);
    const isPublished = status === 2;

    // 回退到上一个页面
    const goBack = () => {
        navigate(-1);
    }

    // 发布智能体
    const publishAgent = async () => {
        if (chatData.agent_id != '') {
            try {
                const param = {
                    id: chatData.agent_id,
                    status: 2
                }
                const rs = await setAgentStatus(param);
                if (rs.code === 0) {
                    setStatus(2);
                    message.success(intl.formatMessage({ id: 'pages.agent.publishSuccess', defaultMessage: "发布成功" }));
                } else {
                    message.error(intl.formatMessage({ id: 'pages.agent.publishFailed', defaultMessage: "发布失败" }));
                }
            } catch (err) {
                console.error(intl.formatMessage({ id: 'pages.agent.publishFailed', defaultMessage: "发布失败" }), err);
                message.error(intl.formatMessage({ id: 'pages.agent.publishFailed', defaultMessage: "发布失败" }));
            }
        } else {
            message.error(intl.formatMessage({ id: 'pages.agent.publishNotAllowed', defaultMessage: "抱歉，该智能体不能进行发布" }));
        }
    }

    useEffect(() => {
        if (agentData) {
            formRef.current?.setFieldsValue({
                // prompt: middleData.prompt,
                model: leftData.modelName
            });
        }
    }, [agentData]);

    return (

        <RcResizeObserver
            key="resize-observer"
            onResize={(offset) => {
                setResponsive(offset.width < 596);
            }}
        >
            <ProForm
                formRef={formRef}
                submitter={false} // 隐藏默认的提交按钮
            >
                {/* 头部 */}
                <ProCard
                    title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <Button
                                onClick={goBack}
                            >
                                <LeftOutlined />
                                {/* 操作 */}
                            </Button>
                            <span>{agentData?.data.agent_item.name || intl.formatMessage({ id: 'pages.agent.config', defaultMessage: "智能体配置" })}</span>
                        </div>
                    }
                    extra={
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <Tag
                                color={isPublished ? 'green' : 'red'}
                                icon={isPublished ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                            >
                                {isPublished ? '已发布' : '未发布'}
                            </Tag>
                            <Button
                                type="primary"
                                onClick={publishAgent}
                            >
                                {intl.formatMessage({ id: 'pages.agent.publish', defaultMessage: '发布' })}
                            </Button>
                        </div>
                    }

                    split={responsive ? 'horizontal' : 'vertical'}
                    bordered
                    headerBordered
                    style={{ height: '80vh' }}
                >
                    {/* 左侧 */}
                    <ProCard colSpan="30%" style={{ height: '100%' }}>
                        <Left leftData={leftData} ></Left>
                    </ProCard>

                    {/*  右侧*/}
                    <ProCard title={intl.formatMessage({ id: 'pages.agent.debug', defaultMessage: "智能体调试" })} style={{
                        height: '100%',

                    }}>

                        <Chat chatData={chatData}></Chat>
                    </ProCard>
                </ProCard>
            </ProForm>
        </RcResizeObserver >
    )
}

export default connect()(AgentArrange);
