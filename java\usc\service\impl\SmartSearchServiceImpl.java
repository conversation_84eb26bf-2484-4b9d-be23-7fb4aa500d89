package com.zkteco.mars.usc.service.impl;

import com.zkteco.framework.core.exception.ZKBusinessException;
import com.zkteco.framework.vo.ApiResultMessage;

import com.zkteco.mars.ai.dto.RagFilterDTO;
import com.zkteco.mars.ai.service.ImageFeatureService;
import com.zkteco.mars.ai.service.ReidFeatureService;
import com.zkteco.mars.ai.service.Speech2TextService;
import com.zkteco.mars.ai.service.TextFeatureService;
import com.zkteco.mars.usc.constant.USCConstants;
import com.zkteco.mars.usc.dao.UscSearchRecordDao;
import com.zkteco.mars.usc.model.UscSearchRecord;
import com.zkteco.mars.usc.service.SmartSearchService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 智能搜索
 *
 * <AUTHOR>
 * @date 2024-12-31 14:36
 * @since 1.0.0
 */
@Slf4j
@Service
public class SmartSearchServiceImpl implements SmartSearchService {

    @Autowired(required = false)
    private ReidFeatureService reidFeatureService;

    @Autowired
    private UscSearchRecordDao uscSearchRecordDao;

    @Autowired(required = false)
    private TextFeatureService textFeatureService;

    @Autowired(required = false)
    private Speech2TextService speech2TextService;

    @Autowired(required = false)
    private ImageFeatureService imageFeatureService;


    @Override
    public ApiResultMessage getHistoryRecord() {
        List<UscSearchRecord> all = uscSearchRecordDao.findAll();
        return ApiResultMessage.successMessage(all);
    }

    @Override
    public ApiResultMessage getHotSearch() {
        return null;
    }

    @Override
    public ApiResultMessage smartSearch(RagFilterDTO ragFilterDTO) {
        return textFeatureService.similaritySearch(ragFilterDTO);
    }

    @Override
    @SneakyThrows
    public ApiResultMessage voiceToText(MultipartFile multipartFile) {
        // 1. 创建目标文件
        File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        // 2. 将 MultipartFile 内容写入文件
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(multipartFile.getBytes());
        }
        return speech2TextService.speech2Text(file);
    }

    @Override
    public ApiResultMessage getTargetImageList(String imageName) {
        String[] fileNameArray = imageName.split("_");
        if (fileNameArray.length < 2) {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, "imageName is illegality.");
        }

        String timeStamp = fileNameArray[0];
        String channelId = fileNameArray[1];
        String rootPath = USCConstants.SNAP_TARGET_FOLDER_PATH;
        List<File> targetFiles = findFilesByPattern(rootPath, timeStamp, channelId);

        List<String> fileNames = targetFiles.stream()
                .map(File::getName)
                .collect(Collectors.toList());
        return ApiResultMessage.successMessage(fileNames);
    }

    @Override
    public ApiResultMessage imageSearch(MultipartFile multipartFile) {
        try (InputStream inputStream = multipartFile.getInputStream()) {
            return imageFeatureService.detectObject(inputStream);
        } catch (IOException e) {
            throw ZKBusinessException.errorException("文件读取失败");
        }
    }

    @Override
    public ApiResultMessage similarityFeaturePage(MultipartFile multipartFile, RagFilterDTO ragFilterDTO) {
        try (InputStream inputStream = multipartFile.getInputStream()) {
            return reidFeatureService.similarityFeaturePage(inputStream, ragFilterDTO);
        } catch (IOException e) {
            throw ZKBusinessException.errorException("文件读取失败");
        }
    }


    @Override
    public ResponseEntity<Resource> getImageFile(String imageName, String imageType) {
        String pngImage = imageName;
        if (pngImage.endsWith(".png")) {
            pngImage = pngImage.substring(0, imageName.lastIndexOf(".png"));
        }
        String[] fileNameArray = pngImage.split("_");
        if (fileNameArray.length < 2) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }

        String timeStamp = fileNameArray[0];
        String channelId = fileNameArray[1];

        // 兼容新老版本时间戳（新：17 位，老：14 位）
        boolean isNewFormat = timeStamp.length() == 17;
        String datePart = isNewFormat ? timeStamp.substring(0, 8) : timeStamp;
        String fileName = timeStamp + ".png"; // 文件名格式

        String rootPath;
        switch (imageType.toLowerCase()) {
            case "thumb":
                rootPath = USCConstants.SNAP_THUMBNAIL_FOLDER_PATH;
                break;
            case "snap":
                rootPath = USCConstants.SNAP_SAVE_FOLDER_PATH;
                break;
            case "target":
                rootPath = USCConstants.SNAP_TARGET_FOLDER_PATH;
                File oldFile = findFileByName(rootPath, imageName);
                if (oldFile != null) {
                    return serveFile(oldFile);
                } else {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
                }
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }


        // 新版本的分层目录结构
        Path newFilePath = Paths.get(rootPath, channelId,
                datePart.substring(0, 4) + "-" + datePart.substring(4, 6),
                datePart.substring(6, 8),
                fileName);
        File newFile = newFilePath.toFile();

        if (newFile.exists()) {
            return serveFile(newFile);
        }

        // 如果新版本路径不存在，则回退到老版本（所有图片存一个目录）
        File oldFile = findFileByName(rootPath, imageName);
        if (oldFile != null) {
            return serveFile(oldFile);
        }

        log.error("Image not found: {}", imageName);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }


    /**
     * 模糊查询
     *
     * @param folderPath:
     * @param timeStamp:
     * @param channelId:
     * @return java.util.List<java.io.File>
     * @throws
     * <AUTHOR>
     * @date 2025-03-19 15:24
     * @since 1.0.0
     */
    private List<File> findFilesByPattern(String folderPath, String timeStamp, String channelId) {
        File folder = new File(folderPath);
        List<File> matchedFiles = new ArrayList<>();

        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.getName().startsWith(timeStamp + "_" + channelId)) {
                        matchedFiles.add(file);
                    }
                }
            }
        }
        return matchedFiles;
    }

    /**
     * 读取并返回文件资源
     */
    private ResponseEntity<Resource> serveFile(File file) {
        try {
            InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_PNG)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + file.getName())
                    .body(resource);
        } catch (FileNotFoundException e) {
            log.error("File not found: {}", file.getAbsolutePath(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }


    /**
     * 通过文件名在指定文件夹中查找文件
     *
     * @param folderPath : 文件夹路径
     * @return java.io.File
     * @throws
     * <AUTHOR>
     * @date 2025-01-03 10:36
     * @since 1.0.0
     */
    private static File findFileByName(String folderPath, String fileName) {
        File folder = new File(folderPath);
        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.getName().equals(fileName)) {
                        return file;
                    }
                }
            }
        }
        return null;
    }

}
