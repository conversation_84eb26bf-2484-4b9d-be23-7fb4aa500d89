/**
 * 图片工具函数测试
 */

import { drawImageWithBox, getProcessedImageBlob } from '../imageUtils';

// Mock DOM APIs
Object.defineProperty(window, 'Image', {
  writable: true,
  value: class MockImage {
    onload: (() => void) | null = null;
    onerror: (() => void) | null = null;
    src: string = '';
    width: number = 1280;
    height: number = 720;
    crossOrigin: string | null = null;

    constructor() {
      // 模拟图片加载成功
      setTimeout(() => {
        if (this.onload) {
          this.onload();
        }
      }, 10);
    }
  }
});

// Mock canvas
const mockCanvas = {
  width: 0,
  height: 0,
  getContext: jest.fn(() => ({
    clearRect: jest.fn(),
    drawImage: jest.fn(),
    strokeRect: jest.fn(),
    strokeStyle: '',
    lineWidth: 0,
  })),
  toBlob: jest.fn((callback) => {
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    callback(mockBlob);
  })
};

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
global.URL.revokeObjectURL = jest.fn();

describe('imageUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('drawImageWithBox', () => {
    it('should draw image with coordinate box', async () => {
      const coordinate = '[100, 100, 200, 200]';
      
      await drawImageWithBox(
        mockCanvas as any,
        'test-image.jpg',
        coordinate,
        248,
        115
      );

      expect(mockCanvas.getContext).toHaveBeenCalled();
    });

    it('should handle invalid coordinate gracefully', async () => {
      const coordinate = 'invalid-json';
      
      await expect(
        drawImageWithBox(
          mockCanvas as any,
          'test-image.jpg',
          coordinate,
          248,
          115
        )
      ).resolves.not.toThrow();
    });
  });

  describe('getProcessedImageBlob', () => {
    const mockFetchImage = jest.fn().mockResolvedValue('http://example.com/image.jpg');

    it('should process image and return blob URL', async () => {
      const coordinate = '[100, 100, 200, 200]';
      
      const result = await getProcessedImageBlob(
        mockFetchImage,
        'test-image',
        coordinate
      );

      expect(mockFetchImage).toHaveBeenCalledWith('test-image', 'snap');
      expect(result).toBe('blob:mock-url');
    });

    it('should handle missing image URL', async () => {
      const mockFetchImageFail = jest.fn().mockResolvedValue(null);
      
      await expect(
        getProcessedImageBlob(mockFetchImageFail, 'test-image')
      ).rejects.toThrow('获取原图URL失败');
    });
  });
});
