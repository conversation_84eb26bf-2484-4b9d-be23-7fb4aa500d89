package com.zkteco.mars.usc.dao;

import com.zkteco.framework.dao.BaseDao;
import com.zkteco.mars.usc.model.PointControlRules;

import java.util.List;

/**
 * 事件类型
 * <AUTHOR>
 * @date  2025-04-09 17:09
 * @since 1.0.0
 */
public interface PointControlRulesDao extends BaseDao<PointControlRules, String> {

    /**
     * 通过 pointId 查询
     * @param pointId
     * @return
     */
     List<PointControlRules> findByPointId(String pointId);

    void deleteByPointId(String pointId);

    /**
     * 判断指定 ruleId 是否存在对应的记录
     * <AUTHOR>
     * @date  2025-06-05 13:52
     * @since 1.0.0
     */
    boolean existsByRuleId(String ruleId);
}
