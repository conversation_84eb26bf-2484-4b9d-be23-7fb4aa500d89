import { forwardRef, useState, useRef, useEffect, useImperativeHandle } from "react";
import { voiceUpload } from '@/services/ant-design-pro/api';
import { Tooltip } from "antd";
import { useIntl } from '@umijs/max';

const AudioRecorder = forwardRef((props: any, ref) => {
  const [isRecording, setIsRecording] = useState(false);

  const [volume, setVolume] = useState(10);

  const mediaRecorderRef = useRef(null);

  const audioChunksRef = useRef([]);

  const analyserRef = useRef(null);

  const audioContextRef = useRef(null);

  const dataArrayRef = useRef(null);

  const isRecordingRef = useRef(false);

  const stopRecordingTimeout = useRef(null); // 用于延迟停止录音

  const { handleVoiceToText } = props;

  const intl = useIntl();


  useImperativeHandle(ref, () => ({
    // 子组件暴露方法给父组件用
    stopRecording
  }));



  // 监听音量变化（无论是否录音，都会检测）
  useEffect(() => {
    if (isRecording) {
      console.log("音量变化:", volume);
      // 录音模式下：控制录音开始/停止
      // if (volume > 10 && mediaRecorderRef.current) {
      //   console.log("监听...");
      //   startRecording();
      // }

      if (volume <= 10) {
        if (stopRecordingTimeout.current) {
          clearTimeout(stopRecordingTimeout.current);
        }

        stopRecordingTimeout.current = setTimeout(() => {
          if (volume <= 10 && mediaRecorderRef.current) {
            console.log("音量持续低于10，停止录音...");
            stopRecording();
            // mediaRecorderRef.current.stop();
            // startRecording();
          }
        }, 3000);
      }
    }
  }, [volume, isRecording]);



  const startRecording = async () => {
    //清空 搜索框内容
    handleVoiceToText("start", "");

    console.log("开始录音");
    setIsRecording(true);
    isRecordingRef.current = true;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);

      analyser.fftSize = 256;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      dataArrayRef.current = dataArray;
      analyserRef.current = analyser;
      audioContextRef.current = audioContext;

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // 绑定 `onstop` 事件，确保能触发上传
      mediaRecorder.onstop = async () => {
        console.log("录音已停止，准备上传音频...");
        console.log("audioChunksRef.current.length == ", audioChunksRef.current.length)
        if (audioChunksRef.current.length > 0) {
          const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" });

          console.log("上传音频数据:", audioChunksRef.current);
          await uploadAudio(audioBlob); // 上传音频

          // // 生成本地下载链接
          // const audioUrl = URL.createObjectURL(audioBlob);
          // const link = document.createElement("a");
          // link.href = audioUrl;
          // link.download = "recording.wav"; // 设置下载的文件名
          // link.click(); // 触发下载

          // // 释放 URL
          // URL.revokeObjectURL(audioUrl);

          audioChunksRef.current = []; // 确保数据上传后再清空
        } else {
          console.log("没有音频数据，不进行上传");
        }

        // setTimeout(() => {
        //   setIsRecording(false); // 允许重新开始录音
        // }, 500);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();

      const updateVolume = () => {
        if (!analyserRef.current || !dataArrayRef.current) return;
        analyserRef.current.getByteFrequencyData(dataArrayRef.current);
        const volumeLevel =
          dataArrayRef.current.reduce((sum, value) => sum + value, 0) /
          dataArrayRef.current.length;
        setVolume(Math.max(10, volumeLevel)); // 最小值为5，防止无限触发
        if (isRecordingRef.current) requestAnimationFrame(updateVolume);
      };
      requestAnimationFrame(updateVolume);
    } catch (error) {
      console.error("无法访问麦克风:", error);
      setIsRecording(false);
      isRecordingRef.current = false;
    }
  };

  const stopRecording = () => {
    console.log("结束录音");
    clearTimeout(stopRecordingTimeout.current);
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
    isRecordingRef.current = false;
    setVolume(10);
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  };

  const uploadAudio = async (audioBlob: any) => {
    console.log("上传音频...");
    try {
      const res = await voiceUpload(audioBlob);
      console.log("识别结果:", res);
      if (res.code == 0 && res?.data) {
        handleVoiceToText("upload", res.data.text)
      }

    } catch (error) {
      console.error("上传失败:", error);
    }
  };


  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {!isRecording ? (
        <Tooltip title={intl.formatMessage({ id: 'pages.pointManage.voiceInput', defaultMessage: '语音输入', })}>
          <div
            onClick={startRecording}
            style={{
              width: "32px",
              height: "32px",
              borderRadius: "25%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#DAFEE8")}
            onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "")}
          >
            <img src="/icons/search/voice.svg" alt={intl.formatMessage({ id: 'pages.pointManage.speechRecognition', defaultMessage: '语音识别', })} width={20} height={20} />
          </div>
        </Tooltip>
      ) : (
        <Tooltip title={intl.formatMessage({ id: 'pages.pointManage.stopVoiceInput', defaultMessage: '停止语音输入', })}>
          <div
            onClick={stopRecording}
            style={{
              width: "32px",
              height: "32px",
              backgroundColor: "#DAFEE8",
              borderRadius: "25%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "2px",
              cursor: "pointer",
              overflow: "hidden",
            }}
          >
            {[...Array(5)].map((_, index) => {
              const barHeight = Math.max(10, Math.min(volume - (4 - index) * 6, 24));
              return (
                <div
                  key={index}
                  style={{
                    width: "3px",
                    height: "24px",
                    background: "linear-gradient(180deg, #0BD357, #7EF9AD)",
                    borderRadius: "2px",
                    transform: `scaleY(${barHeight / 70})`,
                    transition: "transform 0.1s ease-in-out",
                  }}
                />
              );
            })}
          </div>
        </Tooltip>
      )}
    </div>
  );
})

export default AudioRecorder;
