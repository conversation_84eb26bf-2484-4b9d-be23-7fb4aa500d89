// @ts-ignore
/* eslint-disable */
import { request, getDvaApp } from '@umijs/max';
const prefix = "/api";
let accessToken = 'BF8C28E9DB4A16E35CE4CB3C4A265D5C887FCD45DC54AA2420496DCD270683B8045A4F222AE3C58348A3F8B4324CEED2'
var defaultAccessToken = accessToken

/**
 * 获取默认AccessToken
 * return defaultAccessToken
 */
export function getDefaultAccessToken() {
  return defaultAccessToken;
}

/**
 * 获取用户秘钥
 * return AccessToken
 **/
export function getAccessToken() {
  const dvaApp = getDvaApp()
  const user = dvaApp._store.getState().user
  return user.accessToken
}

/**
 * 设置全局AccessToken
 * @param data AccessToken
 */
export async function setAccessToken(data: string) {
  accessToken = data
}



/** 获取当前的用户 /api/currentUser */
export async function currentUser(data: string) {
  return request<API.CurrentUser>(prefix + '/v1/auth/user/currentUser?access_token=' + data, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/** 退出登录接口 POST  */
export async function outLogin(options?: { [key: string]: any }) {
  return request<Record<string, any>>(prefix + '/v1/auth/user/logout?access_token=' + getAccessToken(), {
    method: 'POST',
    ...(options || {}),
  });
}

/** 登录接口 POST  */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>(prefix + '/v1/auth/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}


/** 获取当前用户历史记录 POST */
export async function getHistoryRecord() {
  const rs = request<any>(prefix + '/v1/smart_search/getHistoryRecord?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 智能搜索 (第一步先查询信息)*/
export async function smartSearch(data: Object) {
  const rs = request<any>(prefix + '/v1/smart_search/search?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 智能搜索 (第二步根据照片ID查询缩略图 或者原图)*/
export async function getImageFile(imageName: string, imageType: string) {
  const rs = fetch(prefix + '/v1/smart_search/getImageFile?access_token=' + getAccessToken() + "&imageName=" + imageName + "&imageType=" + imageType, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 模糊查询抠图列表 */
export async function getTargetImageList(imageName: string) {
  const rs = request<any>(prefix + '/v1/smart_search/getTargetImageList?access_token=' + getAccessToken() + "&imageName=" + imageName, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 以图搜图第二步（相似度检索）分页 */
export async function similarityFeaturePage(file: File, ragFilterDTO: any) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('ragFilterDTO', new Blob([JSON.stringify(ragFilterDTO)], { type: 'application/json' }));

  return request<any>(prefix + '/v1/smart_search/similarityFeaturePage?access_token=' + getAccessToken(), {
    method: 'POST',
    data: formData,
    // 注意：不要加 requestType:'form'，umi 的 request 会自动识别 FormData
    headers: {
      // 不要自己设置 Content-Type，浏览器会自动带上 multipart/form-data; boundary=...
    },
  });
}




/** 添加设备信息 */
export async function addDeviceInfos(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/addDeviceInfos?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

export async function deleteDevice(deviceId: String) {
  const rs = request<any>(prefix + '/v1/dmc_Point/deleteDevice?access_token=' + getAccessToken() + "&deviceId=" + deviceId, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 获取通道信息 */
export async function getChannelInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/getChannelInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


export async function getChannelInfos(params: any) {
  const { signal, ...data } = params;

  const rs = request<any>(
    prefix + '/v1/dmc_Point/getChannelInfo?access_token=' + getAccessToken(),
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      signal,
    }
  );

  return rs;
}

/** 添加点位信息 */
export async function addPointInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/addPointInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 编辑点位信息 */
export async function editPointInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/editPointInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 获取点位信息 */
export async function getPointInfoList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/dmc_Point/getPointInfoList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}



/** 编辑点位名称 */
export async function editPointName(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/editPointName?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 删除点位信息 */
export async function delPoints(data: Object) {
  const rs = request<any>(prefix + '/v1/dmc_Point/delPoints?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

//  ================================ 智能体 =====================================

/** 添加智能体信息 */
export async function addAgentInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/addAgentInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取智能体信息 */
export async function getAgentInfoList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/agent/getAgentList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 删除智能体信息 */
export async function delAgents(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/delAgents?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取智能体详细信息 */
export async function getAgentDetailInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/getAgentDetailInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 设置智能体发布状态 */
export async function setAgentStatus(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/setAgentBotStatus?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 发起对话 */
export async function createChat(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/createChat?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 发起对话流式请求，返回可订阅的事件 */
export async function createChatStream(data: Object, onChunk: (chunk: string) => void, onComplete: () => void, onError: (err: any) => void) {
  try {
    const response = await fetch(prefix + '/v1/agent/createChatStream?access_token=' + getAccessToken(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.body) {
      throw new Error('ReadableStream not supported or no response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let done = false;

    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;
      if (value) {
        const chunk = decoder.decode(value, { stream: true });
        onChunk(chunk);
      }
    }
    onComplete();
  } catch (e) {
    onError(e);
  }
}


/** 获取对话详情 */
export async function getChatDetails(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/getChatDetails?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取对话消息详情 */
export async function getChatDetailInfo(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/getChatDetailInfo?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取参数配置 */
export async function getParamConfig() {
  const rs = request<any>(prefix + '/v1/param_config/getParamConfig?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 编辑参数配置 */
export async function editParamConfig(data: Object) {
  const rs = request<any>(prefix + '/v1/param_config/editParamConfig?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取日志文件列表 */
export async function getLogFiles() {
  const rs = request<any>(prefix + '/v1/log_manage/getLogFiles?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 获取实时日志内容 */
export async function getRealTimeLog(fileName: string) {
  console.log('fileName', fileName);

  const rs = request<any>(prefix + '/v1/log_manage/getRealTimeLog?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'text/plain',
    },
    data: fileName,
  });
  return rs;
}

/** 获取分页日志内容 */
export async function getLogsByPage(params: {
  fileName: string;
  page?: number;
  size?: number;
  level?: string;
  keyword?: string;
}) {
  const { fileName, page = 1, size = 100, level, keyword } = params;
  const queryParams = new URLSearchParams({
    fileName,
    page: page.toString(),
    size: size.toString(),
    ...(level && { level }),
    ...(keyword && { keyword }),
  });

  const rs = request<any>(
    prefix + '/v1/log_manage/getLogsByPage?access_token=' + getAccessToken() + '&' + queryParams.toString(),
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return rs;
}

/** 获取日志文件信息 */
export async function getLogFileInfo(fileName: string) {
  const rs = request<any>(
    prefix + '/v1/log_manage/getLogFileInfo?access_token=' + getAccessToken() + '&fileName=' + fileName,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return rs;
}

/** 下载日志文件 */
export async function downloadLog(fileName: string) {
  try {
    const url =
      prefix + '/v1/log_manage/downloadLog?access_token=' + getAccessToken() + "&fileName=" + encodeURIComponent(fileName);

    const response = await fetch(url, { method: 'GET' });

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(downloadUrl);

    return { success: true, message: '下载成功' };
  } catch (error) {
    console.error('下载日志文件失败:', error);
    return { success: false, message: error instanceof Error ? error.message : '下载失败' };
  }
}





/** 语音文件上传 (表单方式) */
export async function voiceUpload(audioBlob: Blob) {
  const formData = new FormData();
  formData.append('file', audioBlob, 'recording.wav'); // 添加文件数据

  // 生成本地下载链接
  // const audioUrl = URL.createObjectURL(audioBlob);
  // const link = document.createElement("a");
  // link.href = audioUrl;
  // link.download = "recording.wav"; // 设置下载的文件名
  // link.click(); // 触发下载
  // // 释放 URL
  // URL.revokeObjectURL(audioUrl);

  return request<any>(`${prefix}/v1/smart_search/voiceToText?access_token=${getAccessToken()}`, {
    method: 'POST',
    headers: {
      // 不要手动设置 Content-Type，浏览器会自动处理
    },
    data: formData, // 发送表单数据

  });
}

/** 获取AppKey*/
export async function getAuthAppList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/auth/app/list?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 删除AppKey */
export async function delAppList(data: Object) {
  const rs = request<any>(prefix + '/v1/auth/app/del?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 删除AppKey */
export async function createAppKey(data: Object) {
  const rs = request<any>(prefix + '/v1/auth/app/createKey?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取智能体模型信息 */
export async function getAgentModelList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/agent/model/getModelList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 删除智能体模型信息 */
export async function delAgentModels(data: Object) {
  const rs = request<any>(prefix + '/v1/agent/model/delAgentModels?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 获取VLM参数 */
export async function getVLMInfo(key: string) {
  const rs = request<any>(prefix + '/v1/vlm/env/get_info?access_token=' + getAccessToken() + "&key=" + key, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  });
  return rs;
}

/** 更新VLM参数 */
export async function updateVLMInfo(key: string, data: Object) {
  const rs = request<any>(prefix + '/v1/vlm/env/update_info?access_token=' + getAccessToken() + "&key=" + key, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 获取事件类型 */
export async function getEventTypeList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/text_control/getEventTypeList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 分页查询布控规则 */
export async function getControlRulesList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/text_control/getControlRulesList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}

/** 获取所有事件类型 */
export async function getAllEventType() {
  const rs = request<any>(prefix + '/v1/text_control/getAllEventType?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 获取所有布控规则 */
export async function getAllControlRules() {
  const rs = request<any>(prefix + '/v1/text_control/getAllControlRules?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}



/** 编辑And添加布控规则 */
export async function editControlRule(data: Object) {
  const rs = request<any>(prefix + '/v1/text_control/editControlRule?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}



/** 删除布控规则 */
export async function delControlRule(ids: string) {
  const rs = request<any>(prefix + '/v1/text_control/delControlRules?access_token=' + getAccessToken() + "&ids=" + ids, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}



/** 开始布控 */
export async function startControl(pointId: string, ruleIds: string) {
  const rs = request<any>(prefix + '/v1/text_control/startControl?access_token=' + getAccessToken() + "&pointId=" + pointId + "&ruleIds=" + ruleIds, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}


/** 开始布控 */
export async function getPointControls(pointId: string) {
  const rs = request<any>(prefix + '/v1/text_control/getPointControls?access_token=' + getAccessToken() + "&pointId=" + pointId, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}


/** 获取事件记录列表 */
export async function getEventRecordList(data: Object, pageNo: number, pageSize: number) {
  const rs = request<any>(prefix + '/v1/text_control/getEventRecordList?access_token=' + getAccessToken() + "&pageNo=" + pageNo + "&pageSize=" + pageSize, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}


/** 获取已使用的事件类型 */
export async function getUsedEventCodes() {
  const rs = request<any>(prefix + '/v1/text_control/getUsedEventCodes?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}


/** 获取所有点位名称 */
export async function getAllPointName() {
  const rs = request<any>(prefix + '/v1/dmc_Point/getAllPointName?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}

/** 获取点位ID和名称集合 */
export async function getPointIdAndNameList() {
  const rs = request<any>(prefix + '/v1/dmc_Point/getPointIdAndNameList?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}



/** 确认规则是否在布控 */
export async function checkRuleIsControl(ruleId: string) {
  const rs = request<any>(prefix + '/v1/text_control/checkRuleIsControl?access_token=' + getAccessToken() + "&ruleId=" + ruleId, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return rs;
}


/**
 * 检测用户是否注册
 * @param account 
 * @returns 
 */
export async function checkRegistered(account: string) {
  const rs = request<any>(prefix + '/v1/auth/user/check-registered?account=' + account, {
    method: 'get',
  });
  return rs;
}


/**
 * 用户注册
 * @param data 
 * @returns 
 */
export async function registerUser(data: object) {
  const rs = request<any>(prefix + '/v1/auth/user/reg', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}



/**
 * 重置密码
 * @param data 
 * @returns 
 */
export async function resetPassword(data: object) {
  const rs = request<any>(prefix + '/v1/auth/user/resetPassword?access_token=' + getAccessToken(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
  return rs;
}



export async function imageSearch(blob: Blob) {
  const formData = new FormData();
  formData.append('file', blob, 'aa.jpg');
  return request<any>(`${prefix}/v1/smart_search/imageSearch?access_token=${getAccessToken()}`, {
    method: 'POST',
    data: formData,
  });
}




