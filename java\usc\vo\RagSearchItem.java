package com.zkteco.mars.usc.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.util.Map;

/**
 * 搜索条件
 * <AUTHOR>
 * @date  2024-12-31 14:03
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class RagSearchItem implements java.io.Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 图片
     */
    private String imageBase64;

    /**
     * 文字
     */
    private String text;

    /**
     * 语音
     */
    private String voice;

    private Map<String, Object> payload;
}
