/**
 * 搜索条件组件 - 时间范围和点位选择
 */
import React, { useState, useEffect } from 'react';
import { Dropdown, Button, Space, DatePicker, Select } from 'antd';
import { CalendarOutlined, DownOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { getPointIdAndNameList } from '@/services/ant-design-pro/api';
import dayjs from 'dayjs';
import './index.less';

const { RangePicker } = DatePicker;
const { Option } = Select;

export interface SearchConditionsProps {
  // 时间相关
  startTime?: number;
  endTime?: number;
  onTimeChange?: (startTime?: number, endTime?: number) => void;
  
  // 点位相关
  selectedPointIds?: string[];
  onPointChange?: (pointIds: string[]) => void;
  
  // 样式相关
  showTimeSelector?: boolean;
  showPointSelector?: boolean;
  compact?: boolean;
}

const SearchConditions: React.FC<SearchConditionsProps> = ({
  startTime,
  endTime,
  onTimeChange,
  selectedPointIds = [],
  onPointChange,
  showTimeSelector = true,
  showPointSelector = true,
  compact = false
}) => {
  const [timeDropdownVisible, setTimeDropdownVisible] = useState<boolean>(false);
  const [pointOptions, setPointOptions] = useState<{ id: string, name: string }[]>([]);

  // 加载点位名称
  const loadPointName = async () => {
    try {
      const rs = await getPointIdAndNameList();
      if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
        setPointOptions(rs.data);
      }
    } catch (error) {
      console.error('加载点位名称失败:', error);
    }
  };

  useEffect(() => {
    if (showPointSelector) {
      loadPointName();
    }
  }, [showPointSelector]);

  // 通用的时间设置函数
  const handleTimeSelection = (newStartTime: number, newEndTime: number) => {
    setTimeDropdownVisible(false);
    onTimeChange?.(newStartTime, newEndTime);
  };

  // 时间范围变化处理
  const handleTimeRangeChange = (dates: any) => {
    let newStartTime: number | undefined;
    let newEndTime: number | undefined;

    if (dates && Array.isArray(dates) && dates.length === 2) {
      const [start, end] = dates;
      if (start && end && dayjs(start).isValid() && dayjs(end).isValid()) {
        newStartTime = Math.floor(dayjs(start).valueOf() / 1000);
        newEndTime = Math.floor(dayjs(end).valueOf() / 1000);
      }
    }

    onTimeChange?.(newStartTime, newEndTime);
  };

  // 点位选择变化处理
  const handlePointChange = (value: string[]) => {
    onPointChange?.(value);
  };

  // 时间选择器组件
  const TimeSelector = () => (
    <Dropdown
      trigger={['click']}
      placement="bottomLeft"
      open={timeDropdownVisible}
      onOpenChange={setTimeDropdownVisible}
      overlayClassName="compact-time-selector"
      dropdownRender={() => (
        <div className="time-dropdown-content" style={{
          background: '#fff',
          borderRadius: '8px',
          boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
          padding: '12px',
          minWidth: '320px',
          maxWidth: '400px'
        }}>
          {/* 快速选择按钮 */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>快速选择：</div>
            <Space size={[8, 8]} wrap>
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  const end = dayjs();
                  const start = dayjs().startOf('day');
                  handleTimeSelection(
                    Math.floor(start.valueOf() / 1000),
                    Math.floor(end.valueOf() / 1000)
                  );
                }}
              >
                今天
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  const end = dayjs();
                  const start = dayjs().subtract(3, 'day').startOf('day');
                  handleTimeSelection(
                    Math.floor(start.valueOf() / 1000),
                    Math.floor(end.valueOf() / 1000)
                  );
                }}
              >
                三天
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  const end = dayjs();
                  const start = dayjs().subtract(7, 'day').startOf('day');
                  handleTimeSelection(
                    Math.floor(start.valueOf() / 1000),
                    Math.floor(end.valueOf() / 1000)
                  );
                }}
              >
                一周
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  const end = dayjs();
                  const start = dayjs().subtract(30, 'day').startOf('day');
                  handleTimeSelection(
                    Math.floor(start.valueOf() / 1000),
                    Math.floor(end.valueOf() / 1000)
                  );
                }}
              >
                一个月
              </Button>
              {/* <Button
                type="default"
                size="small"
                danger
                onClick={() => {
                  setTimeDropdownVisible(false);
                  onTimeChange?.(undefined, undefined);
                }}
              >
                清除
              </Button> */}
            </Space>
          </div>
          
          {/* 分隔线 */}
          <div style={{ 
            height: '1px', 
            background: '#f0f0f0', 
            margin: '8px 0 12px 0' 
          }} />
          
          {/* 自定义时间选择 */}
          <div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>自定义时间：</div>
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <RangePicker
                value={startTime && endTime ? [dayjs(startTime * 1000), dayjs(endTime * 1000)] : undefined}
                onChange={handleTimeRangeChange}
                style={{ flex: 1 }}
                placeholder={['开始时间', '结束时间']}
                allowClear
                showTime={{ format: 'HH:mm:ss' }}
                format="YYYY-MM-DD HH:mm:ss"
                size="small"
              />
              {/* <Button 
                type="primary" 
                size="small"
                onClick={() => setTimeDropdownVisible(false)}
                style={{ flexShrink: 0 }}
              >
                确定
              </Button> */}
            </div>
          </div>
        </div>
      )}
    >
      <Button 
        style={{ 
          minWidth: compact ? '80px' : '100px',
          maxWidth: compact ? '120px' : '140px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          // padding: '4px 8px',
          height: '32px',
        }}
        type={startTime && endTime ? 'primary' : 'default'}
      >
        <div style={{ display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
          <CalendarOutlined style={{ fontSize: '12px', flexShrink: 0 }} />
          {startTime && endTime ? (
            <span style={{ 
              marginLeft: '4px', 
              fontSize: '12px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}>
              {(() => {
                const startDate = dayjs(startTime * 1000);
                const endDate = dayjs(endTime * 1000);
                const now = dayjs();
                
                // 判断是否是预设的时间范围
                if (startDate.isSame(now.startOf('day'), 'day') && endDate.isSame(now, 'day')) {
                  return '今天';
                } else if (startDate.isSame(now.subtract(3, 'day').startOf('day'), 'day')) {
                  return '三天';
                } else if (startDate.isSame(now.subtract(7, 'day').startOf('day'), 'day')) {
                  return '一周';
                } else if (startDate.isSame(now.subtract(30, 'day').startOf('day'), 'day')) {
                  return '一个月';
                } else {
                  // 自定义时间范围，显示简化格式
                  return `${startDate.format('MM-DD')}~${endDate.format('MM-DD')}`;
                }
              })()}
            </span>
          ) : (
            <span style={{ marginLeft: '4px', fontSize: '12px', color: '#999' }}>选择时间</span>
          )}
        </div>
        <DownOutlined style={{ fontSize: '10px', flexShrink: 0, marginLeft: '4px' }} />
      </Button>
    </Dropdown>
  );

  // 点位选择器组件
  const PointSelector = () => (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <label style={{
        marginRight: '8px',
        fontSize: compact ? '12px' : '14px',
        color: '#666',
        whiteSpace: 'nowrap'
      }}>
        <EnvironmentOutlined style={{ marginRight: '4px', fontSize: compact ? '12px' : '14px' }} />
        点位：
      </label>
      <Select
        mode="multiple"
        placeholder="选择点位"
        style={{
          minWidth: compact ? '100px' : '160px',
          maxWidth: compact ? '150px' : '200px',
          fontSize: compact ? '12px' : '14px'
        }}
        value={selectedPointIds}
        onChange={handlePointChange}
        allowClear
        maxTagCount={compact ? 1 : 2}
        size={compact ? 'small' : 'middle'}
      >
        {pointOptions.map(option => (
          <Option key={option.id} value={option.id}>
            {option.name}
          </Option>
        ))}
      </Select>
    </div>
  );

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: compact ? '12px' : '16px',
      flexWrap: 'wrap',
      fontSize: compact ? '12px' : '14px'
    }}>
      {showTimeSelector && (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <label style={{
            marginRight: '8px',
            fontSize: compact ? '12px' : '14px',
            color: '#666',
            whiteSpace: 'nowrap'
          }}>
            时间范围：
          </label>
          <TimeSelector />
        </div>
      )}

      {showPointSelector && <PointSelector />}
    </div>
  );
};

export default SearchConditions;
