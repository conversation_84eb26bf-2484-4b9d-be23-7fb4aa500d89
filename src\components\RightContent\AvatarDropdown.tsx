import { outLogin, resetPassword } from '@/services/ant-design-pro/api';
import { LogoutOutlined, SettingOutlined, UserOutlined, KeyOutlined } from '@ant-design/icons';
import { history, useModel, useIntl } from '@umijs/max';
import { Avatar, Menu, Spin, Modal, Form, Input, Button, message, Typography, Space } from 'antd';
import { createStyles } from 'antd-style';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback, useState } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';
import { encryptAES } from '@/utils/encrypt';



const centeredFooterStyle = {
  display: 'flex',
  justifyContent: 'center'
};

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon" style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
    <img
      src="/icons/search/headpicture.svg"
      style={{
        width: '18px',
        height: '18px',
        objectFit: 'cover',
      }}
    />
    {currentUser?.name}
  </span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    await outLogin();
    const { search, pathname } = window.location;
    const urlParams = new URL(window.location.href).searchParams;
    /** 此方法会跳转到 redirect 参数所在的位置 */
    const redirect = urlParams.get('redirect');
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login' && !redirect) {
      history.replace({
        pathname: '/user/login',
        search: stringify({
          redirect: pathname + search,
        }),
      });
    }
  };
  const { styles } = useStyles();

  const { initialState, setInitialState } = useModel('@@initialState');

  const intl = useIntl();

  // 密码框表单
  const [updatePwdForm] = Form.useForm();

  // 显示修改密码窗口
  const [updatePwdModalVisible, setUpdatePwdModalVisible] = useState(false);

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        flushSync(() => {
          setInitialState((s) => ({ ...s, currentUser: undefined }));
        });
        loginOut();
        return;
      }
      if (key === 'updatePwd') {
        setUpdatePwdModalVisible(true);
        return
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
        {
          key: 'center',
          icon: <UserOutlined />,
          label: '个人中心',
        },
        {
          key: 'settings',
          icon: <SettingOutlined />,
          label: '个人设置',
        },
        {
          type: 'divider' as const,
        },
      ]
      : []),
    {
      key: 'updatePwd',
      icon: <KeyOutlined />,
      label: intl.formatMessage({ id: 'pages.account.updatePwd', defaultMessage: '修改密码', }),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: intl.formatMessage({ id: 'pages.common.logout', defaultMessage: '退出登录', }),
    },
  ];

  /**
    * 处理窗口关闭
    * @returns
    */
  const handleCancel = () => {
    updatePwdForm.resetFields();
    setUpdatePwdModalVisible(false);
  };

  /**
   * 处理修改密码
   * @returns
   */
  const handleUpdatePwd = async () => {

    let formData
    try {
      formData = await updatePwdForm.validateFields();
      //console.log(formData);
    } catch (errorInfo) {
      console.error('handle UpdatePwd error:', errorInfo);
      return
    }

    const encryptedOldPassword = encryptAES(formData.oldPassword);
    const encryptedPassword = encryptAES(formData.password);
    const encryptedConfirmPass = encryptAES(formData.confirmPass);

    const res = await resetPassword({
      oldPassword: encryptedOldPassword,
      password: encryptedPassword,
      confirmPass: encryptedConfirmPass,
    });

    if (res.code !== 0) {
      message.error(
        intl.formatMessage({
          id: 'pages.serverCode.' + res.code,
          defaultMessage: intl.formatMessage({
            id: 'pages.password.reset.fail',
            defaultMessage: '重置密码失败',
          }),
        })
      );
      return;
    } else {
      message.success(intl.formatMessage({ id: 'pages.password.reset.success', defaultMessage: '密码重置成功', }));
      updatePwdForm.resetFields();
      setUpdatePwdModalVisible(false);
    }
  }

  return (
    <>
      <HeaderDropdown
        menu={{
          selectedKeys: [],
          onClick: onMenuClick,
          items: menuItems,
        }}
      >
        {children}
      </HeaderDropdown>
      <Modal
        title={intl.formatMessage({ id: 'pages.account.updatePwd', defaultMessage: '修改密码' })}
        open={updatePwdModalVisible}
        onCancel={handleCancel}
        footer={
          <div>
            <div style={centeredFooterStyle}>
              <Button type="primary" onClick={handleUpdatePwd}>{intl.formatMessage({ id: 'pages.pointManage.confirm' })}</Button>
              <Button onClick={handleCancel}>{intl.formatMessage({ id: 'pages.pointManage.cancel' })}</Button>
            </div>
          </div>
        }
        destroyOnClose={true}
      >

        <Form
          form={updatePwdForm}
          name="dependencies"
          autoComplete="off"
          style={{ maxWidth: 500 }}
          layout="vertical"
        >
          <Form.Item label={intl.formatMessage({ id: 'pages.account.oldPassword', defaultMessage: '原密码' })} name="oldPassword" rules={[{ required: true }]}>
            <Input.Password maxLength={18} />
          </Form.Item>

          <Form.Item label={intl.formatMessage({ id: 'pages.account.newPassword', defaultMessage: '新密码' })} name="password" rules={[{ required: true }]}>
            <Input.Password maxLength={18} />
          </Form.Item>

          <Form.Item
            label={intl.formatMessage({ id: 'pages.account.confirmPwd', defaultMessage: '确认密码' })}
            name="confirmPass"
            dependencies={['password']}
            rules={[
              {
                required: true,
              },
              { max: 18, message: intl.formatMessage({ id: 'pages.account.maxlength', defaultMessage: '密码最多18位' }) },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(intl.formatMessage({ id: 'pages.account.passwordmatch', defaultMessage: '您输入的新密码不匹配' })));
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
        </Form>
      </Modal >
    </>
  );
};
