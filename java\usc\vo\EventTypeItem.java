package com.zkteco.mars.usc.vo;


import com.zkteco.framework.base.annotation.Column;
import com.zkteco.framework.base.annotation.From;
import com.zkteco.framework.base.annotation.OrderBy;
import com.zkteco.framework.base.bean.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 事件类型item
 * <AUTHOR>
 * @date  2025-04-09 16:20
 * @since 1.0.0
 */
@From(after = "EVENT_TYPE t ")
@OrderBy(after = "t.CREATE_TIME ASC")
@Getter
@Setter
@Accessors(chain = true)
public class EventTypeItem extends BaseItem implements Serializable {


    /**
     * id
     */
    @Column(name = "t.ID")
    private String id;

    /**
     * 事件编码
     */
    @Column(name = "t.event_code")
    private String eventCode;


    /**
     * 事件类型
     */
    @Column(name = "t.event_type")
    private String eventType;


}
