import { ProLayoutProps } from '@ant-design/pro-components';
// 获取当前语言（从 umi_locale 或浏览器）
const rawLocale = typeof window !== 'undefined'
  ? localStorage.getItem('umi_locale') || navigator.language
  : 'en-US';

const logo = rawLocale === 'zh-CN' || rawLocale === 'zh-TW'
  ? '/logo_white_CN.svg'
  : '/logo_white_EN.svg';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // 拂晓蓝
  colorPrimary: 'rgb(11, 211, 87)',
  layout: 'top', // 修改为混合布局
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: 'Mars',
  pwa: true,
  logo: logo,
  iconfontUrl: '',
  token: {
    colorPrimary: 'rgb(11, 211, 87)', // 设置主色调为绿色
    header: {
      colorBgHeader: 'rgb(17, 30, 51)', // 顶部背景颜色
      heightLayoutHeader: 50, // 顶部高度
      colorTextMenu: '#ffffff', // 顶部菜单字体颜色
      colorTextMenuSelected: '#ffffff', // 选中菜单项字体颜色（亮绿色）
      colorTextMenuActive: '#ffffff', // 悬停菜单项字体颜色
      colorBgMenuItemHover: '#0BD357', // 悬停菜单项背景颜色
      colorBgMenuItemSelected: '#0BD357', // 选中菜单项背景颜色
      colorBgRightActionsItemHover: 'rgba(255, 255, 255, 0.1)', // 右侧操作项悬停背景
      colorTextRightActionsItem: '#ffffff', // 右侧操作项字体颜色
      // colorHeaderTitle: '#ffffff', // 标题颜色
      // colorBgScrollHeader: 'rgb(17, 30, 51)', // 滚动时的头部背景颜色
      //colorBgMenuElevated: 'rgb(20, 40, 60)', // 菜单悬浮背景颜色
      colorTextMenuSecondary: 'black', // 次级菜单字体颜色
    },
    pageContainer: {
      colorBgPageContainer: '#ffffff', // 设置页面容器的背景颜色
      paddingInlinePageContainerContent: 0, // 设置页面容器的左右内边距
      paddingBlockPageContainerContent: 0, // 设置页面容器的上下内边距
    },

    // sider: {
    //   colorMenuBackground: 'rgb(17, 30, 51)', // 设置侧边栏背景颜色
    //   colorTextMenu: '#ffffff', // 侧边栏菜单字体颜色设置为白色
    //   colorTextMenuSelected: 'rgb(11, 211, 87)', // 选中菜单项的字体颜色
    // },
    // 参见ts声明，demo 见文档，通过token 修改样式
    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
  },

};

export default Settings;
