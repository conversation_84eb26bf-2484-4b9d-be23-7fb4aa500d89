package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备点位表
 *
 * <AUTHOR>
 * @date 2024-12-31 16:07
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "DMC_POINT", indexes = {@Index(name = "DMC_POINT_ID_IDX", columnList = "ID"), @Index(name = "DMC_POINT_CRT_IDX", columnList = "CREATE_TIME"), @Index(name = "DMC_POINT_UPT_IDX", columnList = "UPDATE_TIME")})
public class DmcPoint extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 点位名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 协议类型【字典值】:1.RTSP(默认，poc界面不需要支持选择);2.ONVIF;3.GB28181
     */
    @Column(name = "protocol_type", length = 10)
    private String protocolType;

    /**
     * 码流类型
     */
    @Column(name = "main_stream", length = 255)
    private String mainStream;

    /**
     * 子码流类型
     */
    @Column(name = "second_stream", length = 255)
    private String secondStream;


    /**
     * 抓拍间隔（秒）0:不抓拍;大于等于1:抓拍的时间间隔关联
     */
    @Column(name = "capture_interval", length = 10)
    private String captureInterval;


    /**
     * 第三方点位业务id
     */
    @Column(name = "business_id", length = 50)
    private String businessId;

    /**
     * 第三方业务系统类型【字典值】:1:zk流媒体 2:万傲瑞达
     */
    @Column(name = "system_type", length = 3)
    private String systemType;

    /**
     * 设备id
     */
    @Column(name = "device_id", length = 50)
    private String deviceId;

    /**
     * 设备ip
     */
    @Column(name = "device_ip", length = 20)
    private String deviceIp;

    /**
     * 设备名称
     */
    @Column(name = "device_name", length = 100)
    private String deviceName;

    /**
     * 设备端口
     */
    @Column(name = "device_port", length = 10)
    private String devicePort;

    /**
     * 设备用户名
     */
    @Column(name = "device_username", length = 50)
    private String deviceUsername;

    /**
     * 设备密码
     */
    @Column(name = "device_userpass", length = 50)
    private String deviceUserpass;


    /**
     * 设备厂商
     */
    @Column(name = "manufacturer", length = 20)
    private String manufacturer;

    /**
     * 检测类型
     */
    @Column(name = "detect_type", length = 20)
    private String detectType;


}

