package com.zkteco.mars.usc.controller;


import com.zkteco.framework.vo.ApiResultMessage;

import com.zkteco.mars.ai.dto.RagFilterDTO;
import com.zkteco.mars.usc.service.SmartSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * 图像理解
 *
 * <AUTHOR>
 * @date 2024-12-31 9:38
 * @since 1.0.0
 */
@RestController
@RequestMapping("/v1/smart_search")
public class SmartSearchController {

    @Autowired
    private SmartSearchService smartSearchService;

    /**
     * 获取历史记录
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 10:12
     * @since 1.0.0
     */
    @PostMapping("/getHistoryRecord")
    @ResponseBody
    public ApiResultMessage getHistoryRecord() {
        return smartSearchService.getHistoryRecord();
    }

    /**
     * 获取热门搜索
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 10:12
     * @since 1.0.0
     */
    @PostMapping("/getHotSearch")
    @ResponseBody
    public ApiResultMessage getHotSearch() {
        return smartSearchService.getHotSearch();
    }


    /**
     * 智能搜索
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2024-12-31 10:12
     * @since 1.0.0
     */
    @PostMapping("/search")
    @ResponseBody
    public ApiResultMessage smartSearch(@RequestBody RagFilterDTO ragFilterDTO) {
        return smartSearchService.smartSearch(ragFilterDTO);
    }


    /**
     * 获取抓拍图片
     *
     * @param imageName: 图片名称
     * @param imageType: 图片类型 snap 抓拍 thumb 缩略图 target 目标抠图
     * @return org.springframework.http.ResponseEntity<org.springframework.core.io.Resource>
     * @throws
     * <AUTHOR>
     * @date 2025-02-14 13:04
     * @since 1.0.0
     */
    @PostMapping("/getImageFile")
    @ResponseBody
    public ResponseEntity<Resource> getImageFile(@RequestParam("imageName") String imageName, @RequestParam("imageType") String imageType) {
        return smartSearchService.getImageFile(imageName, imageType);
    }


    /**
     * 语音转文字
     *
     * @param multipartFile: 语音文件
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-02-13 11:56
     * @since 1.0.0
     */
    @PostMapping("/voiceToText")
    @ResponseBody
    public ApiResultMessage voiceToText(@RequestParam("file") MultipartFile multipartFile) {
        return smartSearchService.voiceToText(multipartFile);
    }


    /**
     * 模糊查询抠图列表
     *
     * @param imageName:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-03-19 15:58
     * @since 1.0.0
     */
    @PostMapping("/getTargetImageList")
    @ResponseBody
    public ApiResultMessage getTargetImageList(@RequestParam String imageName) {
        return smartSearchService.getTargetImageList(imageName);
    }


    /**
     * 以图搜图第一步 （抠图）
     *
     * @param multipartFile:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @date 2025/8/15 14:45
     * @since 1.0.0
     */
    @PostMapping("/imageSearch")
    @ResponseBody
    public ApiResultMessage imageSearch(@RequestParam("file") MultipartFile multipartFile) {
        return smartSearchService.imageSearch(multipartFile);
    }


    /**
     * 以图搜图第二步 （相似度检索） 分页
     *
     * @param multipartFile:
     * @param ragFilterDTO:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @date 2025/8/20 11:31
     * @since 1.0.0
     */
    @PostMapping("/similarityFeaturePage")
    public ApiResultMessage similarityFeaturePage(
            @RequestPart("file") MultipartFile multipartFile,
            @RequestPart("ragFilterDTO") RagFilterDTO ragFilterDTO) {
        return smartSearchService.similarityFeaturePage(multipartFile, ragFilterDTO);
    }


}
