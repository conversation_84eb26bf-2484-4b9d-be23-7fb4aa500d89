import { getAgentInfoList, delAgents, addAgentInfo, getAgentDetailInfo } from "@/services/ant-design-pro/api";
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ProFormDigit,
  ProFormText,
  ProFormSelect,
  ProTable,
  ProForm,
  ProFormUploadButton,
  ProFormTextArea
} from '@ant-design/pro-components';
import { Button, message, Modal, Tag, Form } from 'antd';
import type { ReactNode } from 'react';
import { useRef, useState, useEffect } from 'react';
import { connect, useIntl, useNavigate } from 'umi';
import './index.less'
import EditAgent from './EditAgent';

const AgentManage: React.FC = (props) => {
  const intl = useIntl();

  /** 表格的引用 */
  const actionRef = useRef<ActionType>();

  /** 当前行 */
  const [currentRow, setCurrentRow] = useState<any>();

  /** 表单引用 */
  const formRef = useRef<ProFormInstance>();

  /**智能体新增窗口 */
  const [manualEditVisible, setManualEditVisible] = useState(false)

  /**编辑智能体信息 */
  const [editAgentData, setEditAgentData] = useState<any>()

  /**智能体数据 */
  const [agentsData, setAgentsData] = useState<any[]>([])

  /** 表单ref */
  const agentsRef = useRef()

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    name: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })

  const [showPlatApiKey, setShowPlatApiKey] = useState(false);

  /** 选中行集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  /** 编辑智能体表单显隐 */
  const [agentEditFormVisible, setAgentEditFormVisible] = useState<boolean>(false);

  /** 表单引用 */
  const editFormRef = useRef<ProFormInstance>();
  /** 切换页面 */
  const navigate = useNavigate();


  const operateRender = (dom: any, record: any): ReactNode => {
    return <div id="operate">
      <a onClick={() => arrangeRecord([record])}>
        <img className="img_arrange" title={intl.formatMessage({ id: 'pages.agent.orchestration', defaultMessage: '编排', })} />
      </a>
      <a onClick={() => editRecord(record)}>
        <img className="img_edit" title={intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', })} />
      </a>
      <a onClick={() => deleteRecord([record])}>
        <img className="img_del" title={intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除', })} />
      </a>
    </div>;
  }

  /**
   * 添加智能体
   */
  const addAgent = async () => {
    setManualEditVisible(true)
  }


  /**
   * 分页
   */
  const handleAgentPageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {
    console.log('page change', pagination, filters, sorter, extra)
    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadAgentInfo({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('point search', params)
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      name: params.name,
    })
    loadAgentInfo({
      current: 1,
      name: params.name ? params.name : 'no&input',
    })
  }

  /**
 * 编排记录
 */
  const arrangeRecord = async (rows: any[]) => {
    console.log("rows", rows)
    if (!rows[0].id) {
      message.error('无法跳转：缺少ID');
      return;
    }
    const rs = await getAgentDetailInfo({ id: rows[0].id });
    if (rs.code === 0 && rs.data) {
      navigate(`/agent/agentManage/agentArrange/${rows[0].id}`, {
        state: {
          // 传递字段
          data: rs.data
        }
      });
    } else {
      navigate(`/agent/agentManage/agentArrange/${rows[0].id}`, {
        state: {
          // 传递字段
          data: {
            agent_item: {
              name: rows[0].name
            }
          }

        }
      })
    }

  }

  /**
   * 编辑记录
   */
  const editRecord = async (rows: any[]) => {
    setAgentEditFormVisible(true)
    console.log("editRecord->", rows)
    setCurrentRow(rows)
    if (rows.type === 3) {
      setShowPlatApiKey(true);
    } else {
      setShowPlatApiKey(false);
    }
  }


  /**
   * 删除记录
   */
  const deleteRecord = async (rows: any[]) => {
    console.log('del data:', rows)
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.pointManage.deleteRecord', defaultMessage: '删除记录', }),
      content: intl.formatMessage({ id: 'pages.pointManage.confirmDeleteRecord', defaultMessage: '确定删除记录吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk: async () => {
        const hide = message.loading(intl.formatMessage({ id: 'pages.pointManage.deleting', defaultMessage: '正在删除', }));
        var opSuccess = 0;
        var opFail = 0;
        const data: any = []
        // for (let i = 0; i < rows.length; i++) {
        //   data.push({
        //     id: rows[i].id,
        //     deviceId: rows[i].deviceId,
        //     businessId: rows[i].businessId
        //   })
        // }
        const rs = await delAgents(rows);
        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }));
        }
        else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.deleteFailure', defaultMessage: '删除失败，请重试', }));
        }
        setSelectedRows([]);
        //actionRef.current?.reloadAndRest?.();
        loadAgentInfo({});

        hide();

      },
    });
  };


  const statusRender = (dom: any, row: any) => {
    // 假设 status 是存储在 row 对象中的
    const statusValues = row.status;

    // 选项映射
    const options = [
      { label: '已发布', value: 2 },
      { label: '未发布', value: 1 },
    ];

    // 如果 status 为空或不存在，直接返回 null
    if (!statusValues) {
      return null;
    }
    // 确保 statusValues 是数组
    // 将值转换为数组，处理单个值和多个值的情况
    const values = typeof statusValues === 'string' ?
      statusValues.split(",").map(Number) :
      [Number(statusValues)];
    // 根据 value 获取对应的 label
    const labels = values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean); // 过滤掉 undefined 或 null 的值

    // 只显示前 4 个类型
    const displayLabels = labels.slice(0, 3);

    return (
      <div style={{ whiteSpace: 'pre-wrap' }}>
        {displayLabels.map((label, index) => (
          <Tag color="rgb(11, 211, 87)" key={index}>{label}</Tag>
        ))}

      </div>
    );
  };

  //列表渲染
  const columns: any = [
    {
      title: "id",
      dataIndex: 'id',
      hideInSearch: true,
      ellipsis: true,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.config.name', defaultMessage: '名称', }),
      dataIndex: 'name',
      width: 300,
      hideInSearch: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.functionIntroduction', defaultMessage: '功能介绍', }),
      dataIndex: 'description',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.icon', defaultMessage: '图标', }),
      dataIndex: 'logo',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.publishStatus', defaultMessage: '发布状态', }),
      dataIndex: 'status',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
      render: statusRender,
    },
    // {
    //   title: intl.formatMessage({ id: 'pages.agent.creator', defaultMessage: '创建者', }),
    //   dataIndex: 'createrName',
    //   width: 120,
    //   hideInSearch: true,
    //   ellipsis: true,
    // },
    {
      title: intl.formatMessage({ id: 'pages.agent.creationTime', defaultMessage: '创建时间', }),
      dataIndex: 'createTime',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.operation', defaultMessage: '操作', }),
      width: 200,
      dataIndex: 'option',
      valueType: 'option',
      render: operateRender,
    },
  ];

  /**
   * 加载智能体信息
   * @param page
   */
  const loadAgentInfo = async (page: any) => {
    // console.log('load device', param)
    console.log('load agents', agentsRef.current, agentsRef.current?.getFieldsValue())
    const searchValue = agentsRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let name = page.name ? page.name : searchValue.name
    if (page.name === 'no&input') {
      name = undefined
    }
    const rs = await getAgentInfoList({ name: name }, currentPage, pageSize);
    console.log("getAgentInfoList rs->", rs)
    if (rs.code === 0 && rs.data && rs.data?.data.length >= 0) {
      setAgentsData(rs.data?.data)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
    }
  }


  return (
    <>
      <ProTable
        rowKey="id"
        headerTitle={intl.formatMessage({ id: 'pages.agent.agentDisplay', defaultMessage: '智能体展示', })}
        actionRef={actionRef}
        formRef={agentsRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,          // 默认每页显示的条数
          showQuickJumper: true, // 显示跳转至某页
          showSizeChanger: true, // 显示 pageSize 切换器
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'], // 指定每页可以显示多少条
          //onChange: (page, size) => setPageSize(size), // 更新 pageSize
          total: searchParams.total,   // 数据总数
        }}
        onChange={handleAgentPageChange}
        rowSelection={{
          onChange: (_, selectedRows) => {
            console.log("selectedRows-> ", selectedRows)
            setSelectedRows(selectedRows);
          },
        }}
        beforeSearchSubmit={handleBeforeSearchDevice}
        columns={columns}
        tableAlertRender={false}
        options={{
          density: false,
          setting: false,
        }}
        dataSource={agentsData}
        //scroll={{ y: 530 }}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={addAgent}>
            <PlusOutlined />
            {intl.formatMessage({ id: 'pages.agent.create', defaultMessage: '创建智能体', })}
          </Button>,
        ]}
      />

      <div>
        <EditAgent visible={manualEditVisible} setManualEditVisible={setManualEditVisible} loadAgentInfo={loadAgentInfo} editData={editAgentData} />
      </div>



      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {intl.formatMessage({ id: 'pages.pointManage.selected', defaultMessage: '已选择', })}
              <a style={{ fontWeight: 600, color: 'rgb(11, 211, 87)' }}>{selectedRows.length}</a>
              {intl.formatMessage({ id: 'pages.pointManage.item', defaultMessage: '项', })}
            </div>
          }
        >
          <Button onClick={() => deleteRecord(selectedRows)}>{intl.formatMessage({ id: 'pages.pointManage.batchDelete', defaultMessage: '批量删除', })}</Button>
        </FooterToolbar>
      )}

      <Modal
        width={500}
        destroyOnClose
        styles={{
          body: { padding: '24px', height: '60vh', overflow: 'auto' }
        }}
        title={intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', })}
        open={agentEditFormVisible}
        onOk={async () => {
          try {
            const values = await formRef.current?.validateFields();
            const id = currentRow?.id;
            values.id = id;
            console.log("addAgentInfo values->", values)
            if (values.logo && values.logo.length > 0) {
              values.logo = values.logo[0].response?.data?.url || values.logo[0].url;
            }

            const rs = await addAgentInfo(values);
            console.log("addAgentInfo rs->", rs)
            if (rs.code === 0) {

              setAgentEditFormVisible(false);
              formRef.current?.resetFields();
              message.success(intl.formatMessage({ id: 'pages.pointManage.editSuccess', defaultMessage: '编辑成功', }));
              loadAgentInfo({});
            } else {
              message.error(rs.code);
            }
          } catch (err) {
            console.error('提交失败', err);
            message.error(intl.formatMessage({ id: 'pages.agent.submitFailed', defaultMessage: '提交失败，请检查表单数据' }));
          }
          setCurrentRow({});
        }}
        onCancel={() => setAgentEditFormVisible(false)}
      >
        <ProForm<{
          name: string;
        }>
          formRef={formRef}
          layout="vertical"
          style={{ width: '100%' }}
          labelAlign="left" // 设置 label 左对齐
          labelCol={{ span: 24 }} // 设置 label 的宽度
          wrapperCol={{ span: 24 }} // 设置输入框的宽度
          submitter={{
            resetButtonProps: {
              hidden: true,
            },
            submitButtonProps: {
              hidden: true,
            },
            searchConfig: {
            }
          }}
          onValuesChange={(changedValues, allValues) => {
            if ('type' in changedValues) {
              setShowPlatApiKey(changedValues.type === 3);
            }
          }}
        >
          <ProFormText
            name="id"
            label={intl.formatMessage({ id: 'pages.primaryKey.id', defaultMessage: '主键 ID' })}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 20 }}
            readonly
            initialValue={currentRow?.id}
          />
          <ProFormText
            width="md"
            name="name"
            initialValue={currentRow?.name}
            rules={[
              {
                required: true
              },
              {
                max: 64,
                message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 64"
              },
            ]}
            label={intl.formatMessage({ id: 'pages.agent.name', defaultMessage: '智能体名称' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.name', defaultMessage: '智能体名称' })}
            labelCol={{ span: 24 }}  // label占满整行
            wrapperCol={{ span: 24 }} // 输入框占满整行
            tooltip={intl.formatMessage({ id: 'pages.agent.nameLimit', defaultMessage: '最多可输入64个字符' })}
            fieldProps={{
              maxLength: 64  // 将 maxLength 放在 fieldProps 中
            }}
          />
          <ProFormTextArea
            name="description"
            label={intl.formatMessage({ id: 'pages.agent.description', defaultMessage: '智能体功能介绍' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.descriptionTip', defaultMessage: '介绍智能体的功能，将会展示给智能体的用户' })}
            initialValue={currentRow?.description}
            rules={[
              {
                required: true
              },
              {
                max: 200,
                message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 200"
              },
            ]}
          />
          <ProFormSelect
            name="type"
            label={intl.formatMessage({ id: 'pages.agent.type', defaultMessage: '类型' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.type.placeholder', defaultMessage: '请选择智能体类型' })}
            options={[
              { label: 'Coze', value: 2 },
              { label: 'Dify', value: 3 },
            ]}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.type.required', defaultMessage: '请选择类型' }) }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 20 }}
            initialValue={currentRow?.type}
          />

          <ProFormText
            name="platBotId"
            label={intl.formatMessage({ id: 'pages.agent.id', defaultMessage: '智能体 ID' })}
            placeholder={intl.formatMessage({ id: 'pages.agent.id.placeholder', defaultMessage: '请输入智能体 ID' })}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.id.required', defaultMessage: '请输入智能体 ID' }) }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 20 }}
            initialValue={currentRow?.platBotId}
            extra={intl.formatMessage({ id: 'pages.agent.botId.tip', defaultMessage: '请前往对应平台（如 Coze、Dify）创建智能体后复制其 ID 粘贴到此处' })}
          />

          {showPlatApiKey && (
            <ProFormText
              name="platApiKey"
              label="API Key"
              placeholder="API Key"
              rules={[{ required: true, message: intl.formatMessage({ id: 'pages.agent.apiKey.required', defaultMessage: 'API Key 为必填项' }) }]}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 20 }}
              initialValue={currentRow?.platApiKey}
              extra={intl.formatMessage({ id: 'pages.agent.apiKey.tip', defaultMessage: '请前往 Dify 平台，复制其 API Key 粘贴到此处' })}
            />
          )}

          <ProFormUploadButton
            width="md"
            name="logo"
            label={intl.formatMessage({ id: 'pages.agent.icon', defaultMessage: '图标' })}
            max={1}
            fieldProps={{
              name: 'file',
              listType: 'picture-card',
              beforeUpload: (file) => {
                // 限制文件类型和大小
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                  message.error(intl.formatMessage({ id: 'pages.agent.imageOnly', defaultMessage: '只能上传图片文件' }));
                  return false;
                }
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                  message.error(intl.formatMessage({ id: 'pages.agent.imageSizeLimit', defaultMessage: '图片大小不能超过2MB' }));
                  return false;
                }
                return true;
              }
            }}
            //==action="/api/upload"
            extra={intl.formatMessage({ id: 'pages.agent.imageFormatLimit', defaultMessage: '支持jpg/png格式，大小不超过2MB' })}
          />
        </ProForm>

      </Modal>
    </>
  );
};

export default connect()(AgentManage);
