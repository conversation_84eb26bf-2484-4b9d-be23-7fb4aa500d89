import { Card, Button, Typography, Space, Row, Col, Alert, Progress } from 'antd';
import { PageHeader } from '@ant-design/pro-components';
import { DownloadOutlined, PlayCircleOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import React, { useState, } from 'react';
import { useIntl } from 'umi';
import LogManagement from './components/LogManagement';
import './index.less';

const { Text } = Typography;


const OperationManage: React.FC = () => {
  const [activeMenu, setActiveMenu] = useState<string>('logs');
  const [systemStatus, setSystemStatus] = useState({
    nginx: 'running',
    redis: 'running',
    mysql: 'running',
    disk: '85%',
    memory: '65%'
  });
  const [menuCollapsed, setMenuCollapsed] = useState<boolean>(false);
  const intl = useIntl();

  /**
   * 重启nginx服务
   */
  const handleRestartNginx = async () => {
    try {
      // 这里可以调用实际的API
      console.log('重启nginx服务...');
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('nginx重启成功');
    } catch (error) {
      console.error('重启nginx失败:', error);
    }
  };

  /**
   * 系统升级
   */
  const handleSystemUpgrade = async () => {
    try {
      console.log('开始系统升级...');
      // 模拟升级过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('系统升级完成');
    } catch (error) {
      console.error('系统升级失败:', error);
    }
  };

  /**
   * 获取系统状态
   */
  const getSystemStatus = async () => {
    try {
      // 这里可以调用实际的系统状态API
      console.log('获取系统状态...');
    } catch (error) {
      console.error('获取系统状态失败:', error);
    }
  };

  return (
    <PageHeader
      style={{ backgroundColor: 'white', padding: '24px' }}
      onBack={() => null}
      title=""
      backIcon=""
    >
      <div className="operation-manage-container">
        <div className={`log-files-section ${menuCollapsed ? 'collapsed' : ''}`}>
          <div className="log-menu-header">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', position: 'relative' }}>
              {!menuCollapsed && (
                <Text strong style={{ fontSize: '16px' }}>
                  系统运维
                </Text>
              )}
              <Button
                type="text"
                icon={menuCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                size="small"
                onClick={() => setMenuCollapsed(!menuCollapsed)}
                title={menuCollapsed ? '展开菜单' : '收起菜单'}
                style={{
                  backgroundColor: 'transparent',
                  borderColor: 'transparent',
                  marginLeft: 'auto'
                }}
                className="menu-collapse-btn"
              />
            </div>
          </div>

          {!menuCollapsed && (
            <div className="log-menu-content">
              <div className="system-menu">
                <div
                  className={`menu-item ${activeMenu === 'dashboard' ? 'active' : ''}`}
                  onClick={() => setActiveMenu('dashboard')}
                >
                  <div className="menu-icon">📊</div>
                  <div className="menu-text">系统概览</div>
                </div>

                <div
                  className={`menu-item ${activeMenu === 'logs' ? 'active' : ''}`}
                  onClick={() => setActiveMenu('logs')}
                >
                  <div className="menu-icon">📝</div>
                  <div className="menu-text">日志管理</div>
                </div>

                <div
                  className={`menu-item ${activeMenu === 'nginx' ? 'active' : ''}`}
                  onClick={() => setActiveMenu('nginx')}
                >
                  <div className="menu-icon">🔄</div>
                  <div className="menu-text">Nginx管理</div>
                </div>

                <div
                  className={`menu-item ${activeMenu === 'upgrade' ? 'active' : ''}`}
                  onClick={() => setActiveMenu('upgrade')}
                >
                  <div className="menu-icon">⬆️</div>
                  <div className="menu-text">系统升级</div>
                </div>

                <div
                  className={`menu-item ${activeMenu === 'monitor' ? 'active' : ''}`}
                  onClick={() => setActiveMenu('monitor')}
                >
                  <div className="menu-icon">📈</div>
                  <div className="menu-text">系统监控</div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="log-content-section">
          {activeMenu === 'dashboard' && (
            <Card title="系统概览" size="small">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card size="small" title="Nginx状态">
                    <Text type={systemStatus.nginx === 'running' ? 'success' : 'danger'}>
                      {systemStatus.nginx === 'running' ? '运行中' : '已停止'}
                    </Text>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small" title="Redis状态">
                    <Text type={systemStatus.redis === 'running' ? 'success' : 'danger'}>
                      {systemStatus.redis === 'running' ? '运行中' : '已停止'}
                    </Text>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small" title="MySQL状态">
                    <Text type={systemStatus.mysql === 'running' ? 'success' : 'danger'}>
                      {systemStatus.mysql === 'running' ? '运行中' : '已停止'}
                    </Text>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small" title="磁盘使用率">
                    <Text>{systemStatus.disk}</Text>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small" title="内存使用率">
                    <Text>{systemStatus.memory}</Text>
                  </Card>
                </Col>
              </Row>
            </Card>
          )}


          {activeMenu === 'logs' && <LogManagement />}


          {activeMenu === 'nginx' && (
            <Card title="Nginx管理" size="small">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card size="small" title="当前状态">
                  <Text type={systemStatus.nginx === 'running' ? 'success' : 'danger'}>
                    Nginx当前状态：{systemStatus.nginx === 'running' ? '运行中' : '已停止'}
                  </Text>
                </Card>

                <Space>
                  <Button
                    type="primary"
                    danger
                    onClick={handleRestartNginx}
                    icon={<PlayCircleOutlined />}
                  >
                    重启Nginx服务
                  </Button>
                  <Button
                    type="default"
                    onClick={() => console.log('查看Nginx配置')}
                  >
                    查看配置
                  </Button>
                </Space>
              </Space>
            </Card>
          )}

          {activeMenu === 'upgrade' && (
            <Card title="系统升级" size="small">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Alert
                  message="系统升级提醒"
                  description="升级前请确保已备份重要数据，升级过程中系统可能会短暂不可用。"
                  type="warning"
                  showIcon
                />

                <Space>
                  <Button
                    type="primary"
                    onClick={handleSystemUpgrade}
                    icon={<DownloadOutlined />}
                  >
                    开始系统升级
                  </Button>
                  <Button
                    type="default"
                    onClick={() => console.log('查看升级日志')}
                  >
                    查看升级日志
                  </Button>
                </Space>
              </Space>
            </Card>
          )}

          {activeMenu === 'monitor' && (
            <Card title="系统监控" size="small">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Card size="small" title="CPU使用率">
                      <Progress percent={45} status="normal" />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="内存使用率">
                      <Progress percent={65} status="normal" />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="磁盘使用率">
                      <Progress percent={85} status="exception" />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="网络状态">
                      <Text type="success">正常</Text>
                    </Card>
                  </Col>
                </Row>
              </Space>
            </Card>
          )}
        </div>
      </div>
    </PageHeader>
  );
};

export default OperationManage;
