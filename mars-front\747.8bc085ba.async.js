!(function(){var n_=Object.defineProperty;var Yc=Re=>{throw TypeError(Re)};var i_=(<PERSON>,ke,Z)=>ke in Re?n_(Re,ke,{enumerable:!0,configurable:!0,writable:!0,value:Z}):Re[ke]=Z;var Fi=(Re,ke,Z)=>i_(Re,typeof ke!="symbol"?ke+"":ke,Z),Zc=(Re,ke,Z)=>ke.has(Re)||Yc("Cannot "+Z);var yt=(Re,ke,Z)=>(Zc(Re,ke,"read from private field"),Z?Z.call(Re):ke.get(Re)),o0=(Re,ke,Z)=>ke.has(Re)?Yc("Cannot add the same private member more than once"):ke instanceof WeakSet?ke.add(Re):ke.set(Re,Z),jt=(<PERSON>,ke,Z,Q)=>(Zc(Re,ke,"write to private field"),Q?Q.call(<PERSON>,Z):ke.set(<PERSON>,Z),Z);var yi=(<PERSON>,ke,Z,Q)=>({set _(De){jt(<PERSON>,ke,De,Z)},get _(){return yt(Re,ke,Q)}});var l0=(Re,ke,Z)=>new Promise((Q,De)=>{var Ne=Oe=>{try{Fe(Z.next(Oe))}catch(Ge){De(Ge)}},Be=Oe=>{try{Fe(Z.throw(Oe))}catch(Ge){De(Ge)}},Fe=Oe=>Oe.done?Q(Oe.value):Promise.resolve(Oe.value).then(Ne,Be);Fe((Z=Z.apply(Re,ke)).next())});(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[747],{94737:function(Re,ke){"use strict";var Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"};ke.Z=Z},69753:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(67294),Ne=Z(49495),Be=Z(91146),Fe=function(be,Ve){return De.createElement(Be.Z,(0,Q.Z)((0,Q.Z)({},be),{},{ref:Ve,icon:Ne.Z}))},Oe=De.forwardRef(Fe);ke.Z=Oe},51042:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(67294),Ne=Z(42110),Be=Z(91146),Fe=function(be,Ve){return De.createElement(Be.Z,(0,Q.Z)((0,Q.Z)({},be),{},{ref:Ve,icon:Ne.Z}))},Oe=De.forwardRef(Fe);ke.Z=Oe},43471:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(67294),Ne=Z(82947),Be=Z(91146),Fe=function(be,Ve){return De.createElement(Be.Z,(0,Q.Z)((0,Q.Z)({},be),{},{ref:Ve,icon:Ne.Z}))},Oe=De.forwardRef(Fe);ke.Z=Oe},26859:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(67294),Ne=Z(94737),Be=Z(91146),Fe=function(be,Ve){return De.createElement(Be.Z,(0,Q.Z)((0,Q.Z)({},be),{},{ref:Ve,icon:Ne.Z}))},Oe=De.forwardRef(Fe);ke.Z=Oe},63434:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(45987),Ne=Z(22270),Be=Z(84567),Fe=Z(67294),Oe=Z(90789),Ge=Z(27577),be=Z(85893),Ve=["options","fieldProps","proFieldProps","valueEnum"],de=Fe.forwardRef(function(Ae,Le){var Qe=Ae.options,Je=Ae.fieldProps,Ee=Ae.proFieldProps,je=Ae.valueEnum,he=(0,De.Z)(Ae,Ve);return(0,be.jsx)(Ge.Z,(0,Q.Z)({ref:Le,valueType:"checkbox",valueEnum:(0,Ne.h)(je,void 0),fieldProps:(0,Q.Z)({options:Qe},Je),lightProps:(0,Q.Z)({labelFormatter:function(){return(0,be.jsx)(Ge.Z,(0,Q.Z)({ref:Le,valueType:"checkbox",mode:"read",valueEnum:(0,Ne.h)(je,void 0),filedConfig:{customLightMode:!0},fieldProps:(0,Q.Z)({options:Qe},Je),proFieldProps:Ee},he))}},he.lightProps),proFieldProps:Ee},he))}),ve=Fe.forwardRef(function(Ae,Le){var Qe=Ae.fieldProps,Je=Ae.children;return(0,be.jsx)(Be.Z,(0,Q.Z)((0,Q.Z)({ref:Le},Qe),{},{children:Je}))}),we=(0,Oe.G)(ve,{valuePropName:"checked"}),er=we;er.Group=de,ke.Z=er},31199:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(45987),Ne=Z(67294),Be=Z(27577),Fe=Z(85893),Oe=["fieldProps","min","proFieldProps","max"],Ge=function(de,ve){var we=de.fieldProps,er=de.min,Ae=de.proFieldProps,Le=de.max,Qe=(0,De.Z)(de,Oe);return(0,Fe.jsx)(Be.Z,(0,Q.Z)({valueType:"digit",fieldProps:(0,Q.Z)({min:er,max:Le},we),ref:ve,filedConfig:{defaultProps:{width:"100%"}},proFieldProps:Ae},Qe))},be=Ne.forwardRef(Ge);ke.Z=be},64317:function(Re,ke,Z){"use strict";var Q=Z(1413),De=Z(45987),Ne=Z(22270),Be=Z(67294),Fe=Z(66758),Oe=Z(27577),Ge=Z(85893),be=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],Ve=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],de=function(Qe,Je){var Ee=Qe.fieldProps,je=Qe.children,he=Qe.params,Ie=Qe.proFieldProps,tr=Qe.mode,Hr=Qe.valueEnum,hr=Qe.request,ye=Qe.showSearch,gr=Qe.options,br=(0,De.Z)(Qe,be),st=(0,Be.useContext)(Fe.Z);return(0,Ge.jsx)(Oe.Z,(0,Q.Z)((0,Q.Z)({valueEnum:(0,Ne.h)(Hr),request:hr,params:he,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,Q.Z)({options:gr,mode:tr,showSearch:ye,getPopupContainer:st.getPopupContainer},Ee),ref:Je,proFieldProps:Ie},br),{},{children:je}))},ve=Be.forwardRef(function(Le,Qe){var Je=Le.fieldProps,Ee=Le.children,je=Le.params,he=Le.proFieldProps,Ie=Le.mode,tr=Le.valueEnum,Hr=Le.request,hr=Le.options,ye=(0,De.Z)(Le,Ve),gr=(0,Q.Z)({options:hr,mode:Ie||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},Je),br=(0,Be.useContext)(Fe.Z);return(0,Ge.jsx)(Oe.Z,(0,Q.Z)((0,Q.Z)({valueEnum:(0,Ne.h)(tr),request:Hr,params:je,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,Q.Z)({getPopupContainer:br.getPopupContainer},gr),ref:Qe,proFieldProps:he},ye),{},{children:Ee}))}),we=Be.forwardRef(de),er=ve,Ae=we;Ae.SearchSelect=er,Ae.displayName="ProFormComponent",ke.Z=Ae},5966:function(Re,ke,Z){"use strict";var Q=Z(97685),De=Z(1413),Ne=Z(45987),Be=Z(21770),Fe=Z(53025),Oe=Z(55241),Ge=Z(97435),be=Z(67294),Ve=Z(27577),de=Z(85893),ve=["fieldProps","proFieldProps"],we=["fieldProps","proFieldProps"],er="text",Ae=function(je){var he=je.fieldProps,Ie=je.proFieldProps,tr=(0,Ne.Z)(je,ve);return(0,de.jsx)(Ve.Z,(0,De.Z)({valueType:er,fieldProps:he,filedConfig:{valueType:er},proFieldProps:Ie},tr))},Le=function(je){var he=(0,Be.Z)(je.open||!1,{value:je.open,onChange:je.onOpenChange}),Ie=(0,Q.Z)(he,2),tr=Ie[0],Hr=Ie[1];return(0,de.jsx)(Fe.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(ye){var gr,br=ye.getFieldValue(je.name||[]);return(0,de.jsx)(Oe.Z,(0,De.Z)((0,De.Z)({getPopupContainer:function(ar){return ar&&ar.parentNode?ar.parentNode:ar},onOpenChange:function(ar){return Hr(ar)},content:(0,de.jsxs)("div",{style:{padding:"4px 0"},children:[(gr=je.statusRender)===null||gr===void 0?void 0:gr.call(je,br),je.strengthText?(0,de.jsx)("div",{style:{marginTop:10},children:(0,de.jsx)("span",{children:je.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},je.popoverProps),{},{open:tr,children:je.children}))}})},Qe=function(je){var he=je.fieldProps,Ie=je.proFieldProps,tr=(0,Ne.Z)(je,we),Hr=(0,be.useState)(!1),hr=(0,Q.Z)(Hr,2),ye=hr[0],gr=hr[1];return he!=null&&he.statusRender&&tr.name?(0,de.jsx)(Le,{name:tr.name,statusRender:he==null?void 0:he.statusRender,popoverProps:he==null?void 0:he.popoverProps,strengthText:he==null?void 0:he.strengthText,open:ye,onOpenChange:gr,children:(0,de.jsx)("div",{children:(0,de.jsx)(Ve.Z,(0,De.Z)({valueType:"password",fieldProps:(0,De.Z)((0,De.Z)({},(0,Ge.Z)(he,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(st){var ar;he==null||(ar=he.onBlur)===null||ar===void 0||ar.call(he,st),gr(!1)},onClick:function(st){var ar;he==null||(ar=he.onClick)===null||ar===void 0||ar.call(he,st),gr(!0)}}),proFieldProps:Ie,filedConfig:{valueType:er}},tr))})}):(0,de.jsx)(Ve.Z,(0,De.Z)({valueType:"password",fieldProps:he,proFieldProps:Ie,filedConfig:{valueType:er}},tr))},Je=Ae;Je.Password=Qe,Je.displayName="ProFormComponent",ke.Z=Je},2236:function(Re,ke,Z){"use strict";Z.d(ke,{S:function(){return je}});var Q=Z(1413),De=Z(4942),Ne=Z(71002),Be=Z(45987),Fe=Z(12044),Oe=Z(21532),Ge=Z(93967),be=Z.n(Ge),Ve=Z(97435),de=Z(67294),ve=Z(73935),we=Z(76509),er=Z(98082),Ae=function(Ie){return(0,De.Z)({},Ie.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,er.uK)(Ie.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(Ie.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:Ie.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:Ie.colorText},"&-right":{color:Ie.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function Le(he){return(0,er.Xj)("ProLayoutFooterToolbar",function(Ie){var tr=(0,Q.Z)((0,Q.Z)({},Ie),{},{componentCls:".".concat(he)});return[Ae(tr)]})}function Qe(he,Ie){var tr=Ie.stylish;return(0,er.Xj)("ProLayoutFooterToolbarStylish",function(Hr){var hr=(0,Q.Z)((0,Q.Z)({},Hr),{},{componentCls:".".concat(he)});return tr?[(0,De.Z)({},"".concat(hr.componentCls),tr==null?void 0:tr(hr))]:[]})}var Je=Z(85893),Ee=["children","className","extra","portalDom","style","renderContent"],je=function(Ie){var tr=Ie.children,Hr=Ie.className,hr=Ie.extra,ye=Ie.portalDom,gr=ye===void 0?!0:ye,br=Ie.style,st=Ie.renderContent,ar=(0,Be.Z)(Ie,Ee),$t=(0,de.useContext)(Oe.ZP.ConfigContext),At=$t.getPrefixCls,Xa=$t.getTargetContainer,Ga=Ie.prefixCls||At("pro"),or="".concat(Ga,"-footer-bar"),An=Le(or),Lr=An.wrapSSR,Bt=An.hashId,xr=(0,de.useContext)(we.X),at=(0,de.useMemo)(function(){var ht=xr.hasSiderMenu,Ca=xr.isMobile,za=xr.siderWidth;if(ht)return za?Ca?"100%":"calc(100% - ".concat(za,"px)"):"100%"},[xr.collapsed,xr.hasSiderMenu,xr.isMobile,xr.siderWidth]),ya=(0,de.useMemo)(function(){return(typeof window=="undefined"?"undefined":(0,Ne.Z)(window))===void 0||(typeof document=="undefined"?"undefined":(0,Ne.Z)(document))===void 0?null:(Xa==null?void 0:Xa())||document.body},[]),Aa=Qe("".concat(or,".").concat(or,"-stylish"),{stylish:Ie.stylish}),Cn=(0,Je.jsxs)(Je.Fragment,{children:[(0,Je.jsx)("div",{className:"".concat(or,"-left ").concat(Bt).trim(),children:hr}),(0,Je.jsx)("div",{className:"".concat(or,"-right ").concat(Bt).trim(),children:tr})]});(0,de.useEffect)(function(){return!xr||!(xr!=null&&xr.setHasFooterToolbar)?function(){}:(xr==null||xr.setHasFooterToolbar(!0),function(){var ht;xr==null||(ht=xr.setHasFooterToolbar)===null||ht===void 0||ht.call(xr,!1)})},[]);var Dn=(0,Je.jsx)("div",(0,Q.Z)((0,Q.Z)({className:be()(Hr,Bt,or,(0,De.Z)({},"".concat(or,"-stylish"),!!Ie.stylish)),style:(0,Q.Z)({width:at},br)},(0,Ve.Z)(ar,["prefixCls"])),{},{children:st?st((0,Q.Z)((0,Q.Z)((0,Q.Z)({},Ie),xr),{},{leftWidth:at}),Cn):Cn})),On=!(0,Fe.j)()||!gr||!ya?Dn:(0,ve.createPortal)(Dn,ya,or);return Aa.wrapSSR(Lr((0,Je.jsx)(de.Fragment,{children:On},or)))}},76509:function(Re,ke,Z){"use strict";Z.d(ke,{X:function(){return De}});var Q=Z(67294),De=(0,Q.createContext)({})},93162:function(Re,ke,Z){var Q,De,Ne;(function(Be,Fe){De=[],Q=Fe,Ne=typeof Q=="function"?Q.apply(ke,De):Q,Ne!==void 0&&(Re.exports=Ne)})(this,function(){"use strict";function Be(ve,we){return typeof we=="undefined"?we={autoBom:!1}:typeof we!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),we={autoBom:!we}),we.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(ve.type)?new Blob(["\uFEFF",ve],{type:ve.type}):ve}function Fe(ve,we,er){var Ae=new XMLHttpRequest;Ae.open("GET",ve),Ae.responseType="blob",Ae.onload=function(){de(Ae.response,we,er)},Ae.onerror=function(){console.error("could not download file")},Ae.send()}function Oe(ve){var we=new XMLHttpRequest;we.open("HEAD",ve,!1);try{we.send()}catch(er){}return 200<=we.status&&299>=we.status}function Ge(ve){try{ve.dispatchEvent(new MouseEvent("click"))}catch(er){var we=document.createEvent("MouseEvents");we.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),ve.dispatchEvent(we)}}var be=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof Z.g=="object"&&Z.g.global===Z.g?Z.g:void 0,Ve=be.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),de=be.saveAs||(typeof window!="object"||window!==be?function(){}:"download"in HTMLAnchorElement.prototype&&!Ve?function(ve,we,er){var Ae=be.URL||be.webkitURL,Le=document.createElement("a");we=we||ve.name||"download",Le.download=we,Le.rel="noopener",typeof ve=="string"?(Le.href=ve,Le.origin===location.origin?Ge(Le):Oe(Le.href)?Fe(ve,we,er):Ge(Le,Le.target="_blank")):(Le.href=Ae.createObjectURL(ve),setTimeout(function(){Ae.revokeObjectURL(Le.href)},4e4),setTimeout(function(){Ge(Le)},0))}:"msSaveOrOpenBlob"in navigator?function(ve,we,er){if(we=we||ve.name||"download",typeof ve!="string")navigator.msSaveOrOpenBlob(Be(ve,er),we);else if(Oe(ve))Fe(ve,we,er);else{var Ae=document.createElement("a");Ae.href=ve,Ae.target="_blank",setTimeout(function(){Ge(Ae)})}}:function(ve,we,er,Ae){if(Ae=Ae||open("","_blank"),Ae&&(Ae.document.title=Ae.document.body.innerText="downloading..."),typeof ve=="string")return Fe(ve,we,er);var Le=ve.type==="application/octet-stream",Qe=/constructor/i.test(be.HTMLElement)||be.safari,Je=/CriOS\/[\d]+/.test(navigator.userAgent);if((Je||Le&&Qe||Ve)&&typeof FileReader!="undefined"){var Ee=new FileReader;Ee.onloadend=function(){var Ie=Ee.result;Ie=Je?Ie:Ie.replace(/^data:[^;]*;/,"data:attachment/file;"),Ae?Ae.location.href=Ie:location=Ie,Ae=null},Ee.readAsDataURL(ve)}else{var je=be.URL||be.webkitURL,he=je.createObjectURL(ve);Ae?Ae.location=he:location.href=he,Ae=null,setTimeout(function(){je.revokeObjectURL(he)},4e4)}});be.saveAs=de.saveAs=de,Re.exports=de})},64599:function(Re,ke,Z){var Q=Z(96263);function De(Ne,Be){var Fe=typeof Symbol!="undefined"&&Ne[Symbol.iterator]||Ne["@@iterator"];if(!Fe){if(Array.isArray(Ne)||(Fe=Q(Ne))||Be&&Ne&&typeof Ne.length=="number"){Fe&&(Ne=Fe);var Oe=0,Ge=function(){};return{s:Ge,n:function(){return Oe>=Ne.length?{done:!0}:{done:!1,value:Ne[Oe++]}},e:function(we){throw we},f:Ge}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var be=!0,Ve=!1,de;return{s:function(){Fe=Fe.call(Ne)},n:function(){var we=Fe.next();return be=we.done,we},e:function(we){Ve=!0,de=we},f:function(){try{!be&&Fe.return!=null&&Fe.return()}finally{if(Ve)throw de}}}}Re.exports=De,Re.exports.__esModule=!0,Re.exports.default=Re.exports},8861:function(Re,ke,Z){"use strict";var Oe,Ge,be;Z.d(ke,{Z:function(){return Ne}});class Q{constructor(de){Fi(this,"value");Fi(this,"next");this.value=de}}class De{constructor(){o0(this,Oe);o0(this,Ge);o0(this,be);this.clear()}enqueue(de){const ve=new Q(de);yt(this,Oe)?(yt(this,Ge).next=ve,jt(this,Ge,ve)):(jt(this,Oe,ve),jt(this,Ge,ve)),yi(this,be)._++}dequeue(){const de=yt(this,Oe);if(de)return jt(this,Oe,yt(this,Oe).next),yi(this,be)._--,de.value}peek(){if(yt(this,Oe))return yt(this,Oe).value}clear(){jt(this,Oe,void 0),jt(this,Ge,void 0),jt(this,be,0)}get size(){return yt(this,be)}*[Symbol.iterator](){let de=yt(this,Oe);for(;de;)yield de.value,de=de.next}*drain(){for(;yt(this,Oe);)yield this.dequeue()}}Oe=new WeakMap,Ge=new WeakMap,be=new WeakMap;function Ne(Ve){Fe(Ve);const de=new De;let ve=0;const we=()=>{ve<Ve&&de.size>0&&(de.dequeue()(),ve++)},er=()=>{ve--,we()},Ae=(Je,Ee,je)=>l0(this,null,function*(){const he=l0(this,null,function*(){return Je(...je)});Ee(he);try{yield he}catch(Ie){}er()}),Le=(Je,Ee,je)=>{new Promise(he=>{de.enqueue(he)}).then(Ae.bind(void 0,Je,Ee,je)),l0(this,null,function*(){yield Promise.resolve(),ve<Ve&&we()})},Qe=(Je,...Ee)=>new Promise(je=>{Le(Je,je,Ee)});return Object.defineProperties(Qe,{activeCount:{get:()=>ve},pendingCount:{get:()=>de.size},clearQueue:{value(){de.clear()}},concurrency:{get:()=>Ve,set(Je){Fe(Je),Ve=Je,queueMicrotask(()=>{for(;ve<Ve&&de.size>0;)we()})}}}),Qe}function Be(Ve,de){const{concurrency:ve}=de,we=Ne(ve);return(...er)=>we(()=>Ve(...er))}function Fe(Ve){if(!((Number.isInteger(Ve)||Ve===Number.POSITIVE_INFINITY)&&Ve>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up")}},84105:function(Re,ke,Z){"use strict";Z.d(ke,{P6:function(){return Bg},cW:function(){return i0},ij:function(){return a0}});var Q=Z(20067).Buffer,De=Z(34155);var Ne={};Ne.version="0.18.5";var Be=1200,Fe=1252,Oe=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],Ge={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},be=function(e){Oe.indexOf(e)!=-1&&(Fe=Ge[0]=e)};function Ve(){be(1252)}var de=function(e){Be=e,be(e)};function ve(){de(1200),Ve()}function we(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function er(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function Ae(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var Le=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?er(e.slice(2)):t==254&&r==255?Ae(e.slice(2)):t==65279?e.slice(1):e},Qe=function(t){return String.fromCharCode(t)},Je=function(t){return String.fromCharCode(t)},Ee;function je(e){Ee=e,de=function(t){Be=t,be(t)},Le=function(t){return t.charCodeAt(0)===255&&t.charCodeAt(1)===254?Ee.utils.decode(1200,we(t.slice(2))):t},Qe=function(r){return Be===1200?String.fromCharCode(r):Ee.utils.decode(Be,[r&255,r>>8])[0]},Je=function(r){return Ee.utils.decode(Fe,[r])[0]},Es()}var he=null,Ie=!0,tr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Hr(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0,o=0;o<e.length;)r=e.charCodeAt(o++),i=r>>2,a=e.charCodeAt(o++),s=(r&3)<<4|a>>4,n=e.charCodeAt(o++),f=(a&15)<<2|n>>6,c=n&63,isNaN(a)?f=c=64:isNaN(n)&&(c=64),t+=tr.charAt(i)+tr.charAt(s)+tr.charAt(f)+tr.charAt(c);return t}function hr(e){var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)i=tr.indexOf(e.charAt(o++)),s=tr.indexOf(e.charAt(o++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=tr.indexOf(e.charAt(o++)),a=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(a)),c=tr.indexOf(e.charAt(o++)),n=(f&3)<<6|c,c!==64&&(t+=String.fromCharCode(n));return t}var ye=function(){return typeof Q!="undefined"&&typeof De!="undefined"&&typeof De.versions!="undefined"&&!!De.versions.node}(),gr=function(){if(typeof Q!="undefined"){var e=!Q.from;if(!e)try{Q.from("foo","utf8")}catch(t){e=!0}return e?function(t,r){return r?new Q(t,r):new Q(t)}:Q.from.bind(Q)}return function(){}}();function br(e){return ye?Q.alloc?Q.alloc(e):new Q(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function st(e){return ye?Q.allocUnsafe?Q.allocUnsafe(e):new Q(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var ar=function(t){return ye?gr(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function $t(e){if(typeof ArrayBuffer=="undefined")return ar(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=e.charCodeAt(a)&255;return t}function At(e){if(Array.isArray(e))return e.map(function(a){return String.fromCharCode(a)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Xa(e){if(typeof Uint8Array=="undefined")throw new Error("Unsupported");return new Uint8Array(e)}function Ga(e){if(typeof ArrayBuffer=="undefined")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return Ga(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var or=ye?function(e){return Q.concat(e.map(function(t){return Q.isBuffer(t)?t:gr(t)}))}:function(e){if(typeof Uint8Array!="undefined"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function An(e){for(var t=[],r=0,a=e.length+250,n=br(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)n[r++]=s;else if(s<2048)n[r++]=192|s>>6&31,n[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;n[r++]=240|s>>8&7,n[r++]=128|s>>2&63,n[r++]=128|f>>6&15|(s&3)<<4,n[r++]=128|f&63}else n[r++]=224|s>>12&15,n[r++]=128|s>>6&63,n[r++]=128|s&63;r>a&&(t.push(n.slice(0,r)),r=0,n=br(65535),a=65530)}return t.push(n.slice(0,r)),or(t)}var Lr=/\u0000/g,Bt=/[\u0001-\u0006]/g;function xr(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function at(e,t){var r=""+e;return r.length>=t?r:Er("0",t-r.length)+r}function ya(e,t){var r=""+e;return r.length>=t?r:Er(" ",t-r.length)+r}function Aa(e,t){var r=""+e;return r.length>=t?r:r+Er(" ",t-r.length)}function Cn(e,t){var r=""+Math.round(e);return r.length>=t?r:Er("0",t-r.length)+r}function Dn(e,t){var r=""+e;return r.length>=t?r:Er("0",t-r.length)+r}var On=Math.pow(2,32);function ht(e,t){if(e>On||e<-On)return Cn(e,t);var r=Math.round(e);return Dn(r,t)}function Ca(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var za=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],u0=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Jc(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "',e}var Se={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "'},Ai={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Qc={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function In(e,t,r){for(var a=e<0?-1:1,n=e*a,i=0,s=1,f=0,c=1,o=0,l=0,h=Math.floor(n);o<t&&(h=Math.floor(n),f=h*s+i,l=h*o+c,!(n-h<5e-8));)n=1/(n-h),i=s,s=f,c=o,o=l;if(l>t&&(o>t?(l=c,f=i):(l=o,f=s)),!r)return[0,a*f,l];var x=Math.floor(a*f/l);return[x,a*f-x*l,l]}function Yt(e,t,r){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0,s=[],f={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(a+=1462),f.u>.9999&&(f.u=0,++n==86400&&(f.T=n=0,++a,++f.D)),a===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(a===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),s=[c.getFullYear(),c.getMonth()+1,c.getDate()],i=c.getDay(),a<60&&(i=(i+6)%7),r&&(i=no(c,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=n%60,n=Math.floor(n/60),f.M=n%60,n=Math.floor(n/60),f.H=n,f.q=i,f}var Ci=new Date(1899,11,31,0,0,0),qc=Ci.getTime(),eo=new Date(1900,2,1,0,0,0);function Di(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=eo&&(r+=24*60*60*1e3),(r-(qc+(e.getTimezoneOffset()-Ci.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function h0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function ro(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function to(e){var t=e<0?12:11,r=h0(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function ao(e){var t=h0(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Ka(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=to(e):t===10?r=e.toFixed(10).substr(0,12):r=ao(e),h0(ro(r.toUpperCase()))}function fa(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Ka(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return ft(14,Di(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function no(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function io(e,t,r,a){var n="",i=0,s=0,f=r.y,c,o=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:c=f%100,o=2;break;default:c=f%1e4,o=4;break}break;case 109:switch(t.length){case 1:case 2:c=r.m,o=t.length;break;case 3:return u0[r.m-1][1];case 5:return u0[r.m-1][0];default:return u0[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:c=r.d,o=t.length;break;case 3:return za[r.q][0];default:return za[r.q][1]}break;case 104:switch(t.length){case 1:case 2:c=1+(r.H+11)%12,o=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:c=r.H,o=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:c=r.M,o=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?at(r.S,t.length):(a>=2?s=a===3?1e3:100:s=a===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(n=at(i,2+a),t==="ss"?n.substr(0,2):"."+n.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":c=r.D*24+r.H;break;case"[m]":case"[mm]":c=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":c=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}o=t.length===3?1:2;break;case 101:c=f,o=1;break}var l=o>0?at(c,o):"";return l}function Zt(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,a=e.substr(0,r);r!=e.length;r+=t)a+=(a.length>0?",":"")+e.substr(r,t);return a}var Oi=/%/g;function so(e,t,r){var a=t.replace(Oi,""),n=t.length-a.length;return Ut(e,a,r*Math.pow(10,2*n))+Er("%",n)}function fo(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return Ut(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function Ii(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ii(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Pi=/# (\?+)( ?)\/( ?)(\d+)/;function co(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),i=Math.floor(n/a),s=n-i*a,f=a;return r+(i===0?"":""+i)+" "+(s===0?Er(" ",e[1].length+1+e[4].length):ya(s,e[1].length)+e[2]+"/"+e[3]+at(f,e[4].length))}function oo(e,t,r){return r+(t===0?"":""+t)+Er(" ",e[1].length+2+e[4].length)}var Ri=/^#*0*\.([0#]+)/,Ni=/\).*[0#]/,bi=/\(###\) ###\\?-####/;function Qr(e){for(var t="",r,a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function Li(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function Mi(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function lo(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function uo(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function xt(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ni)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?xt("n",a,r):"("+xt("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return fo(e,t,r);if(t.indexOf("%")!==-1)return so(e,t,r);if(t.indexOf("E")!==-1)return Ii(t,r);if(t.charCodeAt(0)===36)return"$"+xt(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+ht(c,t.length);if(t.match(/^[#?]+$/))return n=ht(r,0),n==="0"&&(n=""),n.length>t.length?n:Qr(t.substr(0,t.length-n.length))+n;if(i=t.match(Pi))return co(i,c,o);if(t.match(/^#+0+$/))return o+ht(c,t.length-t.indexOf("0"));if(i=t.match(Ri))return n=Li(r,i[1].length).replace(/^([^\.]+)$/,"$1."+Qr(i[1])).replace(/\.$/,"."+Qr(i[1])).replace(/\.(\d*)$/,function(v,u){return"."+u+Er("0",Qr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+Li(c,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Zt(ht(c,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+xt(e,t,-r):Zt(""+(Math.floor(r)+lo(r,i[1].length)))+"."+at(Mi(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return xt(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=xr(xt(e,t.replace(/[\\-]/g,""),r)),s=0,xr(xr(t.replace(/\\/g,"")).replace(/[0#]/g,function(v){return s<n.length?n.charAt(s++):v==="0"?"0":""}));if(t.match(bi))return n=xt(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=In(c,Math.pow(10,s)-1,!1),n=""+o,l=Ut("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=Aa(f[2],s),l.length<i[4].length&&(l=Qr(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=In(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?ya(f[1],s)+i[2]+"/"+i[3]+Aa(f[2],s):Er(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=ht(r,0),t.length<=n.length?n:Qr(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var h=t.indexOf(".")-s,x=t.length-n.length-h;return Qr(t.substr(0,h)+n+t.substr(t.length-x))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=Mi(r,i[1].length),r<0?"-"+xt(e,t,-r):Zt(uo(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(v){return"00,"+(v.length<3?at(0,3-v.length):"")+v})+"."+at(s,i[1].length);switch(t){case"###,##0.00":return xt(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=Zt(ht(c,0));return d!=="0"?o+d:"";case"###,###.00":return xt(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return xt(e,"#,##0.00",r).replace(/^0\./,".");default:}throw new Error("unsupported format |"+t+"|")}function ho(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return Ut(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function xo(e,t,r){var a=t.replace(Oi,""),n=t.length-a.length;return Ut(e,a,r*Math.pow(10,2*n))+Er("%",n)}function Bi(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Bi(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Ct(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ni)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Ct("n",a,r):"("+Ct("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return ho(e,t,r);if(t.indexOf("%")!==-1)return xo(e,t,r);if(t.indexOf("E")!==-1)return Bi(t,r);if(t.charCodeAt(0)===36)return"$"+Ct(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+at(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,r===0&&(n=""),n.length>t.length?n:Qr(t.substr(0,t.length-n.length))+n;if(i=t.match(Pi))return oo(i,c,o);if(t.match(/^#+0+$/))return o+at(c,t.length-t.indexOf("0"));if(i=t.match(Ri))return n=(""+r).replace(/^([^\.]+)$/,"$1."+Qr(i[1])).replace(/\.$/,"."+Qr(i[1])),n=n.replace(/\.(\d*)$/,function(v,u){return"."+u+Er("0",Qr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Zt(""+c);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Ct(e,t,-r):Zt(""+r)+"."+Er("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Ct(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=xr(Ct(e,t.replace(/[\\-]/g,""),r)),s=0,xr(xr(t.replace(/\\/g,"")).replace(/[0#]/g,function(v){return s<n.length?n.charAt(s++):v==="0"?"0":""}));if(t.match(bi))return n=Ct(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=In(c,Math.pow(10,s)-1,!1),n=""+o,l=Ut("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=Aa(f[2],s),l.length<i[4].length&&(l=Qr(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=In(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?ya(f[1],s)+i[2]+"/"+i[3]+Aa(f[2],s):Er(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:Qr(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var h=t.indexOf(".")-s,x=t.length-n.length-h;return Qr(t.substr(0,h)+n+t.substr(t.length-x))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Ct(e,t,-r):Zt(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(v){return"00,"+(v.length<3?at(0,3-v.length):"")+v})+"."+at(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=Zt(""+c);return d!=="0"?o+d:"";default:if(t.match(/\.[0#?]*$/))return Ct(e,t.slice(0,t.lastIndexOf(".")),r)+Qr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Ut(e,t,r){return(r|0)===r?Ct(e,t,r):xt(e,t,r)}function vo(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Ui=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ca(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":Ca(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4E0A":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348")return!0;++t;break;case"[":for(a=r;e.charAt(t++)!=="]"&&t<e.length;)a+=e.charAt(t);if(a.match(Ui))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function po(e,t,r,a){for(var n=[],i="",s=0,f="",c="t",o,l,h,x="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!Ca(e,s))throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(h=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(h);n[n.length]={t:"t",v:i},++s;break;case"\\":var d=e.charAt(++s),v=d==="("||d===")"?d:"t";n[n.length]={t:v,v:d},++s;break;case"_":n[n.length]={t:"t",v:" "},s+=2;break;case"@":n[n.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(o==null&&(o=Yt(t,r,e.charAt(s+1)==="2"),o==null))return"";n[n.length]={t:"X",v:e.substr(s,2)},c=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||o==null&&(o=Yt(t,r),o==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&c.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=x),n[n.length]={t:f,v:i},c=f;break;case"A":case"a":case"\u4E0A":var u={t:f,v:f};if(o==null&&(o=Yt(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(o!=null&&(u.v=o.H>=12?"P":"A"),u.t="T",x="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(o!=null&&(u.v=o.H>=12?"PM":"AM"),u.t="T",s+=5,x="h"):e.substr(s,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348"?(o!=null&&(u.v=o.H>=12?"\u4E0B\u5348":"\u4E0A\u5348"),u.t="T",s+=5,x="h"):(u.t="t",++s),o==null&&u.t==="T")return"";n[n.length]=u,c=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Ui)){if(o==null&&(o=Yt(t,r),o==null))return"";n[n.length]={t:"Z",v:i.toLowerCase()},c=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",ca(e)||(n[n.length]={t:"t",v:i}));break;case".":if(o!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;n[n.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;n[n.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;n[n.length]={t:f,v:i},c=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":n[n.length]={t:a===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:f,v:f},++s;break;case"$":n[n.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=\u20ACacfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"t",v:f},++s;break}var p=0,E=0,T;for(s=n.length-1,c="t";s>=0;--s)switch(n[s].t){case"h":case"H":n[s].t=x,c="h",p<1&&(p=1);break;case"s":(T=n[s].v.match(/\.0+$/))&&(E=Math.max(E,T[0].length-1)),p<3&&(p=3);case"d":case"y":case"M":case"e":c=n[s].t;break;case"m":c==="s"&&(n[s].t="M",p<2&&(p=2));break;case"X":break;case"Z":p<1&&n[s].v.match(/[Hh]/)&&(p=1),p<2&&n[s].v.match(/[Mm]/)&&(p=2),p<3&&n[s].v.match(/[Ss]/)&&(p=3)}switch(p){case 0:break;case 1:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M),o.M>=60&&(o.M=0,++o.H);break;case 2:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M);break}var g="",R;for(s=0;s<n.length;++s)switch(n[s].t){case"t":case"T":case" ":case"D":break;case"X":n[s].v="",n[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":n[s].v=io(n[s].t.charCodeAt(0),n[s].v,o,E),n[s].t="t";break;case"n":case"?":for(R=s+1;n[R]!=null&&((f=n[R].t)==="?"||f==="D"||(f===" "||f==="t")&&n[R+1]!=null&&(n[R+1].t==="?"||n[R+1].t==="t"&&n[R+1].v==="/")||n[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(n[R].v==="/"||n[R].v===" "&&n[R+1]!=null&&n[R+1].t=="?"));)n[s].v+=n[R].v,n[R]={v:"",t:";"},++R;g+=n[s].v,s=R-1;break;case"G":n[s].t="t",n[s].v=fa(t,r);break}var L="",I,F;if(g.length>0){g.charCodeAt(0)==40?(I=t<0&&g.charCodeAt(0)===45?-t:t,F=Ut("n",g,I)):(I=t<0&&a>1?-t:t,F=Ut("n",g,I),I<0&&n[0]&&n[0].t=="t"&&(F=F.substr(1),n[0].v="-"+n[0].v)),R=F.length-1;var N=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){N=s;break}var P=n.length;if(N===n.length&&F.indexOf("E")===-1){for(s=n.length-1;s>=0;--s)n[s]==null||"n?".indexOf(n[s].t)===-1||(R>=n[s].v.length-1?(R-=n[s].v.length,n[s].v=F.substr(R+1,n[s].v.length)):R<0?n[s].v="":(n[s].v=F.substr(0,R+1),R=-1),n[s].t="t",P=s);R>=0&&P<n.length&&(n[P].v=F.substr(0,R+1)+n[P].v)}else if(N!==n.length&&F.indexOf("E")===-1){for(R=F.indexOf(".")-1,s=N;s>=0;--s)if(!(n[s]==null||"n?".indexOf(n[s].t)===-1)){for(l=n[s].v.indexOf(".")>-1&&s===N?n[s].v.indexOf(".")-1:n[s].v.length-1,L=n[s].v.substr(l+1);l>=0;--l)R>=0&&(n[s].v.charAt(l)==="0"||n[s].v.charAt(l)==="#")&&(L=F.charAt(R--)+L);n[s].v=L,n[s].t="t",P=s}for(R>=0&&P<n.length&&(n[P].v=F.substr(0,R+1)+n[P].v),R=F.indexOf(".")+1,s=N;s<n.length;++s)if(!(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==N)){for(l=n[s].v.indexOf(".")>-1&&s===N?n[s].v.indexOf(".")+1:0,L=n[s].v.substr(0,l);l<n[s].v.length;++l)R<F.length&&(L+=F.charAt(R++));n[s].v=L,n[s].t="t",P=s}}}for(s=0;s<n.length;++s)n[s]!=null&&"n?".indexOf(n[s].t)>-1&&(I=a>1&&t<0&&s>0&&n[s-1].v==="-"?-t:t,n[s].v=Ut(n[s].t,n[s].v,I),n[s].t="t");var V="";for(s=0;s!==n.length;++s)n[s]!=null&&(V+=n[s].v);return V}var Wi=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Hi(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function mo(e,t){var r=vo(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break;case 4:break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[a,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(Wi),f=r[1].match(Wi);return Hi(t,s)?[a,r[0]]:Hi(t,f)?[a,r[1]]:[a,r[s!=null&&f!=null?2:1]]}return[a,i]}function ft(e,t,r){r==null&&(r={});var a="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?a=r.dateNF:a=e;break;case"number":e==14&&r.dateNF?a=r.dateNF:a=(r.table!=null?r.table:Se)[e],a==null&&(a=r.table&&r.table[Ai[e]]||Se[Ai[e]]),a==null&&(a=Qc[e]||"General");break}if(Ca(a,0))return fa(t,r);t instanceof Date&&(t=Di(t,r.date1904));var n=mo(a,t);if(Ca(n[1]))return fa(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return po(n[1],t,r,n[0])}function Dt(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Se[r]==null){t<0&&(t=r);continue}if(Se[r]==e){t=r;break}}t<0&&(t=391)}return Se[t]=e,t}function ja(e){for(var t=0;t!=392;++t)e[t]!==void 0&&Dt(e[t],t)}function Da(){Se=Jc()}var go={format:ft,load:Dt,_table:Se,load_table:ja,parse_date_code:Yt,is_date:ca,get_table:function(){return go._table=Se}},_o={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Vi=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function wo(e){var t=typeof e=="number"?Se[e]:e;return t=t.replace(Vi,"(\\d+)"),new RegExp("^"+t+"$")}function Eo(e,t,r){var a=-1,n=-1,i=-1,s=-1,f=-1,c=-1;(t.match(Vi)||[]).forEach(function(h,x){var d=parseInt(r[x+1],10);switch(h.toLowerCase().charAt(0)){case"y":a=d;break;case"d":i=d;break;case"h":s=d;break;case"s":c=d;break;case"m":s>=0?f=d:n=d;break}}),c>=0&&f==-1&&n>=0&&(f=n,n=-1);var o=(""+(a>=0?a:new Date().getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);o.length==7&&(o="0"+o),o.length==8&&(o="20"+o);var l=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return s==-1&&f==-1&&c==-1?o:a==-1&&n==-1&&i==-1?l:o+"T"+l}var To=function(){var e={};e.version="1.2.0";function t(){for(var F=0,N=new Array(256),P=0;P!=256;++P)F=P,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,N[P]=F;return typeof Int32Array!="undefined"?new Int32Array(N):N}var r=t();function a(F){var N=0,P=0,V=0,X=typeof Int32Array!="undefined"?new Int32Array(4096):new Array(4096);for(V=0;V!=256;++V)X[V]=F[V];for(V=0;V!=256;++V)for(P=F[V],N=256+V;N<4096;N+=256)P=X[N]=P>>>8^F[P&255];var b=[];for(V=1;V!=16;++V)b[V-1]=typeof Int32Array!="undefined"?X.subarray(V*256,V*256+256):X.slice(V*256,V*256+256);return b}var n=a(r),i=n[0],s=n[1],f=n[2],c=n[3],o=n[4],l=n[5],h=n[6],x=n[7],d=n[8],v=n[9],u=n[10],p=n[11],E=n[12],T=n[13],g=n[14];function R(F,N){for(var P=N^-1,V=0,X=F.length;V<X;)P=P>>>8^r[(P^F.charCodeAt(V++))&255];return~P}function L(F,N){for(var P=N^-1,V=F.length-15,X=0;X<V;)P=g[F[X++]^P&255]^T[F[X++]^P>>8&255]^E[F[X++]^P>>16&255]^p[F[X++]^P>>>24]^u[F[X++]]^v[F[X++]]^d[F[X++]]^x[F[X++]]^h[F[X++]]^l[F[X++]]^o[F[X++]]^c[F[X++]]^f[F[X++]]^s[F[X++]]^i[F[X++]]^r[F[X++]];for(V+=15;X<V;)P=P>>>8^r[(P^F[X++])&255];return~P}function I(F,N){for(var P=N^-1,V=0,X=F.length,b=0,ae=0;V<X;)b=F.charCodeAt(V++),b<128?P=P>>>8^r[(P^b)&255]:b<2048?(P=P>>>8^r[(P^(192|b>>6&31))&255],P=P>>>8^r[(P^(128|b&63))&255]):b>=55296&&b<57344?(b=(b&1023)+64,ae=F.charCodeAt(V++)&1023,P=P>>>8^r[(P^(240|b>>8&7))&255],P=P>>>8^r[(P^(128|b>>2&63))&255],P=P>>>8^r[(P^(128|ae>>6&15|(b&3)<<4))&255],P=P>>>8^r[(P^(128|ae&63))&255]):(P=P>>>8^r[(P^(224|b>>12&15))&255],P=P>>>8^r[(P^(128|b>>6&63))&255],P=P>>>8^r[(P^(128|b&63))&255]);return~P}return e.table=r,e.bstr=R,e.buf=L,e.str=I,e}(),Te=function(){var t={};t.version="1.2.1";function r(m,k){for(var _=m.split("/"),w=k.split("/"),S=0,y=0,M=Math.min(_.length,w.length);S<M;++S){if(y=_[S].length-w[S].length)return y;if(_[S]!=w[S])return _[S]<w[S]?-1:1}return _.length-w.length}function a(m){if(m.charAt(m.length-1)=="/")return m.slice(0,-1).indexOf("/")===-1?m:a(m.slice(0,-1));var k=m.lastIndexOf("/");return k===-1?m:m.slice(0,k+1)}function n(m){if(m.charAt(m.length-1)=="/")return n(m.slice(0,-1));var k=m.lastIndexOf("/");return k===-1?m:m.slice(k+1)}function i(m,k){typeof k=="string"&&(k=new Date(k));var _=k.getHours();_=_<<6|k.getMinutes(),_=_<<5|k.getSeconds()>>>1,m.write_shift(2,_);var w=k.getFullYear()-1980;w=w<<4|k.getMonth()+1,w=w<<5|k.getDate(),m.write_shift(2,w)}function s(m){var k=m.read_shift(2)&65535,_=m.read_shift(2)&65535,w=new Date,S=_&31;_>>>=5;var y=_&15;_>>>=4,w.setMilliseconds(0),w.setFullYear(_+1980),w.setMonth(y-1),w.setDate(S);var M=k&31;k>>>=5;var z=k&63;return k>>>=6,w.setHours(k),w.setMinutes(z),w.setSeconds(M<<1),w}function f(m){Xr(m,0);for(var k={},_=0;m.l<=m.length-4;){var w=m.read_shift(2),S=m.read_shift(2),y=m.l+S,M={};switch(w){case 21589:_=m.read_shift(1),_&1&&(M.mtime=m.read_shift(4)),S>5&&(_&2&&(M.atime=m.read_shift(4)),_&4&&(M.ctime=m.read_shift(4))),M.mtime&&(M.mt=new Date(M.mtime*1e3));break}m.l=y,k[w]=M}return k}var c;function o(){return c||(c={})}function l(m,k){if(m[0]==80&&m[1]==75)return $c(m,k);if((m[0]|32)==109&&(m[1]|32)==105)return Qg(m,k);if(m.length<512)throw new Error("CFB file size "+m.length+" < 512");var _=3,w=512,S=0,y=0,M=0,z=0,W=0,B=[],H=m.slice(0,512);Xr(H,0);var q=h(H);switch(_=q[0],_){case 3:w=512;break;case 4:w=4096;break;case 0:if(q[1]==0)return $c(m,k);default:throw new Error("Major Version: Expected 3 or 4 saw "+_)}w!==512&&(H=m.slice(0,w),Xr(H,28));var se=m.slice(0,w);x(H,_);var me=H.read_shift(4,"i");if(_===3&&me!==0)throw new Error("# Directory Sectors: Expected 0 saw "+me);H.l+=4,M=H.read_shift(4,"i"),H.l+=4,H.chk("00100000","Mini Stream Cutoff Size: "),z=H.read_shift(4,"i"),S=H.read_shift(4,"i"),W=H.read_shift(4,"i"),y=H.read_shift(4,"i");for(var fe=-1,oe=0;oe<109&&(fe=H.read_shift(4,"i"),!(fe<0));++oe)B[oe]=fe;var Me=d(m,w);p(W,y,Me,w,B);var mr=T(Me,M,B,w);mr[M].name="!Directory",S>0&&z!==ae&&(mr[z].name="!MiniFAT"),mr[B[0]].name="!FAT",mr.fat_addrs=B,mr.ssz=w;var it={},Pr=[],ut=[],Fn=[];g(M,mr,Me,Pr,S,it,ut,z),v(ut,Fn,Pr),Pr.shift();var yn={FileIndex:ut,FullPaths:Fn};return k&&k.raw&&(yn.raw={header:se,sectors:Me}),yn}function h(m){if(m[m.l]==80&&m[m.l+1]==75)return[0,0];m.chk(xe,"Header Signature: "),m.l+=16;var k=m.read_shift(2,"u");return[m.read_shift(2,"u"),k]}function x(m,k){var _=9;switch(m.l+=2,_=m.read_shift(2)){case 9:if(k!=3)throw new Error("Sector Shift: Expected 9 saw "+_);break;case 12:if(k!=4)throw new Error("Sector Shift: Expected 12 saw "+_);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+_)}m.chk("0600","Mini Sector Shift: "),m.chk("000000000000","Reserved: ")}function d(m,k){for(var _=Math.ceil(m.length/k)-1,w=[],S=1;S<_;++S)w[S-1]=m.slice(S*k,(S+1)*k);return w[_-1]=m.slice(_*k),w}function v(m,k,_){for(var w=0,S=0,y=0,M=0,z=0,W=_.length,B=[],H=[];w<W;++w)B[w]=H[w]=w,k[w]=_[w];for(;z<H.length;++z)w=H[z],S=m[w].L,y=m[w].R,M=m[w].C,B[w]===w&&(S!==-1&&B[S]!==S&&(B[w]=B[S]),y!==-1&&B[y]!==y&&(B[w]=B[y])),M!==-1&&(B[M]=w),S!==-1&&w!=B[w]&&(B[S]=B[w],H.lastIndexOf(S)<z&&H.push(S)),y!==-1&&w!=B[w]&&(B[y]=B[w],H.lastIndexOf(y)<z&&H.push(y));for(w=1;w<W;++w)B[w]===w&&(y!==-1&&B[y]!==y?B[w]=B[y]:S!==-1&&B[S]!==S&&(B[w]=B[S]));for(w=1;w<W;++w)if(m[w].type!==0){if(z=w,z!=B[z])do z=B[z],k[w]=k[z]+"/"+k[w];while(z!==0&&B[z]!==-1&&z!=B[z]);B[w]=-1}for(k[0]+="/",w=1;w<W;++w)m[w].type!==2&&(k[w]+="/")}function u(m,k,_){for(var w=m.start,S=m.size,y=[],M=w;_&&S>0&&M>=0;)y.push(k.slice(M*b,M*b+b)),S-=b,M=la(_,M*4);return y.length===0?G(0):or(y).slice(0,m.size)}function p(m,k,_,w,S){var y=ae;if(m===ae){if(k!==0)throw new Error("DIFAT chain shorter than expected")}else if(m!==-1){var M=_[m],z=(w>>>2)-1;if(!M)return;for(var W=0;W<z&&(y=la(M,W*4))!==ae;++W)S.push(y);p(la(M,w-4),k-1,_,w,S)}}function E(m,k,_,w,S){var y=[],M=[];S||(S=[]);var z=w-1,W=0,B=0;for(W=k;W>=0;){S[W]=!0,y[y.length]=W,M.push(m[W]);var H=_[Math.floor(W*4/w)];if(B=W*4&z,w<4+B)throw new Error("FAT boundary crossed: "+W+" 4 "+w);if(!m[H])break;W=la(m[H],B)}return{nodes:y,data:ls([M])}}function T(m,k,_,w){var S=m.length,y=[],M=[],z=[],W=[],B=w-1,H=0,q=0,se=0,me=0;for(H=0;H<S;++H)if(z=[],se=H+k,se>=S&&(se-=S),!M[se]){W=[];var fe=[];for(q=se;q>=0;){fe[q]=!0,M[q]=!0,z[z.length]=q,W.push(m[q]);var oe=_[Math.floor(q*4/w)];if(me=q*4&B,w<4+me)throw new Error("FAT boundary crossed: "+q+" 4 "+w);if(!m[oe]||(q=la(m[oe],me),fe[q]))break}y[se]={nodes:z,data:ls([W])}}return y}function g(m,k,_,w,S,y,M,z){for(var W=0,B=w.length?2:0,H=k[m].data,q=0,se=0,me;q<H.length;q+=128){var fe=H.slice(q,q+128);Xr(fe,64),se=fe.read_shift(2),me=Mn(fe,0,se-B),w.push(me);var oe={name:me,type:fe.read_shift(1),color:fe.read_shift(1),L:fe.read_shift(4,"i"),R:fe.read_shift(4,"i"),C:fe.read_shift(4,"i"),clsid:fe.read_shift(16),state:fe.read_shift(4,"i"),start:0,size:0},Me=fe.read_shift(2)+fe.read_shift(2)+fe.read_shift(2)+fe.read_shift(2);Me!==0&&(oe.ct=R(fe,fe.l-8));var mr=fe.read_shift(2)+fe.read_shift(2)+fe.read_shift(2)+fe.read_shift(2);mr!==0&&(oe.mt=R(fe,fe.l-8)),oe.start=fe.read_shift(4,"i"),oe.size=fe.read_shift(4,"i"),oe.size<0&&oe.start<0&&(oe.size=oe.type=0,oe.start=ae,oe.name=""),oe.type===5?(W=oe.start,S>0&&W!==ae&&(k[W].name="!StreamData")):oe.size>=4096?(oe.storage="fat",k[oe.start]===void 0&&(k[oe.start]=E(_,oe.start,k.fat_addrs,k.ssz)),k[oe.start].name=oe.name,oe.content=k[oe.start].data.slice(0,oe.size)):(oe.storage="minifat",oe.size<0?oe.size=0:W!==ae&&oe.start!==ae&&k[W]&&(oe.content=u(oe,k[W].data,(k[z]||{}).data))),oe.content&&Xr(oe.content,0),y[me]=oe,M.push(oe)}}function R(m,k){return new Date((Mr(m,k+4)/1e7*Math.pow(2,32)+Mr(m,k)/1e7-11644473600)*1e3)}function L(m,k){return o(),l(c.readFileSync(m),k)}function I(m,k){var _=k&&k.type;switch(_||ye&&Q.isBuffer(m)&&(_="buffer"),_||"base64"){case"file":return L(m,k);case"base64":return l(ar(hr(m)),k);case"binary":return l(ar(m),k)}return l(m,k)}function F(m,k){var _=k||{},w=_.root||"Root Entry";if(m.FullPaths||(m.FullPaths=[]),m.FileIndex||(m.FileIndex=[]),m.FullPaths.length!==m.FileIndex.length)throw new Error("inconsistent CFB structure");m.FullPaths.length===0&&(m.FullPaths[0]=w+"/",m.FileIndex[0]={name:w,type:5}),_.CLSID&&(m.FileIndex[0].clsid=_.CLSID),N(m)}function N(m){var k="Sh33tJ5";if(!Te.find(m,"/"+k)){var _=G(4);_[0]=55,_[1]=_[3]=50,_[2]=54,m.FileIndex.push({name:k,type:2,content:_,size:4,L:69,R:69,C:69}),m.FullPaths.push(m.FullPaths[0]+k),P(m)}}function P(m,k){F(m);for(var _=!1,w=!1,S=m.FullPaths.length-1;S>=0;--S){var y=m.FileIndex[S];switch(y.type){case 0:w?_=!0:(m.FileIndex.pop(),m.FullPaths.pop());break;case 1:case 2:case 5:w=!0,isNaN(y.R*y.L*y.C)&&(_=!0),y.R>-1&&y.L>-1&&y.R==y.L&&(_=!0);break;default:_=!0;break}}if(!(!_&&!k)){var M=new Date(1987,1,19),z=0,W=Object.create?Object.create(null):{},B=[];for(S=0;S<m.FullPaths.length;++S)W[m.FullPaths[S]]=!0,m.FileIndex[S].type!==0&&B.push([m.FullPaths[S],m.FileIndex[S]]);for(S=0;S<B.length;++S){var H=a(B[S][0]);w=W[H],w||(B.push([H,{name:n(H).replace("/",""),type:1,clsid:_e,ct:M,mt:M,content:null}]),W[H]=!0)}for(B.sort(function(me,fe){return r(me[0],fe[0])}),m.FullPaths=[],m.FileIndex=[],S=0;S<B.length;++S)m.FullPaths[S]=B[S][0],m.FileIndex[S]=B[S][1];for(S=0;S<B.length;++S){var q=m.FileIndex[S],se=m.FullPaths[S];if(q.name=n(se).replace("/",""),q.L=q.R=q.C=-(q.color=1),q.size=q.content?q.content.length:0,q.start=0,q.clsid=q.clsid||_e,S===0)q.C=B.length>1?1:-1,q.size=0,q.type=5;else if(se.slice(-1)=="/"){for(z=S+1;z<B.length&&a(m.FullPaths[z])!=se;++z);for(q.C=z>=B.length?-1:z,z=S+1;z<B.length&&a(m.FullPaths[z])!=a(se);++z);q.R=z>=B.length?-1:z,q.type=1}else a(m.FullPaths[S+1]||"")==a(se)&&(q.R=S+1),q.type=2}}}function V(m,k){var _=k||{};if(_.fileType=="mad")return qg(m,_);switch(P(m),_.fileType){case"zip":return Kg(m,_)}var w=function(me){for(var fe=0,oe=0,Me=0;Me<me.FileIndex.length;++Me){var mr=me.FileIndex[Me];if(mr.content){var it=mr.content.length;it>0&&(it<4096?fe+=it+63>>6:oe+=it+511>>9)}}for(var Pr=me.FullPaths.length+3>>2,ut=fe+7>>3,Fn=fe+127>>7,yn=ut+oe+Pr+Fn,Fa=yn+127>>7,Si=Fa<=109?0:Math.ceil((Fa-109)/127);yn+Fa+Si+127>>7>Fa;)Si=++Fa<=109?0:Math.ceil((Fa-109)/127);var Kt=[1,Si,Fa,Fn,Pr,oe,fe,0];return me.FileIndex[0].size=fe<<6,Kt[7]=(me.FileIndex[0].start=Kt[0]+Kt[1]+Kt[2]+Kt[3]+Kt[4]+Kt[5])+(Kt[6]+7>>3),Kt}(m),S=G(w[7]<<9),y=0,M=0;{for(y=0;y<8;++y)S.write_shift(1,ne[y]);for(y=0;y<8;++y)S.write_shift(2,0);for(S.write_shift(2,62),S.write_shift(2,3),S.write_shift(2,65534),S.write_shift(2,9),S.write_shift(2,6),y=0;y<3;++y)S.write_shift(2,0);for(S.write_shift(4,0),S.write_shift(4,w[2]),S.write_shift(4,w[0]+w[1]+w[2]+w[3]-1),S.write_shift(4,0),S.write_shift(4,4096),S.write_shift(4,w[3]?w[0]+w[1]+w[2]-1:ae),S.write_shift(4,w[3]),S.write_shift(-4,w[1]?w[0]-1:ae),S.write_shift(4,w[1]),y=0;y<109;++y)S.write_shift(-4,y<w[2]?w[1]+y:-1)}if(w[1])for(M=0;M<w[1];++M){for(;y<236+M*127;++y)S.write_shift(-4,y<w[2]?w[1]+y:-1);S.write_shift(-4,M===w[1]-1?ae:M+1)}var z=function(me){for(M+=me;y<M-1;++y)S.write_shift(-4,y+1);me&&(++y,S.write_shift(-4,ae))};for(M=y=0,M+=w[1];y<M;++y)S.write_shift(-4,pe.DIFSECT);for(M+=w[2];y<M;++y)S.write_shift(-4,pe.FATSECT);z(w[3]),z(w[4]);for(var W=0,B=0,H=m.FileIndex[0];W<m.FileIndex.length;++W)H=m.FileIndex[W],H.content&&(B=H.content.length,!(B<4096)&&(H.start=M,z(B+511>>9)));for(z(w[6]+7>>3);S.l&511;)S.write_shift(-4,pe.ENDOFCHAIN);for(M=y=0,W=0;W<m.FileIndex.length;++W)H=m.FileIndex[W],H.content&&(B=H.content.length,!(!B||B>=4096)&&(H.start=M,z(B+63>>6)));for(;S.l&511;)S.write_shift(-4,pe.ENDOFCHAIN);for(y=0;y<w[4]<<2;++y){var q=m.FullPaths[y];if(!q||q.length===0){for(W=0;W<17;++W)S.write_shift(4,0);for(W=0;W<3;++W)S.write_shift(4,-1);for(W=0;W<12;++W)S.write_shift(4,0);continue}H=m.FileIndex[y],y===0&&(H.start=H.size?H.start-1:ae);var se=y===0&&_.root||H.name;if(B=2*(se.length+1),S.write_shift(64,se,"utf16le"),S.write_shift(2,B),S.write_shift(1,H.type),S.write_shift(1,H.color),S.write_shift(-4,H.L),S.write_shift(-4,H.R),S.write_shift(-4,H.C),H.clsid)S.write_shift(16,H.clsid,"hex");else for(W=0;W<4;++W)S.write_shift(4,0);S.write_shift(4,H.state||0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,0),S.write_shift(4,H.start),S.write_shift(4,H.size),S.write_shift(4,0)}for(y=1;y<m.FileIndex.length;++y)if(H=m.FileIndex[y],H.size>=4096)if(S.l=H.start+1<<9,ye&&Q.isBuffer(H.content))H.content.copy(S,S.l,0,H.size),S.l+=H.size+511&-512;else{for(W=0;W<H.size;++W)S.write_shift(1,H.content[W]);for(;W&511;++W)S.write_shift(1,0)}for(y=1;y<m.FileIndex.length;++y)if(H=m.FileIndex[y],H.size>0&&H.size<4096)if(ye&&Q.isBuffer(H.content))H.content.copy(S,S.l,0,H.size),S.l+=H.size+63&-64;else{for(W=0;W<H.size;++W)S.write_shift(1,H.content[W]);for(;W&63;++W)S.write_shift(1,0)}if(ye)S.l=S.length;else for(;S.l<S.length;)S.write_shift(1,0);return S}function X(m,k){var _=m.FullPaths.map(function(W){return W.toUpperCase()}),w=_.map(function(W){var B=W.split("/");return B[B.length-(W.slice(-1)=="/"?2:1)]}),S=!1;k.charCodeAt(0)===47?(S=!0,k=_[0].slice(0,-1)+k):S=k.indexOf("/")!==-1;var y=k.toUpperCase(),M=S===!0?_.indexOf(y):w.indexOf(y);if(M!==-1)return m.FileIndex[M];var z=!y.match(Bt);for(y=y.replace(Lr,""),z&&(y=y.replace(Bt,"!")),M=0;M<_.length;++M)if((z?_[M].replace(Bt,"!"):_[M]).replace(Lr,"")==y||(z?w[M].replace(Bt,"!"):w[M]).replace(Lr,"")==y)return m.FileIndex[M];return null}var b=64,ae=-2,xe="d0cf11e0a1b11ae1",ne=[208,207,17,224,161,177,26,225],_e="00000000000000000000000000000000",pe={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:ae,FREESECT:-1,HEADER_SIGNATURE:xe,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:_e,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ze(m,k,_){o();var w=V(m,_);c.writeFileSync(k,w)}function le(m){for(var k=new Array(m.length),_=0;_<m.length;++_)k[_]=String.fromCharCode(m[_]);return k.join("")}function ge(m,k){var _=V(m,k);switch(k&&k.type||"buffer"){case"file":return o(),c.writeFileSync(k.filename,_),_;case"binary":return typeof _=="string"?_:le(_);case"base64":return Hr(typeof _=="string"?_:le(_));case"buffer":if(ye)return Q.isBuffer(_)?_:gr(_);case"array":return typeof _=="string"?ar(_):_}return _}var j;function C(m){try{var k=m.InflateRaw,_=new k;if(_._processChunk(new Uint8Array([3,0]),_._finishFlushFlag),_.bytesRead)j=m;else throw new Error("zlib does not expose bytesRead")}catch(w){console.error("cannot use native zlib: "+(w.message||w))}}function U(m,k){if(!j)return Kc(m,k);var _=j.InflateRaw,w=new _,S=w._processChunk(m.slice(m.l),w._finishFlushFlag);return m.l+=w.bytesRead,S}function O(m){return j?j.deflateRawSync(m):Ke(m)}var D=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],K=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],ce=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ee(m){var k=(m<<1|m<<11)&139536|(m<<5|m<<15)&558144;return(k>>16|k>>8|k)&255}for(var re=typeof Uint8Array!="undefined",Y=re?new Uint8Array(256):[],He=0;He<256;++He)Y[He]=ee(He);function A(m,k){var _=Y[m&255];return k<=8?_>>>8-k:(_=_<<8|Y[m>>8&255],k<=16?_>>>16-k:(_=_<<8|Y[m>>16&255],_>>>24-k))}function fr(m,k){var _=k&7,w=k>>>3;return(m[w]|(_<=6?0:m[w+1]<<8))>>>_&3}function $e(m,k){var _=k&7,w=k>>>3;return(m[w]|(_<=5?0:m[w+1]<<8))>>>_&7}function cr(m,k){var _=k&7,w=k>>>3;return(m[w]|(_<=4?0:m[w+1]<<8))>>>_&15}function Ye(m,k){var _=k&7,w=k>>>3;return(m[w]|(_<=3?0:m[w+1]<<8))>>>_&31}function ue(m,k){var _=k&7,w=k>>>3;return(m[w]|(_<=1?0:m[w+1]<<8))>>>_&127}function yr(m,k,_){var w=k&7,S=k>>>3,y=(1<<_)-1,M=m[S]>>>w;return _<8-w||(M|=m[S+1]<<8-w,_<16-w)||(M|=m[S+2]<<16-w,_<24-w)||(M|=m[S+3]<<24-w),M&y}function Et(m,k,_){var w=k&7,S=k>>>3;return w<=5?m[S]|=(_&7)<<w:(m[S]|=_<<w&255,m[S+1]=(_&7)>>8-w),k+3}function Lt(m,k,_){var w=k&7,S=k>>>3;return _=(_&1)<<w,m[S]|=_,k+1}function Gt(m,k,_){var w=k&7,S=k>>>3;return _<<=w,m[S]|=_&255,_>>>=8,m[S+1]=_,k+8}function kn(m,k,_){var w=k&7,S=k>>>3;return _<<=w,m[S]|=_&255,_>>>=8,m[S+1]=_&255,m[S+2]=_>>>8,k+16}function ia(m,k){var _=m.length,w=2*_>k?2*_:k+5,S=0;if(_>=k)return m;if(ye){var y=st(w);if(m.copy)m.copy(y);else for(;S<m.length;++S)y[S]=m[S];return y}else if(re){var M=new Uint8Array(w);if(M.set)M.set(m);else for(;S<_;++S)M[S]=m[S];return M}return m.length=w,m}function lt(m){for(var k=new Array(m),_=0;_<m;++_)k[_]=0;return k}function zt(m,k,_){var w=1,S=0,y=0,M=0,z=0,W=m.length,B=re?new Uint16Array(32):lt(32);for(y=0;y<32;++y)B[y]=0;for(y=W;y<_;++y)m[y]=0;W=m.length;var H=re?new Uint16Array(W):lt(W);for(y=0;y<W;++y)B[S=m[y]]++,w<S&&(w=S),H[y]=0;for(B[0]=0,y=1;y<=w;++y)B[y+16]=z=z+B[y-1]<<1;for(y=0;y<W;++y)z=m[y],z!=0&&(H[y]=B[z+16]++);var q=0;for(y=0;y<W;++y)if(q=m[y],q!=0)for(z=A(H[y],w)>>w-q,M=(1<<w+4-q)-1;M>=0;--M)k[z|M<<q]=q&15|y<<4;return w}var sa=re?new Uint16Array(512):lt(512),Sn=re?new Uint16Array(32):lt(32);if(!re){for(var tt=0;tt<512;++tt)sa[tt]=0;for(tt=0;tt<32;++tt)Sn[tt]=0}(function(){for(var m=[],k=0;k<32;k++)m.push(5);zt(m,Sn,32);var _=[];for(k=0;k<=143;k++)_.push(8);for(;k<=255;k++)_.push(9);for(;k<=279;k++)_.push(7);for(;k<=287;k++)_.push(8);zt(_,sa,288)})();var Mt=function(){for(var k=re?new Uint8Array(32768):[],_=0,w=0;_<ce.length-1;++_)for(;w<ce[_+1];++w)k[w]=_;for(;w<32768;++w)k[w]=29;var S=re?new Uint8Array(259):[];for(_=0,w=0;_<K.length-1;++_)for(;w<K[_+1];++w)S[w]=_;function y(z,W){for(var B=0;B<z.length;){var H=Math.min(65535,z.length-B),q=B+H==z.length;for(W.write_shift(1,+q),W.write_shift(2,H),W.write_shift(2,~H&65535);H-- >0;)W[W.l++]=z[B++]}return W.l}function M(z,W){for(var B=0,H=0,q=re?new Uint16Array(32768):[];H<z.length;){var se=Math.min(65535,z.length-H);if(se<10){for(B=Et(W,B,+(H+se==z.length)),B&7&&(B+=8-(B&7)),W.l=B/8|0,W.write_shift(2,se),W.write_shift(2,~se&65535);se-- >0;)W[W.l++]=z[H++];B=W.l*8;continue}B=Et(W,B,+(H+se==z.length)+2);for(var me=0;se-- >0;){var fe=z[H];me=(me<<5^fe)&32767;var oe=-1,Me=0;if((oe=q[me])&&(oe|=H&-32768,oe>H&&(oe-=32768),oe<H))for(;z[oe+Me]==z[H+Me]&&Me<250;)++Me;if(Me>2){fe=S[Me],fe<=22?B=Gt(W,B,Y[fe+1]>>1)-1:(Gt(W,B,3),B+=5,Gt(W,B,Y[fe-23]>>5),B+=3);var mr=fe<8?0:fe-4>>2;mr>0&&(kn(W,B,Me-K[fe]),B+=mr),fe=k[H-oe],B=Gt(W,B,Y[fe]>>3),B-=3;var it=fe<4?0:fe-2>>1;it>0&&(kn(W,B,H-oe-ce[fe]),B+=it);for(var Pr=0;Pr<Me;++Pr)q[me]=H&32767,me=(me<<5^z[H])&32767,++H;se-=Me-1}else fe<=143?fe=fe+48:B=Lt(W,B,1),B=Gt(W,B,Y[fe]),q[me]=H&32767,++H}B=Gt(W,B,0)-1}return W.l=(B+7)/8|0,W.l}return function(W,B){return W.length<8?y(W,B):M(W,B)}}();function Ke(m){var k=G(50+Math.floor(m.length*1.1)),_=Mt(m,k);return k.slice(0,_)}var Ar=re?new Uint16Array(32768):lt(32768),Tt=re?new Uint16Array(32768):lt(32768),Nr=re?new Uint16Array(128):lt(128),Sa=1,zc=1;function Xg(m,k){var _=Ye(m,k)+257;k+=5;var w=Ye(m,k)+1;k+=5;var S=cr(m,k)+4;k+=4;for(var y=0,M=re?new Uint8Array(19):lt(19),z=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],W=1,B=re?new Uint8Array(8):lt(8),H=re?new Uint8Array(8):lt(8),q=M.length,se=0;se<S;++se)M[D[se]]=y=$e(m,k),W<y&&(W=y),B[y]++,k+=3;var me=0;for(B[0]=0,se=1;se<=W;++se)H[se]=me=me+B[se-1]<<1;for(se=0;se<q;++se)(me=M[se])!=0&&(z[se]=H[me]++);var fe=0;for(se=0;se<q;++se)if(fe=M[se],fe!=0){me=Y[z[se]]>>8-fe;for(var oe=(1<<7-fe)-1;oe>=0;--oe)Nr[me|oe<<fe]=fe&7|se<<3}var Me=[];for(W=1;Me.length<_+w;)switch(me=Nr[ue(m,k)],k+=me&7,me>>>=3){case 16:for(y=3+fr(m,k),k+=2,me=Me[Me.length-1];y-- >0;)Me.push(me);break;case 17:for(y=3+$e(m,k),k+=3;y-- >0;)Me.push(0);break;case 18:for(y=11+ue(m,k),k+=7;y-- >0;)Me.push(0);break;default:Me.push(me),W<me&&(W=me);break}var mr=Me.slice(0,_),it=Me.slice(_);for(se=_;se<286;++se)mr[se]=0;for(se=w;se<30;++se)it[se]=0;return Sa=zt(mr,Ar,286),zc=zt(it,Tt,30),k}function Gg(m,k){if(m[0]==3&&!(m[1]&3))return[br(k),2];for(var _=0,w=0,S=st(k||1<<18),y=0,M=S.length>>>0,z=0,W=0;!(w&1);){if(w=$e(m,_),_+=3,w>>>1)w>>1==1?(z=9,W=5):(_=Xg(m,_),z=Sa,W=zc);else{_&7&&(_+=8-(_&7));var B=m[_>>>3]|m[(_>>>3)+1]<<8;if(_+=32,B>0)for(!k&&M<y+B&&(S=ia(S,y+B),M=S.length);B-- >0;)S[y++]=m[_>>>3],_+=8;continue}for(;;){!k&&M<y+32767&&(S=ia(S,y+32767),M=S.length);var H=yr(m,_,z),q=w>>>1==1?sa[H]:Ar[H];if(_+=q&15,q>>>=4,!(q>>>8&255))S[y++]=q;else{if(q==256)break;q-=257;var se=q<8?0:q-4>>2;se>5&&(se=0);var me=y+K[q];se>0&&(me+=yr(m,_,se),_+=se),H=yr(m,_,W),q=w>>>1==1?Sn[H]:Tt[H],_+=q&15,q>>>=4;var fe=q<4?0:q-2>>1,oe=ce[q];for(fe>0&&(oe+=yr(m,_,fe),_+=fe),!k&&M<me&&(S=ia(S,me+100),M=S.length);y<me;)S[y]=S[y-oe],++y}}}return k?[S,_+7>>>3]:[S.slice(0,y),_+7>>>3]}function Kc(m,k){var _=m.slice(m.l||0),w=Gg(_,k);return m.l+=w[1],w[0]}function jc(m,k){if(m)typeof console!="undefined"&&console.error(k);else throw new Error(k)}function $c(m,k){var _=m;Xr(_,0);var w=[],S=[],y={FileIndex:w,FullPaths:S};F(y,{root:k.root});for(var M=_.length-4;(_[M]!=80||_[M+1]!=75||_[M+2]!=5||_[M+3]!=6)&&M>=0;)--M;_.l=M+4,_.l+=4;var z=_.read_shift(2);_.l+=6;var W=_.read_shift(4);for(_.l=W,M=0;M<z;++M){_.l+=20;var B=_.read_shift(4),H=_.read_shift(4),q=_.read_shift(2),se=_.read_shift(2),me=_.read_shift(2);_.l+=8;var fe=_.read_shift(4),oe=f(_.slice(_.l+q,_.l+q+se));_.l+=q+se+me;var Me=_.l;_.l=fe+4,zg(_,B,H,y,oe),_.l=Me}return y}function zg(m,k,_,w,S){m.l+=2;var y=m.read_shift(2),M=m.read_shift(2),z=s(m);if(y&8257)throw new Error("Unsupported ZIP encryption");for(var W=m.read_shift(4),B=m.read_shift(4),H=m.read_shift(4),q=m.read_shift(2),se=m.read_shift(2),me="",fe=0;fe<q;++fe)me+=String.fromCharCode(m[m.l++]);if(se){var oe=f(m.slice(m.l,m.l+se));(oe[21589]||{}).mt&&(z=oe[21589].mt),((S||{})[21589]||{}).mt&&(z=S[21589].mt)}m.l+=se;var Me=m.slice(m.l,m.l+B);switch(M){case 8:Me=U(m,H);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+M)}var mr=!1;y&8&&(W=m.read_shift(4),W==134695760&&(W=m.read_shift(4),mr=!0),B=m.read_shift(4),H=m.read_shift(4)),B!=k&&jc(mr,"Bad compressed size: "+k+" != "+B),H!=_&&jc(mr,"Bad uncompressed size: "+_+" != "+H),ki(w,me,Me,{unsafe:!0,mt:z})}function Kg(m,k){var _=k||{},w=[],S=[],y=G(1),M=_.compression?8:0,z=0,W=!1;W&&(z|=8);var B=0,H=0,q=0,se=0,me=m.FullPaths[0],fe=me,oe=m.FileIndex[0],Me=[],mr=0;for(B=1;B<m.FullPaths.length;++B)if(fe=m.FullPaths[B].slice(me.length),oe=m.FileIndex[B],!(!oe.size||!oe.content||fe=="Sh33tJ5")){var it=q,Pr=G(fe.length);for(H=0;H<fe.length;++H)Pr.write_shift(1,fe.charCodeAt(H)&127);Pr=Pr.slice(0,Pr.l),Me[se]=To.buf(oe.content,0);var ut=oe.content;M==8&&(ut=O(ut)),y=G(30),y.write_shift(4,67324752),y.write_shift(2,20),y.write_shift(2,z),y.write_shift(2,M),oe.mt?i(y,oe.mt):y.write_shift(4,0),y.write_shift(-4,z&8?0:Me[se]),y.write_shift(4,z&8?0:ut.length),y.write_shift(4,z&8?0:oe.content.length),y.write_shift(2,Pr.length),y.write_shift(2,0),q+=y.length,w.push(y),q+=Pr.length,w.push(Pr),q+=ut.length,w.push(ut),z&8&&(y=G(12),y.write_shift(-4,Me[se]),y.write_shift(4,ut.length),y.write_shift(4,oe.content.length),q+=y.l,w.push(y)),y=G(46),y.write_shift(4,33639248),y.write_shift(2,0),y.write_shift(2,20),y.write_shift(2,z),y.write_shift(2,M),y.write_shift(4,0),y.write_shift(-4,Me[se]),y.write_shift(4,ut.length),y.write_shift(4,oe.content.length),y.write_shift(2,Pr.length),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(4,0),y.write_shift(4,it),mr+=y.l,S.push(y),mr+=Pr.length,S.push(Pr),++se}return y=G(22),y.write_shift(4,101010256),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,se),y.write_shift(2,se),y.write_shift(4,mr),y.write_shift(4,q),y.write_shift(2,0),or([or(w),or(S),y])}var c0={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function jg(m,k){if(m.ctype)return m.ctype;var _=m.name||"",w=_.match(/\.([^\.]+)$/);return w&&c0[w[1]]||k&&(w=(_=k).match(/[\.\\]([^\.\\])+$/),w&&c0[w[1]])?c0[w[1]]:"application/octet-stream"}function $g(m){for(var k=Hr(m),_=[],w=0;w<k.length;w+=76)_.push(k.slice(w,w+76));return _.join(`\r
`)+`\r
`}function Yg(m){var k=m.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(B){var H=B.charCodeAt(0).toString(16).toUpperCase();return"="+(H.length==1?"0"+H:H)});k=k.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),k.charAt(0)==`
`&&(k="=0D"+k.slice(1)),k=k.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var _=[],w=k.split(`\r
`),S=0;S<w.length;++S){var y=w[S];if(y.length==0){_.push("");continue}for(var M=0;M<y.length;){var z=76,W=y.slice(M,M+z);W.charAt(z-1)=="="?z--:W.charAt(z-2)=="="?z-=2:W.charAt(z-3)=="="&&(z-=3),W=y.slice(M,M+z),M+=z,M<y.length&&(W+="="),_.push(W)}}return _.join(`\r
`)}function Zg(m){for(var k=[],_=0;_<m.length;++_){for(var w=m[_];_<=m.length&&w.charAt(w.length-1)=="=";)w=w.slice(0,w.length-1)+m[++_];k.push(w)}for(var S=0;S<k.length;++S)k[S]=k[S].replace(/[=][0-9A-Fa-f]{2}/g,function(y){return String.fromCharCode(parseInt(y.slice(1),16))});return ar(k.join(`\r
`))}function Jg(m,k,_){for(var w="",S="",y="",M,z=0;z<10;++z){var W=k[z];if(!W||W.match(/^\s*$/))break;var B=W.match(/^(.*?):\s*([^\s].*)$/);if(B)switch(B[1].toLowerCase()){case"content-location":w=B[2].trim();break;case"content-type":y=B[2].trim();break;case"content-transfer-encoding":S=B[2].trim();break}}switch(++z,S.toLowerCase()){case"base64":M=ar(hr(k.slice(z).join("")));break;case"quoted-printable":M=Zg(k.slice(z));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+S)}var H=ki(m,w.slice(_.length),M,{unsafe:!0});y&&(H.ctype=y)}function Qg(m,k){if(le(m.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var _=k&&k.root||"",w=(ye&&Q.isBuffer(m)?m.toString("binary"):le(m)).split(`\r
`),S=0,y="";for(S=0;S<w.length;++S)if(y=w[S],!!/^Content-Location:/i.test(y)&&(y=y.slice(y.indexOf("file")),_||(_=y.slice(0,y.lastIndexOf("/")+1)),y.slice(0,_.length)!=_))for(;_.length>0&&(_=_.slice(0,_.length-1),_=_.slice(0,_.lastIndexOf("/")+1),y.slice(0,_.length)!=_););var M=(w[1]||"").match(/boundary="(.*?)"/);if(!M)throw new Error("MAD cannot find boundary");var z="--"+(M[1]||""),W=[],B=[],H={FileIndex:W,FullPaths:B};F(H);var q,se=0;for(S=0;S<w.length;++S){var me=w[S];me!==z&&me!==z+"--"||(se++&&Jg(H,w.slice(q,S),_),q=S)}return H}function qg(m,k){var _=k||{},w=_.boundary||"SheetJS";w="------="+w;for(var S=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+w.slice(2)+'"',"","",""],y=m.FullPaths[0],M=y,z=m.FileIndex[0],W=1;W<m.FullPaths.length;++W)if(M=m.FullPaths[W].slice(y.length),z=m.FileIndex[W],!(!z.size||!z.content||M=="Sh33tJ5")){M=M.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(Me){return"_x"+Me.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(Me){return"_u"+Me.charCodeAt(0).toString(16)+"_"});for(var B=z.content,H=ye&&Q.isBuffer(B)?B.toString("binary"):le(B),q=0,se=Math.min(1024,H.length),me=0,fe=0;fe<=se;++fe)(me=H.charCodeAt(fe))>=32&&me<128&&++q;var oe=q>=se*4/5;S.push(w),S.push("Content-Location: "+(_.root||"file:///C:/SheetJS/")+M),S.push("Content-Transfer-Encoding: "+(oe?"quoted-printable":"base64")),S.push("Content-Type: "+jg(z,M)),S.push(""),S.push(oe?Yg(H):$g(H))}return S.push(w+`--\r
`),S.join(`\r
`)}function e_(m){var k={};return F(k,m),k}function ki(m,k,_,w){var S=w&&w.unsafe;S||F(m);var y=!S&&Te.find(m,k);if(!y){var M=m.FullPaths[0];k.slice(0,M.length)==M?M=k:(M.slice(-1)!="/"&&(M+="/"),M=(M+k).replace("//","/")),y={name:n(k),type:2},m.FileIndex.push(y),m.FullPaths.push(M),S||Te.utils.cfb_gc(m)}return y.content=_,y.size=_?_.length:0,w&&(w.CLSID&&(y.clsid=w.CLSID),w.mt&&(y.mt=w.mt),w.ct&&(y.ct=w.ct)),y}function r_(m,k){F(m);var _=Te.find(m,k);if(_){for(var w=0;w<m.FileIndex.length;++w)if(m.FileIndex[w]==_)return m.FileIndex.splice(w,1),m.FullPaths.splice(w,1),!0}return!1}function t_(m,k,_){F(m);var w=Te.find(m,k);if(w){for(var S=0;S<m.FileIndex.length;++S)if(m.FileIndex[S]==w)return m.FileIndex[S].name=n(_),m.FullPaths[S]=_,!0}return!1}function a_(m){P(m,!0)}return t.find=X,t.read=I,t.parse=l,t.write=ge,t.writeFile=ze,t.utils={cfb_new:e_,cfb_add:ki,cfb_del:r_,cfb_mov:t_,cfb_gc:a_,ReadShift:qa,CheckField:Ts,prep_blob:Xr,bconcat:or,use_zlib:C,_deflateRaw:Ke,_inflateRaw:Kc,consts:pe},t}();let Jt;function s_(e){Jt=e}function ko(e){return typeof e=="string"?$t(e):Array.isArray(e)?Xa(e):e}function $a(e,t,r){if(typeof Jt!="undefined"&&Jt.writeFileSync)return r?Jt.writeFileSync(e,t,r):Jt.writeFileSync(e,t);if(typeof Deno!="undefined"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=$t(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a=r=="utf8"?It(t):t;if(typeof IE_SaveFile!="undefined")return IE_SaveFile(a,e);if(typeof Blob!="undefined"){var n=new Blob([ko(a)],{type:"application/octet-stream"});if(typeof navigator!="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs!="undefined")return saveAs(n,e);if(typeof URL!="undefined"&&typeof document!="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $!="undefined"&&typeof File!="undefined"&&typeof Folder!="undefined")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=At(t)),f.write(t),f.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function So(e){if(typeof Jt!="undefined")return Jt.readFileSync(e);if(typeof Deno!="undefined")return Deno.readFileSync(e);if(typeof $!="undefined"&&typeof File!="undefined"&&typeof Folder!="undefined")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function wr(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function Xi(e,t){for(var r=[],a=wr(e),n=0;n!==a.length;++n)r[e[a[n]][t]]==null&&(r[e[a[n]][t]]=a[n]);return r}function Pn(e){for(var t=[],r=wr(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function Rn(e){for(var t=[],r=wr(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}function Fo(e){for(var t=[],r=wr(e),a=0;a!==r.length;++a)t[e[r[a]]]==null&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}var Nn=new Date(1899,11,30,0,0,0);function Rr(e,t){var r=e.getTime();t&&(r-=1462*24*60*60*1e3);var a=Nn.getTime()+(e.getTimezoneOffset()-Nn.getTimezoneOffset())*6e4;return(r-a)/(24*60*60*1e3)}var Gi=new Date,yo=Nn.getTime()+(Gi.getTimezoneOffset()-Nn.getTimezoneOffset())*6e4,zi=Gi.getTimezoneOffset();function bn(e){var t=new Date;return t.setTime(e*24*60*60*1e3+yo),t.getTimezoneOffset()!==zi&&t.setTime(t.getTime()+(t.getTimezoneOffset()-zi)*6e4),t}function Ao(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i)if(n[i]){switch(r=1,i>3&&(a=!0),n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw new Error("Unsupported ISO Duration Field: M");case"S":break}t+=r*parseInt(n[i],10)}return t}var Ki=new Date("2017-02-19T19:06:09.000Z"),ji=isNaN(Ki.getFullYear())?new Date("2/19/17"):Ki,Co=ji.getFullYear()==2017;function dr(e,t){var r=new Date(e);if(Co)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(ji.getFullYear()==1917&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function oa(e,t){if(ye&&Q.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return It(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return It(Ae(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder!="undefined")try{if(t){if(e[0]==255&&e[1]==254)return It(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return It(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20AC":"\x80","\u201A":"\x82",\u0192:"\x83","\u201E":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02C6":"\x88","\u2030":"\x89",\u0160:"\x8A","\u2039":"\x8B",\u0152:"\x8C",\u017D:"\x8E","\u2018":"\x91","\u2019":"\x92","\u201C":"\x93","\u201D":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02DC":"\x98","\u2122":"\x99",\u0161:"\x9A","\u203A":"\x9B",\u0153:"\x9C",\u017E:"\x9E",\u0178:"\x9F"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch(i){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function ur(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ur(e[r]));return t}function Er(e,t){for(var r="";r.length<t;)r+=e;return r}function kt(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(a))||(a=a.replace(/[(](.*)[)]/,function(n,i){return r=-r,i}),!isNaN(t=Number(a)))?t/r:t}var Do=["january","february","march","april","may","june","july","august","september","october","november","december"];function Oa(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&Do.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||i>1)&&a!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var Oo=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,a,n){if(e||typeof a=="string")return r.split(a);for(var i=r.split(a),s=[i[0]],f=1;f<i.length;++f)s.push(n),s.push(i[f]);return s}}();function $i(e){return e?e.content&&e.type?oa(e.content,!0):e.data?Le(e.data):e.asNodeBuffer&&ye?Le(e.asNodeBuffer().toString("binary")):e.asBinary?Le(e.asBinary()):e._data&&e._data.getContent?Le(oa(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Yi(e){if(!e)return null;if(e.data)return we(e.data);if(e.asNodeBuffer&&ye)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?we(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Io(e){return e&&e.name.slice(-4)===".bin"?Yi(e):$i(e)}function dt(e,t){for(var r=e.FullPaths||wr(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),i=0;i<r.length;++i){var s=r[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[r[i]]:e.FileIndex[i]}return null}function x0(e,t){var r=dt(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function Cr(e,t,r){if(!r)return Io(x0(e,t));if(!t)return null;try{return Cr(e,t)}catch(a){return null}}function ct(e,t,r){if(!r)return $i(x0(e,t));if(!t)return null;try{return ct(e,t)}catch(a){return null}}function Zi(e,t,r){if(!r)return Yi(x0(e,t));if(!t)return null;try{return Zi(e,t)}catch(a){return null}}function Ji(e){for(var t=e.FullPaths||wr(e.files),r=[],a=0;a<t.length;++a)t[a].slice(-1)!="/"&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function Xe(e,t,r){if(e.FullPaths){if(typeof r=="string"){var a;return ye?a=gr(r):a=An(r),Te.utils.cfb_add(e,t,a)}Te.utils.cfb_add(e,t,r)}else e.file(t,r)}function d0(){return Te.utils.cfb_new()}function Qi(e,t){switch(t.type){case"base64":return Te.read(e,{type:"base64"});case"binary":return Te.read(e,{type:"binary"});case"buffer":case"array":return Te.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function Ya(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var a=e.split("/");a.length!==0;){var n=a.shift();n===".."?r.pop():n!=="."&&r.push(n)}return r.join("/")}var Tr=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Po=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,qi=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,Ro=/<[^>]*>/g,Gr=Tr.match(qi)?qi:Ro,No=/<\w*:/,bo=/<(\/?)\w+:/;function Pe(e,t,r){for(var a={},n=0,i=0;n!==e.length&&!((i=e.charCodeAt(n))===32||i===10||i===13);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var s=e.match(Po),f=0,c="",o=0,l="",h="",x=1;if(s)for(o=0;o!=s.length;++o){for(h=s[o],i=0;i!=h.length&&h.charCodeAt(i)!==61;++i);for(l=h.slice(0,i).trim();h.charCodeAt(i+1)==32;)++i;for(x=(n=h.charCodeAt(i+1))==34||n==39?1:0,c=h.slice(i+1+x,h.length-x),f=0;f!=l.length&&l.charCodeAt(f)!==58;++f);if(f===l.length)l.indexOf("_")>0&&(l=l.slice(0,l.indexOf("_"))),a[l]=c,r||(a[l.toLowerCase()]=c);else{var d=(f===5&&l.slice(0,5)==="xmlns"?"xmlns":"")+l.slice(f+1);if(a[d]&&l.slice(f-3,f)=="ext")continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function Ot(e){return e.replace(bo,"<$1")}var es={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},v0=Pn(es),qe=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(f,c){return es[f]||String.fromCharCode(parseInt(c,f.indexOf("x")>-1?16:10))||f}).replace(t,function(f,c){return String.fromCharCode(parseInt(c,16))});var s=n.indexOf("]]>");return r(n.slice(0,i))+n.slice(i+9,s)+r(n.slice(s+3))}}(),p0=/[&<>'"]/g,Lo=/[\u0000-\u0008\u000b-\u001f]/g;function sr(e){var t=e+"";return t.replace(p0,function(r){return v0[r]}).replace(Lo,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function rs(e){return sr(e).replace(/ /g,"_x0020_")}var ts=/[\u0000-\u001f]/g;function m0(e){var t=e+"";return t.replace(p0,function(r){return v0[r]}).replace(/\n/g,"<br/>").replace(ts,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function Mo(e){var t=e+"";return t.replace(p0,function(r){return v0[r]}).replace(ts,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}var as=function(){var e=/&#(\d+);/g;function t(r,a){return String.fromCharCode(parseInt(a,10))}return function(a){return a.replace(e,t)}}();function Bo(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function vr(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function g0(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0;r<e.length;){if(a=e.charCodeAt(r++),a<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){s=(a&31)<<6,s|=n&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),a<240){t+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function ns(e){var t=br(2*e.length),r,a,n=1,i=0,s=0,f;for(a=0;a<e.length;a+=n)n=1,(f=e.charCodeAt(a))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(a+1)&63),n=2):f<240?(r=(f&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63),n=3):(n=4,r=(f&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function is(e){return gr(e,"binary").toString("utf8")}var Ln="foo bar baz\xE2\x98\x83\xF0\x9F\x8D\xA3",lr=ye&&(is(Ln)==g0(Ln)&&is||ns(Ln)==g0(Ln)&&ns)||g0,It=ye?function(e){return gr(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(a&63)));break;case(a>=55296&&a<57344):a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)))}return t.join("")},Za=function(){var e={};return function(r,a){var n=r+"|"+(a||"");return e[n]?e[n]:e[n]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",a||"")}}(),ss=function(){var e=[["nbsp"," "],["middot","\xB7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var a=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}(),Uo=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),Wo=/<\/?(?:vt:)?variant>/g,Ho=/<(?:vt:)([^>]*)>([\s\S]*)</;function fs(e,t){var r=Pe(e),a=e.match(Uo(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(i){var s=i.replace(Wo,"").match(Ho);s&&n.push({v:lr(s[2]),t:s[1]})}),n}var cs=/(^\s|\s$|\n)/;function Vr(e,t){return"<"+e+(t.match(cs)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Ja(e){return wr(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function te(e,t,r){return"<"+e+(r!=null?Ja(r):"")+(t!=null?(t.match(cs)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function _0(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Vo(e,t){switch(typeof e){case"string":var r=te("vt:lpwstr",sr(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return te((e|0)==e?"vt:i4":"vt:r8",sr(String(e)));case"boolean":return te("vt:bool",e?"true":"false")}if(e instanceof Date)return te("vt:filetime",_0(e));throw new Error("Unable to serialize "+e)}function w0(e){if(ye&&Q.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array!="undefined"&&e instanceof Uint8Array)return lr(At(Ga(e)));throw new Error("Bad input format: expected Buffer or string")}var Qa=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,Dr={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Qt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],ot={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Xo(e,t){for(var r=1-2*(e[t+7]>>>7),a=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),n=e[t+6]&15,i=5;i>=0;--i)n=n*256+e[t+i];return a==2047?n==0?r*(1/0):NaN:(a==0?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}function Go(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,i=0,s=a?-t:t;isFinite(s)?s==0?n=i=0:(n=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-n),n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?n=-1022:(i-=Math.pow(2,52),n+=1023)):(n=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(n&15)<<4|i&15,e[r+7]=n>>4|a}var os=function(e){for(var t=[],r=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=r)t.push.apply(t,e[0][a].slice(n,n+r));return t},ls=ye?function(e){return e[0].length>0&&Q.isBuffer(e[0][0])?Q.concat(e[0].map(function(t){return Q.isBuffer(t)?t:gr(t)})):os(e)}:os,us=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(Wt(e,n)));return a.join("").replace(Lr,"")},Mn=ye?function(e,t,r){return Q.isBuffer(e)?e.toString("utf16le",t,r).replace(Lr,""):us(e,t,r)}:us,hs=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},xs=ye?function(e,t,r){return Q.isBuffer(e)?e.toString("hex",t,t+r):hs(e,t,r)}:hs,ds=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Pa(e,n)));return a.join("")},Ia=ye?function(t,r,a){return Q.isBuffer(t)?t.toString("utf8",r,a):ds(t,r,a)}:ds,vs=function(e,t){var r=Mr(e,t);return r>0?Ia(e,t+4,t+4+r-1):""},E0=vs,ps=function(e,t){var r=Mr(e,t);return r>0?Ia(e,t+4,t+4+r-1):""},T0=ps,ms=function(e,t){var r=2*Mr(e,t);return r>0?Ia(e,t+4,t+4+r-1):""},k0=ms,gs=function(t,r){var a=Mr(t,r);return a>0?Mn(t,r+4,r+4+a):""},S0=gs,_s=function(e,t){var r=Mr(e,t);return r>0?Ia(e,t+4,t+4+r):""},F0=_s,ws=function(e,t){return Xo(e,t)},Bn=ws,y0=function(t){return Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array};ye&&(E0=function(t,r){if(!Q.isBuffer(t))return vs(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},T0=function(t,r){if(!Q.isBuffer(t))return ps(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},k0=function(t,r){if(!Q.isBuffer(t))return ms(t,r);var a=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a-1)},S0=function(t,r){if(!Q.isBuffer(t))return gs(t,r);var a=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a)},F0=function(t,r){if(!Q.isBuffer(t))return _s(t,r);var a=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+a)},Bn=function(t,r){return Q.isBuffer(t)?t.readDoubleLE(r):ws(t,r)},y0=function(t){return Q.isBuffer(t)||Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array});function Es(){Mn=function(e,t,r){return Ee.utils.decode(1200,e.slice(t,r)).replace(Lr,"")},Ia=function(e,t,r){return Ee.utils.decode(65001,e.slice(t,r))},E0=function(e,t){var r=Mr(e,t);return r>0?Ee.utils.decode(Fe,e.slice(t+4,t+4+r-1)):""},T0=function(e,t){var r=Mr(e,t);return r>0?Ee.utils.decode(Be,e.slice(t+4,t+4+r-1)):""},k0=function(e,t){var r=2*Mr(e,t);return r>0?Ee.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},S0=function(e,t){var r=Mr(e,t);return r>0?Ee.utils.decode(1200,e.slice(t+4,t+4+r)):""},F0=function(e,t){var r=Mr(e,t);return r>0?Ee.utils.decode(65001,e.slice(t+4,t+4+r)):""}}typeof Ee!="undefined"&&Es();var Pa=function(e,t){return e[t]},Wt=function(e,t){return e[t+1]*256+e[t]},zo=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Mr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},la=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Ko=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function qa(e,t){var r="",a,n,i=[],s,f,c,o;switch(t){case"dbcs":if(o=this.l,ye&&Q.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)r+=String.fromCharCode(Wt(this,o)),o+=2;e*=2;break;case"utf8":r=Ia(this,this.l,this.l+e);break;case"utf16le":e*=2,r=Mn(this,this.l,this.l+e);break;case"wstr":if(typeof Ee!="undefined")r=Ee.utils.decode(Be,this.slice(this.l,this.l+2*e));else return qa.call(this,e,"dbcs");e=2*e;break;case"lpstr-ansi":r=E0(this,this.l),e=4+Mr(this,this.l);break;case"lpstr-cp":r=T0(this,this.l),e=4+Mr(this,this.l);break;case"lpwstr":r=k0(this,this.l),e=4+2*Mr(this,this.l);break;case"lpp4":e=4+Mr(this,this.l),r=S0(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Mr(this,this.l),r=F0(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=Pa(this,this.l+e++))!==0;)i.push(Qe(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=Wt(this,this.l+e))!==0;)i.push(Qe(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",o=this.l,c=0;c<e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=Pa(this,o),this.l=o+1,f=qa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Qe(Wt(this,o))),o+=2}r=i.join(""),e*=2;break;case"cpstr":if(typeof Ee!="undefined"){r=Ee.utils.decode(Be,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(r="",o=this.l,c=0;c!=e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=Pa(this,o),this.l=o+1,f=qa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Qe(Pa(this,o))),o+=1}r=i.join("");break;default:switch(e){case 1:return a=Pa(this,this.l),this.l++,a;case 2:return a=(t==="i"?zo:Wt)(this,this.l),this.l+=2,a;case 4:case-4:return t==="i"||!(this[this.l+3]&128)?(a=(e>0?la:Ko)(this,this.l),this.l+=4,a):(n=Mr(this,this.l),this.l+=4,n);case 8:case-8:if(t==="f")return e==8?n=Bn(this,this.l):n=Bn([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:r=xs(this,this.l,e);break}}return this.l+=e,r}var jo=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},$o=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Yo=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Zo(e,t,r){var a=0,n=0;if(r==="dbcs"){for(n=0;n!=t.length;++n)Yo(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if(r==="sbcs"){if(typeof Ee!="undefined"&&Fe==874)for(n=0;n!=t.length;++n){var i=Ee.utils.encode(Fe,t.charAt(n));this[this.l+n]=i[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=t.charCodeAt(n)&255;a=t.length}else if(r==="hex"){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var f=t.charCodeAt(n);this[this.l++]=f&255,this[this.l++]=f>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=t&255;break;case 2:a=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:a=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:a=4,jo(this,t,this.l);break;case 8:if(a=8,r==="f"){Go(this,t,this.l);break}case 16:break;case-4:a=4,$o(this,t,this.l);break}return this.l+=a,this}function Ts(e,t){var r=xs(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Xr(e,t){e.l=t,e.read_shift=qa,e.chk=Ts,e.write_shift=Zo}function zr(e,t){e.l+=t}function G(e){var t=br(e);return Xr(t,0),t}function Ht(e,t,r){if(e){var a,n,i;Xr(e,e.l||0);for(var s=e.length,f=0,c=0;e.l<s;){f=e.read_shift(1),f&128&&(f=(f&127)+((e.read_shift(1)&127)<<7));var o=_n[f]||_n[65535];for(a=e.read_shift(1),i=a&127,n=1;n<4&&a&128;++n)i+=((a=e.read_shift(1))&127)<<7*n;c=e.l+i;var l=o.f&&o.f(e,i,r);if(e.l=c,t(l,o,f))return}}}function qr(){var e=[],t=ye?256:2048,r=function(o){var l=G(o);return Xr(l,0),l},a=r(t),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},i=function(o){return a&&o<a.length-a.l?a:(n(),a=r(Math.max(o+1,t)))},s=function(){return n(),or(e)},f=function(o){n(),a=o,a.l==null&&(a.l=a.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function J(e,t,r,a){var n=+t,i;if(!isNaN(n)){a||(a=_n[n].p||(r||[]).length||0),i=1+(n>=128?1:0)+1,a>=128&&++i,a>=16384&&++i,a>=2097152&&++i;var s=e.next(i);n<=127?s.write_shift(1,n):(s.write_shift(1,(n&127)+128),s.write_shift(1,n>>7));for(var f=0;f!=4;++f)if(a>=128)s.write_shift(1,(a&127)+128),a>>=7;else{s.write_shift(1,a);break}a>0&&y0(r)&&e.push(r)}}function en(e,t,r){var a=ur(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function ks(e,t,r){var a=ur(e);return a.s=en(a.s,t.s,r),a.e=en(a.e,t.s,r),a}function rn(e,t){if(e.cRel&&e.c<0)for(e=ur(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=ur(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ce(e);return!e.cRel&&e.cRel!=null&&(r=qo(r)),!e.rRel&&e.rRel!=null&&(r=Jo(r)),r}function A0(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+pr(e.s.c)+":"+(e.e.cRel?"":"$")+pr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+kr(e.s.r)+":"+(e.e.rRel?"":"$")+kr(e.e.r):rn(e.s,t.biff)+":"+rn(e.e,t.biff)}function C0(e){return parseInt(Qo(e),10)-1}function kr(e){return""+(e+1)}function Jo(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Qo(e){return e.replace(/\$(\d+)$/,"$1")}function D0(e){for(var t=el(e),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function pr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function qo(e){return e.replace(/^([A-Z])/,"$$$1")}function el(e){return e.replace(/^\$([A-Z])/,"$1")}function rl(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function _r(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function Ce(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function et(e){var t=e.indexOf(":");return t==-1?{s:_r(e),e:_r(e)}:{s:_r(e.slice(0,t)),e:_r(e.slice(t+1))}}function Ue(e,t){return typeof t=="undefined"||typeof t=="number"?Ue(e.s,e.e):(typeof e!="string"&&(e=Ce(e)),typeof t!="string"&&(t=Ce(t)),e==t?e:e+":"+t)}function Ze(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,i=e.length;for(r=0;a<i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===i||n!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function Ss(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=ft(e.z,r?Rr(t):t)}catch(a){}try{return e.w=ft((e.XF||{}).numFmtId||(r?14:0),r?Rr(t):t)}catch(a){return""+t}}function Pt(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Vt[e.v]||e.v:t==null?Ss(e,e.v):Ss(e,t))}function qt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function Fs(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense;he!=null&&n==null&&(n=he);var i=e||(n?[]:{}),s=0,f=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?_r(a.origin):a.origin;s=c.r,f=c.c}i["!ref"]||(i["!ref"]="A1:A1")}var o={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var l=Ze(i["!ref"]);o.s.c=l.s.c,o.s.r=l.s.r,o.e.c=Math.max(o.e.c,l.e.c),o.e.r=Math.max(o.e.r,l.e.r),s==-1&&(o.e.r=s=l.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var x=0;x!=t[h].length;++x)if(typeof t[h][x]!="undefined"){var d={v:t[h][x]},v=s+h,u=f+x;if(o.s.r>v&&(o.s.r=v),o.s.c>u&&(o.s.c=u),o.e.r<v&&(o.e.r=v),o.e.c<u&&(o.e.c=u),t[h][x]&&typeof t[h][x]=="object"&&!Array.isArray(t[h][x])&&!(t[h][x]instanceof Date))d=t[h][x];else if(Array.isArray(d.v)&&(d.f=t[h][x][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else if(a.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=a.dateNF||Se[14],a.cellDates?(d.t="d",d.w=ft(d.z,Rr(d.v))):(d.t="n",d.v=Rr(d.v),d.w=ft(d.z,d.v))):d.t="s";if(n)i[v]||(i[v]=[]),i[v][u]&&i[v][u].z&&(d.z=i[v][u].z),i[v][u]=d;else{var p=Ce({c:u,r:v});i[p]&&i[p].z&&(d.z=i[p].z),i[p]=d}}}return o.s.c<1e7&&(i["!ref"]=Ue(o)),i}function Ra(e,t){return Fs(null,e,t)}function tl(e){return e.read_shift(4,"i")}function St(e,t){return t||(t=G(4)),t.write_shift(4,e),t}function Kr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function Br(e,t){var r=!1;return t==null&&(r=!0,t=G(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function al(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function nl(e,t){return t||(t=G(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function O0(e,t){var r=e.l,a=e.read_shift(1),n=Kr(e),i=[],s={t:n,h:n};if(a&1){for(var f=e.read_shift(4),c=0;c!=f;++c)i.push(al(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function il(e,t){var r=!1;return t==null&&(r=!0,t=G(15+4*e.t.length)),t.write_shift(1,0),Br(e.t,t),r?t.slice(0,t.l):t}var sl=O0;function fl(e,t){var r=!1;return t==null&&(r=!0,t=G(23+4*e.t.length)),t.write_shift(1,1),Br(e.t,t),t.write_shift(4,1),nl({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function vt(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function ua(e,t){return t==null&&(t=G(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function ha(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function xa(e,t){return t==null&&(t=G(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var cl=Kr,ys=Br;function I0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function Un(e,t){var r=!1;return t==null&&(r=!0,t=G(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var ol=Kr,P0=I0,R0=Un;function N0(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,a=t[0]&2;e.l+=4;var n=a===0?Bn([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):la(t,0)>>2;return r?n/100:n}function As(e,t){t==null&&(t=G(4));var r=0,a=0,n=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29?a=1:n==(n|0)&&n>=-(1<<29)&&n<1<<29&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Cs(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function ll(e,t){return t||(t=G(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var da=Cs,Na=ll;function jr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function va(e,t){return(t||G(8)).write_shift(8,e,"f")}function ul(e){var t={},r=e.read_shift(1),a=r>>>1,n=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),c=e.read_shift(1);switch(e.l++,a){case 0:t.auto=1;break;case 1:t.index=n;var o=pa[n];o&&(t.rgb=fn(o));break;case 2:t.rgb=fn([s,f,c]);break;case 3:t.theme=n;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function Wn(e,t){if(t||(t=G(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var a=e.rgb||"FFFFFF";typeof a=="number"&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}return t}function hl(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function xl(e,t){t||(t=G(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}function Ds(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function dl(e){return Ds(e,1)}function vl(e){return Ds(e,2)}var b0=2,nt=3,Hn=11,Os=12,Vn=19,f_=30,Xn=64,pl=65,ml=71,c_=4096,gl=4108,_l=4126,Ur=80,Is=81,wl=[Ur,Is],L0={1:{n:"CodePage",t:b0},2:{n:"Category",t:Ur},3:{n:"PresentationFormat",t:Ur},4:{n:"ByteCount",t:nt},5:{n:"LineCount",t:nt},6:{n:"ParagraphCount",t:nt},7:{n:"SlideCount",t:nt},8:{n:"NoteCount",t:nt},9:{n:"HiddenCount",t:nt},10:{n:"MultimediaClipCount",t:nt},11:{n:"ScaleCrop",t:Hn},12:{n:"HeadingPairs",t:gl},13:{n:"TitlesOfParts",t:_l},14:{n:"Manager",t:Ur},15:{n:"Company",t:Ur},16:{n:"LinksUpToDate",t:Hn},17:{n:"CharacterCount",t:nt},19:{n:"SharedDoc",t:Hn},22:{n:"HyperlinksChanged",t:Hn},23:{n:"AppVersion",t:nt,p:"version"},24:{n:"DigSig",t:pl},26:{n:"ContentType",t:Ur},27:{n:"ContentStatus",t:Ur},28:{n:"Language",t:Ur},29:{n:"Version",t:Ur},255:{},2147483648:{n:"Locale",t:Vn},2147483651:{n:"Behavior",t:Vn},1919054434:{}},M0={1:{n:"CodePage",t:b0},2:{n:"Title",t:Ur},3:{n:"Subject",t:Ur},4:{n:"Author",t:Ur},5:{n:"Keywords",t:Ur},6:{n:"Comments",t:Ur},7:{n:"Template",t:Ur},8:{n:"LastAuthor",t:Ur},9:{n:"RevNumber",t:Ur},10:{n:"EditTime",t:Xn},11:{n:"LastPrinted",t:Xn},12:{n:"CreatedDate",t:Xn},13:{n:"ModifiedDate",t:Xn},14:{n:"PageCount",t:nt},15:{n:"WordCount",t:nt},16:{n:"CharCount",t:nt},17:{n:"Thumbnail",t:ml},18:{n:"Application",t:Ur},19:{n:"DocSecurity",t:nt},255:{},2147483648:{n:"Locale",t:Vn},2147483651:{n:"Behavior",t:Vn},1919054434:{}},Ps={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},El=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Tl(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var kl=Tl([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),pa=ur(kl),Vt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Rs={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},B0={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Gn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function U0(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Sl(e){var t=U0();if(!e||!e.match)return t;var r={};if((e.match(Gr)||[]).forEach(function(a){var n=Pe(a);switch(n[0].replace(No,"<")){case"<?xml":break;case"<Types":t.xmlns=n["xmlns"+(n[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[n.Extension]=n.ContentType;break;case"<Override":t[B0[n.ContentType]]!==void 0&&t[B0[n.ContentType]].push(n.PartName);break}}),t.xmlns!==Dr.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}function Ns(e,t){var r=Fo(B0),a=[],n;a[a.length]=Tr,a[a.length]=te("Types",null,{xmlns:Dr.CT,"xmlns:xsd":Dr.xsd,"xmlns:xsi":Dr.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(c){return te("Default",null,{Extension:c[0],ContentType:c[1]})}));var i=function(c){e[c]&&e[c].length>0&&(n=e[c][0],a[a.length]=te("Override",null,{PartName:(n[0]=="/"?"":"/")+n,ContentType:Gn[c][t.bookType]||Gn[c].xlsx}))},s=function(c){(e[c]||[]).forEach(function(o){a[a.length]=te("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:Gn[c][t.bookType]||Gn[c].xlsx})})},f=function(c){(e[c]||[]).forEach(function(o){a[a.length]=te("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:r[c][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var We={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function tn(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function an(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var a={};return(e.match(Gr)||[]).forEach(function(n){var i=Pe(n);if(i[0]==="<Relationship"){var s={};s.Type=i.Type,s.Target=i.Target,s.Id=i.Id,i.TargetMode&&(s.TargetMode=i.TargetMode);var f=i.TargetMode==="External"?i.Target:Ya(i.Target,t);r[f]=s,a[i.Id]=s}}),r["!id"]=a,r}function ba(e){var t=[Tr,te("Relationships",null,{xmlns:Dr.RELS})];return wr(e["!id"]).forEach(function(r){t[t.length]=te("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function nr(e,t,r,a,n,i){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,i?n.TargetMode=i:[We.HLINK,We.XPATH,We.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}var Fl="application/vnd.oasis.opendocument.spreadsheet";function yl(e,t){for(var r=w0(e),a,n;a=Qa.exec(r);)switch(a[3]){case"manifest":break;case"file-entry":if(n=Pe(a[0],!1),n.path=="/"&&n.type!==Fl)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw a}}function Al(e){var t=[Tr];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function bs(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function Cl(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function Dl(e){var t=[Tr];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(bs(e[r][0],e[r][1])),t.push(Cl("",e[r][0]));return t.push(bs("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function Ls(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+Ne.version+"</meta:generator></office:meta></office:document-meta>"}var pt=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],Ol=function(){for(var e=new Array(pt.length),t=0;t<pt.length;++t){var r=pt[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function Ms(e){var t={};e=lr(e);for(var r=0;r<pt.length;++r){var a=pt[r],n=e.match(Ol[r]);n!=null&&n.length>0&&(t[a[1]]=qe(n[1])),a[2]==="date"&&t[a[1]]&&(t[a[1]]=dr(t[a[1]]))}return t}function W0(e,t,r,a,n){n[e]!=null||t==null||t===""||(n[e]=t,t=sr(t),a[a.length]=r?te(e,t,r):Vr(e,t))}function Bs(e,t){var r=t||{},a=[Tr,te("cp:coreProperties",null,{"xmlns:cp":Dr.CORE_PROPS,"xmlns:dc":Dr.dc,"xmlns:dcterms":Dr.dcterms,"xmlns:dcmitype":Dr.dcmitype,"xmlns:xsi":Dr.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(e.CreatedDate!=null&&W0("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:_0(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),e.ModifiedDate!=null&&W0("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:_0(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var i=0;i!=pt.length;++i){var s=pt[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&W0(s[0],f,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var ma=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],Us=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function Ws(e,t,r,a){var n=[];if(typeof e=="string")n=fs(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(l){return{v:l}}));var s=typeof t=="string"?fs(t,a).map(function(l){return l.v}):t,f=0,c=0;if(s.length>0)for(var o=0;o!==n.length;o+=2){switch(c=+n[o+1].v,n[o].v){case"Worksheets":case"\u5DE5\u4F5C\u8868":case"\u041B\u0438\u0441\u0442\u044B":case"\u0623\u0648\u0631\u0627\u0642 \u0627\u0644\u0639\u0645\u0644":case"\u30EF\u30FC\u30AF\u30B7\u30FC\u30C8":case"\u05D2\u05DC\u05D9\u05D5\u05E0\u05D5\u05EA \u05E2\u05D1\u05D5\u05D3\u05D4":case"Arbeitsbl\xE4tter":case"\xC7al\u0131\u015Fma Sayfalar\u0131":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xE1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xE1lculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=s.slice(f,f+c);break;case"Named Ranges":case"Rangos con nombre":case"\u540D\u524D\u4ED8\u304D\u4E00\u89A7":case"Benannte Bereiche":case"Navngivne omr\xE5der":r.NamedRanges=c,r.DefinedNames=s.slice(f,f+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=s.slice(f,f+c);break}f+=c}}function Il(e,t,r){var a={};return t||(t={}),e=lr(e),ma.forEach(function(n){var i=(e.match(Za(n[0]))||[])[1];switch(n[2]){case"string":i&&(t[n[1]]=qe(i));break;case"bool":t[n[1]]=i==="true";break;case"raw":var s=e.match(new RegExp("<"+n[0]+"[^>]*>([\\s\\S]*?)</"+n[0]+">"));s&&s.length>0&&(a[n[1]]=s[1]);break}}),a.HeadingPairs&&a.TitlesOfParts&&Ws(a.HeadingPairs,a.TitlesOfParts,t,r),t}function Hs(e){var t=[],r=te;return e||(e={}),e.Application="SheetJS",t[t.length]=Tr,t[t.length]=te("Properties",null,{xmlns:Dr.EXT_PROPS,"xmlns:vt":Dr.vt}),ma.forEach(function(a){if(e[a[1]]!==void 0){var n;switch(a[2]){case"string":n=sr(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break}n!==void 0&&(t[t.length]=r(a[0],n))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(a){return"<vt:lpstr>"+sr(a)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Pl=/<[^>]+>[^<]*/g;function Rl(e,t){var r={},a="",n=e.match(Pl);if(n)for(var i=0;i!=n.length;++i){var s=n[i],f=Pe(s);switch(f[0]){case"<?xml":break;case"<Properties":break;case"<property":a=qe(f.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var c=s.split(">"),o=c[0].slice(4),l=c[1];switch(o){case"lpstr":case"bstr":case"lpwstr":r[a]=qe(l);break;case"bool":r[a]=vr(l);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(l,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(l);break;case"filetime":case"date":r[a]=dr(l);break;case"cy":case"error":r[a]=qe(l);break;default:if(o.slice(-1)=="/")break;t.WTF&&typeof console!="undefined"&&console.warn("Unexpected",s,o,c)}}else if(s.slice(0,2)!=="</"){if(t.WTF)throw new Error(s)}}}return r}function Vs(e){var t=[Tr,te("Properties",null,{xmlns:Dr.CUST_PROPS,"xmlns:vt":Dr.vt})];if(!e)return t.join("");var r=1;return wr(e).forEach(function(n){++r,t[t.length]=te("property",Vo(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:sr(n)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var H0={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},V0;function Nl(e,t,r){V0||(V0=Pn(H0)),t=V0[t]||t,e[t]=r}function bl(e,t){var r=[];return wr(H0).map(function(a){for(var n=0;n<pt.length;++n)if(pt[n][1]==a)return pt[n];for(n=0;n<ma.length;++n)if(ma[n][1]==a)return ma[n];throw a}).forEach(function(a){if(e[a[1]]!=null){var n=t&&t.Props&&t.Props[a[1]]!=null?t.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break}typeof n=="number"?n=String(n):n===!0||n===!1?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(Vr(H0[a[1]]||a[1],n))}}),te("DocumentProperties",r.join(""),{xmlns:ot.o})}function Ll(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&wr(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<pt.length;++s)if(i==pt[s][1])return;for(s=0;s<ma.length;++s)if(i==ma[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],c="string";typeof f=="number"?(c="float",f=String(f)):f===!0||f===!1?(c="boolean",f=f?"1":"0"):f=String(f),n.push(te(rs(i),f,{"dt:dt":c}))}}),t&&wr(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),n.push(te(rs(i),s,{"dt:dt":f}))}}),"<"+a+' xmlns="'+ot.o+'">'+n.join("")+"</"+a+">"}function X0(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function Ml(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,a=r%Math.pow(2,32),n=(r-a)/Math.pow(2,32);a*=1e7,n*=1e7;var i=a/Math.pow(2,32)|0;i>0&&(a=a%Math.pow(2,32),n+=i);var s=G(8);return s.write_shift(4,a),s.write_shift(4,n),s}function Xs(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function Gs(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function zs(e,t,r){return t===31?Gs(e):Xs(e,t,r)}function G0(e,t,r){return zs(e,t,r===!1?0:4)}function Bl(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return zs(e,t,0)}function Ul(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(Lr,""),e.l-n&2&&(e.l+=2)}return r}function Wl(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(Lr,"");return r}function Hl(e){var t=e.l,r=zn(e,Is);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var a=zn(e,nt);return[r,a]}function Vl(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(Hl(e));return r}function Ks(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var i=e.read_shift(4),s=e.read_shift(4);a[i]=e.read_shift(s,t===1200?"utf16le":"utf8").replace(Lr,"").replace(Bt,"!"),t===1200&&s%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),a}function js(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function Xl(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function zn(e,t,r){var a=e.read_shift(2),n,i=r||{};if(e.l+=2,t!==Os&&a!==t&&wl.indexOf(t)===-1&&!((t&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+t+" saw "+a);switch(t===Os?a:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i"),n;case 11:return e.read_shift(4)!==0;case 19:return n=e.read_shift(4),n;case 30:return Xs(e,a,4).replace(Lr,"");case 31:return Gs(e);case 64:return X0(e);case 65:return js(e);case 71:return Xl(e);case 80:return G0(e,a,!i.raw).replace(Lr,"");case 81:return Bl(e,a).replace(Lr,"");case 4108:return Vl(e);case 4126:case 4127:return a==4127?Ul(e):Wl(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+a)}}function $s(e,t){var r=G(4),a=G(4);switch(r.write_shift(4,e==80?31:e),e){case 3:a.write_shift(-4,t);break;case 5:a=G(8),a.write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=Ml(t);break;case 31:case 80:for(a=G(4+2*(t.length+1)+(t.length%2?0:2)),a.write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return or([r,a])}function Ys(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0,f=0,c=-1,o={};for(s=0;s!=n;++s){var l=e.read_shift(4),h=e.read_shift(4);i[s]=[l,h+r]}i.sort(function(T,g){return T[1]-g[1]});var x={};for(s=0;s!=n;++s){if(e.l!==i[s][1]){var d=!0;if(s>0&&t)switch(t[i[s-1][0]].t){case 2:e.l+2===i[s][1]&&(e.l+=2,d=!1);break;case 80:e.l<=i[s][1]&&(e.l=i[s][1],d=!1);break;case 4108:e.l<=i[s][1]&&(e.l=i[s][1],d=!1);break}if((!t||s==0)&&e.l<=i[s][1]&&(d=!1,e.l=i[s][1]),d)throw new Error("Read Error: Expected address "+i[s][1]+" at "+e.l+" :"+s)}if(t){var v=t[i[s][0]];if(x[v.n]=zn(e,v.t,{raw:!0}),v.p==="version"&&(x[v.n]=String(x[v.n]>>16)+"."+("0000"+String(x[v.n]&65535)).slice(-4)),v.n=="CodePage")switch(x[v.n]){case 0:x[v.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:de(f=x[v.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+x[v.n])}}else if(i[s][0]===1){if(f=x.CodePage=zn(e,b0),de(f),c!==-1){var u=e.l;e.l=i[c][1],o=Ks(e,f),e.l=u}}else if(i[s][0]===0){if(f===0){c=s,e.l=i[s+1][1];continue}o=Ks(e,f)}else{var p=o[i[s][0]],E;switch(e[e.l]){case 65:e.l+=4,E=js(e);break;case 30:e.l+=4,E=G0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,E=G0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,E=e.read_shift(4,"i");break;case 19:e.l+=4,E=e.read_shift(4);break;case 5:e.l+=4,E=e.read_shift(8,"f");break;case 11:e.l+=4,E=Fr(e,4);break;case 64:e.l+=4,E=dr(X0(e));break;default:throw new Error("unparsed value: "+e[e.l])}x[p]=E}}return e.l=r+a,x}var Zs=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function Gl(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Js(e,t,r){var a=G(8),n=[],i=[],s=8,f=0,c=G(8),o=G(8);if(c.write_shift(4,2),c.write_shift(4,1200),o.write_shift(4,1),i.push(c),n.push(o),s+=8+c.length,!t){o=G(8),o.write_shift(4,0),n.unshift(o);var l=[G(4)];for(l[0].write_shift(4,e.length),f=0;f<e.length;++f){var h=e[f][0];for(c=G(8+2*(h.length+1)+(h.length%2?0:2)),c.write_shift(4,f+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);l.push(c)}c=or(l),i.unshift(c),s+=8+c.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(Zs.indexOf(e[f][0])>-1||Us.indexOf(e[f][0])>-1)&&e[f][1]!=null){var x=e[f][1],d=0;if(t){d=+t[e[f][0]];var v=r[d];if(v.p=="version"&&typeof x=="string"){var u=x.split(".");x=(+u[0]<<16)+(+u[1]||0)}c=$s(v.t,x)}else{var p=Gl(x);p==-1&&(p=31,x=String(x)),c=$s(p,x)}i.push(c),o=G(8),o.write_shift(4,t?d:2+f),n.push(o),s+=8+c.length}var E=8*(i.length+1);for(f=0;f<i.length;++f)n[f].write_shift(4,E),E+=i[f].length;return a.write_shift(4,s),a.write_shift(4,i.length),or([a].concat(n).concat(i))}function Qs(e,t,r){var a=e.content;if(!a)return{};Xr(a,0);var n,i,s,f,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var o=a.read_shift(4),l=a.read_shift(16);if(l!==Te.utils.consts.HEADER_CLSID&&l!==r)throw new Error("Bad PropertySet CLSID "+l);if(n=a.read_shift(4),n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);if(i=a.read_shift(16),f=a.read_shift(4),n===1&&f!==a.l)throw new Error("Length mismatch: "+f+" !== "+a.l);n===2&&(s=a.read_shift(16),c=a.read_shift(4));var h=Ys(a,t),x={SystemIdentifier:o};for(var d in h)x[d]=h[d];if(x.FMTID=i,n===1)return x;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);var v;try{v=Ys(a,null)}catch(u){}for(d in v)x[d]=v[d];return x.FMTID=[i,s],x}function qs(e,t,r,a,n,i){var s=G(n?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,Te.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,n?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,n?68:48);var c=Js(e,r,a);if(f.push(c),n){var o=Js(n,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+c.length),f.push(o)}return or(f)}function ea(e,t){return e.read_shift(t),null}function zl(e,t){t||(t=G(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Kl(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function Fr(e,t){return e.read_shift(t)===1}function rt(e,t){return t||(t=G(2)),t.write_shift(2,+!!e),t}function Or(e){return e.read_shift(2,"u")}function mt(e,t){return t||(t=G(2)),t.write_shift(2,e),t}function ef(e,t){return Kl(e,t,Or)}function jl(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function rf(e,t,r){return r||(r=G(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function nn(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",i=Be;if(r&&r.biff>=8&&(Be=1200),!r||r.biff==8){var s=e.read_shift(1);s&&(n="dbcs-cont")}else r.biff==12&&(n="wstr");r.biff>=2&&r.biff<=5&&(n="cpstr");var f=a?e.read_shift(a,n):"";return Be=i,f}function $l(e){var t=Be;Be=1200;var r=e.read_shift(2),a=e.read_shift(1),n=a&4,i=a&8,s=1+(a&1),f=0,c,o={};i&&(f=e.read_shift(2)),n&&(c=e.read_shift(4));var l=s==2?"dbcs-cont":"sbcs-cont",h=r===0?"":e.read_shift(r,l);return i&&(e.l+=4*f),n&&(e.l+=c),o.t=h,i||(o.raw="<t>"+o.t+"</t>",o.r=o.t),Be=t,o}function Yl(e){var t=e.t||"",r=1,a=G(3+(r>1?2:0));a.write_shift(2,t.length),a.write_shift(1,(r>1?8:0)|1),r>1&&a.write_shift(2,r);var n=G(2*t.length);n.write_shift(2*t.length,t,"utf16le");var i=[a,n];return or(i)}function ga(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var n=e.read_shift(1);return n===0?a=e.read_shift(t,"sbcs-cont"):a=e.read_shift(t,"dbcs-cont"),a}function sn(e,t,r){var a=e.read_shift(r&&r.biff==2?1:2);return a===0?(e.l++,""):ga(e,a,r)}function _a(e,t,r){if(r.biff>5)return sn(e,t,r);var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function tf(e,t,r){return r||(r=G(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Zl(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function Jl(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(Lr,"");return a&&(e.l+=24),n}function Ql(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(n===0)return r+a.replace(/\\/g,"/");var i=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var s=e.read_shift(i>>1,"utf16le").replace(Lr,"");return r+s}function ql(e,t){var r=e.read_shift(16);switch(t-=16,r){case"e0c9ea79f9bace118c8200aa004ba90b":return Jl(e,t);case"0303000000000000c000000000000046":return Ql(e,t);default:throw new Error("Unsupported Moniker "+r)}}function Kn(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace(Lr,""):"";return r}function af(e,t){t||(t=G(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function eu(e,t){var r=e.l+t,a=e.read_shift(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var i,s,f,c,o="",l,h;n&16&&(i=Kn(e,r-e.l)),n&128&&(s=Kn(e,r-e.l)),(n&257)===257&&(f=Kn(e,r-e.l)),(n&257)===1&&(c=ql(e,r-e.l)),n&8&&(o=Kn(e,r-e.l)),n&32&&(l=e.read_shift(16)),n&64&&(h=X0(e)),e.l=r;var x=s||f||c||"";x&&o&&(x+="#"+o),x||(x="#"+o),n&2&&x.charAt(0)=="/"&&x.charAt(1)!="/"&&(x="file://"+x);var d={Target:x};return l&&(d.guid=l),h&&(d.time=h),i&&(d.Tooltip=i),d}function ru(e){var t=G(512),r=0,a=e.Target;a.slice(0,7)=="file://"&&(a=a.slice(7));var n=a.indexOf("#"),i=n>-1?31:23;switch(a.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)a=a.slice(1),af(a,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&af(n>-1?a.slice(n+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var c=0;a.slice(c*3,c*3+3)=="../"||a.slice(c*3,c*3+3)=="..\\";)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,a.charCodeAt(r+3*c)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function nf(e){var t=e.read_shift(1),r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[t,r,a,n]}function sf(e,t){var r=nf(e,t);return r[3]=0,r}function Rt(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return{r:t,c:r,ixfe:a}}function wa(e,t,r,a){return a||(a=G(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function tu(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function au(e,t,r){return t===0?"":_a(e,t,r)}function nu(e,t,r){var a=r.biff>8?4:2,n=e.read_shift(a),i=e.read_shift(a,"i"),s=e.read_shift(a,"i");return[n,i,s]}function ff(e){var t=e.read_shift(2),r=N0(e);return[t,r]}function iu(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=nn(e,t,r),i=e.read_shift(2);if(a-=e.l,i!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+i);return e.l+=i,n}function jn(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:t},e:{c:n,r}}}function cf(e,t){return t||(t=G(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function of(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:t},e:{c:n,r}}}var su=of;function lf(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function fu(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function cu(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function $r(e){e.l+=2,e.l+=e.read_shift(2)}var ou={0:$r,4:$r,5:$r,6:$r,7:cu,8:$r,9:$r,10:$r,11:$r,12:$r,13:fu,14:$r,15:$r,16:$r,17:$r,18:$r,19:$r,20:$r,21:lf};function lu(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(ou[n](e,r-e.l))}catch(i){return e.l=r,a}}return e.l!=r&&(e.l=r),a}function $n(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function z0(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=G(n);return i.write_shift(2,a),i.write_shift(2,t),n>4&&i.write_shift(2,29282),n>6&&i.write_shift(2,1997),n>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function uu(e,t){return t===0||e.read_shift(2),1200}function hu(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=_a(e,0,r);return e.read_shift(t+a-e.l),n}function xu(e,t){var r=!t||t.biff==8,a=G(r?112:54);for(a.write_shift(t.biff==8?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}function du(e,t,r){var a=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function vu(e,t,r){var a=e.read_shift(4),n=e.read_shift(1)&3,i=e.read_shift(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule";break}var s=nn(e,0,r);return s.length===0&&(s="Sheet1"),{pos:a,hs:n,dt:i,name:s}}function pu(e,t){var r=!t||t.biff>=8?2:1,a=G(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function mu(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0;s!=n&&e.l<r;++s)i.push($l(e));return i.Count=a,i.Unique=n,i}function gu(e,t){var r=G(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=Yl(e[n],t);var i=or([r].concat(a));return i.parts=[r.length].concat(a.map(function(s){return s.length})),i}function _u(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function wu(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,a&7&&(t.level=a&7),a&32&&(t.hidden=!0),a&64&&(t.hpt=r/20),t}function Eu(e){var t=tu(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function Tu(e){return e.read_shift(2),e.read_shift(4)}function uf(e,t,r){var a=0;r&&r.biff==2||(a=e.read_shift(2));var n=e.read_shift(2);r&&r.biff==2&&(a=1-(n>>15),n&=32767);var i={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[i,n]}function ku(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=e.read_shift(2),c=e.read_shift(2),o=e.read_shift(2);return{Pos:[t,r],Dim:[a,n],Flags:i,CurTab:s,FirstTab:f,Selected:c,TabRatio:o}}function Su(){var e=G(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function Fu(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var a=e.read_shift(2);return{RTL:a&64}}function yu(e){var t=G(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Au(){}function Cu(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=nn(e,0,r),a}function Du(e,t){var r=e.name||"Arial",a=t&&t.biff==5,n=a?15+r.length:16+2*r.length,i=G(n);return i.write_shift(2,(e.sz||12)*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),a||i.write_shift(1,1),i.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),i}function Ou(e){var t=Rt(e);return t.isst=e.read_shift(4),t}function Iu(e,t,r,a){var n=G(10);return wa(e,t,a,n),n.write_shift(4,r),n}function Pu(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=e.l+t,n=Rt(e,6);r.biff==2&&e.l++;var i=sn(e,a-e.l,r);return n.val=i,n}function Ru(e,t,r,a,n){var i=!n||n.biff==8,s=G(8+ +i+(1+i)*r.length);return wa(e,t,a,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function Nu(e,t,r){var a=e.read_shift(2),n=_a(e,0,r);return[a,n]}function bu(e,t,r,a){var n=r&&r.biff==5;a||(a=G(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var i=a.length>a.l?a.slice(0,a.l):a;return i.l==null&&(i.l=i.length),i}var Lu=_a;function hf(e,t,r){var a=e.l+t,n=r.biff==8||!r.biff?4:2,i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:i,c:f},e:{r:s,c}}}function Mu(e,t){var r=t.biff==8||!t.biff?4:2,a=G(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}function Bu(e){var t=e.read_shift(2),r=e.read_shift(2),a=ff(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}function Uu(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(ff(e));if(e.l!==r)throw new Error("MulRK read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:s,rkrec:i}}function Wu(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:s,ixfe:i}}function Hu(e,t,r,a){var n={},i=e.read_shift(4),s=e.read_shift(4),f=e.read_shift(4),c=e.read_shift(2);return n.patternType=El[f>>26],a.cellStyles&&(n.alc=i&7,n.fWrap=i>>3&1,n.alcV=i>>4&7,n.fJustLast=i>>7&1,n.trot=i>>8&255,n.cIndent=i>>16&15,n.fShrinkToFit=i>>20&1,n.iReadOrder=i>>22&2,n.fAtrNum=i>>26&1,n.fAtrFnt=i>>27&1,n.fAtrAlc=i>>28&1,n.fAtrBdr=i>>29&1,n.fAtrPat=i>>30&1,n.fAtrProt=i>>31&1,n.dgLeft=s&15,n.dgRight=s>>4&15,n.dgTop=s>>8&15,n.dgBottom=s>>12&15,n.icvLeft=s>>16&127,n.icvRight=s>>23&127,n.grbitDiag=s>>30&3,n.icvTop=f&127,n.icvBottom=f>>7&127,n.icvDiag=f>>14&127,n.dgDiag=f>>21&15,n.icvFore=c&127,n.icvBack=c>>7&127,n.fsxButton=c>>14&1),n}function Vu(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,t-=6,a.data=Hu(e,t,a.fStyle,r),a}function xf(e,t,r,a){var n=r&&r.biff==5;a||(a=G(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&n&&(i|=1024),a.write_shift(4,i),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Xu(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function Gu(e){var t=G(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function df(e,t,r){var a=Rt(e,6);(r.biff==2||t==9)&&++e.l;var n=jl(e,2);return a.val=n,a.t=n===!0||n===!1?"b":"e",a}function zu(e,t,r,a,n,i){var s=G(8);return wa(e,t,a,s),rf(r,i,s),s}function Ku(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=Rt(e,6),n=jr(e,8);return a.val=n,a}function ju(e,t,r,a){var n=G(14);return wa(e,t,a,n),va(r,n),n}var vf=au;function $u(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(2);if(r.sbcch=i,i==1025||i==14849)return[i,n];if(i<1||i>255)throw new Error("Unexpected SupBook type: "+i);for(var s=ga(e,i),f=[];a>e.l;)f.push(sn(e));return[i,n,s,f]}function pf(e,t,r){var a=e.read_shift(2),n,i={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return r.sbcch===14849&&(n=iu(e,t-2,r)),i.body=n||e.read_shift(t-2),typeof n=="string"&&(i.Name=n),i}var Yu=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function mf(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(1),s=e.read_shift(1),f=e.read_shift(r&&r.biff==2?1:2),c=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),c=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var o=ga(e,s,r);n&32&&(o=Yu[o.charCodeAt(0)]);var l=a-e.l;r&&r.biff==2&&--l;var h=a==e.l||f===0||!(l>0)?[]:lv(e,l,r,f);return{chKey:i,Name:o,itab:c,rgce:h}}function gf(e,t,r){if(r.biff<8)return Zu(e,t,r);for(var a=[],n=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)a.push(nu(e,r.biff>8?12:6,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Zu(e,t,r){e[e.l+1]==3&&e[e.l]++;var a=nn(e,t,r);return a.charCodeAt(0)==3?a.slice(1):a}function Ju(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2),i=ga(e,a,r),s=ga(e,n,r);return[i,s]}function Qu(e,t,r){var a=of(e,6);e.l++;var n=e.read_shift(1);return t-=8,[uv(e,t,r),n,a]}function _f(e,t,r){var a=su(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,cv(e,t,r,a)]}function qu(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,a=e.read_shift(4);return[t,r,a]}function eh(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=_a(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},f,s,i]}}function rh(e,t,r){return eh(e,t,r)}function th(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(jn(e,t));return r}function ah(e){var t=G(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)cf(e[r],t);return t}function nh(e,t,r){if(r&&r.biff<8)return sh(e,t,r);var a=lf(e,22),n=lu(e,t-22,a[1]);return{cmo:a,ft:n}}var ih={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function sh(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var s=[];return s.push((ih[a]||zr)(e,t,r)),{cmo:[n,a,i],ft:s}}function fh(e,t,r){var a=e.l,n="";try{e.l+=4;var i=(r.lastobj||{cmo:[0,0]}).cmo[1],s;[0,5,7,11,12,14].indexOf(i)==-1?e.l+=6:s=Zl(e,6,r);var f=e.read_shift(2);e.read_shift(2),Or(e,2);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var l=e[e.l],h=ga(e,e.lens[o+1]-e.lens[o]-1);if(n+=h,n.length>=(l?f:2*f))break}if(n.length!==f&&n.length!==f*2)throw new Error("cchText: "+f+" != "+n.length);return e.l=a+t,{t:n}}catch(x){return e.l=a+t,{t:n}}}function ch(e,t){var r=jn(e,8);e.l+=16;var a=eu(e,t-24);return[r,a]}function oh(e){var t=G(24),r=_r(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return or([t,ru(e[1])])}function lh(e,t){e.read_shift(2);var r=jn(e,8),a=e.read_shift((t-10)/2,"dbcs-cont");return a=a.replace(Lr,""),[r,a]}function uh(e){var t=e[1].Tooltip,r=G(10+2*(t.length+1));r.write_shift(2,2048);var a=_r(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function hh(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=Ps[r]||r,r=e.read_shift(2),t[1]=Ps[r]||r,t}function xh(e){return e||(e=G(4)),e.write_shift(2,1),e.write_shift(2,1),e}function dh(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(sf(e,8));return r}function vh(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(sf(e,8));return r}function ph(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function wf(e,t,r){if(!r.cellStyles)return zr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),i=e.read_shift(a),s=e.read_shift(a),f=e.read_shift(a),c=e.read_shift(2);a==2&&(e.l+=2);var o={s:n,e:i,w:s,ixfe:f,flags:c};return(r.biff>=5||!r.biff)&&(o.level=c>>8&7),o}function mh(e,t){var r=G(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}function gh(e,t){var r={};return t<32||(e.l+=16,r.header=jr(e,8),r.footer=jr(e,8),e.l+=2),r}function _h(e,t,r){var a={area:!1};if(r.biff!=5)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,n&16&&(a.area=!0),a}function wh(e){for(var t=G(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}var Eh=Rt,Th=ef,kh=sn;function Sh(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function Fh(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var a=Rt(e,6);++e.l;var n=_a(e,t-7,r);return a.t="str",a.val=n,a}function yh(e){var t=Rt(e,6);++e.l;var r=jr(e,8);return t.t="n",t.val=r,t}function Ah(e,t,r){var a=G(15);return wn(a,e,t),a.write_shift(8,r,"f"),a}function Ch(e){var t=Rt(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function Dh(e,t,r){var a=G(9);return wn(a,e,t),a.write_shift(2,r),a}function Oh(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function Ih(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function Ph(e,t,r){var a=e.l+t,n=Rt(e,6),i=e.read_shift(2),s=ga(e,i,r);return e.l=a,n.t="str",n.val=s,n}var Rh=[2,3,48,49,131,139,140,245],K0=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Pn({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,c){var o=[],l=br(1);switch(c.type){case"base64":l=ar(hr(f));break;case"binary":l=ar(f);break;case"buffer":case"array":l=f;break}Xr(l,0);var h=l.read_shift(1),x=!!(h&136),d=!1,v=!1;switch(h){case 2:break;case 3:break;case 48:d=!0,x=!0;break;case 49:d=!0,x=!0;break;case 131:break;case 139:break;case 140:v=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+h.toString(16))}var u=0,p=521;h==2&&(u=l.read_shift(2)),l.l+=3,h!=2&&(u=l.read_shift(4)),u>1048576&&(u=1e6),h!=2&&(p=l.read_shift(2));var E=l.read_shift(2),T=c.codepage||1252;h!=2&&(l.l+=16,l.read_shift(1),l[l.l]!==0&&(T=e[l[l.l]]),l.l+=1,l.l+=2),v&&(l.l+=36);for(var g=[],R={},L=Math.min(l.length,h==2?521:p-10-(d?264:0)),I=v?32:11;l.l<L&&l[l.l]!=13;)switch(R={},R.name=Ee.utils.decode(T,l.slice(l.l,l.l+I)).replace(/[\u0000\r\n].*$/g,""),l.l+=I,R.type=String.fromCharCode(l.read_shift(1)),h!=2&&!v&&(R.offset=l.read_shift(4)),R.len=l.read_shift(1),h==2&&(R.offset=l.read_shift(2)),R.dec=l.read_shift(1),R.name.length&&g.push(R),h!=2&&(l.l+=v?13:14),R.type){case"B":(!d||R.len!=8)&&c.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"G":case"P":c.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+R.type)}if(l[l.l]!==13&&(l.l=p-1),l.read_shift(1)!==13)throw new Error("DBF Terminator not found "+l.l+" "+l[l.l]);l.l=p;var F=0,N=0;for(o[0]=[],N=0;N!=g.length;++N)o[0][N]=g[N].name;for(;u-- >0;){if(l[l.l]===42){l.l+=E;continue}for(++l.l,o[++F]=[],N=0,N=0;N!=g.length;++N){var P=l.slice(l.l,l.l+g[N].len);l.l+=g[N].len,Xr(P,0);var V=Ee.utils.decode(T,P);switch(g[N].type){case"C":V.trim().length&&(o[F][N]=V.replace(/\s+$/,""));break;case"D":V.length===8?o[F][N]=new Date(+V.slice(0,4),+V.slice(4,6)-1,+V.slice(6,8)):o[F][N]=V;break;case"F":o[F][N]=parseFloat(V.trim());break;case"+":case"I":o[F][N]=v?P.read_shift(-4,"i")^2147483648:P.read_shift(4,"i");break;case"L":switch(V.trim().toUpperCase()){case"Y":case"T":o[F][N]=!0;break;case"N":case"F":o[F][N]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+V+"|")}break;case"M":if(!x)throw new Error("DBF Unexpected MEMO for type "+h.toString(16));o[F][N]="##MEMO##"+(v?parseInt(V.trim(),10):P.read_shift(4));break;case"N":V=V.replace(/\u0000/g,"").trim(),V&&V!="."&&(o[F][N]=+V||0);break;case"@":o[F][N]=new Date(P.read_shift(-8,"f")-621356832e5);break;case"T":o[F][N]=new Date((P.read_shift(4)-2440588)*864e5+P.read_shift(4));break;case"Y":o[F][N]=P.read_shift(4,"i")/1e4+P.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":o[F][N]=-P.read_shift(-8,"f");break;case"B":if(d&&g[N].len==8){o[F][N]=P.read_shift(8,"f");break}case"G":case"P":P.l+=g[N].len;break;case"0":if(g[N].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+g[N].type)}}}if(h!=2&&l.l<l.length&&l[l.l++]!=26)throw new Error("DBF EOF Marker missing "+(l.l-1)+" of "+l.length+" "+l[l.l-1].toString(16));return c&&c.sheetRows&&(o=o.slice(0,c.sheetRows)),c.DBF=g,o}function a(f,c){var o=c||{};o.dateNF||(o.dateNF="yyyymmdd");var l=Ra(r(f,o),o);return l["!cols"]=o.DBF.map(function(h){return{wch:h.len,DBF:h}}),delete o.DBF,l}function n(f,c){try{return qt(a(f,c),c)}catch(o){if(c&&c.WTF)throw o}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,c){var o=c||{};if(+o.codepage>=0&&de(+o.codepage),o.type=="string")throw new Error("Cannot write DBF to JS string");var l=qr(),h=s0(f,{header:1,raw:!0,cellDates:!0}),x=h[0],d=h.slice(1),v=f["!cols"]||[],u=0,p=0,E=0,T=1;for(u=0;u<x.length;++u){if(((v[u]||{}).DBF||{}).name){x[u]=v[u].DBF.name,++E;continue}if(x[u]!=null){if(++E,typeof x[u]=="number"&&(x[u]=x[u].toString(10)),typeof x[u]!="string")throw new Error("DBF Invalid column name "+x[u]+" |"+typeof x[u]+"|");if(x.indexOf(x[u])!==u){for(p=0;p<1024;++p)if(x.indexOf(x[u]+"_"+p)==-1){x[u]+="_"+p;break}}}}var g=Ze(f["!ref"]),R=[],L=[],I=[];for(u=0;u<=g.e.c-g.s.c;++u){var F="",N="",P=0,V=[];for(p=0;p<d.length;++p)d[p][u]!=null&&V.push(d[p][u]);if(V.length==0||x[u]==null){R[u]="?";continue}for(p=0;p<V.length;++p){switch(typeof V[p]){case"number":N="B";break;case"string":N="C";break;case"boolean":N="L";break;case"object":N=V[p]instanceof Date?"D":"C";break;default:N="C"}P=Math.max(P,String(V[p]).length),F=F&&F!=N?"C":N}P>250&&(P=250),N=((v[u]||{}).DBF||{}).type,N=="C"&&v[u].DBF.len>P&&(P=v[u].DBF.len),F=="B"&&N=="N"&&(F="N",I[u]=v[u].DBF.dec,P=v[u].DBF.len),L[u]=F=="C"||N=="N"?P:i[F]||0,T+=L[u],R[u]=F}var X=l.next(32);for(X.write_shift(4,318902576),X.write_shift(4,d.length),X.write_shift(2,296+32*E),X.write_shift(2,T),u=0;u<4;++u)X.write_shift(4,0);for(X.write_shift(4,0|(+t[Fe]||3)<<8),u=0,p=0;u<x.length;++u)if(x[u]!=null){var b=l.next(32),ae=(x[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);b.write_shift(1,ae,"sbcs"),b.write_shift(1,R[u]=="?"?"C":R[u],"sbcs"),b.write_shift(4,p),b.write_shift(1,L[u]||i[R[u]]||0),b.write_shift(1,I[u]||0),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(1,0),b.write_shift(4,0),b.write_shift(4,0),p+=L[u]||i[R[u]]||0}var xe=l.next(264);for(xe.write_shift(4,13),u=0;u<65;++u)xe.write_shift(4,0);for(u=0;u<d.length;++u){var ne=l.next(T);for(ne.write_shift(1,0),p=0;p<x.length;++p)if(x[p]!=null)switch(R[p]){case"L":ne.write_shift(1,d[u][p]==null?63:d[u][p]?84:70);break;case"B":ne.write_shift(8,d[u][p]||0,"f");break;case"N":var _e="0";for(typeof d[u][p]=="number"&&(_e=d[u][p].toFixed(I[p]||0)),E=0;E<L[p]-_e.length;++E)ne.write_shift(1,32);ne.write_shift(1,_e,"sbcs");break;case"D":d[u][p]?(ne.write_shift(4,("0000"+d[u][p].getFullYear()).slice(-4),"sbcs"),ne.write_shift(2,("00"+(d[u][p].getMonth()+1)).slice(-2),"sbcs"),ne.write_shift(2,("00"+d[u][p].getDate()).slice(-2),"sbcs")):ne.write_shift(8,"00000000","sbcs");break;case"C":var pe=String(d[u][p]!=null?d[u][p]:"").slice(0,L[p]);for(ne.write_shift(1,pe,"sbcs"),E=0;E<L[p]-pe.length;++E)ne.write_shift(1,32);break}}return l.next(1).write_shift(1,26),l.end()}return{to_workbook:n,to_sheet:a,from_sheet:s}}(),Ef=function(){var e={AA:"\xC0",BA:"\xC1",CA:"\xC2",DA:195,HA:"\xC4",JA:197,AE:"\xC8",BE:"\xC9",CE:"\xCA",HE:"\xCB",AI:"\xCC",BI:"\xCD",CI:"\xCE",HI:"\xCF",AO:"\xD2",BO:"\xD3",CO:"\xD4",DO:213,HO:"\xD6",AU:"\xD9",BU:"\xDA",CU:"\xDB",HU:"\xDC",Aa:"\xE0",Ba:"\xE1",Ca:"\xE2",Da:227,Ha:"\xE4",Ja:229,Ae:"\xE8",Be:"\xE9",Ce:"\xEA",He:"\xEB",Ai:"\xEC",Bi:"\xED",Ci:"\xEE",Hi:"\xEF",Ao:"\xF2",Bo:"\xF3",Co:"\xF4",Do:245,Ho:"\xF6",Au:"\xF9",Bu:"\xFA",Cu:"\xFB",Hu:"\xFC",KC:"\xC7",Kc:"\xE7",q:"\xE6",z:"\u0153",a:"\xC6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+wr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(x,d){var v=e[d];return typeof v=="number"?Je(v):v},a=function(x,d,v){var u=d.charCodeAt(0)-32<<4|v.charCodeAt(0)-48;return u==59?x:Je(u)};e["|"]=254;function n(x,d){switch(d.type){case"base64":return i(hr(x),d);case"binary":return i(x,d);case"buffer":return i(ye&&Q.isBuffer(x)?x.toString("binary"):At(x),d);case"array":return i(oa(x),d)}throw new Error("Unrecognized type "+d.type)}function i(x,d){var v=x.split(/[\n\r]+/),u=-1,p=-1,E=0,T=0,g=[],R=[],L=null,I={},F=[],N=[],P=[],V=0,X;for(+d.codepage>=0&&de(+d.codepage);E!==v.length;++E){V=0;var b=v[E].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),ae=b.replace(/;;/g,"\0").split(";").map(function(D){return D.replace(/\u0000/g,";")}),xe=ae[0],ne;if(b.length>0)switch(xe){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":ae[1].charAt(0)=="P"&&R.push(b.slice(3).replace(/;;/g,";"));break;case"C":var _e=!1,pe=!1,ze=!1,le=!1,ge=-1,j=-1;for(T=1;T<ae.length;++T)switch(ae[T].charAt(0)){case"A":break;case"X":p=parseInt(ae[T].slice(1))-1,pe=!0;break;case"Y":for(u=parseInt(ae[T].slice(1))-1,pe||(p=0),X=g.length;X<=u;++X)g[X]=[];break;case"K":ne=ae[T].slice(1),ne.charAt(0)==='"'?ne=ne.slice(1,ne.length-1):ne==="TRUE"?ne=!0:ne==="FALSE"?ne=!1:isNaN(kt(ne))?isNaN(Oa(ne).getDate())||(ne=dr(ne)):(ne=kt(ne),L!==null&&ca(L)&&(ne=bn(ne))),typeof Ee!="undefined"&&typeof ne=="string"&&(d||{}).type!="string"&&(d||{}).codepage&&(ne=Ee.utils.decode(d.codepage,ne)),_e=!0;break;case"E":le=!0;var C=Ua(ae[T].slice(1),{r:u,c:p});g[u][p]=[g[u][p],C];break;case"S":ze=!0,g[u][p]=[g[u][p],"S5S"];break;case"G":break;case"R":ge=parseInt(ae[T].slice(1))-1;break;case"C":j=parseInt(ae[T].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+b)}if(_e&&(g[u][p]&&g[u][p].length==2?g[u][p][0]=ne:g[u][p]=ne,L=null),ze){if(le)throw new Error("SYLK shared formula cannot have own formula");var U=ge>-1&&g[ge][j];if(!U||!U[1])throw new Error("SYLK shared formula cannot find base");g[u][p][1]=Hf(U[1],{r:u-ge,c:p-j})}break;case"F":var O=0;for(T=1;T<ae.length;++T)switch(ae[T].charAt(0)){case"X":p=parseInt(ae[T].slice(1))-1,++O;break;case"Y":for(u=parseInt(ae[T].slice(1))-1,X=g.length;X<=u;++X)g[X]=[];break;case"M":V=parseInt(ae[T].slice(1))/20;break;case"F":break;case"G":break;case"P":L=R[parseInt(ae[T].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(P=ae[T].slice(1).split(" "),X=parseInt(P[0],10);X<=parseInt(P[1],10);++X)V=parseInt(P[2],10),N[X-1]=V===0?{hidden:!0}:{wch:V},ra(N[X-1]);break;case"C":p=parseInt(ae[T].slice(1))-1,N[p]||(N[p]={});break;case"R":u=parseInt(ae[T].slice(1))-1,F[u]||(F[u]={}),V>0?(F[u].hpt=V,F[u].hpx=Ma(V)):V===0&&(F[u].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+b)}O<1&&(L=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+b)}}return F.length>0&&(I["!rows"]=F),N.length>0&&(I["!cols"]=N),d&&d.sheetRows&&(g=g.slice(0,d.sheetRows)),[g,I]}function s(x,d){var v=n(x,d),u=v[0],p=v[1],E=Ra(u,d);return wr(p).forEach(function(T){E[T]=p[T]}),E}function f(x,d){return qt(s(x,d),d)}function c(x,d,v,u){var p="C;Y"+(v+1)+";X"+(u+1)+";K";switch(x.t){case"n":p+=x.v||0,x.f&&!x.F&&(p+=";E"+ei(x.f,{r:v,c:u}));break;case"b":p+=x.v?"TRUE":"FALSE";break;case"e":p+=x.w||x.v;break;case"d":p+='"'+(x.w||x.v)+'"';break;case"s":p+='"'+x.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return p}function o(x,d){d.forEach(function(v,u){var p="F;W"+(u+1)+" "+(u+1)+" ";v.hidden?p+="0":(typeof v.width=="number"&&!v.wpx&&(v.wpx=cn(v.width)),typeof v.wpx=="number"&&!v.wch&&(v.wch=on(v.wpx)),typeof v.wch=="number"&&(p+=Math.round(v.wch))),p.charAt(p.length-1)!=" "&&x.push(p)})}function l(x,d){d.forEach(function(v,u){var p="F;";v.hidden?p+="M0;":v.hpt?p+="M"+20*v.hpt+";":v.hpx&&(p+="M"+20*ln(v.hpx)+";"),p.length>2&&x.push(p+"R"+(u+1))})}function h(x,d){var v=["ID;PWXL;N;E"],u=[],p=Ze(x["!ref"]),E,T=Array.isArray(x),g=`\r
`;v.push("P;PGeneral"),v.push("F;P0;DG0G8;M255"),x["!cols"]&&o(v,x["!cols"]),x["!rows"]&&l(v,x["!rows"]),v.push("B;Y"+(p.e.r-p.s.r+1)+";X"+(p.e.c-p.s.c+1)+";D"+[p.s.c,p.s.r,p.e.c,p.e.r].join(" "));for(var R=p.s.r;R<=p.e.r;++R)for(var L=p.s.c;L<=p.e.c;++L){var I=Ce({r:R,c:L});E=T?(x[R]||[])[L]:x[I],!(!E||E.v==null&&(!E.f||E.F))&&u.push(c(E,x,R,L,d))}return v.join(g)+g+u.join(g)+g+"E"+g}return{to_workbook:f,to_sheet:s,from_sheet:h}}(),Tf=function(){function e(i,s){switch(s.type){case"base64":return t(hr(i),s);case"binary":return t(i,s);case"buffer":return t(ye&&Q.isBuffer(i)?i.toString("binary"):At(i),s);case"array":return t(oa(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),c=-1,o=-1,l=0,h=[];l!==f.length;++l){if(f[l].trim()==="BOT"){h[++c]=[],o=0;continue}if(!(c<0)){var x=f[l].trim().split(","),d=x[0],v=x[1];++l;for(var u=f[l]||"";(u.match(/["]/g)||[]).length&1&&l<f.length-1;)u+=`
`+f[++l];switch(u=u.trim(),+d){case-1:if(u==="BOT"){h[++c]=[],o=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?h[c][o]=!0:u==="FALSE"?h[c][o]=!1:isNaN(kt(v))?isNaN(Oa(v).getDate())?h[c][o]=v:h[c][o]=dr(v):h[c][o]=kt(v),++o;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),Ie&&u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),h[c][o++]=u!==""?u:null;break}if(u==="EOD")break}}return s&&s.sheetRows&&(h=h.slice(0,s.sheetRows)),h}function r(i,s){return Ra(e(i,s),s)}function a(i,s){return qt(r(i,s),s)}var n=function(){var i=function(c,o,l,h,x){c.push(o),c.push(l+","+h),c.push('"'+x.replace(/"/g,'""')+'"')},s=function(c,o,l,h){c.push(o+","+l),c.push(o==1?'"'+h.replace(/"/g,'""')+'"':h)};return function(c){var o=[],l=Ze(c["!ref"]),h,x=Array.isArray(c);i(o,"TABLE",0,1,"sheetjs"),i(o,"VECTORS",0,l.e.r-l.s.r+1,""),i(o,"TUPLES",0,l.e.c-l.s.c+1,""),i(o,"DATA",0,0,"");for(var d=l.s.r;d<=l.e.r;++d){s(o,-1,0,"BOT");for(var v=l.s.c;v<=l.e.c;++v){var u=Ce({r:d,c:v});if(h=x?(c[d]||[])[v]:c[u],!h){s(o,1,0,"");continue}switch(h.t){case"n":var p=Ie?h.w:h.v;!p&&h.v!=null&&(p=h.v),p==null?Ie&&h.f&&!h.F?s(o,1,0,"="+h.f):s(o,1,0,""):s(o,0,p,"V");break;case"b":s(o,0,h.v?1:0,h.v?"TRUE":"FALSE");break;case"s":s(o,1,0,!Ie||isNaN(h.v)?h.v:'="'+h.v+'"');break;case"d":h.w||(h.w=ft(h.z||Se[14],Rr(dr(h.v)))),Ie?s(o,0,h.w,"V"):s(o,1,0,h.w);break;default:s(o,1,0,"")}}}s(o,-1,0,"EOD");var E=`\r
`,T=o.join(E);return T}}();return{to_workbook:a,to_sheet:r,from_sheet:n}}(),kf=function(){function e(h){return h.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(h){return h.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(h,x){for(var d=h.split(`
`),v=-1,u=-1,p=0,E=[];p!==d.length;++p){var T=d[p].trim().split(":");if(T[0]==="cell"){var g=_r(T[1]);if(E.length<=g.r)for(v=E.length;v<=g.r;++v)E[v]||(E[v]=[]);switch(v=g.r,u=g.c,T[2]){case"t":E[v][u]=e(T[3]);break;case"v":E[v][u]=+T[3];break;case"vtf":var R=T[T.length-1];case"vtc":switch(T[3]){case"nl":E[v][u]=!!+T[4];break;default:E[v][u]=+T[4];break}T[2]=="vtf"&&(E[v][u]=[E[v][u],R])}}}return x&&x.sheetRows&&(E=E.slice(0,x.sheetRows)),E}function a(h,x){return Ra(r(h,x),x)}function n(h,x){return qt(a(h,x),x)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),c="--SocialCalcSpreadsheetControlSave--";function o(h){if(!h||!h["!ref"])return"";for(var x=[],d=[],v,u="",p=et(h["!ref"]),E=Array.isArray(h),T=p.s.r;T<=p.e.r;++T)for(var g=p.s.c;g<=p.e.c;++g)if(u=Ce({r:T,c:g}),v=E?(h[T]||[])[g]:h[u],!(!v||v.v==null||v.t==="z")){switch(d=["cell",u,"t"],v.t){case"s":case"str":d.push(t(v.v));break;case"n":v.f?(d[2]="vtf",d[3]="n",d[4]=v.v,d[5]=t(v.f)):(d[2]="v",d[3]=v.v);break;case"b":d[2]="vt"+(v.f?"f":"c"),d[3]="nl",d[4]=v.v?"1":"0",d[5]=t(v.f||(v.v?"TRUE":"FALSE"));break;case"d":var R=Rr(dr(v.v));d[2]="vtc",d[3]="nd",d[4]=""+R,d[5]=v.w||ft(v.z||Se[14],R);break;case"e":continue}x.push(d.join(":"))}return x.push("sheet:c:"+(p.e.c-p.s.c+1)+":r:"+(p.e.r-p.s.r+1)+":tvf:1"),x.push("valueformat:1:text-wiki"),x.join(`
`)}function l(h){return[i,s,f,s,o(h),c].join(`
`)}return{to_workbook:n,to_sheet:a,from_sheet:l}}(),La=function(){function e(l,h,x,d,v){v.raw?h[x][d]=l:l===""||(l==="TRUE"?h[x][d]=!0:l==="FALSE"?h[x][d]=!1:isNaN(kt(l))?isNaN(Oa(l).getDate())?h[x][d]=l:h[x][d]=dr(l):h[x][d]=kt(l))}function t(l,h){var x=h||{},d=[];if(!l||l.length===0)return d;for(var v=l.split(/[\r\n]/),u=v.length-1;u>=0&&v[u].length===0;)--u;for(var p=10,E=0,T=0;T<=u;++T)E=v[T].indexOf(" "),E==-1?E=v[T].length:E++,p=Math.max(p,E);for(T=0;T<=u;++T){d[T]=[];var g=0;for(e(v[T].slice(0,p).trim(),d,T,g,x),g=1;g<=(v[T].length-p)/10+1;++g)e(v[T].slice(p+(g-1)*10,p+g*10).trim(),d,T,g,x)}return x.sheetRows&&(d=d.slice(0,x.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(l){for(var h={},x=!1,d=0,v=0;d<l.length;++d)(v=l.charCodeAt(d))==34?x=!x:!x&&v in r&&(h[v]=(h[v]||0)+1);v=[];for(d in h)Object.prototype.hasOwnProperty.call(h,d)&&v.push([h[d],d]);if(!v.length){h=a;for(d in h)Object.prototype.hasOwnProperty.call(h,d)&&v.push([h[d],d])}return v.sort(function(u,p){return u[0]-p[0]||a[u[1]]-a[p[1]]}),r[v.pop()[1]]||44}function i(l,h){var x=h||{},d="";he!=null&&x.dense==null&&(x.dense=he);var v=x.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};l.slice(0,4)=="sep="?l.charCodeAt(5)==13&&l.charCodeAt(6)==10?(d=l.charAt(4),l=l.slice(7)):l.charCodeAt(5)==13||l.charCodeAt(5)==10?(d=l.charAt(4),l=l.slice(6)):d=n(l.slice(0,1024)):x&&x.FS?d=x.FS:d=n(l.slice(0,1024));var p=0,E=0,T=0,g=0,R=0,L=d.charCodeAt(0),I=!1,F=0,N=l.charCodeAt(0);l=l.replace(/\r\n/mg,`
`);var P=x.dateNF!=null?wo(x.dateNF):null;function V(){var X=l.slice(g,R),b={};if(X.charAt(0)=='"'&&X.charAt(X.length-1)=='"'&&(X=X.slice(1,-1).replace(/""/g,'"')),X.length===0)b.t="z";else if(x.raw)b.t="s",b.v=X;else if(X.trim().length===0)b.t="s",b.v=X;else if(X.charCodeAt(0)==61)X.charCodeAt(1)==34&&X.charCodeAt(X.length-1)==34?(b.t="s",b.v=X.slice(2,-1).replace(/""/g,'"')):$x(X)?(b.t="n",b.f=X.slice(1)):(b.t="s",b.v=X);else if(X=="TRUE")b.t="b",b.v=!0;else if(X=="FALSE")b.t="b",b.v=!1;else if(!isNaN(T=kt(X)))b.t="n",x.cellText!==!1&&(b.w=X),b.v=T;else if(!isNaN(Oa(X).getDate())||P&&X.match(P)){b.z=x.dateNF||Se[14];var ae=0;P&&X.match(P)&&(X=Eo(X,x.dateNF,X.match(P)||[]),ae=1),x.cellDates?(b.t="d",b.v=dr(X,ae)):(b.t="n",b.v=Rr(dr(X,ae))),x.cellText!==!1&&(b.w=ft(b.z,b.v instanceof Date?Rr(b.v):b.v)),x.cellNF||delete b.z}else b.t="s",b.v=X;if(b.t=="z"||(x.dense?(v[p]||(v[p]=[]),v[p][E]=b):v[Ce({c:E,r:p})]=b),g=R+1,N=l.charCodeAt(g),u.e.c<E&&(u.e.c=E),u.e.r<p&&(u.e.r=p),F==L)++E;else if(E=0,++p,x.sheetRows&&x.sheetRows<=p)return!0}e:for(;R<l.length;++R)switch(F=l.charCodeAt(R)){case 34:N===34&&(I=!I);break;case L:case 10:case 13:if(!I&&V())break e;break;default:break}return R-g>0&&V(),v["!ref"]=Ue(u),v}function s(l,h){return!(h&&h.PRN)||h.FS||l.slice(0,4)=="sep="||l.indexOf("	")>=0||l.indexOf(",")>=0||l.indexOf(";")>=0?i(l,h):Ra(t(l,h),h)}function f(l,h){var x="",d=h.type=="string"?[0,0,0,0]:mi(l,h);switch(h.type){case"base64":x=hr(l);break;case"binary":x=l;break;case"buffer":h.codepage==65001?x=l.toString("utf8"):h.codepage&&typeof Ee!="undefined"?x=Ee.utils.decode(h.codepage,l):x=ye&&Q.isBuffer(l)?l.toString("binary"):At(l);break;case"array":x=oa(l);break;case"string":x=l;break;default:throw new Error("Unrecognized type "+h.type)}return d[0]==239&&d[1]==187&&d[2]==191?x=lr(x.slice(3)):h.type!="string"&&h.type!="buffer"&&h.codepage==65001?x=lr(x):h.type=="binary"&&typeof Ee!="undefined"&&h.codepage&&(x=Ee.utils.decode(h.codepage,Ee.utils.encode(28591,x))),x.slice(0,19)=="socialcalc:version:"?kf.to_sheet(h.type=="string"?x:lr(x),h):s(x,h)}function c(l,h){return qt(f(l,h),h)}function o(l){for(var h=[],x=Ze(l["!ref"]),d,v=Array.isArray(l),u=x.s.r;u<=x.e.r;++u){for(var p=[],E=x.s.c;E<=x.e.c;++E){var T=Ce({r:u,c:E});if(d=v?(l[u]||[])[E]:l[T],!d||d.v==null){p.push("          ");continue}for(var g=(d.w||(Pt(d),d.w)||"").slice(0,10);g.length<10;)g+=" ";p.push(g+(E===0?" ":""))}h.push(p.join(""))}return h.join(`
`)}return{to_workbook:c,to_sheet:f,from_sheet:o}}();function Nh(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Ef.to_workbook(e,r);return r.WTF=a,n}catch(i){if(r.WTF=a,!i.message.match(/SYLK bad record ID/)&&a)throw i;return La.to_workbook(e,t)}}var Ea=function(){function e(C,U,O){if(C){Xr(C,C.l||0);for(var D=O.Enum||ge;C.l<C.length;){var K=C.read_shift(2),ce=D[K]||D[65535],ee=C.read_shift(2),re=C.l+ee,Y=ce.f&&ce.f(C,ee,O);if(C.l=re,U(Y,ce,K))return}}}function t(C,U){switch(U.type){case"base64":return r(ar(hr(C)),U);case"binary":return r(ar(C),U);case"buffer":case"array":return r(C,U)}throw"Unsupported type "+U.type}function r(C,U){if(!C)return C;var O=U||{};he!=null&&O.dense==null&&(O.dense=he);var D=O.dense?[]:{},K="Sheet1",ce="",ee=0,re={},Y=[],He=[],A={s:{r:0,c:0},e:{r:0,c:0}},fr=O.sheetRows||0;if(C[2]==0&&(C[3]==8||C[3]==9)&&C.length>=16&&C[14]==5&&C[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(C[2]==2)O.Enum=ge,e(C,function(ue,yr,Et){switch(Et){case 0:O.vers=ue,ue>=4096&&(O.qpro=!0);break;case 6:A=ue;break;case 204:ue&&(ce=ue);break;case 222:ce=ue;break;case 15:case 51:O.qpro||(ue[1].v=ue[1].v.slice(1));case 13:case 14:case 16:Et==14&&(ue[2]&112)==112&&(ue[2]&15)>1&&(ue[2]&15)<15&&(ue[1].z=O.dateNF||Se[14],O.cellDates&&(ue[1].t="d",ue[1].v=bn(ue[1].v))),O.qpro&&ue[3]>ee&&(D["!ref"]=Ue(A),re[K]=D,Y.push(K),D=O.dense?[]:{},A={s:{r:0,c:0},e:{r:0,c:0}},ee=ue[3],K=ce||"Sheet"+(ee+1),ce="");var Lt=O.dense?(D[ue[0].r]||[])[ue[0].c]:D[Ce(ue[0])];if(Lt){Lt.t=ue[1].t,Lt.v=ue[1].v,ue[1].z!=null&&(Lt.z=ue[1].z),ue[1].f!=null&&(Lt.f=ue[1].f);break}O.dense?(D[ue[0].r]||(D[ue[0].r]=[]),D[ue[0].r][ue[0].c]=ue[1]):D[Ce(ue[0])]=ue[1];break;default:}},O);else if(C[2]==26||C[2]==14)O.Enum=j,C[2]==14&&(O.qpro=!0,C.l=0),e(C,function(ue,yr,Et){switch(Et){case 204:K=ue;break;case 22:ue[1].v=ue[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(ue[3]>ee&&(D["!ref"]=Ue(A),re[K]=D,Y.push(K),D=O.dense?[]:{},A={s:{r:0,c:0},e:{r:0,c:0}},ee=ue[3],K="Sheet"+(ee+1)),fr>0&&ue[0].r>=fr)break;O.dense?(D[ue[0].r]||(D[ue[0].r]=[]),D[ue[0].r][ue[0].c]=ue[1]):D[Ce(ue[0])]=ue[1],A.e.c<ue[0].c&&(A.e.c=ue[0].c),A.e.r<ue[0].r&&(A.e.r=ue[0].r);break;case 27:ue[14e3]&&(He[ue[14e3][0]]=ue[14e3][1]);break;case 1537:He[ue[0]]=ue[1],ue[0]==ee&&(K=ue[1]);break;default:break}},O);else throw new Error("Unrecognized LOTUS BOF "+C[2]);if(D["!ref"]=Ue(A),re[ce||K]=D,Y.push(ce||K),!He.length)return{SheetNames:Y,Sheets:re};for(var $e={},cr=[],Ye=0;Ye<He.length;++Ye)re[Y[Ye]]?(cr.push(He[Ye]||Y[Ye]),$e[He[Ye]]=re[He[Ye]]||re[Y[Ye]]):(cr.push(He[Ye]),$e[He[Ye]]={"!ref":"A1"});return{SheetNames:cr,Sheets:$e}}function a(C,U){var O=U||{};if(+O.codepage>=0&&de(+O.codepage),O.type=="string")throw new Error("Cannot write WK1 to JS string");var D=qr(),K=Ze(C["!ref"]),ce=Array.isArray(C),ee=[];ie(D,0,i(1030)),ie(D,6,c(K));for(var re=Math.min(K.e.r,8191),Y=K.s.r;Y<=re;++Y)for(var He=kr(Y),A=K.s.c;A<=K.e.c;++A){Y===K.s.r&&(ee[A]=pr(A));var fr=ee[A]+He,$e=ce?(C[Y]||[])[A]:C[fr];if(!(!$e||$e.t=="z"))if($e.t=="n")($e.v|0)==$e.v&&$e.v>=-32768&&$e.v<=32767?ie(D,13,d(Y,A,$e.v)):ie(D,14,u(Y,A,$e.v));else{var cr=Pt($e);ie(D,15,h(Y,A,cr.slice(0,239)))}}return ie(D,1),D.end()}function n(C,U){var O=U||{};if(+O.codepage>=0&&de(+O.codepage),O.type=="string")throw new Error("Cannot write WK3 to JS string");var D=qr();ie(D,0,s(C));for(var K=0,ce=0;K<C.SheetNames.length;++K)(C.Sheets[C.SheetNames[K]]||{})["!ref"]&&ie(D,27,le(C.SheetNames[K],ce++));var ee=0;for(K=0;K<C.SheetNames.length;++K){var re=C.Sheets[C.SheetNames[K]];if(!(!re||!re["!ref"])){for(var Y=Ze(re["!ref"]),He=Array.isArray(re),A=[],fr=Math.min(Y.e.r,8191),$e=Y.s.r;$e<=fr;++$e)for(var cr=kr($e),Ye=Y.s.c;Ye<=Y.e.c;++Ye){$e===Y.s.r&&(A[Ye]=pr(Ye));var ue=A[Ye]+cr,yr=He?(re[$e]||[])[Ye]:re[ue];if(!(!yr||yr.t=="z"))if(yr.t=="n")ie(D,23,V($e,Ye,ee,yr.v));else{var Et=Pt(yr);ie(D,22,F($e,Ye,ee,Et.slice(0,239)))}}++ee}}return ie(D,1),D.end()}function i(C){var U=G(2);return U.write_shift(2,C),U}function s(C){var U=G(26);U.write_shift(2,4096),U.write_shift(2,4),U.write_shift(4,0);for(var O=0,D=0,K=0,ce=0;ce<C.SheetNames.length;++ce){var ee=C.SheetNames[ce],re=C.Sheets[ee];if(!(!re||!re["!ref"])){++K;var Y=et(re["!ref"]);O<Y.e.r&&(O=Y.e.r),D<Y.e.c&&(D=Y.e.c)}}return O>8191&&(O=8191),U.write_shift(2,O),U.write_shift(1,K),U.write_shift(1,D),U.write_shift(2,0),U.write_shift(2,0),U.write_shift(1,1),U.write_shift(1,2),U.write_shift(4,0),U.write_shift(4,0),U}function f(C,U,O){var D={s:{c:0,r:0},e:{c:0,r:0}};return U==8&&O.qpro?(D.s.c=C.read_shift(1),C.l++,D.s.r=C.read_shift(2),D.e.c=C.read_shift(1),C.l++,D.e.r=C.read_shift(2),D):(D.s.c=C.read_shift(2),D.s.r=C.read_shift(2),U==12&&O.qpro&&(C.l+=2),D.e.c=C.read_shift(2),D.e.r=C.read_shift(2),U==12&&O.qpro&&(C.l+=2),D.s.c==65535&&(D.s.c=D.e.c=D.s.r=D.e.r=0),D)}function c(C){var U=G(8);return U.write_shift(2,C.s.c),U.write_shift(2,C.s.r),U.write_shift(2,C.e.c),U.write_shift(2,C.e.r),U}function o(C,U,O){var D=[{c:0,r:0},{t:"n",v:0},0,0];return O.qpro&&O.vers!=20768?(D[0].c=C.read_shift(1),D[3]=C.read_shift(1),D[0].r=C.read_shift(2),C.l+=2):(D[2]=C.read_shift(1),D[0].c=C.read_shift(2),D[0].r=C.read_shift(2)),D}function l(C,U,O){var D=C.l+U,K=o(C,U,O);if(K[1].t="s",O.vers==20768){C.l++;var ce=C.read_shift(1);return K[1].v=C.read_shift(ce,"utf8"),K}return O.qpro&&C.l++,K[1].v=C.read_shift(D-C.l,"cstr"),K}function h(C,U,O){var D=G(7+O.length);D.write_shift(1,255),D.write_shift(2,U),D.write_shift(2,C),D.write_shift(1,39);for(var K=0;K<D.length;++K){var ce=O.charCodeAt(K);D.write_shift(1,ce>=128?95:ce)}return D.write_shift(1,0),D}function x(C,U,O){var D=o(C,U,O);return D[1].v=C.read_shift(2,"i"),D}function d(C,U,O){var D=G(7);return D.write_shift(1,255),D.write_shift(2,U),D.write_shift(2,C),D.write_shift(2,O,"i"),D}function v(C,U,O){var D=o(C,U,O);return D[1].v=C.read_shift(8,"f"),D}function u(C,U,O){var D=G(13);return D.write_shift(1,255),D.write_shift(2,U),D.write_shift(2,C),D.write_shift(8,O,"f"),D}function p(C,U,O){var D=C.l+U,K=o(C,U,O);if(K[1].v=C.read_shift(8,"f"),O.qpro)C.l=D;else{var ce=C.read_shift(2);R(C.slice(C.l,C.l+ce),K),C.l+=ce}return K}function E(C,U,O){var D=U&32768;return U&=-32769,U=(D?C:0)+(U>=8192?U-16384:U),(D?"":"$")+(O?pr(U):kr(U))}var T={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function R(C,U){Xr(C,0);for(var O=[],D=0,K="",ce="",ee="",re="";C.l<C.length;){var Y=C[C.l++];switch(Y){case 0:O.push(C.read_shift(8,"f"));break;case 1:ce=E(U[0].c,C.read_shift(2),!0),K=E(U[0].r,C.read_shift(2),!1),O.push(ce+K);break;case 2:{var He=E(U[0].c,C.read_shift(2),!0),A=E(U[0].r,C.read_shift(2),!1);ce=E(U[0].c,C.read_shift(2),!0),K=E(U[0].r,C.read_shift(2),!1),O.push(He+A+":"+ce+K)}break;case 3:if(C.l<C.length){console.error("WK1 premature formula end");return}break;case 4:O.push("("+O.pop()+")");break;case 5:O.push(C.read_shift(2));break;case 6:{for(var fr="";Y=C[C.l++];)fr+=String.fromCharCode(Y);O.push('"'+fr.replace(/"/g,'""')+'"')}break;case 8:O.push("-"+O.pop());break;case 23:O.push("+"+O.pop());break;case 22:O.push("NOT("+O.pop()+")");break;case 20:case 21:re=O.pop(),ee=O.pop(),O.push(["AND","OR"][Y-20]+"("+ee+","+re+")");break;default:if(Y<32&&g[Y])re=O.pop(),ee=O.pop(),O.push(ee+g[Y]+re);else if(T[Y]){if(D=T[Y][1],D==69&&(D=C[C.l++]),D>O.length){console.error("WK1 bad formula parse 0x"+Y.toString(16)+":|"+O.join("|")+"|");return}var $e=O.slice(-D);O.length-=D,O.push(T[Y][0]+"("+$e.join(",")+")")}else return Y<=7?console.error("WK1 invalid opcode "+Y.toString(16)):Y<=24?console.error("WK1 unsupported op "+Y.toString(16)):Y<=30?console.error("WK1 invalid opcode "+Y.toString(16)):Y<=115?console.error("WK1 unsupported function opcode "+Y.toString(16)):console.error("WK1 unrecognized opcode "+Y.toString(16))}}O.length==1?U[1].f=""+O[0]:console.error("WK1 bad formula parse |"+O.join("|")+"|")}function L(C){var U=[{c:0,r:0},{t:"n",v:0},0];return U[0].r=C.read_shift(2),U[3]=C[C.l++],U[0].c=C[C.l++],U}function I(C,U){var O=L(C,U);return O[1].t="s",O[1].v=C.read_shift(U-4,"cstr"),O}function F(C,U,O,D){var K=G(6+D.length);K.write_shift(2,C),K.write_shift(1,O),K.write_shift(1,U),K.write_shift(1,39);for(var ce=0;ce<D.length;++ce){var ee=D.charCodeAt(ce);K.write_shift(1,ee>=128?95:ee)}return K.write_shift(1,0),K}function N(C,U){var O=L(C,U);O[1].v=C.read_shift(2);var D=O[1].v>>1;if(O[1].v&1)switch(D&7){case 0:D=(D>>3)*5e3;break;case 1:D=(D>>3)*500;break;case 2:D=(D>>3)/20;break;case 3:D=(D>>3)/200;break;case 4:D=(D>>3)/2e3;break;case 5:D=(D>>3)/2e4;break;case 6:D=(D>>3)/16;break;case 7:D=(D>>3)/64;break}return O[1].v=D,O}function P(C,U){var O=L(C,U),D=C.read_shift(4),K=C.read_shift(4),ce=C.read_shift(2);if(ce==65535)return D===0&&K===3221225472?(O[1].t="e",O[1].v=15):D===0&&K===3489660928?(O[1].t="e",O[1].v=42):O[1].v=0,O;var ee=ce&32768;return ce=(ce&32767)-16446,O[1].v=(1-ee*2)*(K*Math.pow(2,ce+32)+D*Math.pow(2,ce)),O}function V(C,U,O,D){var K=G(14);if(K.write_shift(2,C),K.write_shift(1,O),K.write_shift(1,U),D==0)return K.write_shift(4,0),K.write_shift(4,0),K.write_shift(2,65535),K;var ce=0,ee=0,re=0,Y=0;return D<0&&(ce=1,D=-D),ee=Math.log2(D)|0,D/=Math.pow(2,ee-31),Y=D>>>0,Y&2147483648||(D/=2,++ee,Y=D>>>0),D-=Y,Y|=2147483648,Y>>>=0,D*=Math.pow(2,32),re=D>>>0,K.write_shift(4,re),K.write_shift(4,Y),ee+=16383+(ce?32768:0),K.write_shift(2,ee),K}function X(C,U){var O=P(C,14);return C.l+=U-14,O}function b(C,U){var O=L(C,U),D=C.read_shift(4);return O[1].v=D>>6,O}function ae(C,U){var O=L(C,U),D=C.read_shift(8,"f");return O[1].v=D,O}function xe(C,U){var O=ae(C,14);return C.l+=U-10,O}function ne(C,U){return C[C.l+U-1]==0?C.read_shift(U,"cstr"):""}function _e(C,U){var O=C[C.l++];O>U-1&&(O=U-1);for(var D="";D.length<O;)D+=String.fromCharCode(C[C.l++]);return D}function pe(C,U,O){if(!(!O.qpro||U<21)){var D=C.read_shift(1);C.l+=17,C.l+=1,C.l+=2;var K=C.read_shift(U-21,"cstr");return[D,K]}}function ze(C,U){for(var O={},D=C.l+U;C.l<D;){var K=C.read_shift(2);if(K==14e3){for(O[K]=[0,""],O[K][0]=C.read_shift(2);C[C.l];)O[K][1]+=String.fromCharCode(C[C.l]),C.l++;C.l++}}return O}function le(C,U){var O=G(5+C.length);O.write_shift(2,14e3),O.write_shift(2,U);for(var D=0;D<C.length;++D){var K=C.charCodeAt(D);O[O.l++]=K>127?95:K}return O[O.l++]=0,O}var ge={0:{n:"BOF",f:Or},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:x},14:{n:"NUMBER",f:v},15:{n:"LABEL",f:l},16:{n:"FORMULA",f:p},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:l},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ne},222:{n:"SHEETNAMELP",f:_e},65535:{n:""}},j={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:I},23:{n:"NUMBER17",f:P},24:{n:"NUMBER18",f:N},25:{n:"FORMULA19",f:X},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:ze},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:b},38:{n:"??"},39:{n:"NUMBER27",f:ae},40:{n:"FORMULA28",f:xe},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ne},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:pe},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:t}}();function bh(e){var t={},r=e.match(Gr),a=0,n=!1;if(r)for(;a!=r.length;++a){var i=Pe(r[a]);switch(i[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!i.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(i.val=="1")break;t.cp=Ge[parseInt(i.val,10)];break;case"<outline":if(!i.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=i.val;break;case"<sz":t.sz=i.val;break;case"<strike":if(!i.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!i.val)break;switch(i.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(i.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(i.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":i.rgb&&(t.color=i.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=i.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=i.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(i[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+i[0])}}return t}var Lh=function(){var e=Za("t"),t=Za("rPr");function r(i){var s=i.match(e);if(!s)return{t:"s",v:""};var f={t:"s",v:qe(s[1])},c=i.match(t);return c&&(f.s=bh(c[1])),f}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(s){return s.replace(a,"").split(n).map(r).filter(function(f){return f.v})}}(),Mh=function(){var t=/(\r\n|\n)/g;function r(n,i,s){var f=[];n.u&&f.push("text-decoration: underline;"),n.uval&&f.push("text-underline-style:"+n.uval+";"),n.sz&&f.push("font-size:"+n.sz+"pt;"),n.outline&&f.push("text-effect: outline;"),n.shadow&&f.push("text-shadow: auto;"),i.push('<span style="'+f.join("")+'">'),n.b&&(i.push("<b>"),s.push("</b>")),n.i&&(i.push("<i>"),s.push("</i>")),n.strike&&(i.push("<s>"),s.push("</s>"));var c=n.valign||"";return c=="superscript"||c=="super"?c="sup":c=="subscript"&&(c="sub"),c!=""&&(i.push("<"+c+">"),s.push("</"+c+">")),s.push("</span>"),n}function a(n){var i=[[],n.v,[]];return n.v?(n.s&&r(n.s,i[0],i[2]),i[0].join("")+i[1].replace(t,"<br/>")+i[2].join("")):""}return function(i){return i.map(a).join("")}}(),Bh=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Uh=/<(?:\w+:)?r>/,Wh=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function j0(e,t){var r=t?t.cellHTML:!0,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=qe(lr(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=lr(e),r&&(a.h=m0(a.t))):e.match(Uh)&&(a.r=lr(e),a.t=qe(lr((e.replace(Wh,"").match(Bh)||[]).join("").replace(Gr,""))),r&&(a.h=Mh(Lh(a.r)))),a):{t:""}}var Hh=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Vh=/<(?:\w+:)?(?:si|sstItem)>/g,Xh=/<\/(?:\w+:)?(?:si|sstItem)>/;function Gh(e,t){var r=[],a="";if(!e)return r;var n=e.match(Hh);if(n){a=n[2].replace(Vh,"").split(Xh);for(var i=0;i!=a.length;++i){var s=j0(a[i].trim(),t);s!=null&&(r[r.length]=s)}n=Pe(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}var zh=/^\s|\s$|[\t\n\r]/;function Sf(e,t){if(!t.bookSST)return"";var r=[Tr];r[r.length]=te("sst",null,{xmlns:Qt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(e[a]!=null){var n=e[a],i="<si>";n.r?i+=n.r:(i+="<t",n.t||(n.t=""),n.t.match(zh)&&(i+=' xml:space="preserve"'),i+=">"+sr(n.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Kh(e){return[e.read_shift(4),e.read_shift(4)]}function jh(e,t){var r=[],a=!1;return Ht(e,function(i,s,f){switch(f){case 159:r.Count=i[0],r.Unique=i[1];break;case 19:r.push(i);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(s.T,!a||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}),r}function $h(e,t){return t||(t=G(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Yh=il;function Zh(e){var t=qr();J(t,159,$h(e));for(var r=0;r<e.length;++r)J(t,19,Yh(e[r]));return J(t,160),t.end()}function Ff(e){if(typeof Ee!="undefined")return Ee.utils.encode(Fe,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function Xt(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function Jh(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=Xt(e,4),t.U=Xt(e,4),t.W=Xt(e,4),t}function Qh(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),i=[];n-- >0;)i.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=i,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function qh(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(Qh(e));return t}function e1(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function r1(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=Xt(e,4),t.U=Xt(e,4),t.W=Xt(e,4),t}function t1(e){var t=r1(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function yf(e,t){var r=e.l+t,a={};a.Flags=e.read_shift(4)&63,e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function Af(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function a1(e){var t=Xt(e);switch(t.Minor){case 2:return[t.Minor,n1(e,t)];case 3:return[t.Minor,i1(e,t)];case 4:return[t.Minor,s1(e,t)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function n1(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),a=yf(e,r),n=Af(e,e.length-e.l);return{t:"Std",h:a,v:n}}function i1(){throw new Error("File is password-protected: ECMA-376 Extensible")}function s1(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(Gr,function(i){var s=Pe(i);switch(Ot(s[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(f){a[f]=s[f]});break;case"<dataIntegrity":a.encryptedHmacKey=s.encryptedHmacKey,a.encryptedHmacValue=s.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=s.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(s);break;default:throw s[0]}}),a}function f1(e,t){var r={},a=r.EncryptionVersionInfo=Xt(e,4);if(t-=4,a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=yf(e,n),t-=n,r.EncryptionVerifier=Af(e,t),r}function c1(e){var t={},r=t.EncryptionVersionInfo=Xt(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function $0(e){var t=0,r,a=Ff(e),n=a.length+1,i,s,f,c,o;for(r=br(n),r[0]=a.length,i=1;i!=n;++i)r[i]=a[i-1];for(i=n-1;i>=0;--i)s=r[i],f=t&16384?1:0,c=t<<1&32767,o=f|c,t=o^s;return t^52811}var Cf=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(s){return(s/2|s*128)&255},n=function(s,f){return a(s^f)},i=function(s){for(var f=t[s.length-1],c=104,o=s.length-1;o>=0;--o)for(var l=s[o],h=0;h!=7;++h)l&64&&(f^=r[c]),l*=2,--c;return f};return function(s){for(var f=Ff(s),c=i(f),o=f.length,l=br(16),h=0;h!=16;++h)l[h]=0;var x,d,v;for((o&1)===1&&(x=c>>8,l[o]=n(e[0],x),--o,x=c&255,d=f[f.length-1],l[o]=n(d,x));o>0;)--o,x=c>>8,l[o]=n(f[o],x),--o,x=c&255,l[o]=n(f[o],x);for(o=15,v=15-f.length;v>0;)x=c>>8,l[o]=n(e[v],x),--o,--v,x=c&255,l[o]=n(f[o],x),--o,--v;return l}}(),o1=function(e,t,r,a,n){n||(n=t),a||(a=Cf(e));var i,s;for(i=0;i!=t.length;++i)s=t[i],s^=a[r],s=(s>>5|s<<3)&255,n[i]=s,++r;return[n,r,a]},l1=function(e){var t=0,r=Cf(e);return function(a){var n=o1("",a,t,r);return t=n[1],n[0]}};function u1(e,t,r,a){var n={key:Or(e),verificationBytes:Or(e)};return r.password&&(n.verifier=$0(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=l1(r.password)),n}function h1(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,a.Info===1?a.Data=c1(e,t):a.Data=f1(e,t),a}function x1(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?h1(e,t-2,a):u1(e,r.biff>=8?t:t-2,r,a),a}var Df=function(){function e(n,i){switch(i.type){case"base64":return t(hr(n),i);case"binary":return t(n,i);case"buffer":return t(ye&&Q.isBuffer(n)?n.toString("binary"):At(n),i);case"array":return t(oa(n),i)}throw new Error("Unrecognized type "+i.type)}function t(n,i){var s=i||{},f=s.dense?[]:{},c=n.match(/\\trowd.*?\\row\b/g);if(!c.length)throw new Error("RTF missing table");var o={s:{c:0,r:0},e:{c:0,r:c.length-1}};return c.forEach(function(l,h){Array.isArray(f)&&(f[h]=[]);for(var x=/\\\w+\b/g,d=0,v,u=-1;v=x.exec(l);){switch(v[0]){case"\\cell":var p=l.slice(d,x.lastIndex-v[0].length);if(p[0]==" "&&(p=p.slice(1)),++u,p.length){var E={v:p,t:"s"};Array.isArray(f)?f[h][u]=E:f[Ce({r:h,c:u})]=E}break}d=x.lastIndex}u>o.e.c&&(o.e.c=u)}),f["!ref"]=Ue(o),f}function r(n,i){return qt(e(n,i),i)}function a(n){for(var i=["{\\rtf1\\ansi"],s=Ze(n["!ref"]),f,c=Array.isArray(n),o=s.s.r;o<=s.e.r;++o){i.push("\\trowd\\trautofit1");for(var l=s.s.c;l<=s.e.c;++l)i.push("\\cellx"+(l+1));for(i.push("\\pard\\intbl"),l=s.s.c;l<=s.e.c;++l){var h=Ce({r:o,c:l});f=c?(n[o]||[])[l]:n[h],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(Pt(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:a}}();function d1(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function fn(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function v1(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),i=Math.min(t,r,a),s=n-i;if(s===0)return[0,0,t];var f=0,c=0,o=n+i;switch(c=s/(o>1?2-o:o),n){case t:f=((r-a)/s+6)%6;break;case r:f=(a-t)/s+2;break;case a:f=(t-r)/s+4;break}return[f/6,c,o/2]}function p1(e){var t=e[0],r=e[1],a=e[2],n=r*2*(a<.5?a:1-a),i=a-n/2,s=[i,i,i],f=6*t,c;if(r!==0)switch(f|0){case 0:case 6:c=n*f,s[0]+=n,s[1]+=c;break;case 1:c=n*(2-f),s[0]+=c,s[1]+=n;break;case 2:c=n*(f-2),s[1]+=n,s[2]+=c;break;case 3:c=n*(4-f),s[1]+=c,s[2]+=n;break;case 4:c=n*(f-4),s[2]+=n,s[0]+=c;break;case 5:c=n*(6-f),s[2]+=c,s[0]+=n;break}for(var o=0;o!=3;++o)s[o]=Math.round(s[o]*255);return s}function Yn(e,t){if(t===0)return e;var r=v1(d1(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),fn(p1(r))}var Of=6,m1=15,g1=1,Yr=Of;function cn(e){return Math.floor((e+Math.round(128/Yr)/256)*Yr)}function on(e){return Math.floor((e-5)/Yr*100+.5)/100}function Zn(e){return Math.round((e*Yr+5)/Yr*256)/256}function Y0(e){return Zn(on(cn(e)))}function Z0(e){var t=Math.abs(e-Y0(e)),r=Yr;if(t>.005)for(Yr=g1;Yr<m1;++Yr)Math.abs(e-Y0(e))<=t&&(t=Math.abs(e-Y0(e)),r=Yr);Yr=r}function ra(e){e.width?(e.wpx=cn(e.width),e.wch=on(e.wpx),e.MDW=Yr):e.wpx?(e.wch=on(e.wpx),e.width=Zn(e.wch),e.MDW=Yr):typeof e.wch=="number"&&(e.width=Zn(e.wch),e.wpx=cn(e.width),e.MDW=Yr),e.customWidth&&delete e.customWidth}var _1=96,If=_1;function ln(e){return e*96/If}function Ma(e){return e*If/96}var w1={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function E1(e,t,r,a){t.Borders=[];var n={},i=!1;(e[0].match(Gr)||[]).forEach(function(s){var f=Pe(s);switch(Ot(f[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},f.diagonalUp&&(n.diagonalUp=vr(f.diagonalUp)),f.diagonalDown&&(n.diagonalDown=vr(f.diagonalDown)),t.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in borders")}})}function T1(e,t,r,a){t.Fills=[];var n={},i=!1;(e[0].match(Gr)||[]).forEach(function(s){var f=Pe(s);switch(Ot(f[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":f.patternType&&(n.patternType=f.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),f.indexed&&(n.bgColor.indexed=parseInt(f.indexed,10)),f.theme&&(n.bgColor.theme=parseInt(f.theme,10)),f.tint&&(n.bgColor.tint=parseFloat(f.tint)),f.rgb&&(n.bgColor.rgb=f.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),f.theme&&(n.fgColor.theme=parseInt(f.theme,10)),f.tint&&(n.fgColor.tint=parseFloat(f.tint)),f.rgb!=null&&(n.fgColor.rgb=f.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fills")}})}function k1(e,t,r,a){t.Fonts=[];var n={},i=!1;(e[0].match(Gr)||[]).forEach(function(s){var f=Pe(s);switch(Ot(f[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":f.val&&(n.name=lr(f.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=f.val?vr(f.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=f.val?vr(f.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(f.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=f.val?vr(f.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=f.val?vr(f.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=f.val?vr(f.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=f.val?vr(f.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=f.val?vr(f.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":f.val&&(n.sz=+f.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":f.val&&(n.vertAlign=f.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":f.val&&(n.family=parseInt(f.val,10));break;case"<family/>":case"</family>":break;case"<scheme":f.val&&(n.scheme=f.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(f.val=="1")break;f.codepage=Ge[parseInt(f.val,10)];break;case"<color":if(n.color||(n.color={}),f.auto&&(n.color.auto=vr(f.auto)),f.rgb)n.color.rgb=f.rgb.slice(-6);else if(f.indexed){n.color.index=parseInt(f.indexed,10);var c=pa[n.color.index];n.color.index==81&&(c=pa[1]),c||(c=pa[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else f.theme&&(n.color.theme=parseInt(f.theme,10),f.tint&&(n.color.tint=parseFloat(f.tint)),f.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=Yn(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":i=!0;break;case"</AlternateContent>":i=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fonts")}})}function S1(e,t,r){t.NumberFmt=[];for(var a=wr(Se),n=0;n<a.length;++n)t.NumberFmt[a[n]]=Se[a[n]];var i=e[0].match(Gr);if(i)for(n=0;n<i.length;++n){var s=Pe(i[n]);switch(Ot(s[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var f=qe(lr(s.formatCode)),c=parseInt(s.numFmtId,10);if(t.NumberFmt[c]=f,c>0){if(c>392){for(c=392;c>60&&t.NumberFmt[c]!=null;--c);t.NumberFmt[c]=f}Dt(f,c)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+s[0]+" in numFmts")}}}function F1(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)e[a]!=null&&(t[t.length]=te("numFmt",null,{numFmtId:a,formatCode:sr(e[a])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=te("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var Jn=["numFmtId","fillId","fontId","borderId","xfId"],Qn=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function y1(e,t,r){t.CellXf=[];var a,n=!1;(e[0].match(Gr)||[]).forEach(function(i){var s=Pe(i),f=0;switch(Ot(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=s,delete a[0],f=0;f<Jn.length;++f)a[Jn[f]]&&(a[Jn[f]]=parseInt(a[Jn[f]],10));for(f=0;f<Qn.length;++f)a[Qn[f]]&&(a[Qn[f]]=vr(a[Qn[f]]));if(t.NumberFmt&&a.numFmtId>392){for(f=392;f>60;--f)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[f]){a.numFmtId=f;break}}t.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var c={};s.vertical&&(c.vertical=s.vertical),s.horizontal&&(c.horizontal=s.horizontal),s.textRotation!=null&&(c.textRotation=s.textRotation),s.indent&&(c.indent=s.indent),s.wrapText&&(c.wrapText=vr(s.wrapText)),a.alignment=c;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}})}function A1(e){var t=[];return t[t.length]=te("cellXfs",null),e.forEach(function(r){t[t.length]=te("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=te("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var C1=function(){var t=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,a=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,n=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,i=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(f,c,o){var l={};if(!f)return l;f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var h;return(h=f.match(t))&&S1(h,l,o),(h=f.match(n))&&k1(h,l,c,o),(h=f.match(a))&&T1(h,l,c,o),(h=f.match(i))&&E1(h,l,c,o),(h=f.match(r))&&y1(h,l,o),l}}();function Pf(e,t){var r=[Tr,te("styleSheet",null,{xmlns:Qt[0],"xmlns:vt":Dr.vt})],a;return e.SSF&&(a=F1(e.SSF))!=null&&(r[r.length]=a),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(a=A1(t.cellXfs))&&(r[r.length]=a),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function D1(e,t){var r=e.read_shift(2),a=Kr(e,t-2);return[r,a]}function O1(e,t,r){r||(r=G(6+4*t.length)),r.write_shift(2,e),Br(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),a}function I1(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=hl(e,2,r);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var i=e.read_shift(2);switch(i===700&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(a.underline=s);var f=e.read_shift(1);f>0&&(a.family=f);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=ul(e,8),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=Kr(e,t-21),a}function P1(e,t){t||(t=G(25+4*32)),t.write_shift(2,e.sz*20),xl(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Wn(e.color,t);var a=0;return e.scheme=="major"&&(a=1),e.scheme=="minor"&&(a=2),t.write_shift(1,a),Br(e.name,t),t.length>t.l?t.slice(0,t.l):t}var R1=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],J0,N1=zr;function Rf(e,t){t||(t=G(4*3+8*7+16*1)),J0||(J0=Pn(R1));var r=J0[e.patternType];r==null&&(r=40),t.write_shift(4,r);var a=0;if(r!=40)for(Wn({auto:1},t),Wn({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function b1(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}function Nf(e,t,r){r||(r=G(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var a=0;return r.write_shift(1,a),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function un(e,t){return t||(t=G(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var L1=zr;function M1(e,t){return t||(t=G(51)),t.write_shift(1,0),un(null,t),un(null,t),un(null,t),un(null,t),un(null,t),t.length>t.l?t.slice(0,t.l):t}function B1(e,t){return t||(t=G(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),Un(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function U1(e,t,r){var a=G(2052);return a.write_shift(4,e),Un(t,a),Un(r,a),a.length>a.l?a.slice(0,a.l):a}function W1(e,t,r){var a={};a.NumberFmt=[];for(var n in Se)a.NumberFmt[n]=Se[n];a.CellXf=[],a.Fonts=[];var i=[],s=!1;return Ht(e,function(c,o,l){switch(l){case 44:a.NumberFmt[c[0]]=c[1],Dt(c[1],c[0]);break;case 43:a.Fonts.push(c),c.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(c.color.rgb=Yn(t.themeElements.clrScheme[c.color.theme].rgb,c.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:i[i.length-1]==617&&a.CellXf.push(c);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:i.push(l),s=!0;break;case 38:i.pop(),s=!1;break;default:if(o.T>0)i.push(l);else if(o.T<0)i.pop();else if(!s||r.WTF&&i[i.length-1]!=37)throw new Error("Unexpected record 0x"+l.toString(16))}}),a}function H1(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&++r}),r!=0&&(J(e,615,St(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&J(e,44,O1(n,t[n]))}),J(e,616))}}function V1(e){var t=1;t!=0&&(J(e,611,St(t)),J(e,43,P1({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),J(e,612))}function X1(e){var t=2;t!=0&&(J(e,603,St(t)),J(e,45,Rf({patternType:"none"})),J(e,45,Rf({patternType:"gray125"})),J(e,604))}function G1(e){var t=1;t!=0&&(J(e,613,St(t)),J(e,46,M1({})),J(e,614))}function z1(e){var t=1;J(e,626,St(t)),J(e,47,Nf({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),J(e,627)}function K1(e,t){J(e,617,St(t.length)),t.forEach(function(r){J(e,47,Nf(r,0))}),J(e,618)}function j1(e){var t=1;J(e,619,St(t)),J(e,48,B1({xfId:0,builtinId:0,name:"Normal"})),J(e,620)}function $1(e){var t=0;J(e,505,St(t)),J(e,506)}function Y1(e){var t=0;J(e,508,U1(t,"TableStyleMedium9","PivotStyleMedium4")),J(e,509)}function o_(){}function Z1(e,t){var r=qr();return J(r,278),H1(r,e.SSF),V1(r,e),X1(r,e),G1(r,e),z1(r,e),K1(r,t.cellXfs),j1(r,e),$1(r,e),Y1(r,e),J(r,279),r.end()}var J1=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Q1(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(Gr)||[]).forEach(function(n){var i=Pe(n);switch(i[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=i.val;break;case"<a:sysClr":a.rgb=i.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":i[0].charAt(1)==="/"?(t.themeElements.clrScheme[J1.indexOf(i[0])]=a,a={}):a.name=i[0].slice(3,i[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+i[0]+" in clrScheme")}})}function q1(){}function ex(){}var rx=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,tx=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,ax=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function nx(e,t,r){t.themeElements={};var a;[["clrScheme",rx,Q1],["fontScheme",tx,q1],["fmtScheme",ax,ex]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)})}var ix=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function bf(e,t){(!e||e.length===0)&&(e=Q0());var r,a={};if(!(r=e.match(ix)))throw new Error("themeElements not found in theme");return nx(r[0],a,t),a.raw=e,a}function Q0(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Tr];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function sx(e,t,r){var a=e.l+t,n=e.read_shift(4);if(n!==124226){if(!r.cellStyles){e.l=a;return}var i=e.slice(e.l);e.l=a;var s;try{s=Qi(i,{type:"array"})}catch(c){return}var f=ct(s,"theme/theme/theme1.xml",!0);if(f)return bf(f,r)}}function fx(e){return e.read_shift(4)}function cx(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=ox(e,4);break;case 2:t.xclrValue=nf(e,4);break;case 3:t.xclrValue=fx(e,4);break;case 4:e.l+=4;break}return e.l+=8,t}function ox(e,t){return zr(e,t)}function lx(e,t){return zr(e,t)}function ux(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=cx(e,r);break;case 6:a[1]=lx(e,r);break;case 14:case 15:a[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function hx(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),i=[];n-- >0;)i.push(ux(e,r-e.l));return{ixfe:a,ext:i}}function xx(e,t){t.forEach(function(r){switch(r[0]){case 4:break;case 5:break;case 6:break;case 7:break;case 8:break;case 9:break;case 10:break;case 11:break;case 13:break;case 14:break;case 15:break}})}function dx(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Kr(e,t-8)}}function vx(e){var t=G(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),Br(e.name,t),t.slice(0,t.l)}function px(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function mx(e){var t=G(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function gx(e,t){var r=G(8+2*t.length);return r.write_shift(4,e),Br(t,r),r.slice(0,r.l)}function _x(e){return e.l+=4,e.read_shift(4)!=0}function wx(e,t){var r=G(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function Ex(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},i=[],s=!1,f=2;return Ht(e,function(c,o,l){switch(l){case 335:a.Types.push({name:c.name});break;case 51:c.forEach(function(h){f==1?a.Cell.push({type:a.Types[h[0]-1].name,index:h[1]}):f==0&&a.Value.push({type:a.Types[h[0]-1].name,index:h[1]})});break;case 337:f=c?1:0;break;case 338:f=2;break;case 35:i.push(l),s=!0;break;case 36:i.pop(),s=!1;break;default:if(!o.T){if(!s||n.WTF&&i[i.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}}),a}function Tx(){var e=qr();return J(e,332),J(e,334,St(1)),J(e,335,vx({name:"XLDAPR",version:12e4,flags:3496657072})),J(e,336),J(e,339,gx(1,"XLDAPR")),J(e,52),J(e,35,St(514)),J(e,4096,St(0)),J(e,4097,mt(1)),J(e,36),J(e,53),J(e,340),J(e,337,wx(1,!0)),J(e,51,mx([[1,0]])),J(e,338),J(e,333),e.end()}function kx(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=!1,i=2,s;return e.replace(Gr,function(f){var c=Pe(f);switch(Ot(c[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:c.name});break;case"</metadataType>":break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==c.name&&(s=a.Types[o]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":i==1?a.Cell.push({type:a.Types[c.t-1].name,index:+c.v}):i==0&&a.Value.push({type:a.Types[c.t-1].name,index:+c.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<rvb":if(!s)break;s.offsets||(s.offsets=[]),s.offsets.push(+c.i);break;default:if(!n&&r.WTF)throw new Error("unrecognized "+c[0]+" in metadata")}return f}),a}function Lf(){var e=[Tr];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Sx(e){var t=[];if(!e)return t;var r=1;return(e.match(Gr)||[]).forEach(function(a){var n=Pe(a);switch(n[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete n[0],n.i?r=n.i:n.i=r,t.push(n);break}}),t}function Fx(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ce(r);var a=e.read_shift(1);return a&2&&(t.l="1"),a&8&&(t.a="1"),t}function yx(e,t,r){var a=[],n=!1;return Ht(e,function(s,f,c){switch(c){case 63:a.push(s);break;default:if(!f.T){if(!n||r.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}}),a}function l_(){}function Ax(e,t,r,a){if(!e)return e;var n=a||{},i=!1,s=!1;Ht(e,function(c,o,l){if(!s)switch(l){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:i=!0;break;case 36:i=!1;break;default:if(!o.T){if(!i||n.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}},n)}function Cx(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}var Ba=1024;function Mf(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[te("xml",null,{"xmlns:v":ot.v,"xmlns:o":ot.o,"xmlns:x":ot.x,"xmlns:mv":ot.mv}).replace(/\/>/,">"),te("o:shapelayout",te("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),te("v:shapetype",[te("v:stroke",null,{joinstyle:"miter"}),te("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];Ba<e*1e3;)Ba+=1e3;return t.forEach(function(i){var s=_r(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var c=f.type=="gradient"?te("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,o=te("v:fill",c,f),l={on:"t",obscured:"t"};++Ba,n=n.concat(["<v:shape"+Ja({id:"_x0000_s"+Ba,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",o,te("v:shadow",null,l),te("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Vr("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),Vr("x:AutoFill","False"),Vr("x:Row",String(s.r)),Vr("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function Bf(e,t,r,a){var n=Array.isArray(e),i;t.forEach(function(s){var f=_r(s.ref);if(n?(e[f.r]||(e[f.r]=[]),i=e[f.r][f.c]):i=e[s.ref],!i){i={t:"z"},n?e[f.r][f.c]=i:e[s.ref]=i;var c=Ze(e["!ref"]||"BDWGO1000001:A1");c.s.r>f.r&&(c.s.r=f.r),c.e.r<f.r&&(c.e.r=f.r),c.s.c>f.c&&(c.s.c=f.c),c.e.c<f.c&&(c.e.c=f.c);var o=Ue(c);o!==e["!ref"]&&(e["!ref"]=o)}i.c||(i.c=[]);var l={a:s.author,t:s.t,r:s.r,T:r};s.h&&(l.h=s.h);for(var h=i.c.length-1;h>=0;--h){if(!r&&i.c[h].T)return;r&&!i.c[h].T&&i.c.splice(h,1)}if(r&&a){for(h=0;h<a.length;++h)if(l.a==a[h].id){l.a=a[h].name||l.a;break}}i.c.push(l)})}function Dx(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?author[^>]*>(.*)/);f&&r.push(f[1])}});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return i&&i[1]&&i[1].split(/<\/\w*:?comment>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?comment[^>]*>/);if(f){var c=Pe(f[0]),o={author:c.authorId&&r[c.authorId]||"sheetjsghost",ref:c.ref,guid:c.guid},l=_r(c.ref);if(!(t.sheetRows&&t.sheetRows<=l.r)){var h=s.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),x=!!h&&!!h[1]&&j0(h[1])||{r:"",t:"",h:""};o.r=x.r,x.r=="<t></t>"&&(x.t=x.h=""),o.t=(x.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(o.h=x.h),a.push(o)}}}}),a}function Uf(e){var t=[Tr,te("comments",null,{xmlns:Qt[0]})],r=[];return t.push("<authors>"),e.forEach(function(a){a[1].forEach(function(n){var i=sr(n.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),n.T&&n.ID&&r.indexOf("tc="+n.ID)==-1&&(r.push("tc="+n.ID),t.push("<author>tc="+n.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(a){var n=0,i=[];if(a[1][0]&&a[1][0].T&&a[1][0].ID?n=r.indexOf("tc="+a[1][0].ID):a[1].forEach(function(c){c.a&&(n=r.indexOf(sr(c.a))),i.push(c.t||"")}),t.push('<comment ref="'+a[0]+'" authorId="'+n+'"><text>'),i.length<=1)t.push(Vr("t",sr(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(Vr("t",sr(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Ox(e,t){var r=[],a=!1,n={},i=0;return e.replace(Gr,function(f,c){var o=Pe(f);switch(Ot(o[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:o.personId,guid:o.id,ref:o.ref,T:1};break;case"</threadedComment>":n.t!=null&&r.push(n);break;case"<text>":case"<text":i=c+f.length;break;case"</text>":n.t=e.slice(i,c).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+o[0]+" in threaded comments")}return f}),r}function Ix(e,t,r){var a=[Tr,te("ThreadedComments",null,{xmlns:Dr.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(n){var i="";(n[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var c={ref:n[0],id:"{54EE7951-**************-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=c.id:c.parentId=i,s.ID=c.id,s.a&&(c.personId="{54EE7950-**************-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(te("threadedComment",Vr("text",s.t||""),c))})}),a.push("</ThreadedComments>"),a.join("")}function Px(e,t){var r=[],a=!1;return e.replace(Gr,function(i){var s=Pe(i);switch(Ot(s[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:s.displayname,id:s.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+s[0]+" in threaded comments")}return i}),r}function Rx(e){var t=[Tr,te("personList",null,{xmlns:Dr.TCMNT,"xmlns:x":Qt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,a){t.push(te("person",null,{displayName:r,id:"{54EE7950-**************-"+("000000000000"+a).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function Nx(e){var t={};t.iauthor=e.read_shift(4);var r=da(e,16);return t.rfx=r.s,t.ref=Ce(r.s),e.l+=16,t}function bx(e,t){return t==null&&(t=G(36)),t.write_shift(4,e[1].iauthor),Na(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Lx=Kr;function Mx(e){return Br(e.slice(0,54))}function Bx(e,t){var r=[],a=[],n={},i=!1;return Ht(e,function(f,c,o){switch(o){case 632:a.push(f);break;case 635:n=f;break;case 637:n.t=f.t,n.h=f.h,n.r=f.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!c.T){if(!i||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}}),r}function Ux(e){var t=qr(),r=[];return J(t,628),J(t,630),e.forEach(function(a){a[1].forEach(function(n){r.indexOf(n.a)>-1||(r.push(n.a.slice(0,54)),J(t,632,Mx(n.a)))})}),J(t,631),J(t,633),e.forEach(function(a){a[1].forEach(function(n){n.iauthor=r.indexOf(n.a);var i={s:_r(a[0]),e:_r(a[0])};J(t,635,bx([i,n])),n.t&&n.t.length>0&&J(t,637,fl(n)),J(t,636),delete n.iauthor})}),J(t,634),J(t,629),t.end()}var Wx="application/vnd.ms-office.vbaProject";function Hx(e){var t=Te.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,a){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Te.utils.cfb_add(t,n,e.FileIndex[a].content)}}),Te.write(t)}function Vx(e,t){t.FullPaths.forEach(function(r,a){if(a!=0){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");n.slice(-1)!=="/"&&Te.utils.cfb_add(e,n,t.FileIndex[a].content)}})}var Wf=["xlsb","xlsm","xlam","biff8","xla"];function Xx(){return{"!type":"dialog"}}function Gx(){return{"!type":"dialog"}}function zx(){return{"!type":"macro"}}function Kx(){return{"!type":"macro"}}var Ua=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(a,n,i,s){var f=!1,c=!1;i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var o=i.length>0?parseInt(i,10)|0:0,l=s.length>0?parseInt(s,10)|0:0;return f?l+=t.c:--l,c?o+=t.r:--o,n+(f?"":"$")+pr(l)+(c?"":"$")+kr(o)}return function(n,i){return t=i,n.replace(e,r)}}(),q0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,ei=function(){return function(t,r){return t.replace(q0,function(a,n,i,s,f,c){var o=D0(s)-(i?0:r.c),l=C0(c)-(f?0:r.r),h=l==0?"":f?l+1:"["+l+"]",x=o==0?"":i?o+1:"["+o+"]";return n+"R"+h+"C"+x})}}();function Hf(e,t){return e.replace(q0,function(r,a,n,i,s,f){return a+(n=="$"?n+i:pr(D0(i)+t.c))+(s=="$"?s+f:kr(C0(f)+t.r))})}function jx(e,t,r){var a=et(t),n=a.s,i=_r(r),s={r:i.r-n.r,c:i.c-n.c};return Hf(e,s)}function $x(e){return e.length!=1}function Vf(e){return e.replace(/_xlfn\./g,"")}function Ir(e){e.l+=1}function ta(e,t){var r=e.read_shift(t==1?1:2);return[r&16383,r>>14&1,r>>15&1]}function Xf(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Gf(e,t,r);r.biff==12&&(a=4)}var n=e.read_shift(a),i=e.read_shift(a),s=ta(e,2),f=ta(e,2);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Gf(e){var t=ta(e,2),r=ta(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Yx(e,t,r){if(r.biff<8)return Gf(e,t,r);var a=e.read_shift(r.biff==12?4:2),n=e.read_shift(r.biff==12?4:2),i=ta(e,2),s=ta(e,2);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:n,c:s[0],cRel:s[1],rRel:s[2]}}}function zf(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Zx(e,t,r);var a=e.read_shift(r&&r.biff==12?4:2),n=ta(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Zx(e){var t=ta(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Jx(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Qx(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return qx(e,t,r);var n=e.read_shift(a>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;n>524287;)n-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:n,c:i,cRel:s,rRel:f}}function qx(e){var t=e.read_shift(2),r=e.read_shift(1),a=(t&32768)>>15,n=(t&16384)>>14;return t&=16383,a==1&&t>=8192&&(t=t-16384),n==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:n,rRel:a}}function ed(e,t,r){var a=(e[e.l++]&96)>>5,n=Xf(e,r.biff>=2&&r.biff<=5?6:8,r);return[a,n]}function rd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Xf(e,i,r);return[a,n,s]}function td(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}function ad(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[a,n]}function nd(e,t,r){var a=(e[e.l++]&96)>>5,n=Yx(e,t-1,r);return[a,n]}function id(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[a]}function Kf(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function sd(e,t,r){e.l+=2;for(var a=e.read_shift(r&&r.biff==2?1:2),n=[],i=0;i<=a;++i)n.push(e.read_shift(r&&r.biff==2?1:2));return n}function fd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function cd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function od(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function ld(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[a]}function jf(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function ud(e){return e.read_shift(2),jf(e,2)}function hd(e){return e.read_shift(2),jf(e,2)}function xd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=zf(e,0,r);return[a,n]}function dd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Qx(e,0,r);return[a,n]}function vd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=zf(e,0,r);return[a,n,i]}function pd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[_v[n],Qf[n],a]}function md(e,t,r){var a=e[e.l++],n=e.read_shift(1),i=r&&r.biff<=3?[a==88?-1:0,e.read_shift(1)]:gd(e);return[n,(i[0]===0?Qf:gv)[i[1]]]}function gd(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function _d(e,t,r){e.l+=r&&r.biff==2?3:4}function wd(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Ed(e){return e.l++,Vt[e.read_shift(1)]}function Td(e){return e.l++,e.read_shift(2)}function kd(e){return e.l++,e.read_shift(1)!==0}function Sd(e){return e.l++,jr(e,8)}function Fd(e,t,r){return e.l++,nn(e,t-1,r)}function yd(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Fr(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Vt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=jr(e,8);break;case 2:r[1]=_a(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Ad(e,t,r){for(var a=e.read_shift(r.biff==12?4:2),n=[],i=0;i!=a;++i)n.push((r.biff==12?da:jn)(e,8));return n}function Cd(e,t,r){var a=0,n=0;r.biff==12?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,--n==0&&(n=256));for(var i=0,s=[];i!=a&&(s[i]=[]);++i)for(var f=0;f!=n;++f)s[i][f]=yd(e,r.biff);return s}function Dd(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,i=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,i]}function Od(e,t,r){if(r.biff==5)return Id(e,t,r);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),i=e.read_shift(4);return[a,n,i]}function Id(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}function Pd(e,t,r){var a=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Rd(e,t,r){var a=e.read_shift(1)>>>5&3,n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Nd(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[a]}function bd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[a,n]}var Ld=zr,Md=zr,Bd=zr;function hn(e,t,r){return e.l+=2,[Jx(e,4,r)]}function ri(e){return e.l+=6,[]}var Ud=hn,Wd=ri,Hd=ri,Vd=hn;function $f(e){return e.l+=2,[Or(e),e.read_shift(2)&1]}var Xd=hn,Gd=$f,zd=ri,Kd=hn,jd=hn,$d=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Yd(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),i=e.read_shift(2),s=$d[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:a,c:n,C:i}}function Zd(e){return e.l+=2,[e.read_shift(4)]}function Jd(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Qd(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function qd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function ev(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function rv(e){return e.l+=4,[0,0]}var Yf={1:{n:"PtgExp",f:wd},2:{n:"PtgTbl",f:Bd},3:{n:"PtgAdd",f:Ir},4:{n:"PtgSub",f:Ir},5:{n:"PtgMul",f:Ir},6:{n:"PtgDiv",f:Ir},7:{n:"PtgPower",f:Ir},8:{n:"PtgConcat",f:Ir},9:{n:"PtgLt",f:Ir},10:{n:"PtgLe",f:Ir},11:{n:"PtgEq",f:Ir},12:{n:"PtgGe",f:Ir},13:{n:"PtgGt",f:Ir},14:{n:"PtgNe",f:Ir},15:{n:"PtgIsect",f:Ir},16:{n:"PtgUnion",f:Ir},17:{n:"PtgRange",f:Ir},18:{n:"PtgUplus",f:Ir},19:{n:"PtgUminus",f:Ir},20:{n:"PtgPercent",f:Ir},21:{n:"PtgParen",f:Ir},22:{n:"PtgMissArg",f:Ir},23:{n:"PtgStr",f:Fd},26:{n:"PtgSheet",f:Jd},27:{n:"PtgEndSheet",f:Qd},28:{n:"PtgErr",f:Ed},29:{n:"PtgBool",f:kd},30:{n:"PtgInt",f:Td},31:{n:"PtgNum",f:Sd},32:{n:"PtgArray",f:id},33:{n:"PtgFunc",f:pd},34:{n:"PtgFuncVar",f:md},35:{n:"PtgName",f:Dd},36:{n:"PtgRef",f:xd},37:{n:"PtgArea",f:ed},38:{n:"PtgMemArea",f:Pd},39:{n:"PtgMemErr",f:Ld},40:{n:"PtgMemNoMem",f:Md},41:{n:"PtgMemFunc",f:Rd},42:{n:"PtgRefErr",f:Nd},43:{n:"PtgAreaErr",f:td},44:{n:"PtgRefN",f:dd},45:{n:"PtgAreaN",f:nd},46:{n:"PtgMemAreaN",f:qd},47:{n:"PtgMemNoMemN",f:ev},57:{n:"PtgNameX",f:Od},58:{n:"PtgRef3d",f:vd},59:{n:"PtgArea3d",f:rd},60:{n:"PtgRefErr3d",f:bd},61:{n:"PtgAreaErr3d",f:ad},255:{}},tv={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},av={1:{n:"PtgElfLel",f:$f},2:{n:"PtgElfRw",f:Kd},3:{n:"PtgElfCol",f:Ud},6:{n:"PtgElfRwV",f:jd},7:{n:"PtgElfColV",f:Vd},10:{n:"PtgElfRadical",f:Xd},11:{n:"PtgElfRadicalS",f:zd},13:{n:"PtgElfColS",f:Wd},15:{n:"PtgElfColSV",f:Hd},16:{n:"PtgElfRadicalLel",f:Gd},25:{n:"PtgList",f:Yd},29:{n:"PtgSxName",f:Zd},255:{}},nv={0:{n:"PtgAttrNoop",f:rv},1:{n:"PtgAttrSemi",f:ld},2:{n:"PtgAttrIf",f:cd},4:{n:"PtgAttrChoose",f:sd},8:{n:"PtgAttrGoto",f:fd},16:{n:"PtgAttrSum",f:_d},32:{n:"PtgAttrBaxcel",f:Kf},33:{n:"PtgAttrBaxcel",f:Kf},64:{n:"PtgAttrSpace",f:ud},65:{n:"PtgAttrSpaceSemi",f:hd},128:{n:"PtgAttrIfError",f:od},255:{}};function xn(e,t,r,a){if(a.biff<8)return zr(e,t);for(var n=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Cd(e,0,a),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Ad(e,r[s][1],a),i.push(r[s][2]);break;case"PtgExp":a&&a.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0];default:break}return t=n-e.l,t!==0&&i.push(zr(e,t)),i}function dn(e,t,r){for(var a=e.l+t,n,i,s=[];a!=e.l;)t=a-e.l,i=e[e.l],n=Yf[i]||Yf[tv[i]],(i===24||i===25)&&(n=(i===24?av:nv)[e[e.l+1]]),!n||!n.f?zr(e,t):s.push([n.n,n.f(e,t,r)]);return s}function iv(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],i=0;i<a.length;++i){var s=a[i];if(s)switch(s[0]){case 2:n.push('"'+s[1].replace(/"/g,'""')+'"');break;default:n.push(s[1])}else n.push("")}t.push(n.join(","))}return t.join(";")}var sv={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function fv(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Zf(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=a[1]==-1?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[a[0]][0][3]?(n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function Jf(e,t,r){var a=Zf(e,t,r);return a=="#REF"?a:fv(a,r)}function Zr(e,t,r,a,n){var i=n&&n.biff||8,s={s:{c:0,r:0},e:{c:0,r:0}},f=[],c,o,l,h=0,x=0,d,v="";if(!e[0]||!e[0][0])return"";for(var u=-1,p="",E=0,T=e[0].length;E<T;++E){var g=e[0][E];switch(g[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(c=f.pop(),o=f.pop(),u>=0){switch(e[0][u][1][0]){case 0:p=Er(" ",e[0][u][1][1]);break;case 1:p=Er("\r",e[0][u][1][1]);break;default:if(p="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}o=o+p,u=-1}f.push(o+sv[g[0]]+c);break;case"PtgIsect":c=f.pop(),o=f.pop(),f.push(o+" "+c);break;case"PtgUnion":c=f.pop(),o=f.pop(),f.push(o+","+c);break;case"PtgRange":c=f.pop(),o=f.pop(),f.push(o+":"+c);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":l=en(g[1][1],s,n),f.push(rn(l,i));break;case"PtgRefN":l=r?en(g[1][1],r,n):g[1][1],f.push(rn(l,i));break;case"PtgRef3d":h=g[1][1],l=en(g[1][2],s,n),v=Jf(a,h,n);var R=v;f.push(v+"!"+rn(l,i));break;case"PtgFunc":case"PtgFuncVar":var L=g[1][0],I=g[1][1];L||(L=0),L&=127;var F=L==0?[]:f.slice(-L);f.length-=L,I==="User"&&(I=F.shift()),f.push(I+"("+F.join(",")+")");break;case"PtgBool":f.push(g[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(g[1]);break;case"PtgNum":f.push(String(g[1]));break;case"PtgStr":f.push('"'+g[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(g[1]);break;case"PtgAreaN":d=ks(g[1][1],r?{s:r}:s,n),f.push(A0(d,n));break;case"PtgArea":d=ks(g[1][1],s,n),f.push(A0(d,n));break;case"PtgArea3d":h=g[1][1],d=g[1][2],v=Jf(a,h,n),f.push(v+"!"+A0(d,n));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":x=g[1][2];var N=(a.names||[])[x-1]||(a[0]||[])[x],P=N?N.Name:"SH33TJSNAME"+String(x);P&&P.slice(0,6)=="_xlfn."&&!n.xlfn&&(P=P.slice(6)),f.push(P);break;case"PtgNameX":var V=g[1][1];x=g[1][2];var X;if(n.biff<=5)V<0&&(V=-V),a[V]&&(X=a[V][x]);else{var b="";if(((a[V]||[])[0]||[])[0]==14849||(((a[V]||[])[0]||[])[0]==1025?a[V][x]&&a[V][x].itab>0&&(b=a.SheetNames[a[V][x].itab-1]+"!"):b=a.SheetNames[x-1]+"!"),a[V]&&a[V][x])b+=a[V][x].Name;else if(a[0]&&a[0][x])b+=a[0][x].Name;else{var ae=(Zf(a,V,n)||"").split(";;");ae[x-1]?b=ae[x-1]:b+="SH33TJSERRX"}f.push(b);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var xe="(",ne=")";if(u>=0){switch(p="",e[0][u][1][0]){case 2:xe=Er(" ",e[0][u][1][1])+xe;break;case 3:xe=Er("\r",e[0][u][1][1])+xe;break;case 4:ne=Er(" ",e[0][u][1][1])+ne;break;case 5:ne=Er("\r",e[0][u][1][1])+ne;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}f.push(xe+f.pop()+ne);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":l={c:g[1][1],r:g[1][0]};var _e={c:r.c,r:r.r};if(a.sharedf[Ce(l)]){var pe=a.sharedf[Ce(l)];f.push(Zr(pe,s,_e,a,n))}else{var ze=!1;for(c=0;c!=a.arrayf.length;++c)if(o=a.arrayf[c],!(l.c<o[0].s.c||l.c>o[0].e.c)&&!(l.r<o[0].s.r||l.r>o[0].e.r)){f.push(Zr(o[1],s,_e,a,n)),ze=!0;break}ze||f.push(g[1])}break;case"PtgArray":f.push("{"+iv(g[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=E;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+g[1].idx+"[#"+g[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(g));default:throw new Error("Unrecognized Formula Token: "+String(g))}var le=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3&&u>=0&&le.indexOf(e[0][E][0])==-1){g=e[0][u];var ge=!0;switch(g[1][0]){case 4:ge=!1;case 0:p=Er(" ",g[1][1]);break;case 5:ge=!1;case 1:p=Er("\r",g[1][1]);break;default:if(p="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+g[1][0])}f.push((ge?p:"")+f.pop()+(ge?"":p)),u=-1}}if(f.length>1&&n.WTF)throw new Error("bad formula stack");return f[0]}function cv(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],zr(e,t-2)];var f=dn(e,s,r);return t!==s+n&&(i=xn(e,t-s-n,f,r)),e.l=a,[f,i]}function ov(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],zr(e,t-2)];var f=dn(e,s,r);return t!==s+n&&(i=xn(e,t-s-n,f,r)),e.l=a,[f,i]}function lv(e,t,r,a){var n=e.l+t,i=dn(e,a,r),s;return n!==e.l&&(s=xn(e,n-e.l,i,r)),[i,s]}function uv(e,t,r){var a=e.l+t,n,i=e.read_shift(2),s=dn(e,i,r);return i==65535?[[],zr(e,t-2)]:(t!==i+2&&(n=xn(e,a-i-2,s,r)),[s,n])}function hv(e){var t;if(Wt(e,e.l+6)!==65535)return[jr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function xv(e){if(e==null){var t=G(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return va(e);return va(0)}function ti(e,t,r){var a=e.l+t,n=Rt(e,6);r.biff==2&&++e.l;var i=hv(e,8),s=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var f=ov(e,a-e.l,r);return{cell:n,val:i[0],formula:f,shared:s>>3&1,tt:i[1]}}function dv(e,t,r,a,n){var i=wa(t,r,n),s=xv(e.v),f=G(6),c=33;f.write_shift(2,c),f.write_shift(4,0);for(var o=G(e.bf.length),l=0;l<e.bf.length;++l)o[l]=e.bf[l];var h=or([i,s,f,o]);return h}function qn(e,t,r){var a=e.read_shift(4),n=dn(e,a,r),i=e.read_shift(4),s=i>0?xn(e,i,n,r):null;return[n,s]}var vv=qn,e0=qn,pv=qn,mv=qn,gv={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Qf={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},_v={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function qf(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function wv(e){var t="of:="+e.replace(q0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function ai(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}function Ev(e){return e.replace(/\./,"!")}var vn={},Wa={},pn=typeof Map!="undefined";function ni(e,t,r){var a=0,n=e.length;if(r){if(pn?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=pn?r.get(t):r[t];a<i.length;++a)if(e[i[a]].t===t)return e.Count++,i[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t},e.Count++,e.Unique++,r&&(pn?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function r0(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(Yr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?a=on(t.wpx):t.wch!=null&&(a=t.wch),a>-1?(r.width=Zn(a),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Ta(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function aa(e,t,r){var a=r.revssf[t.z!=null?t.z:"General"],n=60,i=e.length;if(a==null&&r.ssf){for(;n<392;++n)if(r.ssf[n]==null){Dt(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;return e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function ec(e,t,r,a,n,i){try{a.cellNF&&(e.z=Se[t])}catch(f){if(a.WTF)throw f}if(!(e.t==="z"&&!a.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=dr(e.v)),(!a||a.cellText!==!1)&&e.t!=="z")try{if(Se[t]==null&&Dt(_o[t]||"General",t),e.t==="e")e.w=e.w||Vt[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Ka(e.v);else if(e.t==="d"){var s=Rr(e.v);(s|0)===s?e.w=s.toString(10):e.w=Ka(s)}else{if(e.v===void 0)return"";e.w=fa(e.v,Wa)}else e.t==="d"?e.w=ft(t,Rr(e.v),Wa):e.w=ft(t,e.v,Wa)}catch(f){if(a.WTF)throw f}if(a.cellStyles&&r!=null)try{e.s=i.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Yn(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Yn(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(f){if(a.WTF&&i.Fills)throw f}}}function Tv(e,t,r){if(e&&e["!ref"]){var a=Ze(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function kv(e,t){var r=Ze(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=Ue(r))}var Sv=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Fv=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,yv=/<(?:\w:)?hyperlink [^>]*>/mg,Av=/"(\w*:\w*)"/,Cv=/<(?:\w:)?col\b[^>]*[\/]?>/g,Dv=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Ov=/<(?:\w:)?pageMargins[^>]*\/>/g,rc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Iv=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Pv=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Rv(e,t,r,a,n,i,s){if(!e)return e;a||(a={"!id":{}}),he!=null&&t.dense==null&&(t.dense=he);var f=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},o="",l="",h=e.match(Fv);h?(o=e.slice(0,h.index),l=e.slice(h.index+h[0].length)):o=l=e;var x=o.match(rc);x?ii(x[0],f,n,r):(x=o.match(Iv))&&bv(x[0],x[1]||"",f,n,r,s,i);var d=(o.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var v=o.slice(d,d+50).match(Av);v&&kv(f,v[1])}var u=o.match(Pv);u&&u[1]&&$v(u[1],n);var p=[];if(t.cellStyles){var E=o.match(Cv);E&&Xv(p,E)}h&&Jv(h[1],f,t,c,i,s);var T=l.match(Dv);T&&(f["!autofilter"]=zv(T[0]));var g=[],R=l.match(Sv);if(R)for(d=0;d!=R.length;++d)g[d]=Ze(R[d].slice(R[d].indexOf('"')+1));var L=l.match(yv);L&&Wv(f,L,a);var I=l.match(Ov);if(I&&(f["!margins"]=Hv(Pe(I[0]))),!f["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(f["!ref"]=Ue(c)),t.sheetRows>0&&f["!ref"]){var F=Ze(f["!ref"]);t.sheetRows<=+F.e.r&&(F.e.r=t.sheetRows-1,F.e.r>c.e.r&&(F.e.r=c.e.r),F.e.r<F.s.r&&(F.s.r=F.e.r),F.e.c>c.e.c&&(F.e.c=c.e.c),F.e.c<F.s.c&&(F.s.c=F.e.c),f["!fullref"]=f["!ref"],f["!ref"]=Ue(F))}return p.length>0&&(f["!cols"]=p),g.length>0&&(f["!merges"]=g),f}function Nv(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Ue(e[r])+'"/>';return t+"</mergeCells>"}function ii(e,t,r,a){var n=Pe(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=qe(lr(n.codeName)))}function bv(e,t,r,a,n){ii(e.slice(0,e.indexOf(">")),r,a,n)}function Lv(e,t,r,a,n){var i=!1,s={},f=null;if(a.bookType!=="xlsx"&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch(l){}i=!0,s.codeName=It(sr(c))}if(e&&e["!outline"]){var o={summaryBelow:1,summaryRight:1};e["!outline"].above&&(o.summaryBelow=0),e["!outline"].left&&(o.summaryRight=0),f=(f||"")+te("outlinePr",null,o)}!i&&!f||(n[n.length]=te("sheetPr",f,s))}var Mv=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Bv=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Uv(e){var t={sheet:1};return Mv.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),Bv.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=$0(e.password).toString(16).toUpperCase()),te("sheetProtection",null,t)}function Wv(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var i=Pe(lr(t[n]),!0);if(!i.ref)return;var s=((r||{})["!id"]||[])[i.id];s?(i.Target=s.Target,i.location&&(i.Target+="#"+qe(i.location))):(i.Target="#"+qe(i.location),s={Target:i.Target,TargetMode:"Internal"}),i.Rel=s,i.tooltip&&(i.Tooltip=i.tooltip,delete i.tooltip);for(var f=Ze(i.ref),c=f.s.r;c<=f.e.r;++c)for(var o=f.s.c;o<=f.e.c;++o){var l=Ce({c:o,r:c});a?(e[c]||(e[c]=[]),e[c][o]||(e[c][o]={t:"z",v:void 0}),e[c][o].l=i):(e[l]||(e[l]={t:"z",v:void 0}),e[l].l=i)}}}function Hv(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function Vv(e){return Ta(e),te("pageMargins",null,e)}function Xv(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=Pe(t[a],!0);n.hidden&&(n.hidden=vr(n.hidden));var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Z0(n.width)),ra(n);i<=s;)e[i++]=ur(n)}}function Gv(e,t){for(var r=["<cols>"],a,n=0;n!=t.length;++n)(a=t[n])&&(r[r.length]=te("col",null,r0(n,a)));return r[r.length]="</cols>",r.join("")}function zv(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}function Kv(e,t,r,a){var n=typeof e.ref=="string"?e.ref:Ue(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=et(n);s.s.r==s.e.r&&(s.e.r=et(t["!ref"]).e.r,n=Ue(s));for(var f=0;f<i.length;++f){var c=i[f];if(c.Name=="_xlnm._FilterDatabase"&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),te("autoFilter",null,{ref:n})}var jv=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function $v(e,t){t.Views||(t.Views=[{}]),(e.match(jv)||[]).forEach(function(r,a){var n=Pe(r);t.Views[a]||(t.Views[a]={}),+n.zoomScale&&(t.Views[a].zoom=+n.zoomScale),vr(n.rightToLeft)&&(t.Views[a].RTL=!0)})}function Yv(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),te("sheetViews",te("sheetView",null,n),{})}function Zv(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var n="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=Vt[e.v];break;case"d":a&&a.cellDates?n=dr(e.v,-1).toISOString():(e=ur(e),e.t="n",n=""+(e.v=Rr(dr(e.v)))),typeof e.z=="undefined"&&(e.z=Se[14]);break;default:n=e.v;break}var f=Vr("v",sr(n)),c={r:t},o=aa(a.cellXfs,e,a);switch(o!==0&&(c.s=o),e.t){case"n":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){f=Vr("v",""+ni(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var l=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=te("f",sr(e.f),l)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),te("c",f,c)}var Jv=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,i=Za("v"),s=Za("f");return function(c,o,l,h,x,d){for(var v=0,u="",p=[],E=[],T=0,g=0,R=0,L="",I,F,N=0,P=0,V,X,b=0,ae=0,xe=Array.isArray(d.CellXf),ne,_e=[],pe=[],ze=Array.isArray(o),le=[],ge={},j=!1,C=!!l.sheetStubs,U=c.split(t),O=0,D=U.length;O!=D;++O){u=U[O].trim();var K=u.length;if(K!==0){var ce=0;e:for(v=0;v<K;++v)switch(u[v]){case">":if(u[v-1]!="/"){++v;break e}if(l&&l.cellStyles){if(F=Pe(u.slice(ce,v),!0),N=F.r!=null?parseInt(F.r,10):N+1,P=-1,l.sheetRows&&l.sheetRows<N)continue;ge={},j=!1,F.ht&&(j=!0,ge.hpt=parseFloat(F.ht),ge.hpx=Ma(ge.hpt)),F.hidden=="1"&&(j=!0,ge.hidden=!0),F.outlineLevel!=null&&(j=!0,ge.level=+F.outlineLevel),j&&(le[N-1]=ge)}break;case"<":ce=v;break}if(ce>=v)break;if(F=Pe(u.slice(ce,v),!0),N=F.r!=null?parseInt(F.r,10):N+1,P=-1,!(l.sheetRows&&l.sheetRows<N)){h.s.r>N-1&&(h.s.r=N-1),h.e.r<N-1&&(h.e.r=N-1),l&&l.cellStyles&&(ge={},j=!1,F.ht&&(j=!0,ge.hpt=parseFloat(F.ht),ge.hpx=Ma(ge.hpt)),F.hidden=="1"&&(j=!0,ge.hidden=!0),F.outlineLevel!=null&&(j=!0,ge.level=+F.outlineLevel),j&&(le[N-1]=ge)),p=u.slice(v).split(e);for(var ee=0;ee!=p.length&&p[ee].trim().charAt(0)=="<";++ee);for(p=p.slice(ee),v=0;v!=p.length;++v)if(u=p[v].trim(),u.length!==0){if(E=u.match(r),T=v,g=0,R=0,u="<c "+(u.slice(0,1)=="<"?">":"")+u,E!=null&&E.length===2){for(T=0,L=E[1],g=0;g!=L.length&&!((R=L.charCodeAt(g)-64)<1||R>26);++g)T=26*T+R;--T,P=T}else++P;for(g=0;g!=u.length&&u.charCodeAt(g)!==62;++g);if(++g,F=Pe(u.slice(0,g),!0),F.r||(F.r=Ce({r:N-1,c:P})),L=u.slice(g),I={t:""},(E=L.match(i))!=null&&E[1]!==""&&(I.v=qe(E[1])),l.cellFormula){if((E=L.match(s))!=null&&E[1]!==""){if(I.f=qe(lr(E[1])).replace(/\r\n/g,`
`),l.xlfn||(I.f=Vf(I.f)),E[0].indexOf('t="array"')>-1)I.F=(L.match(n)||[])[1],I.F.indexOf(":")>-1&&_e.push([Ze(I.F),I.F]);else if(E[0].indexOf('t="shared"')>-1){X=Pe(E[0]);var re=qe(lr(E[1]));l.xlfn||(re=Vf(re)),pe[parseInt(X.si,10)]=[X,re,F.r]}}else(E=L.match(/<f[^>]*\/>/))&&(X=Pe(E[0]),pe[X.si]&&(I.f=jx(pe[X.si][1],pe[X.si][2],F.r)));var Y=_r(F.r);for(g=0;g<_e.length;++g)Y.r>=_e[g][0].s.r&&Y.r<=_e[g][0].e.r&&Y.c>=_e[g][0].s.c&&Y.c<=_e[g][0].e.c&&(I.F=_e[g][1])}if(F.t==null&&I.v===void 0)if(I.f||I.F)I.v=0,I.t="n";else if(C)I.t="z";else continue;else I.t=F.t||"n";switch(h.s.c>P&&(h.s.c=P),h.e.c<P&&(h.e.c=P),I.t){case"n":if(I.v==""||I.v==null){if(!C)continue;I.t="z"}else I.v=parseFloat(I.v);break;case"s":if(typeof I.v=="undefined"){if(!C)continue;I.t="z"}else V=vn[parseInt(I.v,10)],I.v=V.t,I.r=V.r,l.cellHTML&&(I.h=V.h);break;case"str":I.t="s",I.v=I.v!=null?lr(I.v):"",l.cellHTML&&(I.h=m0(I.v));break;case"inlineStr":E=L.match(a),I.t="s",E!=null&&(V=j0(E[1]))?(I.v=V.t,l.cellHTML&&(I.h=V.h)):I.v="";break;case"b":I.v=vr(I.v);break;case"d":l.cellDates?I.v=dr(I.v,1):(I.v=Rr(dr(I.v,1)),I.t="n");break;case"e":(!l||l.cellText!==!1)&&(I.w=I.v),I.v=Rs[I.v];break}if(b=ae=0,ne=null,xe&&F.s!==void 0&&(ne=d.CellXf[F.s],ne!=null&&(ne.numFmtId!=null&&(b=ne.numFmtId),l.cellStyles&&ne.fillId!=null&&(ae=ne.fillId))),ec(I,b,ae,l,x,d),l.cellDates&&xe&&I.t=="n"&&ca(Se[b])&&(I.t="d",I.v=bn(I.v)),F.cm&&l.xlmeta){var He=(l.xlmeta.Cell||[])[+F.cm-1];He&&He.type=="XLDAPR"&&(I.D=!0)}if(ze){var A=_r(F.r);o[A.r]||(o[A.r]=[]),o[A.r][A.c]=I}else o[F.r]=I}}}}le.length>0&&(o["!rows"]=le)}}();function Qv(e,t,r,a){var n=[],i=[],s=Ze(e["!ref"]),f="",c,o="",l=[],h=0,x=0,d=e["!rows"],v=Array.isArray(e),u={r:o},p,E=-1;for(x=s.s.c;x<=s.e.c;++x)l[x]=pr(x);for(h=s.s.r;h<=s.e.r;++h){for(i=[],o=kr(h),x=s.s.c;x<=s.e.c;++x){c=l[x]+o;var T=v?(e[h]||[])[x]:e[c];T!==void 0&&(f=Zv(T,c,e,t,r,a))!=null&&i.push(f)}(i.length>0||d&&d[h])&&(u={r:o},d&&d[h]&&(p=d[h],p.hidden&&(u.hidden=1),E=-1,p.hpx?E=ln(p.hpx):p.hpt&&(E=p.hpt),E>-1&&(u.ht=E,u.customHeight=1),p.level&&(u.outlineLevel=p.level)),n[n.length]=te("row",i.join(""),u))}if(d)for(;h<d.length;++h)d&&d[h]&&(u={r:h+1},p=d[h],p.hidden&&(u.hidden=1),E=-1,p.hpx?E=ln(p.hpx):p.hpt&&(E=p.hpt),E>-1&&(u.ht=E,u.customHeight=1),p.level&&(u.outlineLevel=p.level),n[n.length]=te("row","",u));return n.join("")}function tc(e,t,r,a){var n=[Tr,te("worksheet",null,{xmlns:Qt[0],"xmlns:r":Dr.r})],i=r.SheetNames[e],s=0,f="",c=r.Sheets[i];c==null&&(c={});var o=c["!ref"]||"A1",l=Ze(o);if(l.e.c>16383||l.e.r>1048575){if(t.WTF)throw new Error("Range "+o+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575),o=Ue(l)}a||(a={}),c["!comments"]=[];var h=[];Lv(c,r,e,t,n),n[n.length]=te("dimension",null,{ref:o}),n[n.length]=Yv(c,t,e,r),t.sheetFormat&&(n[n.length]=te("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),c["!cols"]!=null&&c["!cols"].length>0&&(n[n.length]=Gv(c,c["!cols"])),n[s=n.length]="<sheetData/>",c["!links"]=[],c["!ref"]!=null&&(f=Qv(c,t,e,r,a),f.length>0&&(n[n.length]=f)),n.length>s+1&&(n[n.length]="</sheetData>",n[s]=n[s].replace("/>",">")),c["!protect"]&&(n[n.length]=Uv(c["!protect"])),c["!autofilter"]!=null&&(n[n.length]=Kv(c["!autofilter"],c,r,e)),c["!merges"]!=null&&c["!merges"].length>0&&(n[n.length]=Nv(c["!merges"]));var x=-1,d,v=-1;return c["!links"].length>0&&(n[n.length]="<hyperlinks>",c["!links"].forEach(function(u){u[1].Target&&(d={ref:u[0]},u[1].Target.charAt(0)!="#"&&(v=nr(a,-1,sr(u[1].Target).replace(/#.*$/,""),We.HLINK),d["r:id"]="rId"+v),(x=u[1].Target.indexOf("#"))>-1&&(d.location=sr(u[1].Target.slice(x+1))),u[1].Tooltip&&(d.tooltip=sr(u[1].Tooltip)),n[n.length]=te("hyperlink",null,d))}),n[n.length]="</hyperlinks>"),delete c["!links"],c["!margins"]!=null&&(n[n.length]=Vv(c["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(n[n.length]=Vr("ignoredErrors",te("ignoredError",null,{numberStoredAsText:1,sqref:o}))),h.length>0&&(v=nr(a,-1,"../drawings/drawing"+(e+1)+".xml",We.DRAW),n[n.length]=te("drawing",null,{"r:id":"rId"+v}),c["!drawing"]=h),c["!comments"].length>0&&(v=nr(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",We.VML),n[n.length]=te("legacyDrawing",null,{"r:id":"rId"+v}),c["!legacy"]=v),n.length>1&&(n[n.length]="</worksheet>",n[1]=n[1].replace("/>",">")),n.join("")}function qv(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=a,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=n/20),r}function ep(e,t,r){var a=G(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var i=320;n.hpx?i=ln(n.hpx)*20:n.hpt&&(i=n.hpt*20),a.write_shift(2,i),a.write_shift(1,0);var s=0;n.level&&(s|=n.level),n.hidden&&(s|=16),(n.hpx||n.hpt)&&(s|=32),a.write_shift(1,s),a.write_shift(1,0);var f=0,c=a.l;a.l+=4;for(var o={r:e,c:0},l=0;l<16;++l)if(!(t.s.c>l+1<<10||t.e.c<l<<10)){for(var h=-1,x=-1,d=l<<10;d<l+1<<10;++d){o.c=d;var v=Array.isArray(r)?(r[o.r]||[])[o.c]:r[Ce(o)];v&&(h<0&&(h=d),x=d)}h<0||(++f,a.write_shift(4,h),a.write_shift(4,x))}var u=a.l;return a.l=c,a.write_shift(4,f),a.l=u,a.length>a.l?a.slice(0,a.l):a}function rp(e,t,r,a){var n=ep(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&J(e,0,n)}var tp=da,ap=Na;function np(){}function ip(e,t){var r={},a=e[e.l];return++e.l,r.above=!(a&64),r.left=!(a&128),e.l+=18,r.name=cl(e,t-19),r}function sp(e,t,r){r==null&&(r=G(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return Wn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),ys(e,r),r.slice(0,r.l)}function fp(e){var t=vt(e);return[t]}function cp(e,t,r){return r==null&&(r=G(8)),ua(t,r)}function op(e){var t=ha(e);return[t]}function lp(e,t,r){return r==null&&(r=G(4)),xa(t,r)}function up(e){var t=vt(e),r=e.read_shift(1);return[t,r,"b"]}function hp(e,t,r){return r==null&&(r=G(9)),ua(t,r),r.write_shift(1,e.v?1:0),r}function xp(e){var t=ha(e),r=e.read_shift(1);return[t,r,"b"]}function dp(e,t,r){return r==null&&(r=G(5)),xa(t,r),r.write_shift(1,e.v?1:0),r}function vp(e){var t=vt(e),r=e.read_shift(1);return[t,r,"e"]}function pp(e,t,r){return r==null&&(r=G(9)),ua(t,r),r.write_shift(1,e.v),r}function mp(e){var t=ha(e),r=e.read_shift(1);return[t,r,"e"]}function gp(e,t,r){return r==null&&(r=G(8)),xa(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function _p(e){var t=vt(e),r=e.read_shift(4);return[t,r,"s"]}function wp(e,t,r){return r==null&&(r=G(12)),ua(t,r),r.write_shift(4,t.v),r}function Ep(e){var t=ha(e),r=e.read_shift(4);return[t,r,"s"]}function Tp(e,t,r){return r==null&&(r=G(8)),xa(t,r),r.write_shift(4,t.v),r}function kp(e){var t=vt(e),r=jr(e);return[t,r,"n"]}function Sp(e,t,r){return r==null&&(r=G(16)),ua(t,r),va(e.v,r),r}function ac(e){var t=ha(e),r=jr(e);return[t,r,"n"]}function Fp(e,t,r){return r==null&&(r=G(12)),xa(t,r),va(e.v,r),r}function yp(e){var t=vt(e),r=N0(e);return[t,r,"n"]}function Ap(e,t,r){return r==null&&(r=G(12)),ua(t,r),As(e.v,r),r}function Cp(e){var t=ha(e),r=N0(e);return[t,r,"n"]}function Dp(e,t,r){return r==null&&(r=G(8)),xa(t,r),As(e.v,r),r}function Op(e){var t=vt(e),r=O0(e);return[t,r,"is"]}function Ip(e){var t=vt(e),r=Kr(e);return[t,r,"str"]}function Pp(e,t,r){return r==null&&(r=G(12+4*e.v.length)),ua(t,r),Br(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Rp(e){var t=ha(e),r=Kr(e);return[t,r,"str"]}function Np(e,t,r){return r==null&&(r=G(8+4*e.v.length)),xa(t,r),Br(e.v,r),r.length>r.l?r.slice(0,r.l):r}function bp(e,t,r){var a=e.l+t,n=vt(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"b"];if(r.cellFormula){e.l+=2;var f=e0(e,a-e.l,r);s[3]=Zr(f,null,n,r.supbooks,r)}else e.l=a;return s}function Lp(e,t,r){var a=e.l+t,n=vt(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"e"];if(r.cellFormula){e.l+=2;var f=e0(e,a-e.l,r);s[3]=Zr(f,null,n,r.supbooks,r)}else e.l=a;return s}function Mp(e,t,r){var a=e.l+t,n=vt(e);n.r=r["!row"];var i=jr(e),s=[n,i,"n"];if(r.cellFormula){e.l+=2;var f=e0(e,a-e.l,r);s[3]=Zr(f,null,n,r.supbooks,r)}else e.l=a;return s}function Bp(e,t,r){var a=e.l+t,n=vt(e);n.r=r["!row"];var i=Kr(e),s=[n,i,"str"];if(r.cellFormula){e.l+=2;var f=e0(e,a-e.l,r);s[3]=Zr(f,null,n,r.supbooks,r)}else e.l=a;return s}var Up=da,Wp=Na;function Hp(e,t){return t==null&&(t=G(4)),t.write_shift(4,e),t}function Vp(e,t){var r=e.l+t,a=da(e,16),n=I0(e),i=Kr(e),s=Kr(e),f=Kr(e);e.l=r;var c={rfx:a,relId:n,loc:i,display:f};return s&&(c.Tooltip=s),c}function Xp(e,t){var r=G(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Na({s:_r(e[0]),e:_r(e[0])},r),R0("rId"+t,r);var a=e[1].Target.indexOf("#"),n=a==-1?"":e[1].Target.slice(a+1);return Br(n||"",r),Br(e[1].Tooltip||"",r),Br("",r),r.slice(0,r.l)}function Gp(){}function zp(e,t,r){var a=e.l+t,n=Cs(e,16),i=e.read_shift(1),s=[n];if(s[2]=i,r.cellFormula){var f=vv(e,a-e.l,r);s[1]=f}else e.l=a;return s}function Kp(e,t,r){var a=e.l+t,n=da(e,16),i=[n];if(r.cellFormula){var s=mv(e,a-e.l,r);i[1]=s,e.l=a}else e.l=a;return i}function jp(e,t,r){r==null&&(r=G(18));var a=r0(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(a.width||10)*256),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),typeof a.width=="number"&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}var nc=["left","right","top","bottom","header","footer"];function $p(e){var t={};return nc.forEach(function(r){t[r]=jr(e,8)}),t}function Yp(e,t){return t==null&&(t=G(6*8)),Ta(e),nc.forEach(function(r){va(e[r],t)}),t}function Zp(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Jp(e,t,r){r==null&&(r=G(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function Qp(e){var t=G(24);return t.write_shift(4,4),t.write_shift(4,1),Na(e,t),t}function qp(e,t){return t==null&&(t=G(16*4+2)),t.write_shift(2,e.password?$0(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function e2(){}function r2(){}function t2(e,t,r,a,n,i,s){if(!e)return e;var f=t||{};a||(a={"!id":{}}),he!=null&&f.dense==null&&(f.dense=he);var c=f.dense?[]:{},o,l={s:{r:2e6,c:2e6},e:{r:0,c:0}},h=[],x=!1,d=!1,v,u,p,E,T,g,R,L,I,F=[];f.biff=12,f["!row"]=0;var N=0,P=!1,V=[],X={},b=f.supbooks||n.supbooks||[[]];if(b.sharedf=X,b.arrayf=V,b.SheetNames=n.SheetNames||n.Sheets.map(function(ge){return ge.name}),!f.supbooks&&(f.supbooks=b,n.Names))for(var ae=0;ae<n.Names.length;++ae)b[0][ae+1]=n.Names[ae];var xe=[],ne=[],_e=!1;_n[16]={n:"BrtShortReal",f:ac};var pe,ze;if(Ht(e,function(j,C,U){if(!d)switch(U){case 148:o=j;break;case 0:v=j,f.sheetRows&&f.sheetRows<=v.r&&(d=!0),L=kr(E=v.r),f["!row"]=v.r,(j.hidden||j.hpt||j.level!=null)&&(j.hpt&&(j.hpx=Ma(j.hpt)),ne[j.r]=j);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(u={t:j[2]},j[2]){case"n":u.v=j[1];break;case"s":R=vn[j[1]],u.v=R.t,u.r=R.r;break;case"b":u.v=!!j[1];break;case"e":u.v=j[1],f.cellText!==!1&&(u.w=Vt[u.v]);break;case"str":u.t="s",u.v=j[1];break;case"is":u.t="s",u.v=j[1].t;break}if((p=s.CellXf[j[0].iStyleRef])&&ec(u,p.numFmtId,null,f,i,s),T=j[0].c==-1?T+1:j[0].c,f.dense?(c[E]||(c[E]=[]),c[E][T]=u):c[pr(T)+L]=u,f.cellFormula){for(P=!1,N=0;N<V.length;++N){var O=V[N];v.r>=O[0].s.r&&v.r<=O[0].e.r&&T>=O[0].s.c&&T<=O[0].e.c&&(u.F=Ue(O[0]),P=!0)}!P&&j.length>3&&(u.f=j[3])}if(l.s.r>v.r&&(l.s.r=v.r),l.s.c>T&&(l.s.c=T),l.e.r<v.r&&(l.e.r=v.r),l.e.c<T&&(l.e.c=T),f.cellDates&&p&&u.t=="n"&&ca(Se[p.numFmtId])){var D=Yt(u.v);D&&(u.t="d",u.v=new Date(D.y,D.m-1,D.d,D.H,D.M,D.S,D.u))}pe&&(pe.type=="XLDAPR"&&(u.D=!0),pe=void 0),ze&&(ze=void 0);break;case 1:case 12:if(!f.sheetStubs||x)break;u={t:"z",v:void 0},T=j[0].c==-1?T+1:j[0].c,f.dense?(c[E]||(c[E]=[]),c[E][T]=u):c[pr(T)+L]=u,l.s.r>v.r&&(l.s.r=v.r),l.s.c>T&&(l.s.c=T),l.e.r<v.r&&(l.e.r=v.r),l.e.c<T&&(l.e.c=T),pe&&(pe.type=="XLDAPR"&&(u.D=!0),pe=void 0),ze&&(ze=void 0);break;case 176:F.push(j);break;case 49:pe=((f.xlmeta||{}).Cell||[])[j-1];break;case 494:var K=a["!id"][j.relId];for(K?(j.Target=K.Target,j.loc&&(j.Target+="#"+j.loc),j.Rel=K):j.relId==""&&(j.Target="#"+j.loc),E=j.rfx.s.r;E<=j.rfx.e.r;++E)for(T=j.rfx.s.c;T<=j.rfx.e.c;++T)f.dense?(c[E]||(c[E]=[]),c[E][T]||(c[E][T]={t:"z",v:void 0}),c[E][T].l=j):(g=Ce({c:T,r:E}),c[g]||(c[g]={t:"z",v:void 0}),c[g].l=j);break;case 426:if(!f.cellFormula)break;V.push(j),I=f.dense?c[E][T]:c[pr(T)+L],I.f=Zr(j[1],l,{r:v.r,c:T},b,f),I.F=Ue(j[0]);break;case 427:if(!f.cellFormula)break;X[Ce(j[0].s)]=j[1],I=f.dense?c[E][T]:c[pr(T)+L],I.f=Zr(j[1],l,{r:v.r,c:T},b,f);break;case 60:if(!f.cellStyles)break;for(;j.e>=j.s;)xe[j.e--]={width:j.w/256,hidden:!!(j.flags&1),level:j.level},_e||(_e=!0,Z0(j.w/256)),ra(xe[j.e+1]);break;case 161:c["!autofilter"]={ref:Ue(j)};break;case 476:c["!margins"]=j;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),j.name&&(n.Sheets[r].CodeName=j.name),(j.above||j.left)&&(c["!outline"]={above:j.above,left:j.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),j.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:x=!0;break;case 36:x=!1;break;case 37:h.push(U),x=!0;break;case 38:h.pop(),x=!1;break;default:if(!C.T){if(!x||f.WTF)throw new Error("Unexpected record 0x"+U.toString(16))}}},f),delete f.supbooks,delete f["!row"],!c["!ref"]&&(l.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(c["!ref"]=Ue(o||l)),f.sheetRows&&c["!ref"]){var le=Ze(c["!ref"]);f.sheetRows<=+le.e.r&&(le.e.r=f.sheetRows-1,le.e.r>l.e.r&&(le.e.r=l.e.r),le.e.r<le.s.r&&(le.s.r=le.e.r),le.e.c>l.e.c&&(le.e.c=l.e.c),le.e.c<le.s.c&&(le.s.c=le.e.c),c["!fullref"]=c["!ref"],c["!ref"]=Ue(le))}return F.length>0&&(c["!merges"]=F),xe.length>0&&(c["!cols"]=xe),ne.length>0&&(c["!rows"]=ne),c}function a2(e,t,r,a,n,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=ur(t),t.z=t.z||Se[14],t.v=Rr(dr(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var c={r,c:a};switch(c.s=aa(n.cellXfs,t,n),t.l&&i["!links"].push([Ce(c),t.l]),t.c&&i["!comments"].push([Ce(c),t.c]),t.t){case"s":case"str":return n.bookSST?(f=ni(n.Strings,t.v,n.revStrings),c.t="s",c.v=f,s?J(e,18,Tp(t,c)):J(e,7,wp(t,c))):(c.t="str",s?J(e,17,Np(t,c)):J(e,6,Pp(t,c))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?J(e,13,Dp(t,c)):J(e,2,Ap(t,c)):s?J(e,16,Fp(t,c)):J(e,5,Sp(t,c)),!0;case"b":return c.t="b",s?J(e,15,dp(t,c)):J(e,4,hp(t,c)),!0;case"e":return c.t="e",s?J(e,14,gp(t,c)):J(e,3,pp(t,c)),!0}return s?J(e,12,lp(t,c)):J(e,1,cp(t,c)),!0}function n2(e,t,r,a){var n=Ze(t["!ref"]||"A1"),i,s="",f=[];J(e,145);var c=Array.isArray(t),o=n.e.r;t["!rows"]&&(o=Math.max(n.e.r,t["!rows"].length-1));for(var l=n.s.r;l<=o;++l){s=kr(l),rp(e,t,n,l);var h=!1;if(l<=n.e.r)for(var x=n.s.c;x<=n.e.c;++x){l===n.s.r&&(f[x]=pr(x)),i=f[x]+s;var d=c?(t[l]||[])[x]:t[i];if(!d){h=!1;continue}h=a2(e,d,l,x,a,t,h)}}J(e,146)}function i2(e,t){!t||!t["!merges"]||(J(e,177,Hp(t["!merges"].length)),t["!merges"].forEach(function(r){J(e,176,Wp(r))}),J(e,178))}function s2(e,t){!t||!t["!cols"]||(J(e,390),t["!cols"].forEach(function(r,a){r&&J(e,60,jp(a,r))}),J(e,391))}function f2(e,t){!t||!t["!ref"]||(J(e,648),J(e,649,Qp(Ze(t["!ref"]))),J(e,650))}function c2(e,t,r){t["!links"].forEach(function(a){if(a[1].Target){var n=nr(r,-1,a[1].Target.replace(/#.*$/,""),We.HLINK);J(e,494,Xp(a,n))}}),delete t["!links"]}function o2(e,t,r,a){if(t["!comments"].length>0){var n=nr(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",We.VML);J(e,551,R0("rId"+n)),t["!legacy"]=n}}function l2(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],i=typeof n.ref=="string"?n.ref:Ue(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=et(i);f.s.r==f.e.r&&(f.e.r=et(t["!ref"]).e.r,i=Ue(f));for(var c=0;c<s.length;++c){var o=s[c];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==a){o.Ref="'"+r.SheetNames[a]+"'!"+i;break}}c==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+i}),J(e,161,Na(Ze(i))),J(e,162)}}function u2(e,t,r){J(e,133),J(e,137,Jp(t,r)),J(e,138),J(e,134)}function u_(){}function h2(e,t){t["!protect"]&&J(e,535,qp(t["!protect"]))}function x2(e,t,r,a){var n=qr(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch(o){}var c=Ze(s["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],J(n,129),(r.vbaraw||s["!outline"])&&J(n,147,sp(f,s["!outline"])),J(n,148,ap(c)),u2(n,s,r.Workbook),s2(n,s,e,t,r),n2(n,s,e,t,r),h2(n,s),l2(n,s,r,e),i2(n,s),c2(n,s,a),s["!margins"]&&J(n,476,Yp(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&f2(n,s),o2(n,s,e,a),J(n,130),n.end()}function d2(e){var t=[],r=e.match(/^<c:numCache>/),a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(i){var s=i.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);s&&(t[+s[1]]=r?+s[2]:s[2])});var n=qe((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(i){a=i.replace(/<.*?>/g,"")}),[t,n,a]}function v2(e,t,r,a,n,i){var s=i||{"!type":"chart"};if(!e)return i;var f=0,c=0,o="A",l={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(h){var x=d2(h);l.s.r=l.s.c=0,l.e.c=f,o=pr(f),x[0].forEach(function(d,v){s[o+kr(v)]={t:"n",v:d,z:x[1]},c=v}),l.e.r<c&&(l.e.r=c),++f}),f>0&&(s["!ref"]=Ue(l)),s}function p2(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s,f=e.match(rc);return f&&ii(f[0],i,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}function m2(e,t,r,a){var n=[Tr,te("chartsheet",null,{xmlns:Qt[0],"xmlns:r":Dr.r})];return n[n.length]=te("drawing",null,{"r:id":"rId1"}),nr(a,-1,"../drawings/drawing"+(e+1)+".xml",We.DRAW),n.length>2&&(n[n.length]="</chartsheet>",n[1]=n[1].replace("/>",">")),n.join("")}function g2(e,t){e.l+=10;var r=Kr(e,t-10);return{name:r}}function _2(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s=[],f=!1;return Ht(e,function(o,l,h){switch(h){case 550:i["!rel"]=o;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),o.name&&(n.Sheets[r].CodeName=o.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:f=!0;break;case 36:f=!1;break;case 37:s.push(h);break;case 38:s.pop();break;default:if(l.T>0)s.push(h);else if(l.T<0)s.pop();else if(!f||t.WTF)throw new Error("Unexpected record 0x"+h.toString(16))}},t),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}function w2(){var e=qr();return J(e,129),J(e,130),e.end()}var si=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],E2=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],T2=[],k2=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function ic(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var i=t[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":typeof a[i[0]]=="string"&&(a[i[0]]=vr(a[i[0]]));break;case"int":typeof a[i[0]]=="string"&&(a[i[0]]=parseInt(a[i[0]],10));break}}}function sc(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":typeof e[a[0]]=="string"&&(e[a[0]]=vr(e[a[0]]));break;case"int":typeof e[a[0]]=="string"&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function fc(e){sc(e.WBProps,si),sc(e.CalcPr,k2),ic(e.WBView,E2),ic(e.Sheets,T2),Wa.date1904=vr(e.WBProps.date1904)}function S2(e){return!e.Workbook||!e.Workbook.WBProps?"false":vr(e.Workbook.WBProps.date1904)?"true":"false"}var F2="][*?/\\".split("");function cc(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return F2.forEach(function(a){if(e.indexOf(a)!=-1){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function y2(e,t,r){e.forEach(function(a,n){cc(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(r){var s=t&&t[n]&&t[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function oc(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];y2(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)Tv(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}var A2=/<\w+:workbook/;function C2(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",i={},s=0;if(e.replace(Gr,function(c,o){var l=Pe(c);switch(Ot(l[0])){case"<?xml":break;case"<workbook":c.match(A2)&&(n="xmlns"+c.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"</workbook>":break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":si.forEach(function(h){if(l[h[0]]!=null)switch(h[2]){case"bool":r.WBProps[h[0]]=vr(l[h[0]]);break;case"int":r.WBProps[h[0]]=parseInt(l[h[0]],10);break;default:r.WBProps[h[0]]=l[h[0]]}}),l.codeName&&(r.WBProps.CodeName=lr(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=qe(lr(l.name)),delete l[0],r.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":i={},i.Name=lr(l.name),l.comment&&(i.Comment=l.comment),l.localSheetId&&(i.Sheet=+l.localSheetId),vr(l.hidden||"0")&&(i.Hidden=!0),s=o+c.length;break;case"</definedName>":i.Ref=qe(lr(e.slice(s,o))),r.Names.push(i);break;case"<definedName/>":break;case"<calcPr":delete l[0],r.CalcPr=l;break;case"<calcPr/>":delete l[0],r.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return c}),Qt.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return fc(r),r}function lc(e){var t=[Tr];t[t.length]=te("workbook",null,{xmlns:Qt[0],"xmlns:r":Dr.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(si.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(a[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=te("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],i=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!n[i]||!n[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:sr(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=te("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var c={name:f.Name};f.Comment&&(c.comment=f.Comment),f.Sheet!=null&&(c.localSheetId=""+f.Sheet),f.Hidden&&(c.hidden="1"),f.Ref&&(t[t.length]=te("definedName",sr(f.Ref),c))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function D2(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=P0(e,t-8),r.name=Kr(e),r}function O2(e,t){return t||(t=G(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),R0(e.strRelID,t),Br(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function I2(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?Kr(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(a&65536),r.backupFile=!!(a&64),r.checkCompatibility=!!(a&4096),r.date1904=!!(a&1),r.filterPrivacy=!!(a&8),r.hidePivotFieldList=!!(a&1024),r.promptedSolutions=!!(a&16),r.publishItems=!!(a&2048),r.refreshAllConnections=!!(a&262144),r.saveExternalLinkValues=!!(a&128),r.showBorderUnselectedTables=!!(a&4),r.showInkAnnotation=!!(a&32),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(a&32768),r.updateLinks=["userSet","never","always"][a>>8&3],r}function P2(e,t){t||(t=G(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),ys(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function R2(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function N2(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),i=ol(e),s=pv(e,0,r),f=I0(e);e.l=a;var c={Name:i,Ptg:s};return n<268435455&&(c.Sheet=n),f&&(c.Comment=f),c}function b2(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var i=[],s=[[]];return s.SheetNames=[],s.XTI=[],_n[16]={n:"BrtFRTArchID$",f:R2},Ht(e,function(c,o,l){switch(l){case 156:s.SheetNames.push(c.name),r.Sheets.push(c);break;case 153:r.WBProps=c;break;case 39:c.Sheet!=null&&(t.SID=c.Sheet),c.Ref=Zr(c.Ptg,null,null,s,t),delete t.SID,delete c.Ptg,i.push(c);break;case 1036:break;case 357:case 358:case 355:case 667:s[0].length?s.push([l,c]):s[0]=[l,c],s[s.length-1].XTI=[];break;case 362:s.length===0&&(s[0]=[],s[0].XTI=[]),s[s.length-1].XTI=s[s.length-1].XTI.concat(c),s.XTI=s.XTI.concat(c);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(l),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(l),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(!o.T){if(!n||t.WTF&&a[a.length-1]!=37&&a[a.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}},t),fc(r),r.Names=i,r.supbooks=s,r}function L2(e,t){J(e,143);for(var r=0;r!=t.SheetNames.length;++r){var a=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,n={Hidden:a,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};J(e,156,O2(n))}J(e,144)}function M2(e,t){t||(t=G(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return Br("SheetJS",t),Br(Ne.version,t),Br(Ne.version,t),Br("7262",t),t.length>t.l?t.slice(0,t.l):t}function B2(e,t){t||(t=G(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function U2(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,a=0,n=-1,i=-1;a<r.length;++a)!r[a]||!r[a].Hidden&&n==-1?n=a:r[a].Hidden==1&&i==-1&&(i=a);i>n||(J(e,135),J(e,158,B2(n)),J(e,136))}}function W2(e,t){var r=qr();return J(r,131),J(r,128,M2()),J(r,153,P2(e.Workbook&&e.Workbook.WBProps||null)),U2(r,e,t),L2(r,e,t),J(r,132),r.end()}function H2(e,t,r){return t.slice(-4)===".bin"?b2(e,r):C2(e,r)}function V2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?t2(e,a,r,n,i,s,f):Rv(e,a,r,n,i,s,f)}function X2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?_2(e,a,r,n,i,s,f):p2(e,a,r,n,i,s,f)}function G2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?zx(e,a,r,n,i,s,f):Kx(e,a,r,n,i,s,f)}function z2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?Xx(e,a,r,n,i,s,f):Gx(e,a,r,n,i,s,f)}function K2(e,t,r,a){return t.slice(-4)===".bin"?W1(e,r,a):C1(e,r,a)}function j2(e,t,r){return bf(e,r)}function $2(e,t,r){return t.slice(-4)===".bin"?jh(e,r):Gh(e,r)}function Y2(e,t,r){return t.slice(-4)===".bin"?Bx(e,r):Dx(e,r)}function Z2(e,t,r){return t.slice(-4)===".bin"?yx(e,t,r):Sx(e,t,r)}function J2(e,t,r,a){return r.slice(-4)===".bin"?Ax(e,t,r,a):void 0}function Q2(e,t,r){return t.slice(-4)===".bin"?Ex(e,t,r):kx(e,t,r)}function q2(e,t,r){return(t.slice(-4)===".bin"?W2:lc)(e,r)}function em(e,t,r,a,n){return(t.slice(-4)===".bin"?x2:tc)(e,r,a,n)}function h_(e,t,r,a,n){return(t.slice(-4)===".bin"?w2:m2)(e,r,a,n)}function rm(e,t,r){return(t.slice(-4)===".bin"?Z1:Pf)(e,r)}function tm(e,t,r){return(t.slice(-4)===".bin"?Zh:Sf)(e,r)}function am(e,t,r){return(t.slice(-4)===".bin"?Ux:Uf)(e,r)}function nm(e){return(e.slice(-4)===".bin"?Tx:Lf)()}var uc=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,hc=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Ft(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),r.length===1)return a;var n=e.match(uc),i,s,f,c;if(n)for(c=0;c!=n.length;++c)i=n[c].match(hc),(s=i[1].indexOf(":"))===-1?a[i[1]]=i[2].slice(1,i[2].length-1):(i[1].slice(0,6)==="xmlns:"?f="xmlns"+i[1].slice(6):f=i[1].slice(s+1),a[f]=i[2].slice(1,i[2].length-1));return a}function im(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var a=e.match(uc),n,i,s,f;if(a)for(f=0;f!=a.length;++f)n=a[f].match(hc),(i=n[1].indexOf(":"))===-1?r[n[1]]=n[2].slice(1,n[2].length-1):(n[1].slice(0,6)==="xmlns:"?s="xmlns"+n[1].slice(6):s=n[1].slice(i+1),r[s]=n[2].slice(1,n[2].length-1));return r}var mn;function sm(e,t){var r=mn[e]||qe(e);return r==="General"?fa(t):ft(r,t)}function fm(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=vr(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=dr(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[qe(t)]=n}function cm(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||Vt[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Ka(e.v):e.w=fa(e.v):e.w=sm(t||"General",e.v)}catch(i){if(r.WTF)throw i}try{var a=mn[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&e.t=="n"&&ca(a)){var n=Yt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(i){if(r.WTF)throw i}}}function om(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=w1[a.Pattern]||a.Pattern)}e[t.ID]=t}function lm(e,t,r,a,n,i,s,f,c,o){var l="General",h=a.StyleID,x={};o=o||{};var d=[],v=0;for(h===void 0&&f&&(h=f.StyleID),h===void 0&&s&&(h=s.StyleID);i[h]!==void 0&&(i[h].nf&&(l=i[h].nf),i[h].Interior&&d.push(i[h].Interior),!!i[h].Parent);)h=i[h].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=vr(e);break;case"String":a.t="s",a.r=as(qe(e)),a.v=e.indexOf("<")>-1?qe(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),a.v=(dr(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),a.v!==a.v?a.v=qe(e):a.v<60&&(a.v=a.v-1),(!l||l=="General")&&(l="yyyy-mm-dd");case"Number":a.v===void 0&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=Rs[e],o.cellText!==!1&&(a.w=e);break;default:e==""&&t==""?a.t="z":(a.t="s",a.v=as(t||e));break}if(cm(a,l,o),o.cellFormula!==!1)if(a.Formula){var u=qe(a.Formula);u.charCodeAt(0)==61&&(u=u.slice(1)),a.f=Ua(u,n),delete a.Formula,a.ArrayRange=="RC"?a.F=Ua("RC:RC",n):a.ArrayRange&&(a.F=Ua(a.ArrayRange,n),c.push([Ze(a.F),a.F]))}else for(v=0;v<c.length;++v)n.r>=c[v][0].s.r&&n.r<=c[v][0].e.r&&n.c>=c[v][0].s.c&&n.c<=c[v][0].e.c&&(a.F=c[v][1]);o.cellStyles&&(d.forEach(function(p){!x.patternType&&p.patternType&&(x.patternType=p.patternType)}),a.s=x),a.StyleID!==void 0&&(a.ixfe=a.StyleID)}function um(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function fi(e,t){var r=t||{};Da();var a=Le(w0(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(typeof Ee!="undefined"?a=Ee.utils.decode(65001,we(a)):a=lr(a));var n=a.slice(0,1024).toLowerCase(),i=!1;if(n=n.replace(/".*?"/g,""),(n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var s=ur(r);return s.type="string",La.to_workbook(a,s)}if(n.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(yr){n.indexOf("<"+yr)>=0&&(i=!0)}),i)return jm(a,r);mn={"General Number":"General","General Date":Se[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":Se[15],"Short Date":Se[14],"Long Time":Se[19],"Medium Time":Se[18],"Short Time":Se[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:Se[2],Standard:Se[4],Percent:Se[10],Scientific:Se[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var f,c=[],o;he!=null&&r.dense==null&&(r.dense=he);var l={},h=[],x=r.dense?[]:{},d="",v={},u={},p=Ft('<Data ss:Type="String">'),E=0,T=0,g=0,R={s:{r:2e6,c:2e6},e:{r:0,c:0}},L={},I={},F="",N=0,P=[],V={},X={},b=0,ae=[],xe=[],ne={},_e=[],pe,ze=!1,le=[],ge=[],j={},C=0,U=0,O={Sheets:[],WBProps:{date1904:!1}},D={};Qa.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"");for(var K="";f=Qa.exec(a);)switch(f[3]=(K=f[3]).toLowerCase()){case"data":if(K=="data"){if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break}if(c[c.length-1][1])break;f[1]==="/"?lm(a.slice(E,f.index),F,p,c[c.length-1][0]=="comment"?ne:v,{c:T,r:g},L,_e[T],u,le,r):(F="",p=Ft(f[0]),E=f.index+f[0].length);break;case"cell":if(f[1]==="/")if(xe.length>0&&(v.c=xe),(!r.sheetRows||r.sheetRows>g)&&v.v!==void 0&&(r.dense?(x[g]||(x[g]=[]),x[g][T]=v):x[pr(T)+kr(g)]=v),v.HRef&&(v.l={Target:qe(v.HRef)},v.HRefScreenTip&&(v.l.Tooltip=v.HRefScreenTip),delete v.HRef,delete v.HRefScreenTip),(v.MergeAcross||v.MergeDown)&&(C=T+(parseInt(v.MergeAcross,10)|0),U=g+(parseInt(v.MergeDown,10)|0),P.push({s:{c:T,r:g},e:{c:C,r:U}})),!r.sheetStubs)v.MergeAcross?T=C+1:++T;else if(v.MergeAcross||v.MergeDown){for(var ce=T;ce<=C;++ce)for(var ee=g;ee<=U;++ee)(ce>T||ee>g)&&(r.dense?(x[ee]||(x[ee]=[]),x[ee][ce]={t:"z"}):x[pr(ce)+kr(ee)]={t:"z"});T=C+1}else++T;else v=im(f[0]),v.Index&&(T=+v.Index-1),T<R.s.c&&(R.s.c=T),T>R.e.c&&(R.e.c=T),f[0].slice(-2)==="/>"&&++T,xe=[];break;case"row":f[1]==="/"||f[0].slice(-2)==="/>"?(g<R.s.r&&(R.s.r=g),g>R.e.r&&(R.e.r=g),f[0].slice(-2)==="/>"&&(u=Ft(f[0]),u.Index&&(g=+u.Index-1)),T=0,++g):(u=Ft(f[0]),u.Index&&(g=+u.Index-1),j={},(u.AutoFitHeight=="0"||u.Height)&&(j.hpx=parseInt(u.Height,10),j.hpt=ln(j.hpx),ge[g]=j),u.Hidden=="1"&&(j.hidden=!0,ge[g]=j));break;case"worksheet":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));h.push(d),R.s.r<=R.e.r&&R.s.c<=R.e.c&&(x["!ref"]=Ue(R),r.sheetRows&&r.sheetRows<=R.e.r&&(x["!fullref"]=x["!ref"],R.e.r=r.sheetRows-1,x["!ref"]=Ue(R))),P.length&&(x["!merges"]=P),_e.length>0&&(x["!cols"]=_e),ge.length>0&&(x["!rows"]=ge),l[d]=x}else R={s:{r:2e6,c:2e6},e:{r:0,c:0}},g=T=0,c.push([f[3],!1]),o=Ft(f[0]),d=qe(o.Name),x=r.dense?[]:{},P=[],le=[],ge=[],D={name:d,Hidden:0},O.Sheets.push(D);break;case"table":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else{if(f[0].slice(-2)=="/>")break;c.push([f[3],!1]),_e=[],ze=!1}break;case"style":f[1]==="/"?om(L,I,r):I=Ft(f[0]);break;case"numberformat":I.nf=qe(Ft(f[0]).Format||"General"),mn[I.nf]&&(I.nf=mn[I.nf]);for(var re=0;re!=392&&Se[re]!=I.nf;++re);if(re==392){for(re=57;re!=392;++re)if(Se[re]==null){Dt(I.nf,re);break}}break;case"column":if(c[c.length-1][0]!=="table")break;if(pe=Ft(f[0]),pe.Hidden&&(pe.hidden=!0,delete pe.Hidden),pe.Width&&(pe.wpx=parseInt(pe.Width,10)),!ze&&pe.wpx>10){ze=!0,Yr=Of;for(var Y=0;Y<_e.length;++Y)_e[Y]&&ra(_e[Y])}ze&&ra(pe),_e[pe.Index-1||_e.length]=pe;for(var He=0;He<+pe.Span;++He)_e[_e.length]=ur(pe);break;case"namedrange":if(f[1]==="/")break;O.Names||(O.Names=[]);var A=Pe(f[0]),fr={Name:A.Name,Ref:Ua(A.RefersTo.slice(1),{r:0,c:0})};O.Sheets.length>0&&(fr.Sheet=O.Sheets.length-1),O.Names.push(fr);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(f[0].slice(-2)==="/>")break;f[1]==="/"?F+=a.slice(N,f.index):N=f.index+f[0].length;break;case"interior":if(!r.cellStyles)break;I.Interior=Ft(f[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(f[0].slice(-2)==="/>")break;f[1]==="/"?Nl(V,K,a.slice(b,f.index)):b=f.index+f[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else c.push([f[3],!1]);break;case"comment":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));um(ne),xe.push(ne)}else c.push([f[3],!1]),o=Ft(f[0]),ne={a:o.Author};break;case"autofilter":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/"){var $e=Ft(f[0]);x["!autofilter"]={ref:Ua($e.Range).replace(/\$/g,"")},c.push([f[3],!0])}break;case"name":break;case"datavalidation":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"null":break;default:if(c.length==0&&f[3]=="document"||c.length==0&&f[3]=="uof")return Cc(a,r);var cr=!0;switch(c[c.length-1][0]){case"officedocumentsettings":switch(f[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:cr=!1}break;case"componentoptions":switch(f[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:cr=!1}break;case"excelworkbook":switch(f[3]){case"date1904":O.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:cr=!1}break;case"workbookoptions":switch(f[3]){case"owcversion":break;case"height":break;case"width":break;default:cr=!1}break;case"worksheetoptions":switch(f[3]){case"visible":if(f[0].slice(-2)!=="/>")if(f[1]==="/")switch(a.slice(b,f.index)){case"SheetHidden":D.Hidden=1;break;case"SheetVeryHidden":D.Hidden=2;break}else b=f.index+f[0].length;break;case"header":x["!margins"]||Ta(x["!margins"]={},"xlml"),isNaN(+Pe(f[0]).Margin)||(x["!margins"].header=+Pe(f[0]).Margin);break;case"footer":x["!margins"]||Ta(x["!margins"]={},"xlml"),isNaN(+Pe(f[0]).Margin)||(x["!margins"].footer=+Pe(f[0]).Margin);break;case"pagemargins":var Ye=Pe(f[0]);x["!margins"]||Ta(x["!margins"]={},"xlml"),isNaN(+Ye.Top)||(x["!margins"].top=+Ye.Top),isNaN(+Ye.Left)||(x["!margins"].left=+Ye.Left),isNaN(+Ye.Right)||(x["!margins"].right=+Ye.Right),isNaN(+Ye.Bottom)||(x["!margins"].bottom=+Ye.Bottom);break;case"displayrighttoleft":O.Views||(O.Views=[]),O.Views[0]||(O.Views[0]={}),O.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:cr=!1}break;case"pivottable":case"pivotcache":switch(f[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:cr=!1}break;case"pagebreaks":switch(f[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:cr=!1}break;case"autofilter":switch(f[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:cr=!1}break;case"querytable":switch(f[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:cr=!1}break;case"datavalidation":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:cr=!1}break;case"sorting":case"conditionalformatting":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:cr=!1}break;case"mapinfo":case"schema":case"data":switch(f[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:cr=!1}break;case"smarttags":break;default:cr=!1;break}if(cr||f[3].match(/!\[CDATA/))break;if(!c[c.length-1][1])throw"Unrecognized tag: "+f[3]+"|"+c.join("|");if(c[c.length-1][0]==="customdocumentproperties"){if(f[0].slice(-2)==="/>")break;f[1]==="/"?fm(X,K,ae,a.slice(b,f.index)):(ae=f,b=f.index+f[0].length);break}if(r.WTF)throw"Unrecognized tag: "+f[3]+"|"+c.join("|")}var ue={};return!r.bookSheets&&!r.bookProps&&(ue.Sheets=l),ue.SheetNames=h,ue.Workbook=O,ue.SSF=ur(Se),ue.Props=V,ue.Custprops=X,ue}function ci(e,t){switch(vi(t=t||{}),t.type||"base64"){case"base64":return fi(hr(e),t);case"binary":case"buffer":case"file":return fi(e,t);case"array":return fi(At(e),t)}}function hm(e,t){var r=[];return e.Props&&r.push(bl(e.Props,t)),e.Custprops&&r.push(Ll(e.Props,e.Custprops,t)),r.join("")}function xm(){return""}function dm(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(a,n){var i=[];i.push(te("NumberFormat",null,{"ss:Format":sr(Se[a.numFmtId])}));var s={"ss:ID":"s"+(21+n)};r.push(te("Style",i.join(""),s))}),te("Styles",r.join(""))}function xc(e){return te("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+ei(e.Ref,{r:0,c:0})})}function vm(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];n.Sheet==null&&(n.Name.match(/^_xlfn\./)||r.push(xc(n)))}return te("Names",r.join(""))}function pm(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,i=[],s=0;s<n.length;++s){var f=n[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(xc(f)))}return i.join("")}function mm(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(te("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(te("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(te("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(te("Visible",a.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(a.Workbook.Sheets[i]&&!a.Workbook.Sheets[i].Hidden);++i);i==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(Vr("ProtectContents","True")),e["!protect"].objects&&n.push(Vr("ProtectObjects","True")),e["!protect"].scenarios&&n.push(Vr("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?n.push(Vr("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&n.push(Vr("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&n.push("<"+s[1]+"/>")})),n.length==0?"":te("WorksheetOptions",n.join(""),{xmlns:ot.x})}function gm(e){return e.map(function(t){var r=Bo(t.t||""),a=te("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return te("Comment",a,{"ss:Author":t.a})}).join("")}function _m(e,t,r,a,n,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+sr(ei(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var c=_r(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(c.r==s.r?"":"["+(c.r-s.r)+"]")+"C"+(c.c==s.c?"":"["+(c.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=sr(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=sr(e.l.Tooltip))),r["!merges"])for(var o=r["!merges"],l=0;l!=o.length;++l)o[l].s.c!=s.c||o[l].s.r!=s.r||(o[l].e.c>o[l].s.c&&(f["ss:MergeAcross"]=o[l].e.c-o[l].s.c),o[l].e.r>o[l].s.r&&(f["ss:MergeDown"]=o[l].e.r-o[l].s.r));var h="",x="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",x=String(e.v);break;case"b":h="Boolean",x=e.v?"1":"0";break;case"e":h="Error",x=Vt[e.v];break;case"d":h="DateTime",x=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Se[14]);break;case"s":h="String",x=Mo(e.v||"");break}var d=aa(a.cellXfs,e,a);f["ss:StyleID"]="s"+(21+d),f["ss:Index"]=s.c+1;var v=e.v!=null?x:"",u=e.t=="z"?"":'<Data ss:Type="'+h+'">'+v+"</Data>";return(e.c||[]).length>0&&(u+=gm(e.c)),te("Cell",u,f)}function wm(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Ma(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function Em(e,t,r,a){if(!e["!ref"])return"";var n=Ze(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(p,E){ra(p);var T=!!p.width,g=r0(E,p),R={"ss:Index":E+1};T&&(R["ss:Width"]=cn(g.width)),p.hidden&&(R["ss:Hidden"]="1"),f.push(te("Column",null,R))});for(var c=Array.isArray(e),o=n.s.r;o<=n.e.r;++o){for(var l=[wm(o,(e["!rows"]||[])[o])],h=n.s.c;h<=n.e.c;++h){var x=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h)&&!(i[s].s.r>o)&&!(i[s].e.c<h)&&!(i[s].e.r<o)){(i[s].s.c!=h||i[s].s.r!=o)&&(x=!0);break}if(!x){var d={r:o,c:h},v=Ce(d),u=c?(e[o]||[])[h]:e[v];l.push(_m(u,v,e,t,r,a,d))}}l.push("</Row>"),l.length>2&&f.push(l.join(""))}return f.join("")}function Tm(e,t,r){var a=[],n=r.SheetNames[e],i=r.Sheets[n],s=i?pm(i,t,e,r):"";return s.length>0&&a.push("<Names>"+s+"</Names>"),s=i?Em(i,t,e,r):"",s.length>0&&a.push("<Table>"+s+"</Table>"),a.push(mm(i,t,e,r)),a.join("")}function km(e,t){t||(t={}),e.SSF||(e.SSF=ur(Se)),e.SSF&&(Da(),ja(e.SSF),t.revssf=Rn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],aa(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(hm(e,t)),r.push(xm(e,t)),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(te("Worksheet",Tm(a,t,e),{"ss:Name":sr(e.SheetNames[a])}));return r[2]=dm(e,t),r[3]=vm(e,t),Tr+te("Workbook",r.join(""),{xmlns:ot.ss,"xmlns:o":ot.o,"xmlns:x":ot.x,"xmlns:ss":ot.ss,"xmlns:dt":ot.dt,"xmlns:html":ot.html})}function Sm(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=dl(r),r.length-r.l<=4)return t;var a=r.read_shift(4);if(a==0||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(a=r.read_shift(4),a!==1907505652)||(t.UnicodeClipboardFormat=vl(r),a=r.read_shift(4),a==0||a>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var Fm=[60,1084,2066,2165,2175];function ym(e,t,r,a,n){var i=a,s=[],f=r.slice(r.l,r.l+i);if(n&&n.enc&&n.enc.insitu&&f.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(f)}s.push(f),r.l+=i;for(var c=Wt(r,r.l),o=oi[c],l=0;o!=null&&Fm.indexOf(c)>-1;)i=Wt(r,r.l+2),l=r.l+4,c==2066?l+=4:(c==2165||c==2175)&&(l+=12),f=r.slice(l,r.l+4+i),s.push(f),r.l+=4+i,o=oi[c=Wt(r,r.l)];var h=or(s);Xr(h,0);var x=0;h.lens=[];for(var d=0;d<s.length;++d)h.lens.push(x),x+=s[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function Nt(e,t,r){if(e.t!=="z"&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=Se[a])}catch(i){if(t.WTF)throw i}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||Vt[e.v]:a===0||a=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Ka(e.v):e.w=fa(e.v):e.w=ft(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(i){if(t.WTF)throw i}if(t.cellDates&&a&&e.t=="n"&&ca(Se[a]||String(a))){var n=Yt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function t0(e,t,r){return{v:e,ixfe:t,t:r}}function Am(e,t){var r={opts:{}},a={};he!=null&&t.dense==null&&(t.dense=he);var n=t.dense?[]:{},i={},s={},f=null,c=[],o="",l={},h,x="",d,v,u,p,E={},T=[],g,R,L=[],I=[],F={Sheets:[],WBProps:{date1904:!1},Views:[{}]},N={},P=function(Ke){return Ke<8?pa[Ke]:Ke<64&&I[Ke-8]||pa[Ke]},V=function(Ke,Ar,Tt){var Nr=Ar.XF.data;if(!(!Nr||!Nr.patternType||!Tt||!Tt.cellStyles)){Ar.s={},Ar.s.patternType=Nr.patternType;var Sa;(Sa=fn(P(Nr.icvFore)))&&(Ar.s.fgColor={rgb:Sa}),(Sa=fn(P(Nr.icvBack)))&&(Ar.s.bgColor={rgb:Sa})}},X=function(Ke,Ar,Tt){if(!(j>1)&&!(Tt.sheetRows&&Ke.r>=Tt.sheetRows)){if(Tt.cellStyles&&Ar.XF&&Ar.XF.data&&V(Ke,Ar,Tt),delete Ar.ixfe,delete Ar.XF,h=Ke,x=Ce(Ke),(!s||!s.s||!s.e)&&(s={s:{r:0,c:0},e:{r:0,c:0}}),Ke.r<s.s.r&&(s.s.r=Ke.r),Ke.c<s.s.c&&(s.s.c=Ke.c),Ke.r+1>s.e.r&&(s.e.r=Ke.r+1),Ke.c+1>s.e.c&&(s.e.c=Ke.c+1),Tt.cellFormula&&Ar.f){for(var Nr=0;Nr<T.length;++Nr)if(!(T[Nr][0].s.c>Ke.c||T[Nr][0].s.r>Ke.r)&&!(T[Nr][0].e.c<Ke.c||T[Nr][0].e.r<Ke.r)){Ar.F=Ue(T[Nr][0]),(T[Nr][0].s.c!=Ke.c||T[Nr][0].s.r!=Ke.r)&&delete Ar.f,Ar.f&&(Ar.f=""+Zr(T[Nr][1],s,Ke,le,b));break}}Tt.dense?(n[Ke.r]||(n[Ke.r]=[]),n[Ke.r][Ke.c]=Ar):n[x]=Ar}},b={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:T,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(b.password=t.password);var ae,xe=[],ne=[],_e=[],pe=[],ze=!1,le=[];le.SheetNames=b.snames,le.sharedf=b.sharedf,le.arrayf=b.arrayf,le.names=[],le.XTI=[];var ge=0,j=0,C=0,U=[],O=[],D;b.codepage=1200,de(1200);for(var K=!1;e.l<e.length-1;){var ce=e.l,ee=e.read_shift(2);if(ee===0&&ge===10)break;var re=e.l===e.length?0:e.read_shift(2),Y=oi[ee];if(Y&&Y.f){if(t.bookSheets&&ge===133&&ee!==133)break;if(ge=ee,Y.r===2||Y.r==12){var He=e.read_shift(2);if(re-=2,!b.enc&&He!==ee&&((He&255)<<8|He>>8)!==ee)throw new Error("rt mismatch: "+He+"!="+ee);Y.r==12&&(e.l+=10,re-=10)}var A={};if(ee===10?A=Y.f(e,re,b):A=ym(ee,Y,e,re,b),j==0&&[9,521,1033,2057].indexOf(ge)===-1)continue;switch(ee){case 34:r.opts.Date1904=F.WBProps.date1904=A;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(b.enc||(e.l=0),b.enc=A,!t.password)throw new Error("File is password-protected");if(A.valid==null)throw new Error("Encryption scheme unsupported");if(!A.valid)throw new Error("Password is incorrect");break;case 92:b.lastuser=A;break;case 66:var fr=Number(A);switch(fr){case 21010:fr=1200;break;case 32768:fr=1e4;break;case 32769:fr=1252;break}de(b.codepage=fr),K=!0;break;case 317:b.rrtabid=A;break;case 25:b.winlocked=A;break;case 439:r.opts.RefreshAll=A;break;case 12:r.opts.CalcCount=A;break;case 16:r.opts.CalcDelta=A;break;case 17:r.opts.CalcIter=A;break;case 13:r.opts.CalcMode=A;break;case 14:r.opts.CalcPrecision=A;break;case 95:r.opts.CalcSaveRecalc=A;break;case 15:b.CalcRefMode=A;break;case 2211:r.opts.FullCalc=A;break;case 129:A.fDialog&&(n["!type"]="dialog"),A.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),A.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:L.push(A);break;case 430:le.push([A]),le[le.length-1].XTI=[];break;case 35:case 547:le[le.length-1].push(A);break;case 24:case 536:D={Name:A.Name,Ref:Zr(A.rgce,s,null,le,b)},A.itab>0&&(D.Sheet=A.itab-1),le.names.push(D),le[0]||(le[0]=[],le[0].XTI=[]),le[le.length-1].push(A),A.Name=="_xlnm._FilterDatabase"&&A.itab>0&&A.rgce&&A.rgce[0]&&A.rgce[0][0]&&A.rgce[0][0][0]=="PtgArea3d"&&(O[A.itab-1]={ref:Ue(A.rgce[0][0][1][2])});break;case 22:b.ExternCount=A;break;case 23:le.length==0&&(le[0]=[],le[0].XTI=[]),le[le.length-1].XTI=le[le.length-1].XTI.concat(A),le.XTI=le.XTI.concat(A);break;case 2196:if(b.biff<8)break;D!=null&&(D.Comment=A[1]);break;case 18:n["!protect"]=A;break;case 19:A!==0&&b.WTF&&console.error("Password verifier: "+A);break;case 133:i[A.pos]=A,b.snames.push(A.name);break;case 10:{if(--j)break;if(s.e){if(s.e.r>0&&s.e.c>0){if(s.e.r--,s.e.c--,n["!ref"]=Ue(s),t.sheetRows&&t.sheetRows<=s.e.r){var $e=s.e.r;s.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=Ue(s),s.e.r=$e}s.e.r++,s.e.c++}xe.length>0&&(n["!merges"]=xe),ne.length>0&&(n["!objects"]=ne),_e.length>0&&(n["!cols"]=_e),pe.length>0&&(n["!rows"]=pe),F.Sheets.push(N)}o===""?l=n:a[o]=n,n=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(b.biff===8&&(b.biff={9:2,521:3,1033:4}[ee]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[A.BIFFVer]||8),b.biffguess=A.BIFFVer==0,A.BIFFVer==0&&A.dt==4096&&(b.biff=5,K=!0,de(b.codepage=28591)),b.biff==8&&A.BIFFVer==0&&A.dt==16&&(b.biff=2),j++)break;if(n=t.dense?[]:{},b.biff<8&&!K&&(K=!0,de(b.codepage=t.codepage||1252)),b.biff<5||A.BIFFVer==0&&A.dt==4096){o===""&&(o="Sheet1"),s={s:{r:0,c:0},e:{r:0,c:0}};var cr={pos:e.l-re,name:o};i[cr.pos]=cr,b.snames.push(o)}else o=(i[ce]||{name:""}).name;A.dt==32&&(n["!type"]="chart"),A.dt==64&&(n["!type"]="macro"),xe=[],ne=[],b.arrayf=T=[],_e=[],pe=[],ze=!1,N={Hidden:(i[ce]||{hs:0}).hs,name:o}}break;case 515:case 3:case 2:n["!type"]=="chart"&&(t.dense?(n[A.r]||[])[A.c]:n[Ce({c:A.c,r:A.r})])&&++A.c,g={ixfe:A.ixfe,XF:L[A.ixfe]||{},v:A.val,t:"n"},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t);break;case 5:case 517:g={ixfe:A.ixfe,XF:L[A.ixfe],v:A.val,t:A.t},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t);break;case 638:g={ixfe:A.ixfe,XF:L[A.ixfe],v:A.rknum,t:"n"},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t);break;case 189:for(var Ye=A.c;Ye<=A.C;++Ye){var ue=A.rkrec[Ye-A.c][0];g={ixfe:ue,XF:L[ue],v:A.rkrec[Ye-A.c][1],t:"n"},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:Ye,r:A.r},g,t)}break;case 6:case 518:case 1030:{if(A.val=="String"){f=A;break}if(g=t0(A.val,A.cell.ixfe,A.tt),g.XF=L[g.ixfe],t.cellFormula){var yr=A.formula;if(yr&&yr[0]&&yr[0][0]&&yr[0][0][0]=="PtgExp"){var Et=yr[0][0][1][0],Lt=yr[0][0][1][1],Gt=Ce({r:Et,c:Lt});E[Gt]?g.f=""+Zr(A.formula,s,A.cell,le,b):g.F=((t.dense?(n[Et]||[])[Lt]:n[Gt])||{}).F}else g.f=""+Zr(A.formula,s,A.cell,le,b)}C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X(A.cell,g,t),f=A}break;case 7:case 519:if(f)f.val=A,g=t0(A,f.cell.ixfe,"s"),g.XF=L[g.ixfe],t.cellFormula&&(g.f=""+Zr(f.formula,s,f.cell,le,b)),C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X(f.cell,g,t),f=null;else throw new Error("String record expects Formula");break;case 33:case 545:{T.push(A);var kn=Ce(A[0].s);if(d=t.dense?(n[A[0].s.r]||[])[A[0].s.c]:n[kn],t.cellFormula&&d){if(!f||!kn||!d)break;d.f=""+Zr(A[1],s,A[0],le,b),d.F=Ue(A[0])}}break;case 1212:{if(!t.cellFormula)break;if(x){if(!f)break;E[Ce(f.cell)]=A[0],d=t.dense?(n[f.cell.r]||[])[f.cell.c]:n[Ce(f.cell)],(d||{}).f=""+Zr(A[0],s,h,le,b)}}break;case 253:g=t0(c[A.isst].t,A.ixfe,"s"),c[A.isst].h&&(g.h=c[A.isst].h),g.XF=L[g.ixfe],C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t);break;case 513:t.sheetStubs&&(g={ixfe:A.ixfe,XF:L[A.ixfe],t:"z"},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t));break;case 190:if(t.sheetStubs)for(var ia=A.c;ia<=A.C;++ia){var lt=A.ixfe[ia-A.c];g={ixfe:lt,XF:L[lt],t:"z"},C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:ia,r:A.r},g,t)}break;case 214:case 516:case 4:g=t0(A.val,A.ixfe,"s"),g.XF=L[g.ixfe],C>0&&(g.z=U[g.ixfe>>8&63]),Nt(g,t,r.opts.Date1904),X({c:A.c,r:A.r},g,t);break;case 0:case 512:j===1&&(s=A);break;case 252:c=A;break;case 1054:if(b.biff==4){U[C++]=A[1];for(var zt=0;zt<C+163&&Se[zt]!=A[1];++zt);zt>=163&&Dt(A[1],C+163)}else Dt(A[1],A[0]);break;case 30:{U[C++]=A;for(var sa=0;sa<C+163&&Se[sa]!=A;++sa);sa>=163&&Dt(A,C+163)}break;case 229:xe=xe.concat(A);break;case 93:ne[A.cmo[0]]=b.lastobj=A;break;case 438:b.lastobj.TxO=A;break;case 127:b.lastobj.ImData=A;break;case 440:for(p=A[0].s.r;p<=A[0].e.r;++p)for(u=A[0].s.c;u<=A[0].e.c;++u)d=t.dense?(n[p]||[])[u]:n[Ce({c:u,r:p})],d&&(d.l=A[1]);break;case 2048:for(p=A[0].s.r;p<=A[0].e.r;++p)for(u=A[0].s.c;u<=A[0].e.c;++u)d=t.dense?(n[p]||[])[u]:n[Ce({c:u,r:p})],d&&d.l&&(d.l.Tooltip=A[1]);break;case 28:{if(b.biff<=5&&b.biff>=2)break;d=t.dense?(n[A[0].r]||[])[A[0].c]:n[Ce(A[0])];var Sn=ne[A[2]];d||(t.dense?(n[A[0].r]||(n[A[0].r]=[]),d=n[A[0].r][A[0].c]={t:"z"}):d=n[Ce(A[0])]={t:"z"},s.e.r=Math.max(s.e.r,A[0].r),s.s.r=Math.min(s.s.r,A[0].r),s.e.c=Math.max(s.e.c,A[0].c),s.s.c=Math.min(s.s.c,A[0].c)),d.c||(d.c=[]),v={a:A[1],t:Sn.TxO.t},d.c.push(v)}break;case 2173:xx(L[A.ixfe],A.ext);break;case 125:{if(!b.cellStyles)break;for(;A.e>=A.s;)_e[A.e--]={width:A.w/256,level:A.level||0,hidden:!!(A.flags&1)},ze||(ze=!0,Z0(A.w/256)),ra(_e[A.e+1])}break;case 520:{var tt={};A.level!=null&&(pe[A.r]=tt,tt.level=A.level),A.hidden&&(pe[A.r]=tt,tt.hidden=!0),A.hpt&&(pe[A.r]=tt,tt.hpt=A.hpt,tt.hpx=Ma(A.hpt))}break;case 38:case 39:case 40:case 41:n["!margins"]||Ta(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[ee]]=A;break;case 161:n["!margins"]||Ta(n["!margins"]={}),n["!margins"].header=A.header,n["!margins"].footer=A.footer;break;case 574:A.RTL&&(F.Views[0].RTL=!0);break;case 146:I=A;break;case 2198:ae=A;break;case 140:R=A;break;case 442:o?N.CodeName=A||N.name:F.WBProps.CodeName=A||"ThisWorkbook";break}}else Y||console.error("Missing Info for XLS Record 0x"+ee.toString(16)),e.l+=re}return r.SheetNames=wr(i).sort(function(Mt,Ke){return Number(Mt)-Number(Ke)}).map(function(Mt){return i[Mt].name}),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&l["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=l)):r.Preamble=l,r.Sheets&&O.forEach(function(Mt,Ke){r.Sheets[r.SheetNames[Ke]]["!autofilter"]=Mt}),r.Strings=c,r.SSF=ur(Se),b.enc&&(r.Encryption=b.enc),ae&&(r.Themes=ae),r.Metadata={},R!==void 0&&(r.Metadata.Country=R),le.names.length>0&&(F.Names=le.names),r.Workbook=F,r}var gn={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function Cm(e,t,r){var a=Te.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Qs(a,L0,gn.DSI);for(var i in n)t[i]=n[i]}catch(o){if(r.WTF)throw o}var s=Te.find(e,"/!SummaryInformation");if(s&&s.size>0)try{var f=Qs(s,M0,gn.SI);for(var c in f)t[c]==null&&(t[c]=f[c])}catch(o){if(r.WTF)throw o}t.HeadingPairs&&t.TitlesOfParts&&(Ws(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function Dm(e,t){var r=[],a=[],n=[],i=0,s,f=Xi(L0,"n"),c=Xi(M0,"n");if(e.Props)for(s=wr(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=wr(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Custprops[s[i]]]);var o=[];for(i=0;i<n.length;++i)Zs.indexOf(n[i][0])>-1||Us.indexOf(n[i][0])>-1||n[i][1]!=null&&o.push(n[i]);a.length&&Te.utils.cfb_add(t,"/SummaryInformation",qs(a,gn.SI,c,M0)),(r.length||o.length)&&Te.utils.cfb_add(t,"/DocumentSummaryInformation",qs(r,gn.DSI,f,L0,o.length?o:null,gn.UDI))}function dc(e,t){t||(t={}),vi(t),ve(),t.codepage&&be(t.codepage);var r,a;if(e.FullPaths){if(Te.find(e,"/encryption"))throw new Error("File is password-protected");r=Te.find(e,"!CompObj"),a=Te.find(e,"/Workbook")||Te.find(e,"/Book")}else{switch(t.type){case"base64":e=ar(hr(e));break;case"binary":e=ar(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}Xr(e,0),a={content:e}}var n,i;if(r&&Sm(r),t.bookProps&&!t.bookSheets)n={};else{var s=ye?"buffer":"array";if(a&&a.content)n=Am(a.content,t);else if((i=Te.find(e,"PerfectOffice_MAIN"))&&i.content)n=Ea.to_workbook(i.content,(t.type=s,t));else if((i=Te.find(e,"NativeContent_MAIN"))&&i.content)n=Ea.to_workbook(i.content,(t.type=s,t));else throw(i=Te.find(e,"MN0"))&&i.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&Te.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=Hx(e))}var f={};return e.FullPaths&&Cm(e,f,t),n.Props=n.Custprops=f,t.bookFiles&&(n.cfb=e),n}function Om(e,t){var r=t||{},a=Te.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Te.utils.cfb_add(a,n,vc(e,r)),r.biff==8&&(e.Props||e.Custprops)&&Dm(e,a),r.biff==8&&e.vbaraw&&Vx(a,Te.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),a}var _n={0:{f:qv},1:{f:fp},2:{f:yp},3:{f:vp},4:{f:up},5:{f:kp},6:{f:Ip},7:{f:_p},8:{f:Bp},9:{f:Mp},10:{f:bp},11:{f:Lp},12:{f:op},13:{f:Cp},14:{f:mp},15:{f:xp},16:{f:ac},17:{f:Rp},18:{f:Ep},19:{f:O0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:N2},40:{},42:{},43:{f:I1},44:{f:D1},45:{f:N1},46:{f:L1},47:{f:b1},48:{},49:{f:tl},50:{},51:{f:px},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:wf},62:{f:Op},63:{f:Fx},64:{f:e2},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:zr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Zp},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:ip},148:{f:tp,p:16},151:{f:Gp},152:{},153:{f:I2},154:{},155:{},156:{f:D2},157:{},158:{},159:{T:1,f:Kh},160:{T:-1},161:{T:1,f:da},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Up},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:dx},336:{T:-1},337:{f:_x,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:P0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:gf},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:zp},427:{f:Kp},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:$p},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:np},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Vp},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:P0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Lx},633:{T:1},634:{T:-1},635:{T:1,f:Nx},636:{T:-1},637:{f:sl},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:g2},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:r2},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},oi={6:{f:ti},10:{f:ea},12:{f:Or},13:{f:Or},14:{f:Fr},15:{f:Fr},16:{f:jr},17:{f:Fr},18:{f:Fr},19:{f:Or},20:{f:vf},21:{f:vf},23:{f:gf},24:{f:mf},25:{f:Fr},26:{},27:{},28:{f:rh},29:{},34:{f:Fr},35:{f:pf},38:{f:jr},39:{f:jr},40:{f:jr},41:{f:jr},42:{f:Fr},43:{f:Fr},47:{f:x1},49:{f:Cu},51:{f:Or},60:{},61:{f:ku},64:{f:Fr},65:{f:Au},66:{f:Or},77:{},80:{},81:{},82:{},85:{f:Or},89:{},90:{},91:{},92:{f:hu},93:{f:nh},94:{},95:{f:Fr},96:{},97:{},99:{f:Fr},125:{f:wf},128:{f:Xu},129:{f:du},130:{f:Or},131:{f:Fr},132:{f:Fr},133:{f:vu},134:{},140:{f:hh},141:{f:Or},144:{},146:{f:vh},151:{},152:{},153:{},154:{},155:{},156:{f:Or},157:{},158:{},160:{f:Th},161:{f:gh},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Uu},190:{f:Wu},193:{f:ea},197:{},198:{},199:{},200:{},201:{},202:{f:Fr},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Or},220:{},221:{f:Fr},222:{},224:{f:Vu},225:{f:uu},226:{f:ea},227:{},229:{f:th},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:mu},253:{f:Ou},255:{f:_u},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:ef},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Fr},353:{f:ea},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:$u},431:{f:Fr},432:{},433:{},434:{},437:{},438:{f:fh},439:{f:Fr},440:{f:ch},441:{},442:{f:sn},443:{},444:{f:Or},445:{},446:{},448:{f:ea},449:{f:Tu,r:2},450:{f:ea},512:{f:hf},513:{f:Eh},515:{f:Ku},516:{f:Pu},517:{f:df},519:{f:kh},520:{f:wu},523:{},545:{f:_f},549:{f:uf},566:{},574:{f:Fu},638:{f:Bu},659:{},1048:{},1054:{f:Nu},1084:{},1212:{f:Qu},2048:{f:lh},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:$n},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ea},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:ph,r:12},2173:{f:hx,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Fr,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:Ju,r:12},2197:{},2198:{f:sx,r:12},2199:{},2200:{},2201:{},2202:{f:qu,r:12},2203:{f:ea},2204:{},2205:{},2206:{},2207:{},2211:{f:Eu},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Or},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:_h},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:dh},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:hf},1:{},2:{f:Ch},3:{f:yh},4:{f:Fh},5:{f:df},7:{f:Oh},8:{},9:{f:$n},11:{},22:{f:Or},30:{f:Lu},31:{},32:{},33:{f:_f},36:{},37:{f:uf},50:{f:Ih},62:{},52:{},67:{},68:{f:Or},69:{},86:{},126:{},127:{f:Sh},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:Ph},223:{},234:{},354:{},421:{},518:{f:ti},521:{f:$n},536:{f:mf},547:{f:pf},561:{},579:{},1030:{f:ti},1033:{f:$n},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function ie(e,t,r,a){var n=t;if(!isNaN(n)){var i=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,n),s.write_shift(2,i),i>0&&y0(r)&&e.push(r)}}function Im(e,t,r,a){var n=a||(r||[]).length||0;if(n<=8224)return ie(e,t,r,n);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,c=0,o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;var l=e.next(4);for(l.write_shift(2,i),l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o;c<n;){for(l=e.next(4),l.write_shift(2,60),o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o}}}function wn(e,t,r){return e||(e=G(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Pm(e,t,r,a){var n=G(9);return wn(n,e,t),rf(r,a||"b",n),n}function Rm(e,t,r){var a=G(8+2*r.length);return wn(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}function Nm(e,t,r,a){if(t.v!=null)switch(t.t){case"d":case"n":var n=t.t=="d"?Rr(dr(t.v)):t.v;n==(n|0)&&n>=0&&n<65536?ie(e,2,Dh(r,a,n)):ie(e,3,Ah(r,a,n));return;case"b":case"e":ie(e,5,Pm(r,a,t.v,t.t));return;case"s":case"str":ie(e,4,Rm(r,a,(t.v||"").slice(0,255)));return}ie(e,1,wn(null,r,a))}function bm(e,t,r,a){var n=Array.isArray(t),i=Ze(t["!ref"]||"A1"),s,f="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=Ue(i)}for(var o=i.s.r;o<=i.e.r;++o){f=kr(o);for(var l=i.s.c;l<=i.e.c;++l){o===i.s.r&&(c[l]=pr(l)),s=c[l]+f;var h=n?(t[o]||[])[l]:t[s];h&&Nm(e,h,o,l,a)}}}function Lm(e,t){var r=t||{};he!=null&&r.dense==null&&(r.dense=he);for(var a=qr(),n=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(n=i);if(n==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ie(a,r.biff==4?1033:r.biff==3?521:9,z0(e,16,r)),bm(a,e.Sheets[e.SheetNames[n]],n,r,e),ie(a,10),a.end()}function Mm(e,t,r){ie(e,49,Du({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function Bm(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&ie(e,1054,bu(n,t[n],r))})}function Um(e,t){var r=G(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ie(e,2151,r),r=G(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),cf(Ze(t["!ref"]||"A1"),r),r.write_shift(4,4),ie(e,2152,r)}function Wm(e,t){for(var r=0;r<16;++r)ie(e,224,xf({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(a){ie(e,224,xf(a,0,t))})}function Hm(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];ie(e,440,oh(a)),a[1].Tooltip&&ie(e,2048,uh(a))}delete t["!links"]}function Vm(e,t){if(t){var r=0;t.forEach(function(a,n){++r<=256&&a&&ie(e,125,mh(r0(n,a),n))})}}function Xm(e,t,r,a,n){var i=16+aa(n.cellXfs,t,n);if(t.v==null&&!t.bf){ie(e,513,wa(r,a,i));return}if(t.bf)ie(e,6,dv(t,r,a,n,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?Rr(dr(t.v)):t.v;ie(e,515,ju(r,a,s,i,n));break;case"b":case"e":ie(e,517,zu(r,a,t.v,i,n,t.t));break;case"s":case"str":if(n.bookSST){var f=ni(n.Strings,t.v,n.revStrings);ie(e,253,Iu(r,a,f,i,n))}else ie(e,516,Ru(r,a,(t.v||"").slice(0,255),i,n));break;default:ie(e,513,wa(r,a,i))}}function Gm(e,t,r){var a=qr(),n=r.SheetNames[e],i=r.Sheets[n]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},c=Array.isArray(i),o=t.biff==8,l,h="",x=[],d=Ze(i["!ref"]||"A1"),v=o?65536:16384;if(d.e.c>255||d.e.r>=v){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,v-1)}ie(a,2057,z0(r,16,t)),ie(a,13,mt(1)),ie(a,12,mt(100)),ie(a,15,rt(!0)),ie(a,17,rt(!1)),ie(a,16,va(.001)),ie(a,95,rt(!0)),ie(a,42,rt(!1)),ie(a,43,rt(!1)),ie(a,130,mt(1)),ie(a,128,Gu([0,0])),ie(a,131,rt(!1)),ie(a,132,rt(!1)),o&&Vm(a,i["!cols"]),ie(a,512,Mu(d,t)),o&&(i["!links"]=[]);for(var u=d.s.r;u<=d.e.r;++u){h=kr(u);for(var p=d.s.c;p<=d.e.c;++p){u===d.s.r&&(x[p]=pr(p)),l=x[p]+h;var E=c?(i[u]||[])[p]:i[l];E&&(Xm(a,E,u,p,t),o&&E.l&&i["!links"].push([l,E.l]))}}var T=f.CodeName||f.name||n;return o&&ie(a,574,yu((s.Views||[])[0])),o&&(i["!merges"]||[]).length&&ie(a,229,ah(i["!merges"])),o&&Hm(a,i),ie(a,442,tf(T,t)),o&&Um(a,i),ie(a,10),a.end()}function zm(e,t,r){var a=qr(),n=(e||{}).Workbook||{},i=n.Sheets||[],s=n.WBProps||{},f=r.biff==8,c=r.biff==5;if(ie(a,2057,z0(e,5,r)),r.bookType=="xla"&&ie(a,135),ie(a,225,f?mt(1200):null),ie(a,193,zl(2)),c&&ie(a,191),c&&ie(a,192),ie(a,226),ie(a,92,xu("SheetJS",r)),ie(a,66,mt(f?1200:1252)),f&&ie(a,353,mt(0)),f&&ie(a,448),ie(a,317,wh(e.SheetNames.length)),f&&e.vbaraw&&ie(a,211),f&&e.vbaraw){var o=s.CodeName||"ThisWorkbook";ie(a,442,tf(o,r))}ie(a,156,mt(17)),ie(a,25,rt(!1)),ie(a,18,rt(!1)),ie(a,19,mt(0)),f&&ie(a,431,rt(!1)),f&&ie(a,444,mt(0)),ie(a,61,Su(r)),ie(a,64,rt(!1)),ie(a,141,mt(0)),ie(a,34,rt(S2(e)=="true")),ie(a,14,rt(!0)),f&&ie(a,439,rt(!1)),ie(a,218,mt(0)),Mm(a,e,r),Bm(a,e.SSF,r),Wm(a,r),f&&ie(a,352,rt(!1));var l=a.end(),h=qr();f&&ie(h,140,xh()),f&&r.Strings&&Im(h,252,gu(r.Strings,r)),ie(h,10);var x=h.end(),d=qr(),v=0,u=0;for(u=0;u<e.SheetNames.length;++u)v+=(f?12:11)+(f?2:1)*e.SheetNames[u].length;var p=l.length+v+x.length;for(u=0;u<e.SheetNames.length;++u){var E=i[u]||{};ie(d,133,pu({pos:p,hs:E.Hidden||0,dt:0,name:e.SheetNames[u]},r)),p+=t[u].length}var T=d.end();if(v!=T.length)throw new Error("BS8 "+v+" != "+T.length);var g=[];return l.length&&g.push(l),T.length&&g.push(T),x.length&&g.push(x),or(g)}function Km(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=ur(Se)),e&&e.SSF&&(Da(),ja(e.SSF),r.revssf=Rn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,pi(r),r.cellXfs=[],aa(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Gm(n,r,e);return a.unshift(zm(e,a,r)),or(a)}function vc(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(!(!a||!a["!ref"])){var n=et(a["!ref"]);n.e.c>255&&typeof console!="undefined"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Km(e,t);case 4:case 3:case 2:return Lm(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function pc(e,t){var r=t||{};he!=null&&r.dense==null&&(r.dense=he);var a=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i),s=n.index,f=i&&i.index||e.length,c=Oo(e.slice(s,f),/(:?<tr[^>]*>)/i,"<tr>"),o=-1,l=0,h=0,x=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},v=[];for(s=0;s<c.length;++s){var u=c[s].trim(),p=u.slice(0,3).toLowerCase();if(p=="<tr"){if(++o,r.sheetRows&&r.sheetRows<=o){--o;break}l=0;continue}if(!(p!="<td"&&p!="<th")){var E=u.split(/<\/t[dh]>/i);for(f=0;f<E.length;++f){var T=E[f].trim();if(T.match(/<t[dh]/i)){for(var g=T,R=0;g.charAt(0)=="<"&&(R=g.indexOf(">"))>-1;)g=g.slice(R+1);for(var L=0;L<v.length;++L){var I=v[L];I.s.c==l&&I.s.r<o&&o<=I.e.r&&(l=I.e.c+1,L=-1)}var F=Pe(T.slice(0,T.indexOf(">")));x=F.colspan?+F.colspan:1,((h=+F.rowspan)>1||x>1)&&v.push({s:{r:o,c:l},e:{r:o+(h||1)-1,c:l+x-1}});var N=F.t||F["data-t"]||"";if(!g.length){l+=x;continue}if(g=ss(g),d.s.r>o&&(d.s.r=o),d.e.r<o&&(d.e.r=o),d.s.c>l&&(d.s.c=l),d.e.c<l&&(d.e.c=l),!g.length){l+=x;continue}var P={t:"s",v:g};r.raw||!g.trim().length||N=="s"||(g==="TRUE"?P={t:"b",v:!0}:g==="FALSE"?P={t:"b",v:!1}:isNaN(kt(g))?isNaN(Oa(g).getDate())||(P={t:"d",v:dr(g)},r.cellDates||(P={t:"n",v:Rr(P.v)}),P.z=r.dateNF||Se[14]):P={t:"n",v:kt(g)}),r.dense?(a[o]||(a[o]=[]),a[o][l]=P):a[Ce({r:o,c:l})]=P,l+=x}}}}return a["!ref"]=Ue(d),v.length&&(a["!merges"]=v),a}function mc(e,t,r,a){for(var n=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,c=0,o=0;o<n.length;++o)if(!(n[o].s.r>r||n[o].s.c>s)&&!(n[o].e.r<r||n[o].e.c<s)){if(n[o].s.r<r||n[o].s.c<s){f=-1;break}f=n[o].e.r-n[o].s.r+1,c=n[o].e.c-n[o].s.c+1;break}if(!(f<0)){var l=Ce({r,c:s}),h=a.dense?(e[r]||[])[s]:e[l],x=h&&h.v!=null&&(h.h||m0(h.w||(Pt(h),h.w)||""))||"",d={};f>1&&(d.rowspan=f),c>1&&(d.colspan=c),a.editable?x='<span contenteditable="true">'+x+"</span>":h&&(d["data-t"]=h&&h.t||"z",h.v!=null&&(d["data-v"]=h.v),h.z!=null&&(d["data-z"]=h.z),h.l&&(h.l.Target||"#").charAt(0)!="#"&&(x='<a href="'+h.l.Target+'">'+x+"</a>")),d.id=(a.id||"sjs")+"-"+l,i.push(te("td",x,d))}}var v="<tr>";return v+i.join("")+"</tr>"}var gc='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',_c="</body></html>";function jm(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return qt(pc(r[0],t),t);var a=Ei();return r.forEach(function(n,i){Ti(a,pc(n,t),"Sheet"+(i+1))}),a}function wc(e,t,r){var a=[];return a.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ec(e,t){var r=t||{},a=r.header!=null?r.header:gc,n=r.footer!=null?r.footer:_c,i=[a],s=et(e["!ref"]);r.dense=Array.isArray(e),i.push(wc(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(mc(e,s,f,r));return i.push("</table>"+n),i.join("")}function Tc(e,t,r){var a=r||{};he!=null&&(a.dense=he);var n=0,i=0;if(a.origin!=null)if(typeof a.origin=="number")n=a.origin;else{var s=typeof a.origin=="string"?_r(a.origin):a.origin;n=s.r,i=s.c}var f=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,f.length),o={s:{r:0,c:0},e:{r:n,c:i}};if(e["!ref"]){var l=et(e["!ref"]);o.s.r=Math.min(o.s.r,l.s.r),o.s.c=Math.min(o.s.c,l.s.c),o.e.r=Math.max(o.e.r,l.e.r),o.e.c=Math.max(o.e.c,l.e.c),n==-1&&(o.e.r=n=l.e.r+1)}var h=[],x=0,d=e["!rows"]||(e["!rows"]=[]),v=0,u=0,p=0,E=0,T=0,g=0;for(e["!cols"]||(e["!cols"]=[]);v<f.length&&u<c;++v){var R=f[v];if(Sc(R)){if(a.display)continue;d[u]={hidden:!0}}var L=R.children;for(p=E=0;p<L.length;++p){var I=L[p];if(!(a.display&&Sc(I))){var F=I.hasAttribute("data-v")?I.getAttribute("data-v"):I.hasAttribute("v")?I.getAttribute("v"):ss(I.innerHTML),N=I.getAttribute("data-z")||I.getAttribute("z");for(x=0;x<h.length;++x){var P=h[x];P.s.c==E+i&&P.s.r<u+n&&u+n<=P.e.r&&(E=P.e.c+1-i,x=-1)}g=+I.getAttribute("colspan")||1,((T=+I.getAttribute("rowspan")||1)>1||g>1)&&h.push({s:{r:u+n,c:E+i},e:{r:u+n+(T||1)-1,c:E+i+(g||1)-1}});var V={t:"s",v:F},X=I.getAttribute("data-t")||I.getAttribute("t")||"";F!=null&&(F.length==0?V.t=X||"z":a.raw||F.trim().length==0||X=="s"||(F==="TRUE"?V={t:"b",v:!0}:F==="FALSE"?V={t:"b",v:!1}:isNaN(kt(F))?isNaN(Oa(F).getDate())||(V={t:"d",v:dr(F)},a.cellDates||(V={t:"n",v:Rr(V.v)}),V.z=a.dateNF||Se[14]):V={t:"n",v:kt(F)})),V.z===void 0&&N!=null&&(V.z=N);var b="",ae=I.getElementsByTagName("A");if(ae&&ae.length)for(var xe=0;xe<ae.length&&!(ae[xe].hasAttribute("href")&&(b=ae[xe].getAttribute("href"),b.charAt(0)!="#"));++xe);b&&b.charAt(0)!="#"&&(V.l={Target:b}),a.dense?(e[u+n]||(e[u+n]=[]),e[u+n][E+i]=V):e[Ce({c:E+i,r:u+n})]=V,o.e.c<E+i&&(o.e.c=E+i),E+=g}}++u}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),o.e.r=Math.max(o.e.r,u-1+n),e["!ref"]=Ue(o),u>=c&&(e["!fullref"]=Ue((o.e.r=f.length-v+u-1+n,o))),e}function kc(e,t){var r=t||{},a=r.dense?[]:{};return Tc(a,e,t)}function $m(e,t){return qt(kc(e,t),t)}function Sc(e){var t="",r=Ym(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Ym(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function Zm(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,n){return Array(parseInt(n,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=qe(t.replace(/<[^>]*>/g,""));return[r]}var Fc={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function yc(e,t){var r=t||{};he!=null&&r.dense==null&&(r.dense=he);var a=w0(e),n=[],i,s,f={name:""},c="",o=0,l,h,x={},d=[],v=r.dense?[]:{},u,p,E={value:""},T="",g=0,R,L=[],I=-1,F=-1,N={s:{r:1e6,c:1e7},e:{r:0,c:0}},P=0,V={},X=[],b={},ae=0,xe=0,ne=[],_e=1,pe=1,ze=[],le={Names:[]},ge={},j=["",""],C=[],U={},O="",D=0,K=!1,ce=!1,ee=0;for(Qa.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");u=Qa.exec(a);)switch(u[3]=u[3].replace(/_.*$/,"")){case"table":case"\u5DE5\u4F5C\u8868":u[1]==="/"?(N.e.c>=N.s.c&&N.e.r>=N.s.r?v["!ref"]=Ue(N):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=N.e.r&&(v["!fullref"]=v["!ref"],N.e.r=r.sheetRows-1,v["!ref"]=Ue(N)),X.length&&(v["!merges"]=X),ne.length&&(v["!rows"]=ne),l.name=l.\u540D\u79F0||l.name,typeof JSON!="undefined"&&JSON.stringify(l),d.push(l.name),x[l.name]=v,ce=!1):u[0].charAt(u[0].length-2)!=="/"&&(l=Pe(u[0],!1),I=F=-1,N.s.r=N.s.c=1e7,N.e.r=N.e.c=0,v=r.dense?[]:{},X=[],ne=[],ce=!0);break;case"table-row-group":u[1]==="/"?--P:++P;break;case"table-row":case"\u884C":if(u[1]==="/"){I+=_e,_e=1;break}if(h=Pe(u[0],!1),h.\u884C\u53F7?I=h.\u884C\u53F7-1:I==-1&&(I=0),_e=+h["number-rows-repeated"]||1,_e<10)for(ee=0;ee<_e;++ee)P>0&&(ne[I+ee]={level:P});F=-1;break;case"covered-table-cell":u[1]!=="/"&&++F,r.sheetStubs&&(r.dense?(v[I]||(v[I]=[]),v[I][F]={t:"z"}):v[Ce({r:I,c:F})]={t:"z"}),T="",L=[];break;case"table-cell":case"\u6570\u636E":if(u[0].charAt(u[0].length-2)==="/")++F,E=Pe(u[0],!1),pe=parseInt(E["number-columns-repeated"]||"1",10),p={t:"z",v:null},E.formula&&r.cellFormula!=!1&&(p.f=qf(qe(E.formula))),(E.\u6570\u636E\u7C7B\u578B||E["value-type"])=="string"&&(p.t="s",p.v=qe(E["string-value"]||""),r.dense?(v[I]||(v[I]=[]),v[I][F]=p):v[Ce({r:I,c:F})]=p),F+=pe-1;else if(u[1]!=="/"){++F,T="",g=0,L=[],pe=1;var re=_e?I+_e-1:I;if(F>N.e.c&&(N.e.c=F),F<N.s.c&&(N.s.c=F),I<N.s.r&&(N.s.r=I),re>N.e.r&&(N.e.r=re),E=Pe(u[0],!1),C=[],U={},p={t:E.\u6570\u636E\u7C7B\u578B||E["value-type"],v:null},r.cellFormula)if(E.formula&&(E.formula=qe(E.formula)),E["number-matrix-columns-spanned"]&&E["number-matrix-rows-spanned"]&&(ae=parseInt(E["number-matrix-rows-spanned"],10)||0,xe=parseInt(E["number-matrix-columns-spanned"],10)||0,b={s:{r:I,c:F},e:{r:I+ae-1,c:F+xe-1}},p.F=Ue(b),ze.push([b,p.F])),E.formula)p.f=qf(E.formula);else for(ee=0;ee<ze.length;++ee)I>=ze[ee][0].s.r&&I<=ze[ee][0].e.r&&F>=ze[ee][0].s.c&&F<=ze[ee][0].e.c&&(p.F=ze[ee][1]);switch((E["number-columns-spanned"]||E["number-rows-spanned"])&&(ae=parseInt(E["number-rows-spanned"],10)||0,xe=parseInt(E["number-columns-spanned"],10)||0,b={s:{r:I,c:F},e:{r:I+ae-1,c:F+xe-1}},X.push(b)),E["number-columns-repeated"]&&(pe=parseInt(E["number-columns-repeated"],10)),p.t){case"boolean":p.t="b",p.v=vr(E["boolean-value"]);break;case"float":p.t="n",p.v=parseFloat(E.value);break;case"percentage":p.t="n",p.v=parseFloat(E.value);break;case"currency":p.t="n",p.v=parseFloat(E.value);break;case"date":p.t="d",p.v=dr(E["date-value"]),r.cellDates||(p.t="n",p.v=Rr(p.v)),p.z="m/d/yy";break;case"time":p.t="n",p.v=Ao(E["time-value"])/86400,r.cellDates&&(p.t="d",p.v=bn(p.v)),p.z="HH:MM:SS";break;case"number":p.t="n",p.v=parseFloat(E.\u6570\u636E\u6570\u503C);break;default:if(p.t==="string"||p.t==="text"||!p.t)p.t="s",E["string-value"]!=null&&(T=qe(E["string-value"]),L=[]);else throw new Error("Unsupported value type "+p.t)}}else{if(K=!1,p.t==="s"&&(p.v=T||"",L.length&&(p.R=L),K=g==0),ge.Target&&(p.l=ge),C.length>0&&(p.c=C,C=[]),T&&r.cellText!==!1&&(p.w=T),K&&(p.t="z",delete p.v),(!K||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=I))for(var Y=0;Y<_e;++Y){if(pe=parseInt(E["number-columns-repeated"]||"1",10),r.dense)for(v[I+Y]||(v[I+Y]=[]),v[I+Y][F]=Y==0?p:ur(p);--pe>0;)v[I+Y][F+pe]=ur(p);else for(v[Ce({r:I+Y,c:F})]=p;--pe>0;)v[Ce({r:I+Y,c:F+pe})]=ur(p);N.e.c<=F&&(N.e.c=F)}pe=parseInt(E["number-columns-repeated"]||"1",10),F+=pe-1,pe=0,p={},T="",L=[]}ge={};break;case"document":case"document-content":case"\u7535\u5B50\u8868\u683C\u6587\u6863":case"spreadsheet":case"\u4E3B\u4F53":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!0]);break;case"annotation":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i;U.t=T,L.length&&(U.R=L),U.a=O,C.push(U)}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);O="",D=0,T="",g=0,L=[];break;case"creator":u[1]==="/"?O=a.slice(D,u.index):D=u.index+u[0].length;break;case"meta":case"\u5143\u6570\u636E":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(u[1]==="/"){if((i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);T="",g=0,L=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(u[1]==="/"){if(V[f.name]=c,(i=n.pop())[0]!==u[3])throw"Bad state: "+i}else u[0].charAt(u[0].length-2)!=="/"&&(c="",f=Pe(u[0],!1),n.push([u[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":case"date-style":s=Pe(u[0],!1),c+=Fc[u[3]][s.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(n[n.length-1][0]){case"time-style":case"date-style":s=Pe(u[0],!1),c+=Fc[u[3]][s.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(u[0].slice(-2)==="/>")break;if(u[1]==="/")switch(n[n.length-1][0]){case"number-style":case"date-style":case"time-style":c+=a.slice(o,u.index);break}else o=u.index+u[0].length;break;case"named-range":s=Pe(u[0],!1),j=ai(s["cell-range-address"]);var He={Name:s.name,Ref:j[0]+"!"+j[1]};ce&&(He.Sheet=d.length),le.Names.push(He);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"\u7535\u5B50\u8868\u683C":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"\u6587\u672C\u4E32":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(u[1]==="/"&&(!E||!E["string-value"])){var A=Zm(a.slice(g,u.index),R);T=(T.length>0?T+`
`:"")+A[0]}else R=Pe(u[0],!1),g=u.index+u[0].length;break;case"s":break;case"database-range":if(u[1]==="/")break;try{j=ai(Pe(u[0])["target-range-address"]),x[j[0]]["!autofilter"]={ref:j[1]}}catch($e){}break;case"date":break;case"object":break;case"title":case"\u6807\u9898":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(u[1]!=="/"){if(ge=Pe(u[0],!1),!ge.href)break;ge.Target=qe(ge.href),delete ge.href,ge.Target.charAt(0)=="#"&&ge.Target.indexOf(".")>-1?(j=ai(ge.Target.slice(1)),ge.Target="#"+j[0]+"!"+j[1]):ge.Target.match(/^\.\.[\\\/]/)&&(ge.Target=ge.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(u[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"\u8868:":case"\u5B57:":break;default:if(r.WTF)throw new Error(u)}}var fr={Sheets:x,SheetNames:d,Workbook:le};return r.bookSheets&&delete fr.Sheets,fr}function Ac(e,t){t=t||{},dt(e,"META-INF/manifest.xml")&&yl(Cr(e,"META-INF/manifest.xml"),t);var r=ct(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=yc(lr(r),t);return dt(e,"meta.xml")&&(a.Props=Ms(Cr(e,"meta.xml"))),a}function Cc(e,t){return yc(e,t)}var Jm=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Ja({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Tr+t}}(),Dc=function(){var e=function(i){return sr(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,a=function(i,s,f){var c=[];c.push('      <table:table table:name="'+sr(s.SheetNames[f])+`" table:style-name="ta1">
`);var o=0,l=0,h=et(i["!ref"]||"A1"),x=i["!merges"]||[],d=0,v=Array.isArray(i);if(i["!cols"])for(l=0;l<=h.e.c;++l)c.push("        <table:table-column"+(i["!cols"][l]?' table:style-name="co'+i["!cols"][l].ods+'"':"")+`></table:table-column>
`);var u="",p=i["!rows"]||[];for(o=0;o<h.s.r;++o)u=p[o]?' table:style-name="ro'+p[o].ods+'"':"",c.push("        <table:table-row"+u+`></table:table-row>
`);for(;o<=h.e.r;++o){for(u=p[o]?' table:style-name="ro'+p[o].ods+'"':"",c.push("        <table:table-row"+u+`>
`),l=0;l<h.s.c;++l)c.push(t);for(;l<=h.e.c;++l){var E=!1,T={},g="";for(d=0;d!=x.length;++d)if(!(x[d].s.c>l)&&!(x[d].s.r>o)&&!(x[d].e.c<l)&&!(x[d].e.r<o)){(x[d].s.c!=l||x[d].s.r!=o)&&(E=!0),T["table:number-columns-spanned"]=x[d].e.c-x[d].s.c+1,T["table:number-rows-spanned"]=x[d].e.r-x[d].s.r+1;break}if(E){c.push(r);continue}var R=Ce({r:o,c:l}),L=v?(i[o]||[])[l]:i[R];if(L&&L.f&&(T["table:formula"]=sr(wv(L.f)),L.F&&L.F.slice(0,R.length)==R)){var I=et(L.F);T["table:number-matrix-columns-spanned"]=I.e.c-I.s.c+1,T["table:number-matrix-rows-spanned"]=I.e.r-I.s.r+1}if(!L){c.push(t);continue}switch(L.t){case"b":g=L.v?"TRUE":"FALSE",T["office:value-type"]="boolean",T["office:boolean-value"]=L.v?"true":"false";break;case"n":g=L.w||String(L.v||0),T["office:value-type"]="float",T["office:value"]=L.v||0;break;case"s":case"str":g=L.v==null?"":L.v,T["office:value-type"]="string";break;case"d":g=L.w||dr(L.v).toISOString(),T["office:value-type"]="date",T["office:date-value"]=dr(L.v).toISOString(),T["table:style-name"]="ce1";break;default:c.push(t);continue}var F=e(g);if(L.l&&L.l.Target){var N=L.l.Target;N=N.charAt(0)=="#"?"#"+Ev(N.slice(1)):N,N.charAt(0)!="#"&&!N.match(/^\w+:/)&&(N="../"+N),F=te("text:a",F,{"xlink:href":N.replace(/&/g,"&amp;")})}c.push("          "+te("table:table-cell",te("text:p",F,{}),T)+`
`)}c.push(`        </table:table-row>
`)}return c.push(`      </table:table>
`),c.join("")},n=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!cols"]){for(var l=0;l<o["!cols"].length;++l)if(o["!cols"][l]){var h=o["!cols"][l];if(h.width==null&&h.wpx==null&&h.wch==null)continue;ra(h),h.ods=f;var x=o["!cols"][l].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+x+`"/>
`),i.push(`  </style:style>
`),++f}}});var c=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!rows"]){for(var l=0;l<o["!rows"].length;++l)if(o["!rows"][l]){o["!rows"][l].ods=c;var h=o["!rows"][l].hpx+"px";i.push('  <style:style style:name="ro'+c+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+h+`"/>
`),i.push(`  </style:style>
`),++c}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var c=[Tr],o=Ja({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),l=Ja({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(c.push("<office:document"+o+l+`>
`),c.push(Ls().replace(/office:document-meta/g,"office:meta"))):c.push("<office:document-content"+o+`>
`),n(c,s),c.push(`  <office:body>
`),c.push(`    <office:spreadsheet>
`);for(var h=0;h!=s.SheetNames.length;++h)c.push(a(s.Sheets[s.SheetNames[h]],s,h,f));return c.push(`    </office:spreadsheet>
`),c.push(`  </office:body>
`),f.bookType=="fods"?c.push("</office:document>"):c.push("</office:document-content>"),c.join("")}}();function Oc(e,t){if(t.bookType=="fods")return Dc(e,t);var r=d0(),a="",n=[],i=[];return a="mimetype",Xe(r,a,"application/vnd.oasis.opendocument.spreadsheet"),a="content.xml",Xe(r,a,Dc(e,t)),n.push([a,"text/xml"]),i.push([a,"ContentFile"]),a="styles.xml",Xe(r,a,Jm(e,t)),n.push([a,"text/xml"]),i.push([a,"StylesFile"]),a="meta.xml",Xe(r,a,Tr+Ls()),n.push([a,"text/xml"]),i.push([a,"MetadataFile"]),a="manifest.rdf",Xe(r,a,Dl(i)),n.push([a,"application/rdf+xml"]),a="META-INF/manifest.xml",Xe(r,a,Al(n)),r}function ka(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function li(e){return typeof TextDecoder!="undefined"?new TextDecoder().decode(e):lr(At(e))}function Qm(e){return typeof TextEncoder!="undefined"?new TextEncoder().encode(e):ar(It(e))}function qm(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}function na(e){var t=e.reduce(function(n,i){return n+i.length},0),r=new Uint8Array(t),a=0;return e.forEach(function(n){r.set(n,a),a+=n.length}),r}function Ic(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function eg(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,a=e[t+14]&1,n=t+13;n>=t;--n)a=a*256+e[n];return(e[t+15]&128?-a:a)*Math.pow(10,r-6176)}function rg(e,t,r){var a=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(a&127)<<1;for(var i=0;n>=1;++i,n/=256)e[t+i]=n&255;e[t+15]|=r>=0?0:128}function En(e,t){var r=t?t[0]:0,a=e[r]&127;e:if(e[r++]>=128&&(a|=(e[r]&127)<<7,e[r++]<128||(a|=(e[r]&127)<<14,e[r++]<128)||(a|=(e[r]&127)<<21,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),a}function ir(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Sr(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function rr(e){for(var t=[],r=[0];r[0]<e.length;){var a=r[0],n=En(e,r),i=n&7;n=Math.floor(n/8);var s=0,f;if(n==0)break;switch(i){case 0:{for(var c=r[0];e[r[0]++]>=128;);f=e.slice(c,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=En(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(n," at offset ").concat(a))}var o={data:f,type:i};t[n]==null?t[n]=[o]:t[n].push(o)}return t}function Wr(e){var t=[];return e.forEach(function(r,a){r.forEach(function(n){n.data&&(t.push(ir(a*8+n.type)),n.type==2&&t.push(ir(n.data.length)),t.push(n.data))})}),na(t)}function ui(e,t){return(e==null?void 0:e.map(function(r){return t(r.data)}))||[]}function gt(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=En(e,a),i=rr(e.slice(a[0],a[0]+n));a[0]+=n;var s={id:Sr(i[1][0].data),messages:[]};i[2].forEach(function(f){var c=rr(f.data),o=Sr(c[3][0].data);s.messages.push({meta:c,data:e.slice(a[0],a[0]+o)}),a[0]+=o}),(t=i[3])!=null&&t[0]&&(s.merge=Sr(i[3][0].data)>>>0>0),r.push(s)}return r}function Ha(e){var t=[];return e.forEach(function(r){var a=[];a[1]=[{data:ir(r.id),type:0}],a[2]=[],r.merge!=null&&(a[3]=[{data:ir(+!!r.merge),type:0}]);var n=[];r.messages.forEach(function(s){n.push(s.data),s.meta[3]=[{type:0,data:ir(s.data.length)}],a[2].push({data:Wr(s.meta),type:2})});var i=Wr(a);t.push(ir(i.length)),t.push(i),n.forEach(function(s){return t.push(s)})}),na(t)}function tg(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=En(t,r),n=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}n.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var c=0,o=0;if(i==1?(o=(t[r[0]]>>2&7)+4,c=(t[r[0]++]&224)<<3,c|=t[r[0]++]):(o=(t[r[0]++]>>2)+1,i==2?(c=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(c=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[na(n)],c==0)throw new Error("Invalid offset 0");if(c>n[0].length)throw new Error("Invalid offset beyond length");if(o>=c)for(n.push(n[0].slice(-c)),o-=c;o>=n[n.length-1].length;)n.push(n[n.length-1]),o-=n[n.length-1].length;n.push(n[0].slice(-c,-c+o))}}var l=na(n);if(l.length!=a)throw new Error("Unexpected length: ".concat(l.length," != ").concat(a));return l}function _t(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(tg(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return na(t)}function Va(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var i=ir(a),s=i.length;t.push(i),a<=60?(s++,t.push(new Uint8Array([a-1<<2]))):a<=256?(s+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(s+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(s+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(s+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),s+=a,n[0]=0,n[1]=s&255,n[2]=s>>8&255,n[3]=s>>16&255,r+=a}return na(t)}function ag(e,t,r,a){var n=ka(e),i=n.getUint32(4,!0),s=(a>1?12:8)+Ic(i&(a>1?3470:398))*4,f=-1,c=-1,o=NaN,l=new Date(2001,0,1);i&512&&(f=n.getUint32(s,!0),s+=4),s+=Ic(i&(a>1?12288:4096))*4,i&16&&(c=n.getUint32(s,!0),s+=4),i&32&&(o=n.getFloat64(s,!0),s+=8),i&64&&(l.setTime(l.getTime()+n.getFloat64(s,!0)*1e3),s+=8);var h;switch(e[2]){case 0:break;case 2:h={t:"n",v:o};break;case 3:h={t:"s",v:t[c]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:o>0};break;case 7:h={t:"n",v:o/86400};break;case 8:h={t:"e",v:0};break;case 9:if(f>-1)h={t:"s",v:r[f]};else if(c>-1)h={t:"s",v:t[c]};else if(!isNaN(o))h={t:"n",v:o};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return h}function ng(e,t,r){var a=ka(e),n=a.getUint32(8,!0),i=12,s=-1,f=-1,c=NaN,o=NaN,l=new Date(2001,0,1);n&1&&(c=eg(e,i),i+=16),n&2&&(o=a.getFloat64(i,!0),i+=8),n&4&&(l.setTime(l.getTime()+a.getFloat64(i,!0)*1e3),i+=8),n&8&&(f=a.getUint32(i,!0),i+=4),n&16&&(s=a.getUint32(i,!0),i+=4);var h;switch(e[1]){case 0:break;case 2:h={t:"n",v:c};break;case 3:h={t:"s",v:t[f]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:o>0};break;case 7:h={t:"n",v:o/86400};break;case 8:h={t:"e",v:0};break;case 9:if(s>-1)h={t:"s",v:r[s]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)));break;case 10:h={t:"n",v:c};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)))}return h}function hi(e,t){var r=new Uint8Array(32),a=ka(r),n=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,rg(r,n,e.v),i|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),i|=2,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,i,!0),r.slice(0,n)}function xi(e,t){var r=new Uint8Array(32),a=ka(r),n=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),i|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),i|=32,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,i,!0),r.slice(0,n)}function ig(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return ag(e,t,r,e[0]);case 5:return ng(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Jr(e){var t=rr(e);return En(t[1][0].data)}function x_(e){var t=[];return t[1]=[{type:0,data:ir(e)}],Wr(t)}function Pc(e,t){var r=rr(t.data),a=Sr(r[1][0].data),n=r[3],i=[];return(n||[]).forEach(function(s){var f=rr(s.data),c=Sr(f[1][0].data)>>>0;switch(a){case 1:i[c]=li(f[3][0].data);break;case 8:{var o=e[Jr(f[9][0].data)][0],l=rr(o.data),h=e[Jr(l[1][0].data)][0],x=Sr(h.meta[1][0].data);if(x!=2001)throw new Error("2000 unexpected reference to ".concat(x));var d=rr(h.data);i[c]=d[3].map(function(v){return li(v.data)}).join("")}break}}),i}function sg(e,t){var r,a,n,i,s,f,c,o,l,h,x,d,v,u,p=rr(e),E=Sr(p[1][0].data)>>>0,T=Sr(p[2][0].data)>>>0,g=((a=(r=p[8])==null?void 0:r[0])==null?void 0:a.data)&&Sr(p[8][0].data)>0||!1,R,L;if((i=(n=p[7])==null?void 0:n[0])!=null&&i.data&&t!=0)R=(f=(s=p[7])==null?void 0:s[0])==null?void 0:f.data,L=(o=(c=p[6])==null?void 0:c[0])==null?void 0:o.data;else if((h=(l=p[4])==null?void 0:l[0])!=null&&h.data&&t!=1)R=(d=(x=p[4])==null?void 0:x[0])==null?void 0:d.data,L=(u=(v=p[3])==null?void 0:v[0])==null?void 0:u.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var I=g?4:1,F=ka(R),N=[],P=0;P<R.length/2;++P){var V=F.getUint16(P*2,!0);V<65535&&N.push([P,V])}if(N.length!=T)throw"Expected ".concat(T," cells, found ").concat(N.length);var X=[];for(P=0;P<N.length-1;++P)X[N[P][0]]=L.subarray(N[P][1]*I,N[P+1][1]*I);return N.length>=1&&(X[N[N.length-1][0]]=L.subarray(N[N.length-1][1]*I)),{R:E,cells:X}}function fg(e,t){var r,a=rr(t.data),n=(r=a==null?void 0:a[7])!=null&&r[0]?Sr(a[7][0].data)>>>0>0?1:0:-1,i=ui(a[5],function(s){return sg(s,n)});return{nrows:Sr(a[4][0].data)>>>0,data:i.reduce(function(s,f){return s[f.R]||(s[f.R]=[]),f.cells.forEach(function(c,o){if(s[f.R][o])throw new Error("Duplicate cell r=".concat(f.R," c=").concat(o));s[f.R][o]=c}),s},[])}}function cg(e,t,r){var a,n=rr(t.data),i={s:{r:0,c:0},e:{r:0,c:0}};if(i.e.r=(Sr(n[6][0].data)>>>0)-1,i.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(i.e.c=(Sr(n[7][0].data)>>>0)-1,i.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=Ue(i);var s=rr(n[4][0].data),f=Pc(e,e[Jr(s[4][0].data)][0]),c=(a=s[17])!=null&&a[0]?Pc(e,e[Jr(s[17][0].data)][0]):[],o=rr(s[3][0].data),l=0;o[1].forEach(function(h){var x=rr(h.data),d=e[Jr(x[2][0].data)][0],v=Sr(d.meta[1][0].data);if(v!=6002)throw new Error("6001 unexpected reference to ".concat(v));var u=fg(e,d);u.data.forEach(function(p,E){p.forEach(function(T,g){var R=Ce({r:l+E,c:g}),L=ig(T,f,c);L&&(r[R]=L)})}),l+=u.nrows})}function og(e,t){var r=rr(t.data),a={"!ref":"A1"},n=e[Jr(r[2][0].data)],i=Sr(n[0].meta[1][0].data);if(i!=6001)throw new Error("6000 unexpected reference to ".concat(i));return cg(e,n[0],a),a}function lg(e,t){var r,a=rr(t.data),n={name:(r=a[1])!=null&&r[0]?li(a[1][0].data):"",sheets:[]},i=ui(a[2],Jr);return i.forEach(function(s){e[s].forEach(function(f){var c=Sr(f.meta[1][0].data);c==6e3&&n.sheets.push(og(e,f))})}),n}function ug(e,t){var r=Ei(),a=rr(t.data),n=ui(a[1],Jr);if(n.forEach(function(i){e[i].forEach(function(s){var f=Sr(s.meta[1][0].data);if(f==2){var c=lg(e,s);c.sheets.forEach(function(o,l){Ti(r,o,l==0?c.name:c.name+"_"+l,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function di(e){var t,r,a,n,i={},s=[];if(e.FullPaths.forEach(function(c){if(c.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(c){if(c.name.match(/\.iwa$/)){var o;try{o=_t(c.content)}catch(h){return console.log("?? "+c.content.length+" "+(h.message||h))}var l;try{l=gt(o)}catch(h){return console.log("## "+(h.message||h))}l.forEach(function(h){i[h.id]=h.messages,s.push(h.id)})}}),!s.length)throw new Error("File has no messages");var f=((n=(a=(r=(t=i==null?void 0:i[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:a[1])==null?void 0:n[0].data)&&Sr(i[1][0].meta[1][0].data)==1&&i[1][0];if(f||s.forEach(function(c){i[c].forEach(function(o){var l=Sr(o.meta[1][0].data)>>>0;if(l==1)if(!f)f=o;else throw new Error("Document has multiple roots")})}),!f)throw new Error("Cannot find Document root");return ug(i,f)}function hg(e,t,r){var a,n,i,s;if(!((a=e[6])!=null&&a[0])||!((n=e[7])!=null&&n[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&Sr(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var c=0,o=ka(e[7][0].data),l=0,h=[],x=ka(e[4][0].data),d=0,v=[],u=0;u<t.length;++u){if(t[u]==null){o.setUint16(u*2,65535,!0),x.setUint16(u*2,65535);continue}o.setUint16(u*2,l,!0),x.setUint16(u*2,d,!0);var p,E;switch(typeof t[u]){case"string":p=hi({t:"s",v:t[u]},r),E=xi({t:"s",v:t[u]},r);break;case"number":p=hi({t:"n",v:t[u]},r),E=xi({t:"n",v:t[u]},r);break;case"boolean":p=hi({t:"b",v:t[u]},r),E=xi({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}h.push(p),l+=p.length,v.push(E),d+=E.length,++c}for(e[2][0].data=ir(c);u<e[7][0].data.length/2;++u)o.setUint16(u*2,65535,!0),x.setUint16(u*2,65535,!0);return e[6][0].data=na(h),e[3][0].data=na(v),c}function xg(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=et(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(Ue(a)));var i=s0(r,{range:a,header:1}),s=["~Sh33tJ5~"];i.forEach(function(O){return O.forEach(function(D){typeof D=="string"&&s.push(D)})});var f={},c=[],o=Te.read(t.numbers,{type:"base64"});o.FileIndex.map(function(O,D){return[O,o.FullPaths[D]]}).forEach(function(O){var D=O[0],K=O[1];if(D.type==2&&D.name.match(/\.iwa/)){var ce=D.content,ee=_t(ce),re=gt(ee);re.forEach(function(Y){c.push(Y.id),f[Y.id]={deps:[],location:K,type:Sr(Y.messages[0].meta[1][0].data)}})}}),c.sort(function(O,D){return O-D});var l=c.filter(function(O){return O>1}).map(function(O){return[O,ir(O)]});o.FileIndex.map(function(O,D){return[O,o.FullPaths[D]]}).forEach(function(O){var D=O[0],K=O[1];if(D.name.match(/\.iwa/)){var ce=gt(_t(D.content));ce.forEach(function(ee){ee.messages.forEach(function(re){l.forEach(function(Y){ee.messages.some(function(He){return Sr(He.meta[1][0].data)!=11006&&qm(He.data,Y[1])})&&f[Y[0]].deps.push(ee.id)})})})}});function h(){for(var O=927262;O<2e6;++O)if(!f[O])return O;throw new Error("Too many messages")}for(var x=Te.find(o,f[1].location),d=gt(_t(x.content)),v,u=0;u<d.length;++u){var p=d[u];p.id==1&&(v=p)}var E=Jr(rr(v.messages[0].data)[1][0].data);for(x=Te.find(o,f[E].location),d=gt(_t(x.content)),u=0;u<d.length;++u)p=d[u],p.id==E&&(v=p);for(E=Jr(rr(v.messages[0].data)[2][0].data),x=Te.find(o,f[E].location),d=gt(_t(x.content)),u=0;u<d.length;++u)p=d[u],p.id==E&&(v=p);for(E=Jr(rr(v.messages[0].data)[2][0].data),x=Te.find(o,f[E].location),d=gt(_t(x.content)),u=0;u<d.length;++u)p=d[u],p.id==E&&(v=p);var T=rr(v.messages[0].data);{T[6][0].data=ir(a.e.r+1),T[7][0].data=ir(a.e.c+1);var g=Jr(T[46][0].data),R=Te.find(o,f[g].location),L=gt(_t(R.content));{for(var I=0;I<L.length&&L[I].id!=g;++I);if(L[I].id!=g)throw"Bad ColumnRowUIDMapArchive";var F=rr(L[I].messages[0].data);F[1]=[],F[2]=[],F[3]=[];for(var N=0;N<=a.e.c;++N){var P=[];P[1]=P[2]=[{type:0,data:ir(N+420690)}],F[1].push({type:2,data:Wr(P)}),F[2].push({type:0,data:ir(N)}),F[3].push({type:0,data:ir(N)})}F[4]=[],F[5]=[],F[6]=[];for(var V=0;V<=a.e.r;++V)P=[],P[1]=P[2]=[{type:0,data:ir(V+726270)}],F[4].push({type:2,data:Wr(P)}),F[5].push({type:0,data:ir(V)}),F[6].push({type:0,data:ir(V)});L[I].messages[0].data=Wr(F)}R.content=Va(Ha(L)),R.size=R.content.length,delete T[46];var X=rr(T[4][0].data);{X[7][0].data=ir(a.e.r+1);var b=rr(X[1][0].data),ae=Jr(b[2][0].data);R=Te.find(o,f[ae].location),L=gt(_t(R.content));{if(L[0].id!=ae)throw"Bad HeaderStorageBucket";var xe=rr(L[0].messages[0].data);for(V=0;V<i.length;++V){var ne=rr(xe[2][0].data);ne[1][0].data=ir(V),ne[4][0].data=ir(i[V].length),xe[2][V]={type:xe[2][0].type,data:Wr(ne)}}L[0].messages[0].data=Wr(xe)}R.content=Va(Ha(L)),R.size=R.content.length;var _e=Jr(X[2][0].data);R=Te.find(o,f[_e].location),L=gt(_t(R.content));{if(L[0].id!=_e)throw"Bad HeaderStorageBucket";for(xe=rr(L[0].messages[0].data),N=0;N<=a.e.c;++N)ne=rr(xe[2][0].data),ne[1][0].data=ir(N),ne[4][0].data=ir(a.e.r+1),xe[2][N]={type:xe[2][0].type,data:Wr(ne)};L[0].messages[0].data=Wr(xe)}R.content=Va(Ha(L)),R.size=R.content.length;var pe=Jr(X[4][0].data);(function(){for(var O=Te.find(o,f[pe].location),D=gt(_t(O.content)),K,ce=0;ce<D.length;++ce){var ee=D[ce];ee.id==pe&&(K=ee)}var re=rr(K.messages[0].data);{re[3]=[];var Y=[];s.forEach(function(fr,$e){Y[1]=[{type:0,data:ir($e)}],Y[2]=[{type:0,data:ir(1)}],Y[3]=[{type:2,data:Qm(fr)}],re[3].push({type:2,data:Wr(Y)})})}K.messages[0].data=Wr(re);var He=Ha(D),A=Va(He);O.content=A,O.size=O.content.length})();var ze=rr(X[3][0].data);{var le=ze[1][0];delete ze[2];var ge=rr(le.data);{var j=Jr(ge[2][0].data);(function(){for(var O=Te.find(o,f[j].location),D=gt(_t(O.content)),K,ce=0;ce<D.length;++ce){var ee=D[ce];ee.id==j&&(K=ee)}var re=rr(K.messages[0].data);{delete re[6],delete ze[7];var Y=new Uint8Array(re[5][0].data);re[5]=[];for(var He=0,A=0;A<=a.e.r;++A){var fr=rr(Y);He+=hg(fr,i[A],s),fr[1][0].data=ir(A),re[5].push({data:Wr(fr),type:2})}re[1]=[{type:0,data:ir(a.e.c+1)}],re[2]=[{type:0,data:ir(a.e.r+1)}],re[3]=[{type:0,data:ir(He)}],re[4]=[{type:0,data:ir(a.e.r+1)}]}K.messages[0].data=Wr(re);var $e=Ha(D),cr=Va($e);O.content=cr,O.size=O.content.length})()}le.data=Wr(ge)}X[3][0].data=Wr(ze)}T[4][0].data=Wr(X)}v.messages[0].data=Wr(T);var C=Ha(d),U=Va(C);return x.content=U,x.size=x.content.length,o}function Rc(e){return function(r){for(var a=0;a!=e.length;++a){var n=e[a];r[n[0]]===void 0&&(r[n[0]]=n[1]),n[2]==="n"&&(r[n[0]]=Number(r[n[0]]))}}}function vi(e){Rc([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function pi(e){Rc([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function dg(e){return We.WS.indexOf(e)>-1?"sheet":We.CS&&e==We.CS?"chart":We.DS&&e==We.DS?"dialog":We.MS&&e==We.MS?"macro":e&&e.length?e:"sheet"}function vg(e,t){if(!e)return 0;try{e=t.map(function(a){return a.id||(a.id=a.strRelID),[a.name,e["!id"][a.id].Target,dg(e["!id"][a.id].Type)]})}catch(r){return null}return!e||e.length===0?null:e}function pg(e,t,r,a,n,i,s,f,c,o,l,h){try{i[a]=an(ct(e,r,!0),t);var x=Cr(e,t),d;switch(f){case"sheet":d=V2(x,t,n,c,i[a],o,l,h);break;case"chart":if(d=X2(x,t,n,c,i[a],o,l,h),!d||!d["!drawel"])break;var v=Ya(d["!drawel"].Target,t),u=tn(v),p=Cx(ct(e,v,!0),an(ct(e,u,!0),v)),E=Ya(p,v),T=tn(E);d=v2(ct(e,E,!0),E,c,an(ct(e,T,!0),E),o,d);break;case"macro":d=G2(x,t,n,c,i[a],o,l,h);break;case"dialog":d=z2(x,t,n,c,i[a],o,l,h);break;default:throw new Error("Unrecognized sheet type "+f)}s[a]=d;var g=[];i&&i[a]&&wr(i[a]).forEach(function(R){var L="";if(i[a][R].Type==We.CMNT){L=Ya(i[a][R].Target,t);var I=Y2(Cr(e,L,!0),L,c);if(!I||!I.length)return;Bf(d,I,!1)}i[a][R].Type==We.TCMNT&&(L=Ya(i[a][R].Target,t),g=g.concat(Ox(Cr(e,L,!0),c)))}),g&&g.length&&Bf(d,g,!0,c.people||[])}catch(R){if(c.WTF)throw R}}function wt(e){return e.charAt(0)=="/"?e.slice(1):e}function mg(e,t){if(Da(),t=t||{},vi(t),dt(e,"META-INF/manifest.xml")||dt(e,"objectdata.xml"))return Ac(e,t);if(dt(e,"Index/Document.iwa")){if(typeof Uint8Array=="undefined")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof di!="undefined"){if(e.FileIndex)return di(e);var r=Te.utils.cfb_new();return Ji(e).forEach(function(xe){Xe(r,xe,Zi(e,xe))}),di(r)}throw new Error("Unsupported NUMBERS file")}if(!dt(e,"[Content_Types].xml"))throw dt(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):dt(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var a=Ji(e),n=Sl(ct(e,"[Content_Types].xml")),i=!1,s,f;if(n.workbooks.length===0&&(f="xl/workbook.xml",Cr(e,f,!0)&&n.workbooks.push(f)),n.workbooks.length===0){if(f="xl/workbook.bin",!Cr(e,f,!0))throw new Error("Could not find workbook");n.workbooks.push(f),i=!0}n.workbooks[0].slice(-3)=="bin"&&(i=!0);var c={},o={};if(!t.bookSheets&&!t.bookProps){if(vn=[],n.sst)try{vn=$2(Cr(e,wt(n.sst)),n.sst,t)}catch(xe){if(t.WTF)throw xe}t.cellStyles&&n.themes.length&&(c=j2(ct(e,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],t)),n.style&&(o=K2(Cr(e,wt(n.style)),n.style,c,t))}n.links.map(function(xe){try{var ne=an(ct(e,tn(wt(xe))),xe);return J2(Cr(e,wt(xe)),ne,xe,t)}catch(_e){}});var l=H2(Cr(e,wt(n.workbooks[0])),n.workbooks[0],t),h={},x="";n.coreprops.length&&(x=Cr(e,wt(n.coreprops[0]),!0),x&&(h=Ms(x)),n.extprops.length!==0&&(x=Cr(e,wt(n.extprops[0]),!0),x&&Il(x,h,t)));var d={};(!t.bookSheets||t.bookProps)&&n.custprops.length!==0&&(x=ct(e,wt(n.custprops[0]),!0),x&&(d=Rl(x,t)));var v={};if((t.bookSheets||t.bookProps)&&(l.Sheets?s=l.Sheets.map(function(ne){return ne.name}):h.Worksheets&&h.SheetNames.length>0&&(s=h.SheetNames),t.bookProps&&(v.Props=h,v.Custprops=d),t.bookSheets&&typeof s!="undefined"&&(v.SheetNames=s),t.bookSheets?v.SheetNames:t.bookProps))return v;s={};var u={};t.bookDeps&&n.calcchain&&(u=Z2(Cr(e,wt(n.calcchain)),n.calcchain,t));var p=0,E={},T,g;{var R=l.Sheets;h.Worksheets=R.length,h.SheetNames=[];for(var L=0;L!=R.length;++L)h.SheetNames[L]=R[L].name}var I=i?"bin":"xml",F=n.workbooks[0].lastIndexOf("/"),N=(n.workbooks[0].slice(0,F+1)+"_rels/"+n.workbooks[0].slice(F+1)+".rels").replace(/^\//,"");dt(e,N)||(N="xl/_rels/workbook."+I+".rels");var P=an(ct(e,N,!0),N.replace(/_rels.*/,"s5s"));(n.metadata||[]).length>=1&&(t.xlmeta=Q2(Cr(e,wt(n.metadata[0])),n.metadata[0],t)),(n.people||[]).length>=1&&(t.people=Px(Cr(e,wt(n.people[0])),t)),P&&(P=vg(P,l.Sheets));var V=Cr(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(p=0;p!=h.Worksheets;++p){var X="sheet";if(P&&P[p]?(T="xl/"+P[p][1].replace(/[\/]?xl\//,""),dt(e,T)||(T=P[p][1]),dt(e,T)||(T=N.replace(/_rels\/.*$/,"")+P[p][1]),X=P[p][2]):(T="xl/worksheets/sheet"+(p+1-V)+"."+I,T=T.replace(/sheet0\./,"sheet.")),g=T.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(p!=t.sheets)continue e;break;case"string":if(h.SheetNames[p].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var b=!1,ae=0;ae!=t.sheets.length;++ae)typeof t.sheets[ae]=="number"&&t.sheets[ae]==p&&(b=1),typeof t.sheets[ae]=="string"&&t.sheets[ae].toLowerCase()==h.SheetNames[p].toLowerCase()&&(b=1);if(!b)continue e}}pg(e,T,g,h.SheetNames[p],p,E,s,X,t,l,c,o)}return v={Directory:n,Workbook:l,Props:h,Custprops:d,Deps:u,Sheets:s,SheetNames:h.SheetNames,Strings:vn,Styles:o,Themes:c,SSF:ur(Se)},t&&t.bookFiles&&(e.files?(v.keys=a,v.files=e.files):(v.keys=[],v.files={},e.FullPaths.forEach(function(xe,ne){xe=xe.replace(/^Root Entry[\/]/,""),v.keys.push(xe),v.files[xe]=e.FileIndex[ne]}))),t&&t.bookVBA&&(n.vba.length>0?v.vbaraw=Cr(e,wt(n.vba[0]),!0):n.defaults&&n.defaults.bin===Wx&&(v.vbaraw=Cr(e,"xl/vbaProject.bin",!0))),v}function gg(e,t){var r=t||{},a="Workbook",n=Te.find(e,a);try{if(a="/!DataSpaces/Version",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(Jh(n.content),a="/!DataSpaces/DataSpaceMap",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=qh(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=e1(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);t1(n.content)}catch(c){}if(a="/EncryptionInfo",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var f=a1(n.content);if(a="/EncryptedPackage",n=Te.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(f[0]==4&&typeof decrypt_agile!="undefined")return decrypt_agile(f[1],n.content,r.password||"",r);if(f[0]==2&&typeof decrypt_std76!="undefined")return decrypt_std76(f[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function _g(e,t){return t.bookType=="ods"?Oc(e,t):t.bookType=="numbers"?xg(e,t):t.bookType=="xlsb"?wg(e,t):Nc(e,t)}function wg(e,t){Ba=1024,e&&!e.SSF&&(e.SSF=ur(Se)),e&&e.SSF&&(Da(),ja(e.SSF),t.revssf=Rn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,pn?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",a=Wf.indexOf(t.bookType)>-1,n=U0();pi(t=t||{});var i=d0(),s="",f=0;if(t.cellXfs=[],aa(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Xe(i,s,Bs(e.Props,t)),n.coreprops.push(s),nr(t.rels,2,s,We.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}for(e.Props.Worksheets=e.Props.SheetNames.length,Xe(i,s,Hs(e.Props,t)),n.extprops.push(s),nr(t.rels,3,s,We.EXT_PROPS),e.Custprops!==e.Props&&wr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Xe(i,s,Vs(e.Custprops,t)),n.custprops.push(s),nr(t.rels,4,s,We.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var l={"!id":{}},h=e.Sheets[e.SheetNames[f-1]],x=(h||{})["!type"]||"sheet";switch(x){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Xe(i,s,em(f-1,s,t,e,l)),n.sheets.push(s),nr(t.wbrels,-1,"worksheets/sheet"+f+"."+r,We.WS[0])}if(h){var d=h["!comments"],v=!1,u="";d&&d.length>0&&(u="xl/comments"+f+"."+r,Xe(i,u,am(d,u,t)),n.comments.push(u),nr(l,-1,"../comments"+f+"."+r,We.CMNT),v=!0),h["!legacy"]&&v&&Xe(i,"xl/drawings/vmlDrawing"+f+".vml",Mf(f,h["!comments"])),delete h["!comments"],delete h["!legacy"]}l["!id"].rId1&&Xe(i,tn(s),ba(l))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Xe(i,s,tm(t.Strings,s,t)),n.strs.push(s),nr(t.wbrels,-1,"sharedStrings."+r,We.SST)),s="xl/workbook."+r,Xe(i,s,q2(e,s,t)),n.workbooks.push(s),nr(t.rels,1,s,We.WB),s="xl/theme/theme1.xml",Xe(i,s,Q0(e.Themes,t)),n.themes.push(s),nr(t.wbrels,-1,"theme/theme1.xml",We.THEME),s="xl/styles."+r,Xe(i,s,rm(e,s,t)),n.styles.push(s),nr(t.wbrels,-1,"styles."+r,We.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Xe(i,s,e.vbaraw),n.vba.push(s),nr(t.wbrels,-1,"vbaProject.bin",We.VBA)),s="xl/metadata."+r,Xe(i,s,nm(s)),n.metadata.push(s),nr(t.wbrels,-1,"metadata."+r,We.XLMETA),Xe(i,"[Content_Types].xml",Ns(n,t)),Xe(i,"_rels/.rels",ba(t.rels)),Xe(i,"xl/_rels/workbook."+r+".rels",ba(t.wbrels)),delete t.revssf,delete t.ssf,i}function Nc(e,t){Ba=1024,e&&!e.SSF&&(e.SSF=ur(Se)),e&&e.SSF&&(Da(),ja(e.SSF),t.revssf=Rn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,pn?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=Wf.indexOf(t.bookType)>-1,n=U0();pi(t=t||{});var i=d0(),s="",f=0;if(t.cellXfs=[],aa(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Xe(i,s,Bs(e.Props,t)),n.coreprops.push(s),nr(t.rels,2,s,We.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}e.Props.Worksheets=e.Props.SheetNames.length,Xe(i,s,Hs(e.Props,t)),n.extprops.push(s),nr(t.rels,3,s,We.EXT_PROPS),e.Custprops!==e.Props&&wr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Xe(i,s,Vs(e.Custprops,t)),n.custprops.push(s),nr(t.rels,4,s,We.CUST_PROPS));var l=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var h={"!id":{}},x=e.Sheets[e.SheetNames[f-1]],d=(x||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Xe(i,s,tc(f-1,t,e,h)),n.sheets.push(s),nr(t.wbrels,-1,"worksheets/sheet"+f+"."+r,We.WS[0])}if(x){var v=x["!comments"],u=!1,p="";if(v&&v.length>0){var E=!1;v.forEach(function(T){T[1].forEach(function(g){g.T==!0&&(E=!0)})}),E&&(p="xl/threadedComments/threadedComment"+f+"."+r,Xe(i,p,Ix(v,l,t)),n.threadedcomments.push(p),nr(h,-1,"../threadedComments/threadedComment"+f+"."+r,We.TCMNT)),p="xl/comments"+f+"."+r,Xe(i,p,Uf(v,t)),n.comments.push(p),nr(h,-1,"../comments"+f+"."+r,We.CMNT),u=!0}x["!legacy"]&&u&&Xe(i,"xl/drawings/vmlDrawing"+f+".vml",Mf(f,x["!comments"])),delete x["!comments"],delete x["!legacy"]}h["!id"].rId1&&Xe(i,tn(s),ba(h))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Xe(i,s,Sf(t.Strings,t)),n.strs.push(s),nr(t.wbrels,-1,"sharedStrings."+r,We.SST)),s="xl/workbook."+r,Xe(i,s,lc(e,t)),n.workbooks.push(s),nr(t.rels,1,s,We.WB),s="xl/theme/theme1.xml",Xe(i,s,Q0(e.Themes,t)),n.themes.push(s),nr(t.wbrels,-1,"theme/theme1.xml",We.THEME),s="xl/styles."+r,Xe(i,s,Pf(e,t)),n.styles.push(s),nr(t.wbrels,-1,"styles."+r,We.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Xe(i,s,e.vbaraw),n.vba.push(s),nr(t.wbrels,-1,"vbaProject.bin",We.VBA)),s="xl/metadata."+r,Xe(i,s,Lf()),n.metadata.push(s),nr(t.wbrels,-1,"metadata."+r,We.XLMETA),l.length>1&&(s="xl/persons/person.xml",Xe(i,s,Rx(l,t)),n.people.push(s),nr(t.wbrels,-1,"persons/person.xml",We.PEOPLE)),Xe(i,"[Content_Types].xml",Ns(n,t)),Xe(i,"_rels/.rels",ba(t.rels)),Xe(i,"xl/_rels/workbook."+r+".rels",ba(t.wbrels)),delete t.revssf,delete t.ssf,i}function mi(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=hr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Eg(e,t){return Te.find(e,"EncryptedPackage")?gg(e,t):dc(e,t)}function Tg(e,t){var r,a=e,n=t||{};return n.type||(n.type=ye&&Q.isBuffer(e)?"buffer":"base64"),r=Qi(a,n),mg(r,n)}function bc(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return ci(e.slice(r),t);default:break e}return La.to_workbook(e,t)}function kg(e,t){var r="",a=mi(e,t);switch(t.type){case"base64":r=hr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=oa(e);break;default:throw new Error("Unrecognized type "+t.type)}return a[0]==239&&a[1]==187&&a[2]==191&&(r=lr(r)),t.type="binary",bc(r,t)}function Sg(e,t){var r=e;return t.type=="base64"&&(r=hr(r)),r=Ee.utils.decode(1200,r.slice(2),"str"),t.type="binary",bc(r,t)}function Fg(e){return e.match(/[^\x00-\x7F]/)?It(e):e}function gi(e,t,r,a){return a?(r.type="string",La.to_workbook(e,r)):La.to_workbook(t,r)}function a0(e,t){ve();var r=t||{};if(typeof ArrayBuffer!="undefined"&&e instanceof ArrayBuffer)return a0(new Uint8Array(e),(r=ur(r),r.type="array",r));typeof Uint8Array!="undefined"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno!="undefined"?"buffer":"array");var a=e,n=[0,0,0,0],i=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),Wa={},r.dateNF&&(Wa.dateNF=r.dateNF),r.type||(r.type=ye&&Q.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=ye?"buffer":"binary",a=So(e),typeof Uint8Array!="undefined"&&!ye&&(r.type="array")),r.type=="string"&&(i=!0,r.type="binary",r.codepage=65001,a=Fg(e)),r.type=="array"&&typeof Uint8Array!="undefined"&&e instanceof Uint8Array&&typeof ArrayBuffer!="undefined"){var s=new ArrayBuffer(3),f=new Uint8Array(s);if(f.foo="bar",!f.foo)return r=ur(r),r.type="array",a0(Ga(a),r)}switch((n=mi(a,r))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return Eg(Te.read(a,r),r);break;case 9:if(n[1]<=8)return dc(a,r);break;case 60:return ci(a,r);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return Nh(a,r);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return Tf.to_workbook(a,r);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?Tg(a,r):gi(e,a,r,i);case 239:return n[3]===60?ci(a,r):gi(e,a,r,i);case 255:if(n[1]===254)return Sg(a,r);if(n[1]===0&&n[2]===2&&n[3]===0)return Ea.to_workbook(a,r);break;case 0:if(n[1]===0&&(n[2]>=2&&n[3]===0||n[2]===0&&(n[3]===8||n[3]===9)))return Ea.to_workbook(a,r);break;case 3:case 131:case 139:case 140:return K0.to_workbook(a,r);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return Df.to_workbook(a,r);break;case 10:case 13:case 32:return kg(a,r);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return Rh.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?K0.to_workbook(a,r):gi(e,a,r,i)}function d_(e,t){var r=t||{};return r.type="file",a0(e,r)}function Lc(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return $a(t.file,Te.write(e,{type:ye?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Te.write(e,t)}function yg(e,t){var r=ur(t||{}),a=_g(e,r);return Mc(a,r)}function Ag(e,t){var r=ur(t||{}),a=Nc(e,r);return Mc(a,r)}function Mc(e,t){var r={},a=ye?"nodebuffer":typeof Uint8Array!="undefined"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?Te.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno!="undefined"&&typeof n=="string"){if(t.type=="binary"||t.type=="base64")return n;n=new Uint8Array($t(n))}return t.password&&typeof encrypt_agile!="undefined"?Lc(encrypt_agile(n,t.password),t):t.type==="file"?$a(t.file,n):t.type=="string"?lr(n):n}function Cg(e,t){var r=t||{},a=Om(e,r);return Lc(a,r)}function bt(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return Hr(It(a));case"binary":return It(a);case"string":return e;case"file":return $a(t.file,a,"utf8");case"buffer":return ye?gr(a,"utf8"):typeof TextEncoder!="undefined"?new TextEncoder().encode(a):bt(a,{type:"binary"}).split("").map(function(n){return n.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function Dg(e,t){switch(t.type){case"base64":return Hr(e);case"binary":return e;case"string":return e;case"file":return $a(t.file,e,"binary");case"buffer":return ye?gr(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function n0(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return t.type=="base64"?Hr(r):t.type=="string"?lr(r):r;case"file":return $a(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Bc(e,t){ve(),oc(e);var r=ur(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=Bc(e,r);return r.type="array",$t(a)}return Ag(e,r)}function i0(e,t){ve(),oc(e);var r=ur(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=i0(e,r);return r.type="array",$t(a)}var n=0;if(r.sheet&&(typeof r.sheet=="number"?n=r.sheet:n=e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return bt(km(e,r),r);case"slk":case"sylk":return bt(Ef.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return bt(Ec(e.Sheets[e.SheetNames[n]],r),r);case"txt":return Dg(Vc(e.Sheets[e.SheetNames[n]],r),r);case"csv":return bt(wi(e.Sheets[e.SheetNames[n]],r),r,"\uFEFF");case"dif":return bt(Tf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return n0(K0.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return bt(La.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return bt(Df.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return bt(kf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return bt(Oc(e,r),r);case"wk1":return n0(Ea.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return n0(Ea.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),n0(vc(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),Cg(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return yg(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function _i(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function v_(e,t,r){var a=r||{};return a.type="file",a.file=t,_i(a),i0(e,a)}function p_(e,t,r){var a=r||{};return a.type="file",a.file=t,_i(a),Bc(e,a)}function m_(e,t,r,a){var n=r||{};n.type="file",n.file=e,_i(n),n.type="buffer";var i=a;return i instanceof Function||(i=r),Jt.writeFile(e,i0(t,n),i)}function Uc(e,t,r,a,n,i,s,f){var c=kr(r),o=f.defval,l=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),h=!0,x=n===1?[]:{};if(n!==1)if(Object.defineProperty)try{Object.defineProperty(x,"__rowNum__",{value:r,enumerable:!1})}catch(p){x.__rowNum__=r}else x.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var v=s?e[r][d]:e[a[d]+c];if(v===void 0||v.t===void 0){if(o===void 0)continue;i[d]!=null&&(x[i[d]]=o);continue}var u=v.v;switch(v.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+v.t)}if(i[d]!=null){if(u==null)if(v.t=="e"&&u===null)x[i[d]]=null;else if(o!==void 0)x[i[d]]=o;else if(l&&u===null)x[i[d]]=null;else continue;else x[i[d]]=l&&(v.t!=="n"||v.t==="n"&&f.rawNumbers!==!1)?u:Pt(v,u,f);u!=null&&(h=!1)}}return{row:x,isempty:h}}function s0(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},a=0,n=1,i=[],s=0,f="",c={s:{r:0,c:0},e:{r:0,c:0}},o=t||{},l=o.range!=null?o.range:e["!ref"];switch(o.header===1?a=1:o.header==="A"?a=2:Array.isArray(o.header)?a=3:o.header==null&&(a=0),typeof l){case"string":c=Ze(l);break;case"number":c=Ze(e["!ref"]),c.s.r=l;break;default:c=l}a>0&&(n=0);var h=kr(c.s.r),x=[],d=[],v=0,u=0,p=Array.isArray(e),E=c.s.r,T=0,g={};p&&!e[E]&&(e[E]=[]);var R=o.skipHidden&&e["!cols"]||[],L=o.skipHidden&&e["!rows"]||[];for(T=c.s.c;T<=c.e.c;++T)if(!(R[T]||{}).hidden)switch(x[T]=pr(T),r=p?e[E][T]:e[x[T]+h],a){case 1:i[T]=T-c.s.c;break;case 2:i[T]=x[T];break;case 3:i[T]=o.header[T-c.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=Pt(r,null,o),u=g[s]||0,!u)g[s]=1;else{do f=s+"_"+u++;while(g[f]);g[s]=u,g[f]=1}i[T]=f}for(E=c.s.r+n;E<=c.e.r;++E)if(!(L[E]||{}).hidden){var I=Uc(e,c,E,x,a,i,p,o);(I.isempty===!1||(a===1?o.blankrows!==!1:o.blankrows))&&(d[v++]=I.row)}return d.length=v,d}var Wc=/"/g;function Hc(e,t,r,a,n,i,s,f){for(var c=!0,o=[],l="",h=kr(r),x=t.s.c;x<=t.e.c;++x)if(a[x]){var d=f.dense?(e[r]||[])[x]:e[a[x]+h];if(d==null)l="";else if(d.v!=null){c=!1,l=""+(f.rawNumbers&&d.t=="n"?d.v:Pt(d,null,f));for(var v=0,u=0;v!==l.length;++v)if((u=l.charCodeAt(v))===n||u===i||u===34||f.forceQuotes){l='"'+l.replace(Wc,'""')+'"';break}l=="ID"&&(l='"ID"')}else d.f!=null&&!d.F?(c=!1,l="="+d.f,l.indexOf(",")>=0&&(l='"'+l.replace(Wc,'""')+'"')):l="";o.push(l)}return f.blankrows===!1&&c?null:o.join(s)}function wi(e,t){var r=[],a=t==null?{}:t;if(e==null||e["!ref"]==null)return"";var n=Ze(e["!ref"]),i=a.FS!==void 0?a.FS:",",s=i.charCodeAt(0),f=a.RS!==void 0?a.RS:`
`,c=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),l="",h=[];a.dense=Array.isArray(e);for(var x=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],v=n.s.c;v<=n.e.c;++v)(x[v]||{}).hidden||(h[v]=pr(v));for(var u=0,p=n.s.r;p<=n.e.r;++p)(d[p]||{}).hidden||(l=Hc(e,n,p,h,s,c,i,a),l!=null&&(a.strip&&(l=l.replace(o,"")),(l||a.blankrows!==!1)&&r.push((u++?f:"")+l)));return delete a.dense,r.join("")}function Vc(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=wi(e,t);if(typeof Ee=="undefined"||t.type=="string")return r;var a=Ee.utils.encode(1200,r,"str");return"\xFF\xFE"+a}function Og(e){var t="",r,a="";if(e==null||e["!ref"]==null)return[];var n=Ze(e["!ref"]),i="",s=[],f,c=[],o=Array.isArray(e);for(f=n.s.c;f<=n.e.c;++f)s[f]=pr(f);for(var l=n.s.r;l<=n.e.r;++l)for(i=kr(l),f=n.s.c;f<=n.e.c;++f)if(t=s[f]+i,r=o?(e[l]||[])[f]:e[t],a="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;a=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)a=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)a=""+r.v;else if(r.t=="b")a=r.v?"TRUE":"FALSE";else if(r.w!==void 0)a="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?a="'"+r.v:a=""+r.v}}c[c.length]=t+"="+a}return c}function Xc(e,t,r){var a=r||{},n=+!a.skipHeader,i=e||{},s=0,f=0;if(i&&a.origin!=null)if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?_r(a.origin):a.origin;s=c.r,f=c.c}var o,l={s:{c:0,r:0},e:{c:f,r:s+t.length-1+n}};if(i["!ref"]){var h=Ze(i["!ref"]);l.e.c=Math.max(l.e.c,h.e.c),l.e.r=Math.max(l.e.r,h.e.r),s==-1&&(s=h.e.r+1,l.e.r=s+t.length-1+n)}else s==-1&&(s=0,l.e.r=t.length-1+n);var x=a.header||[],d=0;t.forEach(function(u,p){wr(u).forEach(function(E){(d=x.indexOf(E))==-1&&(x[d=x.length]=E);var T=u[E],g="z",R="",L=Ce({c:f+d,r:s+p+n});o=Tn(i,L),T&&typeof T=="object"&&!(T instanceof Date)?i[L]=T:(typeof T=="number"?g="n":typeof T=="boolean"?g="b":typeof T=="string"?g="s":T instanceof Date?(g="d",a.cellDates||(g="n",T=Rr(T)),R=a.dateNF||Se[14]):T===null&&a.nullError&&(g="e",T=0),o?(o.t=g,o.v=T,delete o.w,delete o.R,R&&(o.z=R)):i[L]=o={t:g,v:T},R&&(o.z=R))})}),l.e.c=Math.max(l.e.c,f+x.length-1);var v=kr(s);if(n)for(d=0;d<x.length;++d)i[pr(d+f)+v]={t:"s",v:x[d]};return i["!ref"]=Ue(l),i}function Ig(e,t){return Xc(null,e,t)}function Tn(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var a=_r(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Tn(e,Ce(t)):Tn(e,Ce({r:t,c:r||0}))}function Pg(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function Ei(){return{SheetNames:[],Sheets:{}}}function Ti(e,t,r,a){var n=1;if(!r)for(;n<=65535&&e.SheetNames.indexOf(r="Sheet"+n)!=-1;++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);n=i&&+i[2]||0;var s=i&&i[1]||r;for(++n;n<=65535&&e.SheetNames.indexOf(r=s+n)!=-1;++n);}if(cc(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function Rg(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=Pg(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r}function Ng(e,t){return e.z=t,e}function Gc(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function bg(e,t,r){return Gc(e,"#"+t,r)}function Lg(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function Mg(e,t,r,a){for(var n=typeof t!="string"?t:Ze(t),i=typeof t=="string"?t:Ue(t),s=n.s.r;s<=n.e.r;++s)for(var f=n.s.c;f<=n.e.c;++f){var c=Tn(e,s,f);c.t="n",c.F=i,delete c.v,s==n.s.r&&f==n.s.c&&(c.f=r,a&&(c.D=!0))}return e}var Bg={encode_col:pr,encode_row:kr,encode_cell:Ce,encode_range:Ue,decode_col:D0,decode_row:C0,split_cell:rl,decode_cell:_r,decode_range:et,format_cell:Pt,sheet_add_aoa:Fs,sheet_add_json:Xc,sheet_add_dom:Tc,aoa_to_sheet:Ra,json_to_sheet:Ig,table_to_sheet:kc,table_to_book:$m,sheet_to_csv:wi,sheet_to_txt:Vc,sheet_to_json:s0,sheet_to_html:Ec,sheet_to_formulae:Og,sheet_to_row_object_array:s0,sheet_get_cell:Tn,book_new:Ei,book_append_sheet:Ti,book_set_sheet_visibility:Rg,cell_set_number_format:Ng,cell_set_hyperlink:Gc,cell_set_internal_link:bg,cell_add_comment:Lg,sheet_set_array_formula:Mg,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},f0;function Ug(e){f0=e}function Wg(e,t){var r=f0(),a=t==null?{}:t;if(e==null||e["!ref"]==null)return r.push(null),r;var n=Ze(e["!ref"]),i=a.FS!==void 0?a.FS:",",s=i.charCodeAt(0),f=a.RS!==void 0?a.RS:`
`,c=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),l="",h=[];a.dense=Array.isArray(e);for(var x=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],v=n.s.c;v<=n.e.c;++v)(x[v]||{}).hidden||(h[v]=pr(v));var u=n.s.r,p=!1,E=0;return r._read=function(){if(!p)return p=!0,r.push("\uFEFF");for(;u<=n.e.r;)if(++u,!(d[u-1]||{}).hidden&&(l=Hc(e,n,u-1,h,s,c,i,a),l!=null&&(a.strip&&(l=l.replace(o,"")),l||a.blankrows!==!1)))return r.push((E++?f:"")+l);return r.push(null)},r}function Hg(e,t){var r=f0(),a=t||{},n=a.header!=null?a.header:gc,i=a.footer!=null?a.footer:_c;r.push(n);var s=et(e["!ref"]);a.dense=Array.isArray(e),r.push(wc(e,s,a));var f=s.s.r,c=!1;return r._read=function(){if(f>s.e.r)return c||(c=!0,r.push("</table>"+i)),r.push(null);for(;f<=s.e.r;){r.push(mc(e,s,f,a)),++f;break}},r}function Vg(e,t){var r=f0({objectMode:!0});if(e==null||e["!ref"]==null)return r.push(null),r;var a={t:"n",v:0},n=0,i=1,s=[],f=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},h=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)&&(n=3),typeof h){case"string":o=Ze(h);break;case"number":o=Ze(e["!ref"]),o.s.r=h;break;default:o=h}n>0&&(i=0);var x=kr(o.s.r),d=[],v=0,u=Array.isArray(e),p=o.s.r,E=0,T={};u&&!e[p]&&(e[p]=[]);var g=l.skipHidden&&e["!cols"]||[],R=l.skipHidden&&e["!rows"]||[];for(E=o.s.c;E<=o.e.c;++E)if(!(g[E]||{}).hidden)switch(d[E]=pr(E),a=u?e[p][E]:e[d[E]+x],n){case 1:s[E]=E-o.s.c;break;case 2:s[E]=d[E];break;case 3:s[E]=l.header[E-o.s.c];break;default:if(a==null&&(a={w:"__EMPTY",t:"s"}),c=f=Pt(a,null,l),v=T[f]||0,!v)T[f]=1;else{do c=f+"_"+v++;while(T[c]);T[f]=v,T[c]=1}s[E]=c}return p=o.s.r+i,r._read=function(){for(;p<=o.e.r;)if(!(R[p-1]||{}).hidden){var L=Uc(e,o,p,d,n,s,u,l);if(++p,L.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows)){r.push(L.row);return}}return r.push(null)},r}var g_={to_json:Vg,to_html:Hg,to_csv:Wg,set_readable:Ug};const __=Ne.version}}]);
}());