package com.zkteco.mars.usc.controller;


import com.zkteco.framework.base.bean.Pager;
import com.zkteco.framework.core.utils.I18nUtil;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.usc.constant.USCConstants;
import com.zkteco.mars.usc.service.TextControlService;
import com.zkteco.mars.usc.vo.ControlRulesItem;
import com.zkteco.mars.usc.vo.EventRecordItem;
import com.zkteco.mars.usc.vo.EventTypeItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 系统管理
 *
 * <AUTHOR>
 * @date 2024-12-31 9:38
 * @since 1.0.0
 */
@RestController
@RequestMapping("/v1/text_control")
public class TextControlController {

    @Autowired
    private TextControlService textControlService;

    /**
     * 分页查询事件类型
     *
     * @param eventTypeItem:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-09 16:30
     * @since 1.0.0
     */
    @PostMapping("/getEventTypeList")
    @ResponseBody
    public ApiResultMessage getEventTypeList(@RequestBody EventTypeItem eventTypeItem, @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(USCConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(USCConstants.API_PAGE_OVERSIZE, I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        try {
            Pager pager = textControlService.getEventTypeItemsByPage(eventTypeItem, pageNo - 1, pageSize);
            List<EventTypeItem> list = (List<EventTypeItem>) pager.getData();
            pager.setData(list);
            return ApiResultMessage.successMessage(pager);
        } catch (Exception e) {
            return ApiResultMessage.failedMessage(-1);
        }
    }


    /**
     * 布控规则查询
     *
     * @param controlRulesItem:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-10 11:50
     * @since 1.0.0
     */
    @PostMapping("/getControlRulesList")
    @ResponseBody
    public ApiResultMessage getControlRulesList(@RequestBody ControlRulesItem controlRulesItem, @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(USCConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(USCConstants.API_PAGE_OVERSIZE, I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        try {
            Pager pager = textControlService.getControlRulesItemsByPage(controlRulesItem, pageNo - 1, pageSize);
            List<ControlRulesItem> list = (List<ControlRulesItem>) pager.getData();
            pager.setData(list);
            return ApiResultMessage.successMessage(pager);
        } catch (Exception e) {
            return ApiResultMessage.failedMessage(-1);
        }
    }


    /**
     * 添加编辑布控规则
     *
     * @param controlRule:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:36
     * @since 1.0.0
     */
    @PostMapping("/editControlRule")
    @ResponseBody
    public ApiResultMessage editControlRules(@RequestBody ControlRulesItem controlRule) {
        return textControlService.editControlRule(controlRule);
    }


    /**
     * 获取所有的事件类型
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:37
     * @since 1.0.0
     */
    @PostMapping("/getAllEventType")
    @ResponseBody
    public ApiResultMessage getAllEventType() {
        return textControlService.getAllEventType();
    }


    /**
     * 删除布控规则
     *
     * @param ids:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:37
     * @since 1.0.0
     */
    @PostMapping("/delControlRules")
    @ResponseBody
    public ApiResultMessage delControlRules(@RequestParam(name = "ids") String ids) {
        return textControlService.delControlRules(ids);
    }


    /**
     * 获取所有的布控规则
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:37
     * @since 1.0.0
     */
    @PostMapping("/getAllControlRules")
    @ResponseBody
    public ApiResultMessage getAllControlRules() {
        return textControlService.getAllControlRules();
    }


    /**
     * 开始布控 and 取消布控
     *
     * @param pointId:
     * @param ruleIds:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:25
     * @since 1.0.0
     */
    @PostMapping("/startControl")
    @ResponseBody
    public ApiResultMessage startControl(@RequestParam(name = "pointId") String pointId,@RequestParam(name = "ruleIds") String ruleIds) {
        return textControlService.startControl(pointId,ruleIds);
    }


    /**
     * 获取点位布控的规则
     *
     * @param pointId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:26
     * @since 1.0.0
     */
    @PostMapping("/getPointControls")
    @ResponseBody
    public ApiResultMessage getPointControls(@RequestParam(name = "pointId") String pointId) {
        return textControlService.getPointControls(pointId);
    }


    /**
     * 获取事件记录
     *
     * @param eventRecordItem:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:27
     * @since 1.0.0
     */
    @PostMapping("/getEventRecordList")
    @ResponseBody
    public ApiResultMessage getEventRecordList(@RequestBody EventRecordItem eventRecordItem, @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(USCConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(USCConstants.API_PAGE_OVERSIZE, I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        try {
            Pager pager = textControlService.getEventRecordsItemsByPage(eventRecordItem, pageNo - 1, pageSize);
            List<EventRecordItem> eventRecordlist = (List<EventRecordItem>) pager.getData();
            pager.setData(eventRecordlist);
            return ApiResultMessage.successMessage(pager);
        } catch (Exception e) {
            return ApiResultMessage.failedMessage(-1);
        }
    }


    /**
     *  获取已使用的事件类型
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 10:07
     * @since 1.0.0
     */
    @PostMapping("/getUsedEventCodes")
    @ResponseBody
    public ApiResultMessage getUsedEventCodes() {
        return textControlService.getUsedEventCodes();
    }


    /**
     * 确认规则是否在布控
     *
     * @param ruleId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @date 2025-06-05 13:46
     * @since 1.0.0
     */
    @PostMapping("/checkRuleIsControl")
    @ResponseBody
    public ApiResultMessage checkRuleIsControl(@RequestParam(name = "ruleId") String ruleId) {
        return textControlService.checkRuleIsControl(ruleId);
    }



}
