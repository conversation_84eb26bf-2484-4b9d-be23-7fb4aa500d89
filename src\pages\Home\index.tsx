import { PageContainer, ProLayout, ProCard } from '@ant-design/pro-components';
import { useModel, useIntl } from '@umijs/max';
import { theme, Input, Menu, Spin, Modal, message, Avatar, Image, Button, Card, Carousel, Select, Slider } from 'antd';
import React, { useState, useEffect, useRef, useLayoutEffect, useCallback } from 'react'
import { SearchOutlined, EllipsisOutlined } from '@ant-design/icons';
import { getHistoryRecord, smartSearch, getImageFile, getTargetImageList, getChannelInfo, getPointControls, getVLMInfo } from '@/services/ant-design-pro/api';
import AudioRecorder from './AudioRecorder';
import ImageSearchPage from './components/ImageSearchPage';
import EventDetailModal from './components/EventDetailModal';
import SearchConditions from '@/components/SearchConditions';
import '@/components/SearchConditions/index.less';
import './index.less';

const infoTextStyle = {
  margin: '3px 0 4px',
  fontSize: '12px',
  color: 'rgb(33, 33, 33)',
  fontWeight: 400,
  lineHeight: '17px',
  textAlign: 'left',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
};

const iconStyle = {
  width: '16px',
  height: '16px',
};

const { confirm } = Modal;

// 缓存键名
const HISTORY_CACHE_KEY = 'smartSearchHistory';

const Home: React.FC = () => {
  // 全局token
  const { token } = theme.useToken();
  // 初始化方法
  const { initialState } = useModel('@@initialState');
  // 侧边栏展开/折叠
  const [collapsed, setCollapsed] = useState(false);
  // 历史记录搜索值
  const [historySearchValue, setHistorySearchValue] = useState('');
  // 历史记录tabel数组
  const [history, setHistory] = useState<string[]>([]);
  // 智能搜索值
  const [smartSearchValue, setSmartSearchValue] = useState('');
  // 搜索切换状态·
  const [searchStatus, setSearchStatus] = useState(false);
  // 搜索结果
  const [searchResults, setSearchResults] = useState<any[]>([]);
  // 搜索加载
  const [searchLoading, setSearchLoading] = useState(false);
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [pageLoading, setPageLoading] = useState(false);
  const [total, setTotal] = useState(0);
  // 音频组件ref引入
  const audioRecorderRef = useRef(null);
  // 搜索框容器
  const containerRef = useRef(null);
  // 搜索框高度
  const [maxHeight, setMaxHeight] = useState('auto');
  // 历史记录hover index 第几个
  const [hoverIndex, setHoverIndex] = useState(null);
  // 历史记录hover时展示按钮
  const [showActions, setShowActions] = useState(null);
  // 上传图片列表
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  // 轨迹图片状态管理
  const [trajectoryImage, setTrajectoryImage] = useState<string[]>([]);
  // 是否隐藏文本区域
  const [hideTextarea, setHideTextarea] = useState(false);
  // 图片搜索页面状态
  const [showImageSearch, setShowImageSearch] = useState(false);

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);
  // 处理 历史记录 文字部分渐隐
  const [overflowItems, setOverflowItems] = useState([]);
  // 历史记录文字部分
  const spanRefs = useRef([]);
  // 打开事件详情窗口
  const [isEventInfoVisible, setIsEventInfoVisible] = useState(false);
  // 当前事件信息
  const [eventDetails, setEventDetails] = useState<any>(null);
  // 视频播放地址
  const [videoSrc, setVideoSrc] = useState("");
  // 相似度阈值
  const [similarityValue, setSimilarityValue] = useState(0.75);


  const intl = useIntl();

  const searchConditionOptions = [
    {
      value: 'time', label: intl.formatMessage({ id: 'pages.search.sortByTime', defaultMessage: '按最新时间', })
    },
    {
      value: 'score', label: intl.formatMessage({ id: 'pages.search.sortBySimilarity', defaultMessage: '按相似度', })
    },
  ];

  const [searchCondition, setSearchCondition] = useState('time');
  // 时间范围状态
  const [startTime, setStartTime] = useState<number>();
  const [endTime, setEndTime] = useState<number>();
  // 点位选择状态
  const [selectedPointIds, setSelectedPointIds] = useState<string[]>([]);

  const handleConditionChange = (value: string) => {
    setSearchCondition(value);
    console.log('选择的检索条件:', value);
  };

  // 时间范围变化处理
  const handleTimeChange = (newStartTime?: number, newEndTime?: number) => {
    setStartTime(newStartTime);
    setEndTime(newEndTime);
    console.log('时间范围变化:', { startTime: newStartTime, endTime: newEndTime });
  };

  // 点位选择变化处理
  const handlePointChange = (pointIds: string[]) => {
    setSelectedPointIds(pointIds);
    console.log('点位选择变化:', pointIds);
  };

  const loadParamConfig = async () => {
    const rs = await getVLMInfo("retrieval_strategy");
    if (rs.code !== 0 || !rs.data) {
      message.error(intl.formatMessage({ id: 'pages.config.paramConfigFailure', defaultMessage: '获取参数配置失败', }))
      return;
    }
    setSimilarityValue(rs.data.vector_min_score)
  };



  const handleRecordClose = useCallback(() => {
    setIsEventInfoVisible(false);
  }, []);




  /**
  * 历史记录重命名
  */
  const handleHistoryEdit = () => {

  };

  /**
   * 删除历史记录
   * @param item 历史记录
   * @param index 历史记录索引
   */
  const handleHistoryDel = (item: any, index: number) => {
    confirm({
      title: intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除', }),
      content: intl.formatMessage({ id: 'pages.search.deleteConfirmWarning', defaultMessage: '删除后将无法恢复，确定要删除吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      okType: 'danger',
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk() {
        console.log("删除历史记录 ->", item, index);
        message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }))
        setHistory(prevHistory => {
          const updatedHistory = prevHistory.filter((_, i) => i !== index);

          // 更新缓存
          localStorage.setItem(HISTORY_CACHE_KEY, JSON.stringify(updatedHistory));

          return updatedHistory;
        });
      },
    });
  };

  /**
   * 语音识别结果处理
   * @param status 状态
   * @param text 文本
   */
  const handleVoiceToText = (status: string, text: any) => {
    if (status == "upload") {
      // 拼接 text 和 current smartSearchValue
      setSmartSearchValue((prevValue) => prevValue + text);
    } else if (status == "start") {
      setSmartSearchValue("");
    }

  };


  /**
   * 点击图片放大时处理
   * @param item 图片
   */
  const handleImageClick = async (item: any) => {
    console.log("item->", item)
    try {
      //显示窗口
      setVideoSrc("");
      setIsEventInfoVisible(true)


      // 立即设置事件详情，避免等待加载
      setEventDetails(item)

      // 异步并行加载所有资源，互不影响

      // 1. 加载原图（异步）
      if (!item.imageUrl) {
        console.log("加载原图 item.name->", item.name)
        fetchImage(item.name, "snap")
          .then(imageUrl => {
            updateImageInResults(item.name, imageUrl, item.thumbUrl);
            setEventDetails(prev => prev?.name === item.name ? { ...prev, imageUrl } : prev);
          })
          .catch(error => console.error('加载原图失败:', error));
      }

      // 2. 加载目标图（异步）
      getTargetImageList(item.name)
        .then(async (res) => {
          const targetImageUrls = await Promise.all(
            res.data.map(async (targetName: string) => {
              return await fetchImage(targetName, "target");
            })
          );
          setEventDetails(prev => prev?.name === item.name ? { ...prev, targetImages: targetImageUrls } : prev);
        })
        .catch(error => console.error('加载目标图失败:', error));

      // 3.加载视频
      const fileNameWithoutPng = item.name.replace('.png', '');
      const serverIP = window.location.hostname;
      const protocol = window.location.protocol;
      const url = `${protocol}//${serverIP}/record/${fileNameWithoutPng}.mp4`; // 动态拼接协议
      //const url = 'https://www.w3schools.com/html/mov_bbb.mp4'
      setVideoSrc(url);

    } catch (error) {
      console.error('originalUrl loading error: ', error);
    }
  };

  // /**
  //  * 获取历史记录
  //  */
  // const getHistoryLog = async () => {
  //   const log = await getHistoryRecord();
  //   setHistory(log.data);
  // }

  /**
   * 搜索历史记录
   */
  const handleHistorySearch = (searchValue) => {
    console.log("handleHistorySearch->", searchValue)
    const existingHistory = JSON.parse(localStorage.getItem(HISTORY_CACHE_KEY)) || []; // 从缓存获取历史记录
    const filteredHistory = existingHistory
      .filter((item) => item.value.includes(searchValue)) // 过滤包含搜索值的记录
      .sort((a, b) => new Date(b.time) - new Date(a.time)); // 保持降序排列

    setHistory(filteredHistory); // 更新展示的历史记录
  };

  /**
   * 智能搜索（先展示文本信息，图片逐步加载）
   */
  const handleSmartSearch = async (isLoadMore = false) => {
    console.log("isLoadMore->", isLoadMore)
    //搜索暂停录音
    audioRecorderRef?.current?.stopRecording();

    setHoverIndex(null)
    setShowActions(null)

    console.log("smartSearchValue->", smartSearchValue);
    if (!smartSearchValue) return;

    // 如果是加载更多，使用pageLoading，否则使用searchLoading
    if (isLoadMore) {
      setPageLoading(true);
    } else {
      setSearchLoading(true);
      setSearchStatus(true);
      // 重置分页状态
      setCurrentPage(1);
      setHasMore(false);
      setTotal(0);

      // 每次搜索前清空搜索结果
      setSearchResults([]);
    }

    try {
      let allResults = isLoadMore ? [...searchResults] : [];
      let currentPageNum = isLoadMore ? (currentPage as number) + 1 : 1;
      let hasMoreData = true;
      let totalCount = 0;

      // 用于追踪已经加载的页数和数据
      let loadedPages = 0;
      const maxContinuousPages = 10; // 最多连续加载页数，防止无限循环

      // 循环获取数据直到满足条件
      while (hasMoreData && loadedPages < maxContinuousPages) {
        const params: {
          keyWord: string;
          sortedBy: string;
          minScore?: number;
          pageNo?: number;
          startTime?: number;
          endTime?: number;
          pointIds?: string[];
        } = {
          keyWord: smartSearchValue,
          sortedBy: searchCondition,
          minScore: similarityValue,
          pageNo: currentPageNum,
          ...(startTime && endTime && { startTime, endTime }),
          ...(selectedPointIds.length > 0 && { pointIds: selectedPointIds }),
        };

        const res = await smartSearch(params);

        if (res.code != 0) {
          message.error(
            intl.formatMessage({
              id: 'pages.serverCode.' + res.code,
              defaultMessage: res.msg
            })
          );
          break;
        }

        // 处理新的API返回格式
        const { data: responseData } = res.data;
        const { total: totalCountRes, hasMore: moreData, data: searchData } = res.data;

        totalCount = totalCountRes;
        hasMoreData = moreData;

        // 先把搜索到的结果（无图片）直接展示
        const newResults = responseData.map((item: any) => ({
          thumbUrl: "", // 图片先为空
          name: item.metadata?.name || "",
          time: formatTimestamp(item.metadata?.originTime),
          address: item.metadata?.pointName || "",
          score: `${(item.score * 100).toFixed(5)}%`,
          targetImages: [],
          content: item.content, // 新增内容字段
          embeddingId: item.embeddingId, // 新增embeddingId字段
        }));

        allResults = [...allResults, ...newResults];
        loadedPages++;

        // 立即更新UI，实现补充式加载
        if (isLoadMore) {
          setSearchResults(prev => [...prev, ...newResults]);
          setCurrentPage(currentPageNum);
        } else if (loadedPages === 1) {
          // 第一次搜索时，先清空再设置
          setSearchResults(newResults);
          setCurrentPage(1);
        } else {
          // 后续页数据追加
          setSearchResults(prev => [...prev, ...newResults]);
        }

        setHasMore(hasMoreData);
        setTotal(totalCount);

        // 立即开始异步加载图片，不阻塞后续数据加载
        newResults.forEach(item => {
          if (!item.name) return;

          // 异步加载图片，不等待
          (async () => {
            try {
              let thumbUrl = await fetchImage(item.name, "thumb");
              if (!thumbUrl) {
                thumbUrl = await fetchImage(item.name, "snap");
              }

              if (thumbUrl) {
                updateImageInResults(item.name, null, thumbUrl);
              } else {
                removeImageFromResults(item.name);
              }
            } catch (error) {
              console.error("加载图片失败:", error);
              removeImageFromResults(item.name);
            }
          })();
        });

        //   // 如果当前页有数据且总数据量已经>=24条，就停止连续加载
        // // 让滚动触发后续加载，提升用户体验
        // if (responseData.length > 0 && allResults.length >= 15) {
        //   break;
        // }

        // 智能加载策略：确保能够触发滚动加载下一页
        // 核心逻辑：当hasMore为true时，确保加载足够数据来触发滚动
        if (responseData.length > 0) {
          // 基础判断：确保至少加载一定数量，避免过度加载
          const baseMinItems = 12; // 基础最小值

          // 动态计算确保能触发滚动的数据量
          const containerWidth = Math.max(800, window.innerWidth - 300); // 最小宽度保护
          const containerHeight = Math.max(600, window.innerHeight - 200);

          const itemsPerRow = Math.max(1, Math.floor(containerWidth / 270));
          const rowsNeeded = Math.ceil(containerHeight / 210) + 1; // 确保多一行
          const calculatedMinItems = itemsPerRow * rowsNeeded;

          // 取计算值和基础值的较大者，但不超过合理上限
          const targetItems = Math.min(Math.max(calculatedMinItems, baseMinItems), 24);

          // 关键判断：当hasMoreData为true时，优先确保能触发滚动
          if (allResults.length >= targetItems) {
            console.log(`已加载${allResults.length}条数据，满足滚动条件`);
            break;
          }

          // 安全机制：防止无限加载，最多加载3页
          if (loadedPages >= 3) {
            console.log(`已加载${loadedPages}页，停止继续加载`);
            break;
          }
        }

        // 如果已经没有更多数据，也停止
        if (!hasMoreData) {
          break;
        }

        currentPageNum++;
      }

      // 只在第一次搜索时保存缓存
      if (!isLoadMore && loadedPages > 0) {
        saveToCache(smartSearchValue);
      }

      // 在循环结束后更新currentPage，确保下次滚动加载从正确的页码开始
      if (!isLoadMore && loadedPages > 0) {
        setCurrentPage(currentPageNum);
      }
    } catch (error) {
      console.error("搜索失败:", error);
      message.error("搜索请求失败，请稍后重试");
    } finally {
      if (isLoadMore) {
        setPageLoading(false);
      } else {
        setSearchLoading(false);
      }
    }
  };

  // 滚动监听函数，用于触发分页加载
  const handleScroll = (event: any) => {
    const { scrollTop, scrollHeight, clientHeight } = event.target;

    // 当滚动到底部附近时（距离底部100px），触发加载更多
    if (scrollHeight - scrollTop - clientHeight < 100) {
      if (hasMore && !pageLoading && !searchLoading) {
        handleSmartSearch(true);
      }
    }
  };

  /**
   * 以图搜图
   */
  const handleImageSearch = async (isLoadMore = false) => {
    console.log("图片搜索 isLoadMore->", isLoadMore)
    setShowImageSearch(true);
  };

  /**
   * 从 searchResults 中删除 name 对应的项
   */
  const removeImageFromResults = (name: any) => {
    setSearchResults((prevResults) => prevResults.filter((item) => item.name !== name));
  };

  /**
   * 获取图片（支持缩略图 & 原图）
   * @param imageName 图片名称
   * @param imageType 是否获取缩略图（snap 抓拍 thumb 缩略图 target 目标抠图）
   * @returns 图片的 URL（本地 Blob URL）
   */
  const fetchImage = async (imageName: string, imageType: string): Promise<string | null> => {
    try {
      const response = await getImageFile(imageName, imageType);
      if (!response.ok) {
        console.warn(`图片 ${imageName} 获取失败，状态码：${response.status}`);
        return null;
      }

      const blob = await response.blob();
      return URL.createObjectURL(blob); // 生成 Blob URL 供前端展示
    } catch (error) {
      console.error(`获取图片 ${imageName} 失败:`, error);
      return null;
    }
  };

  /**
   * 更新 searchResults 中对应 name 的图片 URL
   * @param name 图片名称
   * @param imageUrl 图片的 URL
   */
  const updateImageInResults = (name: string, imageUrl: any, thumbUrl: any,) => {
    setSearchResults((prevResults) =>
      prevResults.map((item) => (item.name === name ? { ...item, imageUrl, thumbUrl } : item))
    );
  };

  /**
   * 处理图片上传
   * @param event 文件选择事件
   */
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // 限制只能上传一张图片
      const file = files[0];
      const newImage = URL.createObjectURL(file);
      setUploadedImages([newImage]);
      // 上传图片后自动隐藏文本区域并切换到图片搜索页面
      setHideTextarea(true);
    }
  };

  /**
   * 移除上传的图片
   * @param index 图片索引
   */
  const handleRemoveImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
    // 删除图片后恢复文本区域的显示
    setHideTextarea(false);
  };

  /**
   * 触发文件输入点击
   */
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  /**
   * 格式化时间戳
   * @param timestamp 时间戳（字符串或数字）
   * @returns 格式化后的时间 `YYYY-MM-DD HH:mm:ss`
   */
  const formatTimestamp = (timestamp: string | number): string => {
    if (!timestamp) return "";
    const date = new Date(Number(timestamp));
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`;
  };


  /**
  * 存储搜索记录到缓存
  * @param {string} searchValue
  */
  const saveToCache = (searchValue) => {
    const now = new Date().toISOString(); // 获取当前时间
    const existingHistory = JSON.parse(localStorage.getItem(HISTORY_CACHE_KEY)) || [];

    // 去重：过滤掉已有的 searchValue
    const filteredHistory = existingHistory.filter(item => item.value !== searchValue);

    // 添加新的记录到最前面
    const updatedHistory = [{ value: searchValue, time: now }, ...filteredHistory];

    // 限制记录数量为 10
    const limitedHistory = updatedHistory.slice(0, 10);

    // 保存到缓存
    localStorage.setItem(HISTORY_CACHE_KEY, JSON.stringify(limitedHistory));

    // 更新组件状态
    setHistory(limitedHistory);
  };



  /**
  * 查询缓存中的历史记录
  */
  const getHistoryLog = () => {
    const rs = JSON.parse(localStorage.getItem(HISTORY_CACHE_KEY)) || [];
    setHistory(rs)
  };


  const handleHistoryItemDoubleClick = (value) => {
    setSmartSearchValue(value); // 将历史记录的值赋给搜索框
  };
  // 使用 useLayoutEffect 确保第一次渲染时高度正确
  // useLayoutEffect(() => {
  //   const updateHeight = () => {
  //     const availableHeight = window.innerHeight - 100; // 预留 100px 头部
  //     const rowCount = Math.floor(availableHeight / 170); // 计算可容纳的行数
  //     const totalHeight = rowCount * 170 + (rowCount - 1) * 16; // 计算高度（包含 `gap`）
  //     setMaxHeight(`${totalHeight}px`);
  //   };

  //   updateHeight(); // **初始化时计算**
  //   setTimeout(updateHeight, 10); // **确保 DOM 加载后计算**
  //   window.addEventListener('resize', updateHeight);

  //   return () => window.removeEventListener('resize', updateHeight);
  // }, []);

  useEffect(() => {
    //查询历史记录
    getHistoryLog()

    const handleKeyDown = (event) => {
      // 如果当前焦点在历史记录搜索框，则不触发智能搜索
      const activeElement = document.activeElement;
      const isHistorySearchInput = activeElement?.getAttribute('data-type') === 'history-search';

      if (event.key === 'Enter' && !event.shiftKey && !isHistorySearchInput) {
        event.preventDefault();
        handleSmartSearch(false); // 调用搜索逻辑
      }
    };

    // 添加键盘事件监听器
    window.addEventListener('keydown', handleKeyDown);


    // 清除事件监听器
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [smartSearchValue]); // 将 smartSearchValue 添加为依赖

  useEffect(() => {
    // 加载相似度参数配置
    loadParamConfig();
  }, []);



  useEffect(() => {
    const checkOverflow = () => {
      const overflows = history.map((item, index) => {
        const spanRef = spanRefs.current[index]; // 获取对应的 span
        return {
          index,
          overflow: spanRef && spanRef.scrollWidth > spanRef.clientWidth, // 判断是否溢出
        };
      });
      setOverflowItems(overflows);
    };

    checkOverflow(); // 初始检查溢出
    window.addEventListener("resize", checkOverflow); // 窗口调整时重新检查

    return () => {
      window.removeEventListener("resize", checkOverflow); // 清理事件监听
    };
  }, [history]);

  const handleSimilarityChange = (value: number) => {
    setSimilarityValue(value);
  };

  // 添加CSS动画样式
  const spinAnimation = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;

  return (
    <>
      <style>{spinAnimation}</style>
      <div style={{
        display: 'flex',
        height: '100%',
        backgroundImage: "url('/icons/search/search_bg.png')",
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}>
        {/* 左侧可折叠侧边栏 */}
        {!showImageSearch ? (
          <div
            style={{
              width: collapsed ? '2.5vw' : '13vw',
              transition: 'width 0.3s',
              background: 'rgb(255, 255, 255)',
              boxShadow: '2px 0 5px rgba(0, 0, 0, 0.1)',
              margin: '12px',
              position: 'relative', // 让子元素可以绝对定位
              borderRadius: '10px'
            }}
          >
            <img
              src={collapsed ? '/icons/search/path_zhankai.svg' : '/icons/search/path_shouqi.svg'} // 替换为实际图片路径
              onClick={() => setCollapsed(!collapsed)}
              title={collapsed ? intl.formatMessage({ id: 'pages.search.expandir', defaultMessage: '展开', }) : intl.formatMessage({ id: 'pages.search.retirar', defaultMessage: '收起', })}
              style={{
                position: 'absolute', // 绝对定位
                bottom: 16, // 距离底部 16px
                right: 16, // 超出侧边栏宽度
                //right: -12, // 超出侧边栏宽度
                // width: 32, // 图片宽度
                //height: 32, // 图片高度
                cursor: 'pointer', // 鼠标变成手型
                zIndex: 1000,
                //borderRadius: '10px', // 圆角
                //boxShadow: '1px 1px 4px 0px rgba(0, 0, 0, 0.15)', // 阴影
              }}
            />
            {collapsed && (
              <>
                <img
                  src='/icons/search/history.svg'
                  onClick={() => setCollapsed(!collapsed)}
                  title={intl.formatMessage({ id: 'pages.search.history', defaultMessage: '搜索', })}
                  style={{
                    position: 'absolute', // 绝对定位
                    top: 30, // 距离底部 16px
                    right: 12, // 超出侧边栏宽度
                    //right: -12, // 超出侧边栏宽度
                    // width: 32, // 图片宽度
                    //height: 32, // 图片高度
                    cursor: 'pointer', // 鼠标变成手型
                    zIndex: 1000,
                    //borderRadius: '10px', // 圆角
                    //boxShadow: '1px 1px 4px 0px rgba(0, 0, 0, 0.15)', // 阴影
                  }}
                />

                <img
                  src='/icons/search/search.svg'
                  onClick={() => setCollapsed(!collapsed)}
                  title={intl.formatMessage({ id: 'pages.search.searchButton', defaultMessage: '历史记录', })}
                  style={{
                    position: 'absolute', // 绝对定位
                    top: 73,
                    right: 15,
                    cursor: 'pointer', // 鼠标变成手型
                    zIndex: 1000,
                  }}
                />
              </>
            )}
            {!collapsed && (
              <div>
                <div>
                  <img src='/icons/search/history.svg' style={{ padding: 20, }} />
                  <label
                    style={{
                      color: 'rgb(0, 0, 0)', // 字体颜色
                      fontSize: '16px', // 字体大小
                      fontWeight: 400, // 字体粗细
                      lineHeight: '23px', // 行高
                      letterSpacing: '0px', // 字间距
                      textAlign: 'left', // 左对齐
                    }}
                  >
                    {intl.formatMessage({ id: 'pages.search.history', defaultMessage: '历史记录', })}
                  </label>
                </div>
                <div>
                  <div style={{ padding: 16 }}>
                    {/* 搜索输入框 */}
                    <Input
                      placeholder={intl.formatMessage({ id: 'pages.search.searchHistory', defaultMessage: "搜索历史记录", })}
                      value={historySearchValue}
                      onChange={(e) => setHistorySearchValue(e.target.value)}
                      onPressEnter={(e) => {
                        // console.log('回车触发', e);
                        handleHistorySearch(historySearchValue);
                      }}
                      data-type="history-search"
                      suffix={
                        <SearchOutlined
                          style={{ cursor: 'pointer' }}
                          onClick={() => handleHistorySearch(historySearchValue)}
                        />
                      }
                    />


                    <div style={{ marginTop: 16 }}>
                      {history.map((item, index) => {
                        // 根据索引查找是否超出
                        const isOverflow = overflowItems.find((o) => o.index === index)?.overflow || false;

                        return (
                          <div
                            key={index}
                            style={{
                              padding: "0px 0px 0px 6px",
                              cursor: "pointer",
                              transition: "background 0.3s",
                              borderRadius: "5px",
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              position: "relative",
                              height: '40px',
                            }}
                            onMouseEnter={(e) => {
                              setHoverIndex(index);
                              e.currentTarget.style.backgroundColor = "rgba(11, 211, 87, 0.1)";
                            }}
                            onMouseLeave={(e) => {
                              setHoverIndex(null);
                              e.currentTarget.style.backgroundColor = "transparent";
                            }}
                          >
                            {/* 文字内容 - 渐变隐藏 */}
                            <span
                              ref={(el) => (spanRefs.current[index] = el)} // 设置 ref
                              onClick={() => handleHistoryItemDoubleClick(item.value)}
                              style={{
                                display: "inline-block",
                                maxWidth: "175px",
                                //maxWidth: "20%", // 限制最大宽度
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                fontSize: "14px",
                                color: "rgb(136, 136, 136)",
                                fontWeight: "400",
                                lineHeight: "23px",
                                letterSpacing: "0px",
                                // 只有当文本超出时才使用 maskImage
                                maskImage: isOverflow ? "linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0))" : "none",
                                WebkitMaskImage: isOverflow ? "linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0))" : "none",
                              }}
                            >
                              {item.value}
                            </span>

                            {/* 操作按钮区域（始终占位，hover 显示） */}
                            <div
                              className="action-container"
                              style={{
                                width: "32px", // 固定宽度，防止跳动
                                height: "32px",
                                position: "relative",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              {/* 悬浮时显示 More 按钮 */}
                              {hoverIndex === index && (
                                <div
                                  onClick={() => setShowActions(showActions === index ? null : index)}
                                  onMouseEnter={(e) => (e.currentTarget.style.background = "rgba(255, 255, 255, 0.85)")}
                                  onMouseLeave={(e) => (e.currentTarget.style.background = "transparent")}
                                  style={{
                                    width: "24px",
                                    height: "24px",
                                    borderRadius: "4px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    cursor: "pointer",
                                    background: "transparent", // 初始透明
                                    transition: "background 0.2s ease-in-out",
                                  }}
                                >
                                  <img
                                    src="/icons/search/ic_more.svg"
                                    style={{
                                      width: "16px",
                                      height: "16px",
                                      opacity: hoverIndex === index ? 1 : 0.7, // 默认轻微可见
                                      visibility: hoverIndex === index ? "visible" : "hidden",
                                      transition: "opacity 0.2s ease-in-out, visibility 0.2s ease-in-out",
                                    }}
                                  />
                                </div>
                              )}

                              {/* 编辑 & 删除按钮（点击 More 按钮后显示） */}
                              <div
                                className="action-buttons"
                                style={{
                                  position: "absolute",
                                  top: "100%",
                                  left: "0",
                                  background: "#fff",
                                  borderRadius: "5px",
                                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                                  padding: "4px",
                                  zIndex: 10,
                                  flexDirection: "column",
                                  display: "flex",
                                  opacity: showActions === index ? 1 : 0, // 使用透明度
                                  visibility: showActions === index ? "visible" : "hidden",
                                  transition: "opacity 0.2s ease-in-out, visibility 0.2s",
                                  height: "40px", // 统一高度
                                  width: "96px", // 统一宽度
                                  justifyContent: "center", // 让按钮垂直居中
                                  alignItems: "center", // 让按钮水平居中
                                }}
                                onMouseEnter={() => setShowActions(index)}
                                onMouseLeave={() => setShowActions(null)}
                              >
                                {/* <button
                              style={{
                                border: "none",
                                background: 'transparent',
                                padding: "4px 8px",
                                borderRadius: "4px",
                                cursor: "pointer",
                                display: "flex",
                                alignItems: "center",
                                width: "88px",
                                height: "32px",
                                justifyContent: "center",
                                //ransition: "background 0.2s ease-in-out", // 平滑过渡效果
                              }}
                              onMouseEnter={(e) => (e.currentTarget.style.background = "rgba(11, 211, 87, 0.1)")}
                              onMouseLeave={(e) => (e.currentTarget.style.background = "transparent")}
                              onClick={() => handleHistoryEdit(item)}
                            >
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  width: "70px",
                                  height: "20px",
                                }}
                              >
                                <img
                                  src="/icons/pointManage/img_edit.svg"
                                  style={{
                                    width: "14px",
                                    height: "14px",
                                    fontSize: '16px',
                                  }}
                                />
                                <span style={{
                                  paddingLeft: "10px",
                                  color: ' rgb(69, 69, 69)',
                                  fontWeight: '400',
                                  lineHeight: '20px',
                                }}>重命名</span>
                              </div>
                            </button> */}

                                <button
                                  style={{
                                    paddingTop: '5px',
                                    border: "none",
                                    background: 'transparent',
                                    padding: "4px 8px",
                                    borderRadius: "4px",
                                    cursor: "pointer",
                                    display: "flex",
                                    alignItems: "center",
                                    width: "88px",
                                    height: "32px",
                                    justifyContent: "center",
                                    transition: "background 0.2s ease-in-out", // 平滑过渡效果
                                  }}
                                  onMouseEnter={(e) => (e.currentTarget.style.background = "rgb(253, 226, 226)")}
                                  onMouseLeave={(e) => (e.currentTarget.style.background = "transparent")}
                                  onClick={() => handleHistoryDel(item, index)}
                                >
                                  <div
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      width: "70px",
                                      height: "20px",
                                    }}
                                  >
                                    <img
                                      src="/icons/pointManage/img_del.svg"
                                      style={{
                                        width: "14px",
                                        height: "14px",
                                        filter: "invert(19%) sepia(95%) saturate(7473%) hue-rotate(350deg) brightness(96%) contrast(103%)",
                                      }}
                                    />
                                    <span style={{
                                      paddingLeft: "10px",
                                      color: 'rgb(246, 59, 66)',
                                      fontWeight: '400',
                                      lineHeight: '20px',
                                    }}>{intl.formatMessage({ id: 'pages.search.delete', defaultMessage: '删除', })}</span>
                                  </div>
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div />
        )}
        {/* 父容器 */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            width: showImageSearch ? '100%' : (collapsed ? '97.5vw' : '87vw'),
          }}
        >
          {showImageSearch ? (
            <ImageSearchPage
              uploadedImages={trajectoryImage.length > 0 ? trajectoryImage : uploadedImages}
              fetchImage={fetchImage}
              onImageClick={handleImageClick}
              onBack={() => {
                setShowImageSearch(false);
              }}
            />
          ) : (
            <div
              style={{
                height: searchStatus ? '25%' : '100%',
                display: 'flex', // 使用 flex 布局
                justifyContent: 'center', // 水平居中
                //width: '100vw',
                // backgroundImage: searchStatus
                //   ? "url('/icons/search/search_bg_a.png')" : "",
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',

              }}
            >
              <div
                style={{
                  flex: 2,
                  maxWidth: '87vw', // 设置最大宽度
                  padding: searchStatus
                    ? "24px"
                    : "15% 24px 24px 24px",
                }}
              >
                {/* 居中容器 - 包含热门搜索和搜索框 */}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center', // 水平居中
                    width: '100%',
                  }}
                >
                  {/* 内容容器 - 固定宽度，内部左对齐 */}
                  <div
                    style={{
                      maxWidth: '780px', // 最大宽度限制
                      width: '100%', // 自适应宽度
                      margin: '0 auto', // 居中
                    }}
                  >
                    {/* 标题 */}
                    {!searchStatus && (
                      <h2
                        style={{
                          fontSize: '20px',
                          fontWeight: 'bold',
                          color: '#000',
                          marginBottom: '16px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                        }}
                      >
                        <img
                          src="/icons/search/hotSearch.png" // 替换为实际的图标路径
                          style={{ width: '28px', height: '28px' }}
                        />
                        {
                          intl.formatMessage({ id: 'pages.search.hotSearch', defaultMessage: '热门搜索', })
                        }
                      </h2>
                    )}

                    {/* 热门搜索标签 */}
                    {!searchStatus && (
                      <div
                        style={{
                          display: 'flex',
                          gap: '8px',
                          flexWrap: 'wrap',
                          marginBottom: '24px',
                          width: '100%', // 占满容器宽度
                          // maxHeight: '60px',
                          // overflow: 'auto', // 添加滚动
                          // paddingBottom: '8px', // 避免内容贴底
                        }}
                      >
                        {[intl.formatMessage({ id: 'pages.search.example1', defaultMessage: '门口机动车旁边的男生', }), intl.formatMessage({ id: 'pages.search.example2', defaultMessage: '电梯间红色衣服打电话的人', }), intl.formatMessage({ id: 'pages.search.example3', defaultMessage: '停车场岗亭戴蓝色帽子的人', })].map((text, index) => (
                          <span
                            key={index}
                            onClick={() => setSmartSearchValue(text)} // 设置 smartSearchValue
                            style={{
                              padding: '6px 12px',
                              fontSize: '14px',
                              color: '#000',
                              background: 'rgb(255, 255, 255)',
                              borderRadius: '12px',
                              cursor: 'pointer',
                              marginRight: '8px', // 添加间距
                              // marginBottom: '8px', // 添加间距
                            }}
                          >
                            {text}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* 输入框和按钮 */}
                    {!searchStatus ? (
                      <>
                        <div
                          style={{
                            background: '#fff',
                            borderRadius: '18px',
                            padding: '16px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                            width: '100%', // 自适应宽度
                            maxWidth: '780px', // 最大宽度限制
                          }}
                        >
                          {/* 隐藏的文件输入 */}
                          <input
                            type="file"
                            ref={fileInputRef}
                            style={{ display: 'none' }}
                            accept="image/*"
                            onChange={handleImageUpload}
                          />

                          {!hideTextarea && (
                            <textarea
                              value={smartSearchValue} // 绑定 state
                              onChange={(e) => setSmartSearchValue(e.target.value)} // 更新 state
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                  e.preventDefault(); // 禁用回车换行
                                }
                              }}
                              placeholder={intl.formatMessage({ id: 'pages.search.assistHint', defaultMessage: '我可以帮助您搜索信息，比如：在园区一楼外卖柜旁的黑色衣服男生', })}
                              style={{
                                width: '100%', // 自适应宽度
                                border: 'none',
                                outline: 'none',
                                fontSize: '14px',
                                color: '#666',
                                padding: '8px 12px',
                                resize: 'none', // 禁用用户调整大小
                                minHeight: uploadedImages.length > 0 ? '80px' : 'auto',
                                boxSizing: 'border-box', // 包含padding在宽度计算内
                              }}
                            ></textarea>
                          )}

                          {/* 上传图片预览区域 */}
                          {uploadedImages.length > 0 && (
                            <div style={{
                              display: 'flex',
                              flexWrap: 'wrap',
                              gap: '8px',
                              marginTop: '8px',
                              padding: '0 12px',
                            }}>
                              {uploadedImages.map((image, index) => (
                                <div key={index} style={{
                                  position: 'relative',
                                  width: '60px',
                                  height: '60px',
                                  borderRadius: '4px',
                                  overflow: 'hidden',
                                }}>
                                  <img
                                    src={image}
                                    alt={`upload-${index}`}
                                    style={{
                                      width: '100%',
                                      height: '100%',
                                      objectFit: 'cover',
                                    }}
                                  />
                                  <button
                                    onClick={() => handleRemoveImage(index)}
                                    style={{
                                      position: 'absolute',
                                      top: '2px',
                                      right: '2px',
                                      width: '16px',
                                      height: '16px',
                                      borderRadius: '50%',
                                      background: 'rgba(0, 0, 0, 0.5)',
                                      color: 'white',
                                      border: 'none',
                                      cursor: 'pointer',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      fontSize: '12px',
                                    }}
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                            </div>
                          )}

                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between', // 左右布局
                              marginTop: '12px',
                            }}
                          >
                            <div>
                              <AudioRecorder ref={audioRecorderRef} handleVoiceToText={handleVoiceToText} />
                            </div>

                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                              }}
                            >

                              <img
                                src="/icons/search/upload.svg"
                                onClick={triggerFileInput}
                                style={{
                                  width: '24px',
                                  height: '24px',
                                  cursor: 'pointer',
                                  marginRight: '12px',
                                }}
                              />

                              <button
                                onClick={() => {
                                  setTrajectoryImage([]);
                                  if (uploadedImages.length > 0) {
                                    handleImageSearch(false);
                                  } else {
                                    handleSmartSearch(false);
                                  }
                                }}
                                disabled={hideTextarea ? false : (!smartSearchValue?.trim() && uploadedImages.length === 0)}
                                style={{
                                  background: (hideTextarea || smartSearchValue?.trim() || uploadedImages.length > 0)
                                    ? 'linear-gradient(221.02deg, rgb(22, 246, 107) 49.835%, rgb(86, 248, 181) 89.849%)'
                                    : '#ccc',
                                  color: '#fff',
                                  border: 'none',
                                  borderRadius: '60px',
                                  padding: '8px 16px',
                                  cursor: 'pointer',
                                  fontSize: '14px',
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <img
                                  src="/icons/search/path.svg"
                                  style={{ width: '16px', height: '16px', marginRight: '8px' }}
                                  alt="search icon"
                                />
                                {intl.formatMessage({ id: 'pages.search.searchButton', defaultMessage: '搜索' })}
                              </button>
                            </div>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 搜索框 */}
                        <div
                          style={{
                            background: '#fff',
                            borderRadius: '60px',
                            padding: '16px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                            width: '100%',
                            maxWidth: '780px',
                            margin: '0 auto',
                            position: 'relative',

                          }}
                        >
                          {/* 上传图片预览区域 */}
                          {uploadedImages.length > 0 && (
                            <div style={{
                              position: 'absolute',
                              top: '0px',
                              left: '50px',
                              flexWrap: 'wrap',
                              gap: '8px',
                              marginTop: '8px',
                              padding: '0 12px',
                              maxWidth: '100%',
                            }}>
                              {uploadedImages.map((image, index) => (
                                <div key={index} style={{
                                  position: 'relative',
                                  width: '48px',
                                  height: '48px',
                                  borderRadius: '4px',
                                  overflow: 'hidden',
                                }}>
                                  <img
                                    src={image}
                                    alt={`upload-${index}`}
                                    style={{
                                      width: '100%',
                                      height: '100%',
                                      objectFit: 'cover',
                                    }}
                                  />
                                  <button
                                    onClick={() => handleRemoveImage(index)}
                                    style={{
                                      position: 'absolute',
                                      top: '2px',
                                      right: '2px',
                                      width: '16px',
                                      height: '16px',
                                      borderRadius: '50%',
                                      background: 'rgba(0, 0, 0, 0.5)',
                                      color: 'white',
                                      border: 'none',
                                      cursor: 'pointer',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      fontSize: '12px',
                                    }}
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                            </div>
                          )}
                          {/* 隐藏的文件输入 */}
                          <input
                            type="file"
                            ref={fileInputRef}
                            style={{ display: 'none' }}
                            accept="image/*"
                            onChange={handleImageUpload}
                          />

                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                            }}
                          >
                            <AudioRecorder ref={audioRecorderRef} handleVoiceToText={handleVoiceToText} />
                            {!hideTextarea && (
                              <input
                                onChange={(e) => setSmartSearchValue(e.target.value)}
                                placeholder={intl.formatMessage({ id: 'pages.search.assistHint', defaultMessage: '我可以帮助您搜索信息，比如：在园区一楼外卖柜旁的黑色衣服男生', })}
                                style={{
                                  flex: 1, // 占满剩下的宽度
                                  border: 'none',
                                  outline: 'none',
                                  fontSize: '14px',
                                  color: '#666',
                                  padding: '8px 12px',
                                  resize: 'none',
                                }}
                                value={smartSearchValue}
                              />
                            )}

                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                              }}
                            >
                              <img
                                src="/icons/search/upload.svg"
                                onClick={triggerFileInput}
                                style={{
                                  width: '24px',
                                  height: '24px',
                                  cursor: 'pointer',
                                  marginRight: '12px',
                                }}
                              />
                              <button
                                onClick={() => {
                                  setTrajectoryImage([]);
                                  if (uploadedImages.length > 0) {
                                    handleImageSearch(false);
                                  } else {
                                    handleSmartSearch(false);
                                  }
                                }}
                                disabled={!smartSearchValue?.trim() && uploadedImages.length === 0}
                                style={{
                                  background: (!smartSearchValue?.trim() && uploadedImages.length === 0)
                                    ? '#ccc'
                                    : 'linear-gradient(221.02deg, rgb(22, 246, 107) 49.835%, rgb(86, 248, 181) 89.849%)',
                                  color: '#fff',
                                  border: 'none',
                                  borderRadius: '60px',
                                  padding: '8px 16px',
                                  cursor: 'pointer',
                                  fontSize: '14px',
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <img
                                  src="/icons/search/path.svg"
                                  style={{ width: '16px', height: '16px', marginRight: '8px' }}
                                />
                                {intl.formatMessage({ id: 'pages.search.searchButton', defaultMessage: '搜索' })}
                              </button>
                            </div>
                          </div>
                        </div>




                      </>

                    )}

                    {/* 检索条件选择框 - 移动到居中容器内 */}
                    {searchStatus && (
                      <div
                        style={{
                          marginTop: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '16px',
                          flexWrap: 'nowrap', // 不换行
                          width: '100%',
                          overflowX: 'auto' // 如果内容太宽，允许水平滚动
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flexWrap: 'nowrap', minWidth: 'max-content' }}>
                          {/* 时间和点位选择器 */}
                          <SearchConditions
                            startTime={startTime}
                            endTime={endTime}
                            onTimeChange={handleTimeChange}
                            selectedPointIds={selectedPointIds}
                            onPointChange={handlePointChange}
                            showTimeSelector={true}
                            showPointSelector={true}
                            compact={true}
                          />

                          {/* 检索条件选择框 */}
                          <div style={{ display: 'flex', alignItems: 'center', flexShrink: 0 }}>
                            <label style={{ marginRight: '6px', fontSize: '14px', color: '#666', whiteSpace: 'nowrap' }}>{intl.formatMessage({ id: 'pages.search.filter', defaultMessage: '检索条件', })}：</label>
                            <Select
                              defaultValue="time"
                              style={{
                                width: 120,
                              }}
                              options={searchConditionOptions}
                              onChange={handleConditionChange}
                            />
                          </div>

                          {/* 相似度滑块 */}
                          <div className="similarity-slider" style={{ display: 'flex', alignItems: 'center', flexShrink: 0, minWidth: '180px' }}>
                            <label style={{ marginRight: '6px', fontSize: '14px', color: '#666', whiteSpace: 'nowrap' }}>{intl.formatMessage({ id: 'pages.search.similarity', defaultMessage: '相似度', })}：</label>
                            <Slider
                              style={{ width: 100, flexShrink: 0 }}
                              min={0}
                              max={100}
                              step={1}
                              onChange={(value) => handleSimilarityChange(value / 100)}
                              value={similarityValue * 100}
                              tooltip={{ formatter: (value) => `${value}%` }}
                            />
                            <span style={{ marginLeft: '6px', minWidth: '32px', color: '#333', whiteSpace: 'nowrap', fontSize: '14px' }}>
                              {Math.round(similarityValue * 100)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                  </div>
                </div>

              </div>

            </div>
          )}

          {/* 蓝色区域 */}
          {!showImageSearch && searchStatus && (
            <Spin
              spinning={searchLoading}
              tip={intl.formatMessage({ id: 'pages.search.loading', defaultMessage: '加载中...', })}
              size="large"
              style={{
                top: '50%', // 垂直居中
                left: '50%', // 水平居中
                transform: 'translate(-50%, -50%)', // 偏移实现完美居中
                fontSize: '24px', // 提示文字的字体大小
              }}
            >
              <div
                style={{
                  height: '75vh',
                  backgroundImage: "url('/icons/search/search_bg_1.png')",
                  backgroundSize: '100% 100%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  marginBottom: '16px',
                  marginRight: '12px',
                  // border: '1px solid rgb(206, 223, 239)',
                  //  transition: 'height 0.3s',
                  borderRadius: '8px',
                }}
              >



                <div ref={containerRef} style={{
                  height: '100%', // 确保容器高度
                  // overflow: 'hidden', // 超出高度时显示滚动条
                }} >

                  <div
                    style={{
                      height: 'calc(100% - 5px)',
                      maxHeight: 'calc(100% - 5px)',
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                      gap: '20px',
                      padding: '12px',
                      overflowY: 'auto',
                      alignContent: 'start',
                    }}
                    onScroll={handleScroll}
                  >
                    {searchResults.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          height: 190,
                          background: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          overflow: 'hidden',
                          transition: 'transform 0.2s',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'scale(1)';
                        }}
                      >
                        {/* 图片区域 */}
                        <div className='search-Image' style={{ position: 'relative' }}>
                          <Image
                            src={item.thumbUrl || '/icons/search/default-noImage.png'} // 显示缩略图或默认图片
                            preview={false} // 预览原图
                            alt={item.title}
                            style={{
                              width: '100%',
                              height: 115,
                              objectFit: 'cover',
                              cursor: 'pointer',
                            }}
                            onClick={() => handleImageClick(item)}
                          />


                          {/* 轨迹图标 - 悬停时显示hover图标 */}
                          <div
                            style={{
                              position: 'absolute',
                              top: '70%',
                              right: '8px',
                              width: '34px',
                              height: '34px',
                              cursor: 'pointer',
                            }}
                            className="trajectory-icon-container"
                            onMouseEnter={(e) => {
                              const hoverIcon = e.currentTarget.querySelector('.trajectory-hover-icon') as HTMLElement;
                              const normalIcon = e.currentTarget.querySelector('.trajectory-normal-icon') as HTMLElement;
                              if (hoverIcon) hoverIcon.style.display = 'block';
                              if (normalIcon) normalIcon.style.display = 'none';
                            }}
                            onMouseLeave={(e) => {
                              const hoverIcon = e.currentTarget.querySelector('.trajectory-hover-icon') as HTMLElement;
                              const normalIcon = e.currentTarget.querySelector('.trajectory-normal-icon') as HTMLElement;
                              if (hoverIcon) hoverIcon.style.display = 'none';
                              if (normalIcon) normalIcon.style.display = 'block';
                            }}
                            onClick={async (e) => {
                              e.stopPropagation();
                              console.log('点击了轨迹图标，项目ID:', item.thumbUrl);
                              let imageUrl = await fetchImage(item.name, "snap");
                              setTrajectoryImage([imageUrl]);
                              handleImageSearch(false)
                            }}
                          >
                            <img
                              className="trajectory-hover-icon"
                              src="/icons/search/trajectory_hover.svg"
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '34px',
                                height: '34px',
                                display: 'none',
                              }}
                              title="查看轨迹详情"
                              alt="轨迹悬停"
                            />
                            <img
                              className="trajectory-normal-icon"
                              src="/icons/search/trajectory.svg"
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '34px',
                                height: '34px',
                              }}
                              title="查看轨迹"
                              alt="轨迹"
                            />
                          </div>
                        </div>

                        {/* 文字信息区域 */}
                        <div style={{ padding: '5px 12px 12px 12px' }}>
                          <p style={infoTextStyle}>
                            <img src="/icons/search/date.svg" style={iconStyle} />
                            {item.time}
                          </p>
                          <p style={infoTextStyle}>
                            <img src="/icons/search/local.svg" style={iconStyle} />
                            {item.address}
                          </p>
                          <p style={infoTextStyle}>
                            <img src="/icons/search/score.svg" style={iconStyle} />
                            {item.score}
                          </p>
                        </div>
                      </div>
                    ))}

                    {/* 加载更多提示 - 只在有更多数据且正在加载时显示 */}
                    {pageLoading && hasMore && (
                      <div style={{
                        gridColumn: '1 / -1',
                        textAlign: 'center',
                        padding: '20px',
                        color: '#666',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px'
                      }}>
                        <div style={{
                          width: '16px',
                          height: '16px',
                          border: '2px solid #e6e6e6',
                          borderTop: '2px solid #0BD357',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite'
                        }}></div>
                        正在加载更多...
                      </div>
                    )}

                    {/* 没有更多数据提示 - 简洁优雅样式 */}
                    {!hasMore && searchResults.length > 0 && (
                      <div style={{
                        gridColumn: '1 / -1',
                        textAlign: 'center',
                        padding: '16px',
                        margin: '12px 0',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px',
                        color: '#8c8c8c',
                        fontSize: '14px',
                        background: 'transparent'
                      }}>
                        <div style={{
                          width: '4px',
                          height: '4px',
                          borderRadius: '50%',
                          background: '#d9d9d9',
                          marginRight: '4px'
                        }}></div>
                        已加载全部数据 {searchResults.length} 条
                      </div>
                    )}
                  </div>

                </div>

              </div>
            </Spin>
          )
          }
        </div >
        <EventDetailModal
          isEventInfoVisible={isEventInfoVisible}
          handleRecordClose={handleRecordClose}
          videoSrc={videoSrc}
          eventDetails={eventDetails}
          smartSearchValue={smartSearchValue}
          visibleThumbnails={eventDetails?.targetImages || []}
          thumbnailWidth={72}
        />
      </div>
    </>
  );
};

export default Home;
