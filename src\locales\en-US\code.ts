export default {
    'pages.serverCode.0': "Success",
    'pages.serverCode.-1': "Business code execution failed",
    'pages.serverCode.-100': "Authentication failed",
    'pages.serverCode.-200': "Sql execution failed",
    'pages.serverCode.-300': "Illegal parameter",
    'pages.serverCode.-400': "Code throw exception",
    'pages.serverCode.-500': "Not found",
    'pages.serverCode.-10000': "Illegal parameter",
    'pages.serverCode.-10001': "Operation failed",
    'pages.serverCode.-10002': "Query timeout",
    'pages.serverCode.-10003': "Response data error",
    'pages.serverCode.-10004': "The required parameter is missing",
    'pages.serverCode.-10005': "The parameter type is incorrect",
    'pages.serverCode.-10006': "The parameter value is invalid",
    'pages.serverCode.-10007': "Operation failed, please check if the device supports this feature",
    'pages.serverCode.-10008': "Starting packet capture failed",
    'pages.serverCode.-10009': "Stop packet capture failed",
    'pages.serverCode.-10010': "Other terminals are upgrading or restoring data, please try again later",
    'pages.serverCode.-10011': "File suffix mismatch, please check if the file is correct",
    'pages.serverCode.-10012': "Subcontracting is too small",
    'pages.serverCode.-10013': "The file content is incorrect. Please check if the file is correct",
    'pages.serverCode.-10014': "Memory allocation failed",
    'pages.serverCode.-10015': "Upload data error",
    'pages.serverCode.-10016': "File verification failed",
    'pages.serverCode.-10017': "File save failed",
    'pages.serverCode.-10018': "Upgrade failed",
    'pages.serverCode.-10019': "The log folder does not exist",
    'pages.serverCode.-10020': "Failed to execute compression command",
    'pages.serverCode.-10021': "Restart failed",
    'pages.serverCode.-10022': "Backup data failed",
    'pages.serverCode.-10023': "Database verification failed",
    'pages.serverCode.-10024': "Data restoration failed",
    'pages.serverCode.-10025': "File key verification failed",
    'pages.serverCode.-10026': "The upgrade function is not supported in this operating mode. Please contact the administrator",
    'pages.serverCode.-11100': "User not initialized",
    'pages.serverCode.-11101': "The user password cannot be empty",
    'pages.serverCode.-11102': "Incorrect user or password",
    'pages.serverCode.-11103': "The user name cannot be empty",
    'pages.serverCode.-11104': "User does not exist",
    'pages.serverCode.-11105': "Incorrect password",
    'pages.serverCode.-11106': "No operation permissions, please contact the administrator",
    'pages.serverCode.-12000': "Device already exists",
    'pages.serverCode.-12001': "Device does not exist",
    'pages.serverCode.-12002': "Device name already exists",
    'pages.serverCode.-12003': "Device offline",
    'pages.serverCode.-12004': "Device code already exists",
    'pages.serverCode.-12005': "Username already exists",
    'pages.serverCode.-13000': "Channel offline",
    'pages.serverCode.-14001': "Video stream already exists",
    'pages.serverCode.-14002': "Video stream already exists, playback failed",
    'pages.serverCode.-14003': "Unknown manufacturer, please change the manufacturer",
    'pages.serverCode.-14004': "Failed to obtain Rtsp address",
    'pages.serverCode.-14005': "Rtsp address is illegal",
    'pages.serverCode.-14006': "Unknown code stream type",
    'pages.serverCode.-14007': "The Rtsp address corresponding to the code stream type is not added",
    'pages.serverCode.-14008': "Streaming offline",
    'pages.serverCode.-14009': "Media information not obtained",
    'pages.serverCode.-14010': "No stream added",
    'pages.serverCode.-14011': "Waiting for playback",
    'pages.serverCode.-14012': "Playback failed",
    'pages.serverCode.-14013': "Does not support playback function",
    'pages.serverCode.-15000': "License file error, please confirm whether to import the license file.",
    'pages.serverCode.-15001': "License verification failed, please confirm whether the correct license file has been imported.",
    'pages.serverCode.-15002': "The license does not include this functional module.",
    'pages.serverCode.-15003': "The number of channels exceeds the license point limit.",
    'pages.serverCode.-15004': "Channel license expired.",
    'pages.serverCode.-15005': "Pull stream proxy exceeds the permitted number of routes.",
    'pages.serverCode.-15006': "Pull stream proxy license expired.",
    'pages.serverCode.-15007': "Push stream proxy exceeds the permitted number of routes.",
    'pages.serverCode.-15008': "Push stream proxy license expired.",
    'pages.serverCode.-15009': "The number of extensions exceeds the license point limit.",
    'pages.serverCode.-15010': "Extension license expired.",
    'pages.serverCode.-16001': "Meetings may not be smaller than 2 persons.",
    'pages.serverCode.-17001': "The audio file already exists.",
    'pages.serverCode.-17002': "Failed to save the audio file.",
    'pages.serverCode.-17003': "The audio file transcoding failed.",
    'pages.serverCode.-17004': "Extension not registered",
    'pages.serverCode.-17005': "Extension short number and username duplicate",
    'pages.serverCode.-17006': "Cannot delete built-in ringtones",
    'pages.serverCode.-17007': "This number is in use by an extension.",
    'pages.serverCode.-17008': "This number is in use by a voicemail group.",
    'pages.serverCode.-17009': "This number is in use by a ring group.",
    'pages.serverCode.-17010': "This number is in use by an IVR.",
    'pages.serverCode.-17011': "This number is in use by a call queue.",
    'pages.serverCode.-17012': "Ringtone name cannot be empty.",
    'pages.serverCode.-20001': "Connection to the database failed or other errors occurred",
    'pages.serverCode.-21000': "Device",
    'pages.serverCode.-21001': "Device general error",
    'pages.serverCode.-21005': "The device database file is being used by another process",
    'pages.serverCode.-21006': "Device database file lock failed",
    'pages.serverCode.-21009': "Device operation interrupted",
    'pages.serverCode.-21010': "Device input/output error",
    'pages.serverCode.-21011': "The device database file is corrupt",
    'pages.serverCode.-21013': "The device database disk space is full",
    'pages.serverCode.-21019': "Device already exists",
    'pages.serverCode.-22000': "Channel",
    'pages.serverCode.-22001': "Channel general error",
    'pages.serverCode.-22005': "The channel database file is being used by other processes",
    'pages.serverCode.-22006': "Channel database file lock failed",
    'pages.serverCode.-22009': "Channel operation interrupted",
    'pages.serverCode.-22010': "Channel input/output error",
    'pages.serverCode.-22011': "The channel database file is corrupt",
    'pages.serverCode.-22013': "The channel database disk space is full",
    'pages.serverCode.-22019': "Channel already exists",
    'pages.serverCode.-23000': "Grouping",
    'pages.serverCode.-23001': "Grouping Common Error",
    'pages.serverCode.-23005': "Grouping database files used by other processes",
    'pages.serverCode.-23006': "Grouping database file lock failed",
    'pages.serverCode.-23009': "Grouping operation interrupted",
    'pages.serverCode.-23010': "Group input/output error",
    'pages.serverCode.-23011': "The grouping database file is corrupt",
    'pages.serverCode.-23013': "The disk space of the grouping database is full",
    'pages.serverCode.-23019': "Group already exists",
    'pages.serverCode.-24000': "Grouping channel",
    'pages.serverCode.-24001': "Grouping channel general error",
    'pages.serverCode.-24005': "Grouping channel database file is being used by other processes",
    'pages.serverCode.-24006': "Grouping channel database file lock failed",
    'pages.serverCode.-24009': "Grouping channel operation interrupted",
    'pages.serverCode.-24010': "Grouping channel input/output error",
    'pages.serverCode.-24011': "The grouping channel database file is corrupt",
    'pages.serverCode.-24013': "The disk space of the grouping channel database is full",
    'pages.serverCode.-24019': "Grouping channel already exists",
    'pages.serverCode.-25000': "Stream",
    'pages.serverCode.-25001': "Stream error",
    'pages.serverCode.-25005': "Stream database file is being used by other processes",
    'pages.serverCode.-25006': "Stream database file lock failed",
    'pages.serverCode.-25009': "Stream operation interrupted",
    'pages.serverCode.-25010': "Stream input/output error",
    'pages.serverCode.-25011': "The streaming database file is corrupt",
    'pages.serverCode.-25013': "The disk space of the streaming database is full",
    'pages.serverCode.-25019': "Stream already exists",
    'pages.serverCode.-26000': "User",
    'pages.serverCode.-26001': "User general error",
    'pages.serverCode.-26005': "User database files are being used by other processes",
    'pages.serverCode.-26006': "User database file lock failed",
    'pages.serverCode.-26009': "User operation interrupted",
    'pages.serverCode.-26010': "User input/output error",
    'pages.serverCode.-26011': "The user database file is corrupt",
    'pages.serverCode.-26013': "The user database disk space is full",
    'pages.serverCode.-26019': "User already exists",
    'pages.serverCode.-27000': "Vendor",
    'pages.serverCode.-27001': "Vendor general error",
    'pages.serverCode.-27005': "The vendor database file is being used by other processes",
    'pages.serverCode.-27006': "Failed to lock the vendor database file",
    'pages.serverCode.-27009': "Vendor operation interrupted",
    'pages.serverCode.-27010': "Vendor input/output error",
    'pages.serverCode.-27011': "The vendor database file is corrupt",
    'pages.serverCode.-27013': "The vendor database disk space is full",
    'pages.serverCode.-27019': "The vendor alredy exists",
    'pages.serverCode.-28000': "Extension",
    'pages.serverCode.-28001': "Extension general error",
    'pages.serverCode.-28005': "The extension database file is being used by other processes",
    'pages.serverCode.-28006': "Failed to lock the extension database file",
    'pages.serverCode.-28009': "Extension operation interrupted",
    'pages.serverCode.-28010': "Extension input/output error",
    'pages.serverCode.-28011': "The extension database file is corrupt",
    'pages.serverCode.-28013': "The extension database disk space is full",
    'pages.serverCode.-28019': "The extension alredy exists",
    'pages.serverCode.-29000': "Extension group",
    'pages.serverCode.-29001': "Extension group general error",
    'pages.serverCode.-29005': "The extension group database file is being used by other processes",
    'pages.serverCode.-29006': "Failed to lock the extension group database file",
    'pages.serverCode.-29009': "Extension group operation interrupted",
    'pages.serverCode.-29010': "Extension group input/output error",
    'pages.serverCode.-29011': "The extension group database file is corrupt",
    'pages.serverCode.-29013': "The extension group database disk space is full",
    'pages.serverCode.-29019': "The extension group alredy exists",
    'pages.serverCode.-30000': "Grouping extension",
    'pages.serverCode.-30001': "Grouping extension general error",
    'pages.serverCode.-30005': "The grouping extension database file is being used by other processes",
    'pages.serverCode.-30006': "Failed to lock the  grouping extension database file",
    'pages.serverCode.-30009': "Grouping extension operation interrupted",
    'pages.serverCode.-30010': "Grouping extension group input/output error",
    'pages.serverCode.-30011': "The grouping extension database file is corrupt",
    'pages.serverCode.-30013': "The grouping extension database disk space is full",
    'pages.serverCode.-30019': "The grouping extension alredy exists",
    'pages.serverCode.-31000': "Call forwarding",
    'pages.serverCode.-31001': "Call forwarding general error",
    'pages.serverCode.-31005': "The call forwarding database file is being used by other processes",
    'pages.serverCode.-31006': "Failed to lock the  call forwarding database file",
    'pages.serverCode.-31009': "Call forwarding operation interrupted",
    'pages.serverCode.-31010': "Call forwarding group input/output error",
    'pages.serverCode.-31011': "The call forwarding database file is corrupt",
    'pages.serverCode.-31013': "The call forwarding database disk space is full",
    'pages.serverCode.-31019': "The call forwarding alredy exists",
    'pages.serverCode.-32000': "Global Setting",
    'pages.serverCode.-32001': "Global Setting general error",
    'pages.serverCode.-32005': "The global Setting database file is being used by other processes",
    'pages.serverCode.-32006': "Failed to lock the  global Setting database file",
    'pages.serverCode.-32009': "Global Setting operation interrupted",
    'pages.serverCode.-32010': "Global Setting group input/output error",
    'pages.serverCode.-32011': "The global Setting database file is corrupt",
    'pages.serverCode.-32013': "The global Setting database disk space is full",
    'pages.serverCode.-32019': "The global Setting alredy exists",
    'pages.serverCode.-33000': "Ringtone",
    'pages.serverCode.-33001': "Ringtone general error",
    'pages.serverCode.-33005': "The ringtone database file is being used by other processes",
    'pages.serverCode.-33006': "Failed to lock the  ringtone database file",
    'pages.serverCode.-33009': "Ringtone operation interrupted",
    'pages.serverCode.-33010': "Ringtone group input/output error",
    'pages.serverCode.-33011': "The ringtone database file is corrupt",
    'pages.serverCode.-33013': "The ringtone database disk space is full",
    'pages.serverCode.-33019': "The ringtone alredy exists",
    'pages.serverCode.-34000': "Call log",
    'pages.serverCode.-34001': "Call log general error",
    'pages.serverCode.-34005': "The call log database file is being used by other processes",
    'pages.serverCode.-34006': "Failed to lock the  call log database file",
    'pages.serverCode.-34009': "Call log operation interrupted",
    'pages.serverCode.-34010': "Call log group input/output error",
    'pages.serverCode.-34011': "The call log database file is corrupt",
    'pages.serverCode.-34013': "The call log database disk space is full",
    'pages.serverCode.-34019': "The call log alredy exists",
    'pages.serverCode.-35000': "Voice mail",
    'pages.serverCode.-35001': "Voice mail general error",
    'pages.serverCode.-35005': "The voice mail database file is being used by other processes",
    'pages.serverCode.-35006': "Failed to lock the  voice mail database file",
    'pages.serverCode.-35009': "Voice mail operation interrupted",
    'pages.serverCode.-35010': "Voice mail group input/output error",
    'pages.serverCode.-35011': "The voice mail database file is corrupt",
    'pages.serverCode.-35013': "The voice mail database disk space is full",
    'pages.serverCode.-35019': "The voice mail alredy exists",
    'pages.serverCode.-36000': "Ring group",
    'pages.serverCode.-36001': "Ring group Common Errors",
    'pages.serverCode.-36005': "The ring group database file is being used by another process",
    'pages.serverCode.-36006': "Ring group database file lock failed",
    'pages.serverCode.-36009': "Ring group operation interrupted",
    'pages.serverCode.-36010': "Ring group input/output error",
    'pages.serverCode.-36011': "Ring group database file corrupted",
    'pages.serverCode.-36013': "Ring group database disk space full",
    'pages.serverCode.-36019': "Ring group already exists",
    'pages.serverCode.-37000': "IVR",
    'pages.serverCode.-37001': "IVR General Error",
    'pages.serverCode.-37005': "IVR Database File Used by Another Process",
    'pages.serverCode.-37006': "IVR Database File Lock Failed",
    'pages.serverCode.-37009': "IVR Operation Interrupted",
    'pages.serverCode.-37010': "IVR Input/Output Error",
    'pages.serverCode.-37011': "IVR Database File Corrupted",
    'pages.serverCode.-37013': "IVR Database Disk Space Full",
    'pages.serverCode.-37019': "IVR Already Exists",
    'pages.serverCode.-50415': "Called without disturbing",
    'pages.serverCode.-50414': "Called busy",
    'pages.serverCode.-50413': "Called unavailable",
    'pages.serverCode.-50412': "Called disabled",
    'pages.serverCode.-50411': "Unsupported extension type for the called party",
    'pages.serverCode.-50410': "Calling without disturbance",
    'pages.serverCode.-50409': "The caller is busy",
    'pages.serverCode.-50408': "The caller is unavailable",
    'pages.serverCode.-50407': "Calling disabled",
    'pages.serverCode.-50406': "Extension type not supported by the caller",
    'pages.serverCode.-50405': "Called does not exist",
    'pages.serverCode.-50404': "The caller does not exist",
    'pages.serverCode.-50403': "Password verification failed",
    'pages.serverCode.-301000': "When adding the streaming agent, the pre-recording time was not set",
    'pages.serverCode.-301001': "The capture time exceeded the pre-recorded time",
    'pages.serverCode.-301002': "The capture time exceeds the video buffering time",
    'pages.serverCode.-301003': "Video frame data not received",
    'pages.serverCode.-301004': "Decoding failed",
    'pages.serverCode.-301005': "Encoding failed",
    'pages.serverCode.-301006': "Capture failed",
    'pages.serverCode.-301007': "Failed to open snapshot file",
    'pages.serverCode.-302001': "Invalid capture time, please set a value less than or equal to 0 and less than the pre-recorded time",
    'pages.serverCode.-302002': "Invalid image width, please set a value greater than 0",
    'pages.serverCode.-302003': "Invalid file format, please change the file extension to .mp4",
    'pages.serverCode.1001': "Parameter cannot be empty",
    'pages.serverCode.1002': "Password decryption failed",
    'pages.serverCode.1003': "The new password does not match the confirmed password",
    'pages.serverCode.1004': "The old password is incorrect",
    'pages.serverCode.1005': "Username or password cannot be empty",
    'pages.serverCode.1006': "The user already exists",
    'pages.serverCode.M0000002': "The search keywords cannot be less than 5 words!",
}
