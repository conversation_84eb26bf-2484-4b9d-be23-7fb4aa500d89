export default {
    'pages.login.success': "ログインに成功しました！",
    'pages.login.failure': "ログインに失敗しました！",
    'pages.login.retry': "ログインに失敗しました！",
    'pages.login.title': "ログインページ",
    'pages.login.welcome': "マーズワイズへようこそ",
    'pages.login.agreementPrefix': "マーズワイズの利用規約を読み、同意しました。",
    'pages.login.terms': "利用規約",
    'pages.login.and': " および",
    'pages.login.privacy': "プライバシーポリシー",
    'pages.login.submit': "ユーザーログイン",
    'pages.login.usernamePlaceholder': "ユーザー名を入力してください",
    'pages.login.passwordPlaceholder': "パスワードを入力してください",
    'pages.search.sortByTime': "最新時刻順",
    'pages.search.sortBySimilarity': "類似度順",
    'pages.search.history': "履歴",
    'pages.search.searchHistory': "検索履歴",
    'pages.search.delete': "削除",
    'pages.search.hotSearch': "人気検索",
    'pages.search.example1': "ドアの前の車の横にいる少年",
    'pages.search.example2': "エレベーターの中で電話をしている赤い服を着た人",
    'pages.search.example3': "駐車場で青い帽子をかぶっている人",
    'pages.search.assistHint': "公園1階のテイクアウト棚の隣にいる黒い服を着た少年など、情報の検索をお手伝いします。",
    'pages.search.searchButton': "検索",
    'pages.search.filter': "検索条件",
    'pages.search.loading': "読み込み中…",
    'pages.search.eventDetail': "イベントの詳細",
    'pages.search.videoPlayback': "動画再生",
    'pages.search.panorama': "パノラマ",
    'pages.search.description': "説明",
    'pages.search.time': "時間",
    'pages.search.location': "場所",
    'pages.search.similarity': "類似度",
    'pages.pointManage.voiceInput': "音声入力",
    'pages.pointManage.speechRecognition': "音声認識",
    'pages.pointManage.stopVoiceInput': "音声入力を停止",
    'pages.pointManage.operationSuccess': "操作成功",
    'pages.pointManage.preview': "プレビュー",
    'pages.pointManage.edit': "編集",
    'pages.pointManage.alreadyMonitored': "コントロールを展開しました",
    'pages.pointManage.monitor': "コントロールを展開しました",
    'pages.pointManage.delete': "削除",
    'pages.pointManage.deleteRecord': "記録を削除",
    'pages.pointManage.confirmDeleteRecord': "記録を削除してもよろしいですか？",
    'pages.pointManage.confirm': "確認",
    'pages.pointManage.cancel': "キャンセル",
    'pages.pointManage.deleting': "削除中",
    'pages.pointManage.deleteSuccess': "削除に成功しました。自動更新されます",
    'pages.pointManage.deleteFailure': "削除に失敗しました。もう一度お試しください",
    'pages.pointManage.person': "人",
    'pages.pointManage.motorVehicle': "自動車",
    'pages.pointManage.nonMotorVehicle': "非自動車",
    'pages.pointManage.pointName': "ポイント名",
    'pages.pointManage.protocolType': "プロトコルの種類",
    'pages.pointManage.captureType': "スナップショットの種類",
    'pages.pointManage.captureInterval': "スナップショット間隔",
    'pages.pointManage.deviceName': "デバイス名",
    'pages.pointManage.deviceIP': "デバイスのIPアドレス",
    'pages.pointManage.devicePort': "デバイスのポート番号",
    'pages.pointManage.operation': "操作",
    'pages.pointManage.pointManagement': "ポイント管理",
    'pages.pointManage.add': "追加",
    'pages.pointManage.selected': "選択済み",
    'pages.pointManage.batchDelete': "一括削除",
    'pages.pointManage.item': "項目",
    'pages.pointManage.inputCaptureInterval': "スナップショット間隔を入力してください",
    'pages.pointManage.inputPointName': "ポイント名を入力してください",
    'pages.pointManage.selectCaptureType': "キャプチャタイプを選択してください",
    'pages.pointManage.addDevice': "デバイスを追加",
    'pages.pointManage.protocol': "プロトコル",
    'pages.pointManage.deviceCode': "デバイスコード",
    'pages.pointManage.inputDeviceName': "デバイス名を入力してください",
    'pages.pointManage.inputDeviceCode': "デバイスコードを入力してください",
    'pages.pointManage.port': "ポート",
    'pages.pointManage.username': "ユーザー名",
    'pages.pointManage.password': "パスワード",
    'pages.pointManage.mainStream': "メインストリーム",
    'pages.pointManage.example': "例",
    'pages.pointManage.subStream': "サブストリーム",
    'pages.pointManage.addPoint': "ポイントを追加",
    'pages.pointManage.validIP': "有効なIPアドレスを入力してください",
    'pages.pointManage.noData': "データがありません",
    'pages.pointManage.selectPoint': "ポイントを選択してください",
    'pages.pointManage.addSuccess': "正常に追加されました",
    'pages.pointManage.prevStep': "前のステップ",
    'pages.pointManage.nextStep': "次のステップ",
    'pages.pointManage.monitorSuccess': "コントロールの展開に成功しました",
    'pages.pointManage.monitorFailure': "コントロールの展開に失敗しました",
    'pages.pointManage.cancelSuccess': "キャンセルに成功しました",
    'pages.pointManage.operationFailure': "操作に失敗しました",
    'pages.pointManage.monitor': "コントロールの展開",
    'pages.pointManage.monitorEvent': "コントロールの展開イベント",
    'pages.pointManage.startMonitor': "コントロールの展開を開始",
    'pages.pointManage.monitorRecord': "コントロールの展開記録",
    'pages.pointManage.noEventRecord': "イベント記録なし",
    'pages.pointManage.charLimitExceeded': "文字数が制限を超えました。最大長さ：",
    'pages.pointManage.eventType': "イベントタイプ",
    'pages.pointManage.prompt': "プロンプト",
    'pages.pointManage.createTime': "作成時間",
    'pages.pointManage.editSuccess': "編集が成功しました",
    'pages.pointManage.monitorRule': "監視ルール",
    'pages.pointManage.selectEventType': "イベントタイプを選択してください",
    'pages.pointManage.inputPrompt': "プロンプトを入力してください",
    'pages.pointManage.eventRecord': "イベント記録",
    'pages.pointManage.paginationInfo': "{total} 件中 {range0} ～ {range1} 件",
    'pages.pointManage.detail': "詳細",
    'pages.pointManage.eventImage': "イベント画像",
    'pages.pointManage.pointInfo': "ポイント情報",
    'pages.pointManage.eventTime': "イベント時間",
    'pages.config.name': "名前",
    'pages.config.creationDate': "作成日",
    'pages.config.expirationTime': "有効期限",
    'pages.config.editApiKey': "APIキーを編集",
    'pages.config.editSuccess': "編集成功、自動更新",
    'pages.config.createSuccess': "作成成功、自動更新",
    'pages.config.editFailure': "編集失敗、再試行してください",
    'pages.config.createFailure': "作成失敗、再試行してください",
    'pages.config.createApiKey': "APIキーを作成",
    'pages.config.authManagement': "認可管理",
    'pages.config.eventCode': "イベントコード",
    'pages.config.paramConfigFailure': "パラメータ設定の取得に失敗しました",
    'pages.config.saveFailure': "保存に失敗しました",
    'pages.config.saveSuccess': "保存に成功しました",
    'pages.config.save': "保存",
    'pages.config.reset': "リセット",
    'pages.config.streamConfig': "ストリーミング設定",
    'pages.config.streamServiceUrl': "ストリーミングサービスのアドレス",
    'pages.config.secretKey': "秘密鍵",
    'pages.config.configuration': "設定",
    'pages.config.workspaceId': "ワークスペースID",
    'pages.config.multiSearchStrategy': "多次元検索戦略",
    'pages.config.dataCleanStrategy': "データクリーニング戦略",
    'pages.config.objectDetection': "オブジェクト検出",
    'pages.config.enableObjectDetection': "オブジェクト検出を有効にしますか？",
    'pages.config.allowObjectWhitelist': "オブジェクト検出ホワイトリストを許可",
    'pages.config.sedan': "セダン",
    'pages.config.bus': "バス",
    'pages.config.truck': "トラック",
    'pages.config.bicycle': "自転車",
    'pages.config.motorcycle': "オートバイ",
    'pages.config.enableImageDupCheck': "画像の重複検出を有効にしますか？",
    'pages.config.intervalSeconds': "間隔時間（秒）",
    'pages.config.minScoreLimitError': "最小スコア値は1を超えることはできません",
    'pages.config.initialSearchStrategy': "初期スクリーニング検索戦略",
    'pages.config.enhancedSearchStrategy': "多次元強化検索戦略",
    'pages.config.multiInitialSearch': "多次元初期スクリーニング",
    'pages.config.minScore': "最小スコア値",
    'pages.config.maxResultSet': "最大結果セット",
    'pages.config.topValue': "トップ値",
    'pages.config.reRanking': "多次元再ランキング",
    'pages.config.batchSize': "バッチサイズ",
    'pages.config.fineSearch': "多次元精密スクリーニング",
    'pages.config.fineSearchStrategy': "精密スクリーニング検索戦略",
    'pages.config.enableReId': "ReIdを有効にしますか？",
    'pages.config.objectMinScore': "オブジェクト検出の最小スコア",
    'pages.config.vectorMinScore': "ベクトル類似度の最小スコア",
    'pages.config.maxResultsPerSearch': "各検索の最大結果数",
    'pages.common.logout': "ログアウト",
    'pages.agent.apiCallFailed': "インタフェースの呼び出しに失敗しました。後で再試行してください",
    'pages.agent.hello': "やあ、私は",
    'pages.agent.agent': "インテリジェントボディ",
    'pages.agent.attachment': "添付ファイル",
    'pages.agent.dropFilesHere': "ファイルをここに配置",
    'pages.agent.uploadFile': "ファイルのアップロード",
    'pages.agent.clickOrDragToUpload': "ファイルをクリックまたはドラッグしてこの領域にアップロードします",
    'pages.agent.shiftEnterNewline': "Shift+Enter改行",
    'pages.agent.basicConfig': "基本構成",
    'pages.agent.llmModel': "使用するLLMモデル",
    'pages.agent.doubaoModel': "豆包モデル",
    'pages.agent.selectAnOption': "オプションを選択してください",
    'pages.agent.memoryMessageCount': "メモリメッセージ数",
    'pages.agent.skillConfig': "スキル構成",
    'pages.agent.toolSet': "ツールセット",
    'pages.agent.toolSetDescription': "ツールセットは、スマートボディが外部ツールを呼び出し、スマートボディの能力境界を拡張することができます。",
    'pages.agent.knowledgeBase': "ナレッジベース",
    'pages.agent.knowledgeBaseDescription': "ユーザーがメッセージを送信すると、エージェントはナレッジベースの内容を参照して、ユーザーの質問に答えることができます。",
    'pages.agent.workflow': "ワークフロー",
    'pages.agent.workflowDescription': "論理的に複雑で多くのステップを持つタスクフローを処理するために使用されます。",
    'pages.agent.describePersona': "キャラクタセット、機能について説明してください",
    'pages.agent.publishSuccess': "公開に成功しました",
    'pages.agent.publishFailed': "パブリッシュに失敗しました",
    'pages.agent.publishNotAllowed': "申し訳ありませんが、このスマートボディはパブリッシュできません",
    'pages.agent.config': "エージェント構成",
    'pages.agent.publish': "リリース",
    'pages.agent.modelCapabilityConfig': "モデルコンピテンシー構成",
    'pages.agent.promptDev': "プロンプト語の開発",
    'pages.agent.debug': "エージェントデバッグ",
    'pages.agent.create': "スマートボディの作成",
    'pages.agent.submitFailed': "送信に失敗しました。フォームデータを確認してください",
    'pages.agent.name': "エージェント名",
    'pages.agent.nameLimit': "最大64文字まで入力可能",
    'pages.agent.description': "インテリジェントボディ機能の紹介",
    'pages.agent.descriptionTip': "エージェントの機能を紹介すると、エージェントのユーザーに表示されます",
    'pages.agent.icon': "アイコン",
    'pages.agent.imageOnly': "画像ファイルのみアップロード可能",
    'pages.agent.imageSizeLimit': "画像サイズは2 MBを超えてはいけません",
    'pages.agent.imageFormatLimit': "jpg/pngフォーマットをサポートし、サイズは2 MBを超えない",
    'pages.agent.flagship': "フラッグシップ",
    'pages.agent.highSpeed': "高速度",
    'pages.agent.toolInvocation': "ツールコール",
    'pages.agent.rolePlay': "ロールプレイング",
    'pages.agent.longText': "長いテキスト",
    'pages.agent.imageUnderstanding': "画像の理解",
    'pages.agent.reasoning': "すいりりょく",
    'pages.agent.videoUnderstanding': "ビデオの理解",
    'pages.agent.costPerformance': "せいかひ",
    'pages.agent.codeExpert': "コードせんせい",
    'pages.agent.audioUnderstanding': "オーディオの理解",
    'pages.agent.visualAnalysis': "しかくぶんせき",
    'pages.agent.running': "実行中",
    'pages.agent.queuing': "キューに入れる",
    'pages.agent.training': "訓練中",
    'pages.agent.trainingFailed': "トレーニングに失敗しました",
    'pages.agent.text': "テキスト",
    'pages.agent.multimodal': "マルチモード",
    'pages.agent.landongModel': "怠惰穴モデル",
    'pages.agent.searchModelName': "モデル名の検索",
    'pages.agent.quotaTrial': "制限付き体験",
    'pages.agent.comingOffline': "間もなくラインオフ",
    'pages.agent.newModelExperience': "新しいモデル体験",
    'pages.agent.advancedModel': "高度なモデル",
    'pages.agent.generalModel': "ユニバーサルモデル",
    'pages.agent.modelType': "モデルタイプ",
    'pages.agent.modelFeature': "モデルの特徴",
    'pages.agent.modelProvider': "モデルメーカ",
    'pages.agent.modelSupportedFunctions': "モデルサポート機能",
    'pages.agent.contextLength': "コンテキスト長",
    'pages.agent.userRights': "ユーザー持分",
    'pages.agent.creator': "作成者",
    'pages.agent.creationTime': "作成時間",
    'pages.agent.describeFunction': "キャラクターと機能について説明してください。",
    'pages.agent.orchestration': "オーケストレーション",
    'pages.agent.functionIntroduction': "機能紹介",
    'pages.agent.publishStatus': "リリース状況",
    'pages.agent.agentDisplay': "エージェント表示",
    'pages.agent.modelStatus': "モデルステータス",
    'pages.search.expandir': "展開",
    'pages.search.retirar': "折りたたみ",
    'pages.search.deleteConfirmWarning': "一度削除すると復元できません。削除してもよろしいですか？",
    'pages.config.applicationId': "アプリケーションID",
    'pages.config.imageDeduplication': "画像重複除去",
    'pages.pointManage.loadingMessage': "読み込み中です。ページを更新しないでください。",
    'pages.pointManage.fetchError': "ポイントの取得がタイムアウトしました。デバイスがオンラインかどうかを確認してください。",
    'pages.pointManage.deviceTimeout': "デバイスのリクエストがタイムアウトしました。",
    'pages.pointManage.streamConnectFailed': "ストリーミングサービスへの接続に失敗しました。",
    'pages.pointManage.serviceException': "サービスに異常があります。しばらくしてからもう一度お試しください。",
    'pages.pointManage.deleteHasControlRule': "選択したポイントは監視中のため、削除できません。",
    'pages.pointManage.online': "オンライン",
    'pages.pointManage.offline': "オフライン",
    'pages.pointManage.confirmDeleteEventType': "このイベントタイプを削除してもよろしいですか？",
    'pages.pointManage.captureIntervalRange': "キャプチャ間隔は1～3600秒です。",
    'pages.pointManage.status': "州",
    'pages.login.terms.title': "利用規約",
    'pages.login.terms.check': "まずは利用規約をお読みいただき、同意してください。",
    'pages.pointManage.confirmDeletePoint': "このポイント情報を削除してもよろしいですか？",
    'pages.pointManage.pointNameRequired': "未入力のポイント名があります。送信前に入力してください。",
    'pages.pointManage.refresh': "更新",
    'pages.account.updateSuc': "正常に変更されました",
    'pages.account.updatePwd': "パスワードを変更する",
    'pages.account.oldPassword': "以前のパスワード",
    'pages.account.newPassword': "新しいパスワード",
    'pages.account.confirmPwd': "パスワードを認証する",
    'pages.account.passwordmatch': "入力した新しいパスワードが一致しません",
    'pages.password.reset.fail': "パスワードのリセットに失敗しました",
    'pages.password.reset.success': "パスワードをリセットしました",
    'pages.password.update': "パスワードを変更する",
    'pages.register.success': "登録が成功しました。ログインしてください。",
    'pages.register.fail': "登録に失敗しました",
    'pages.login.fail': "ユーザー名またはパスワードが間違っています",
    'pages.login.needRegister': "先にアカウントを登録してください",
    'pages.system.check.fail': "サービスチェックに失敗しました",
    'pages.account.maxlength': "パスワードは最大18文字まで入力できます",
    'pages.login.login': "ログイン",
    'pages.login.register': "登録",
    'pages.login.registerTitle': "ユーザー登録",
    'pages.search.similarity': "類似度",
    'pages.common.sessionExpired': "セッションの有効期限が切れました。再度ログインしてください。",
    'pages.primaryKey.id': "プライマリ・キーID",
    'pages.agent.type': "を選択してオプションを設定します。",
    'pages.agent.type.placeholder': "スマートボディタイプを選択してください",
    'pages.agent.type.required': "タイプを選択してください",
    'pages.agent.id': "エージェントID",
    'pages.agent.id.placeholder': "エージェントIDを入力してください",
    'pages.agent.id.required': "エージェントIDを入力してください",
    'pages.agent.suggestedQuestions': "あなたは私にこう聞くことができます：",
    'pages.agent.botId.tip': "Coze、Difyなどの対応するプラットフォームでエージェントを作成した後、IDをコピーしてここに貼り付けてください",
    'pages.agent.apiKey.tip': "Difyプラットフォームに行って、API Keyをコピーしてここに貼り付けてください",
    'pages.agent.apiKey.required': "API Keyは必須です",
}
