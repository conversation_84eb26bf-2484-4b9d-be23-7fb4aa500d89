package com.zkteco.mars.usc.service;

import com.zkteco.framework.base.bean.BaseItem;
import com.zkteco.framework.base.bean.Pager;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.usc.model.EventType;
import com.zkteco.mars.usc.vo.ControlRulesItem;

import java.util.List;

/**
 * 文本布控
 *
 * <AUTHOR>
 * @date 2025-04-09 18:15
 * @since 1.0.0
 */
public interface TextControlService {

    /**
     * 事件类型分页查询
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.framework.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2025-04-09 18:14
     * @since 1.0.0
     */
    Pager getEventTypeItemsByPage(BaseItem condition, int page, int size);


    /**
     * 初始化数据
     *
     * @param eventTypeList:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2025-04-09 17:04
     * @since 1.0.0
     */
    void insertEventTypeData(List<EventType> eventTypeList);


    /**
     * 事件类型分页查询
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.framework.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2025-04-09 18:14
     * @since 1.0.0
     */
    Pager getControlRulesItemsByPage(BaseItem condition, int page, int size);


    /**
     * 添加 编辑布控规则
     *
     * @param controlRule:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:39
     * @since 1.0.0
     */
    ApiResultMessage editControlRule(ControlRulesItem controlRule);


    /**
     * 获取所有的事件类型
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:41
     * @since 1.0.0
     */
    ApiResultMessage getAllEventType();

    /**
     * 获取所有的布控规则
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:41
     * @since 1.0.0
     */
    ApiResultMessage getAllControlRules();

    /**
     * 删除布控记录
     *
     * @param ids:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * @throws
     * <AUTHOR>
     * @date 2025-04-15 14:41
     * @since 1.0.0
     */
    ApiResultMessage delControlRules(String ids);

    /**
     * 开始布控 and 取消布控
     *
     * @param pointId:
     * @param ruleIds:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:27
     * @since 1.0.0
     */
    ApiResultMessage startControl(String pointId,String ruleIds);

    /**
     * 获取点位布控的规则
     *
     * @param pointId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:28
     * @since 1.0.0
     */
    ApiResultMessage getPointControls(String pointId);

    /**
     * 获取事件记录
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.framework.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date  2025-04-22 18:28
     * @since 1.0.0
     */
    Pager getEventRecordsItemsByPage(BaseItem condition, int page, int size);



    /**
     * 获取已使用的事件类型
     *
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 10:11
     * @since 1.0.0
     */
    ApiResultMessage getUsedEventCodes();


    /**
     * 确认规则是否在布控
     *
     * @param pointId:
     * @return com.zkteco.framework.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date  2025-06-05 13:48
     * @since 1.0.0
     */
    ApiResultMessage checkRuleIsControl(String pointId);


}
