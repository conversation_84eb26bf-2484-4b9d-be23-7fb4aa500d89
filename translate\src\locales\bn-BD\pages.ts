export default {
    'pages.login.success': "লগইন সফল!",
    'pages.login.failure': "লগইন ব্যর্থ!",
    'pages.login.retry': "লগইন ব্যর্থ, অনুগ্রহ করে আবার চেষ্টা করুন!",
    'pages.login.title': "লগইন পৃষ্ঠা",
    'pages.login.welcome': "মার্স উইজডমে স্বাগতম",
    'pages.login.agreementPrefix': "আপনি মার্স উইজডম পড়েছেন এবং সম্মত হয়েছেন",
    'pages.login.terms': "ব্যবহারকারীর ব্যবহারের শর্তাবলী",
    'pages.login.and': "এবং",
    'pages.login.privacy': "গোপনীয়তা নীতি",
    'pages.login.submit': "ব্যবহারকারীর লগইন",
    'pages.login.usernamePlaceholder': "দয়া করে আপনার ব্যবহারকারীর নাম লিখুন",
    'pages.login.passwordPlaceholder': "দয়া করে আপনার পাসওয়ার্ড লিখুন",
    'pages.search.sortByTime': "সর্বশেষ সময় অনুসারে",
    'pages.search.sortBySimilarity': "সাদৃশ্য অনুসারে",
    'pages.search.history': "ইতিহাস",
    'pages.search.searchHistory': "সার্চ ইতিহাস",
    'pages.search.delete': "মুছুন",
    'pages.search.hotSearch': "সর্বাধিক অনুসন্ধান",
    'pages.search.example1': "দরজার কাছে মোটর গাড়ির পাশে থাকা ছেলেটি",
    'pages.search.example2': "লিফটে লাল পোশাক পরা ব্যক্তি ফোন করছে",
    'pages.search.example3': "পার্কিং বুথে নীল টুপি পরা ব্যক্তি",
    'pages.search.assistHint': "আমি আপনাকে তথ্য অনুসন্ধানে সাহায্য করতে পারি, যেমন: পার্কের প্রথম তলায় টেকওয়ে ক্যাবিনেটের পাশে কালো পোশাক পরা ছেলেটি",
    'pages.search.searchButton': "অনুসন্ধান",
    'pages.search.filter': "অনুসন্ধানের অবস্থা",
    'pages.search.loading': "লোড হচ্ছে...",
    'pages.search.eventDetail': "ইভেন্টের বিবরণ",
    'pages.search.videoPlayback': "ভিডিও প্লেব্যাক",
    'pages.search.panorama': "প্যানোরামা",
    'pages.search.description': "বর্ণনা",
    'pages.search.time': "সময়",
    'pages.search.location': "অবস্থান",
    'pages.search.similarity': "সাদৃশ্য",
    'pages.pointManage.voiceInput': "ভয়েস ইনপুট",
    'pages.pointManage.speechRecognition': "ভয়েস স্বীকৃতি",
    'pages.pointManage.stopVoiceInput': "ভয়েস ইনপুট বন্ধ করুন",
    'pages.pointManage.operationSuccess': "অপারেশন সফল",
    'pages.pointManage.preview': "প্রিভিউ",
    'pages.pointManage.edit': "সম্পাদনা",
    'pages.pointManage.alreadyMonitored': "নিয়ন্ত্রণ মোতায়েন",
    'pages.pointManage.monitor': "নিয়ন্ত্রণ মোতায়েন",
    'pages.pointManage.delete': "মুছুন",
    'pages.pointManage.deleteRecord': "রেকর্ড মুছুন",
    'pages.pointManage.confirmDeleteRecord': "আপনি কি রেকর্ডটি মুছে ফেলার বিষয়ে নিশ্চিত?",
    'pages.pointManage.confirm': "নিশ্চিত করুন",
    'pages.pointManage.cancel': "বাতিল করুন",
    'pages.pointManage.deleting': "মুছে ফেলা",
    'pages.pointManage.deleteSuccess': "মুছুন সফল, স্বয়ংক্রিয়ভাবে রিফ্রেশ করুন",
    'pages.pointManage.deleteFailure': "মুছুন ব্যর্থ হয়েছে, অনুগ্রহ করে আবার চেষ্টা করুন",
    'pages.pointManage.person': "ব্যক্তি",
    'pages.pointManage.motorVehicle': "মোটর গাড়ি",
    'pages.pointManage.nonMotorVehicle': "নন-মোটর গাড়ি",
    'pages.pointManage.pointName': "পয়েন্টের নাম",
    'pages.pointManage.protocolType': "প্রোটোকলের ধরণ",
    'pages.pointManage.captureType': "স্ন্যাপশটের ধরণ",
    'pages.pointManage.captureInterval': "স্ন্যাপশটের ব্যবধান",
    'pages.pointManage.deviceName': "ডিভাইসের নাম",
    'pages.pointManage.deviceIP': "ডিভাইস আইপি",
    'pages.pointManage.devicePort': "ডিভাইস পোর্ট",
    'pages.pointManage.operation': "অপারেশন",
    'pages.pointManage.pointManagement': "পয়েন্ট ব্যবস্থাপনা",
    'pages.pointManage.add': "যোগ করুন",
    'pages.pointManage.selected': "নির্বাচিত",
    'pages.pointManage.batchDelete': "ব্যাচ মোছা",
    'pages.pointManage.item': "আইটেম",
    'pages.pointManage.inputCaptureInterval': "দয়া করে স্ন্যাপশট ব্যবধান লিখুন",
    'pages.pointManage.inputPointName': "অনুগ্রহ করে পয়েন্টের নাম লিখুন",
    'pages.pointManage.selectCaptureType': "ক্যাপচারের ধরণ নির্বাচন করুন",
    'pages.pointManage.addDevice': "ডিভাইস যোগ করুন",
    'pages.pointManage.protocol': "প্রোটোকল",
    'pages.pointManage.deviceCode': "ডিভাইস কোড",
    'pages.pointManage.inputDeviceName': "অনুগ্রহ করে ডিভাইসের নাম লিখুন",
    'pages.pointManage.inputDeviceCode': "অনুগ্রহ করে ডিভাইস কোড লিখুন",
    'pages.pointManage.port': "পোর্ট",
    'pages.pointManage.username': "ব্যবহারকারীর নাম",
    'pages.pointManage.password': "পাসওয়ার্ড",
    'pages.pointManage.mainStream': "মূল ধারা",
    'pages.pointManage.example': "উদাহরণ",
    'pages.pointManage.subStream': "সাব স্ট্রিম",
    'pages.pointManage.addPoint': "পয়েন্ট যোগ করুন",
    'pages.pointManage.validIP': "অনুগ্রহ করে একটি বৈধ আইপি ঠিকানা লিখুন",
    'pages.pointManage.noData': "কোনও ডেটা নেই",
    'pages.pointManage.selectPoint': "অনুগ্রহ করে একটি পয়েন্ট নির্বাচন করুন",
    'pages.pointManage.addSuccess': "সফলভাবে যোগ করা হয়েছে",
    'pages.pointManage.prevStep': "পূর্ববর্তী ধাপ",
    'pages.pointManage.nextStep': "পরবর্তী ধাপ",
    'pages.pointManage.monitorSuccess': "নিয়ন্ত্রণ স্থাপন সফল",
    'pages.pointManage.monitorFailure': "নিয়ন্ত্রণ স্থাপন ব্যর্থ",
    'pages.pointManage.cancelSuccess': "সফলভাবে বাতিল করা হয়েছে",
    'pages.pointManage.operationFailure': "অপারেশন ব্যর্থ হয়েছে",
    'pages.pointManage.monitor': "নিয়ন্ত্রণ স্থাপন",
    'pages.pointManage.monitorEvent': "নিয়ন্ত্রণ স্থাপন ইভেন্ট",
    'pages.pointManage.startMonitor': "নিয়ন্ত্রণ স্থাপন শুরু করুন",
    'pages.pointManage.monitorRecord': "নিয়ন্ত্রণ স্থাপন রেকর্ড",
    'pages.pointManage.noEventRecord': "কোনও ইভেন্ট রেকর্ড নেই",
    'pages.pointManage.charLimitExceeded': "অক্ষরের দৈর্ঘ্য সীমা অতিক্রম করেছে, সর্বাধিক দৈর্ঘ্য:",
    'pages.pointManage.eventType': "ইভেন্টের ধরন",
    'pages.pointManage.prompt': "প্রম্পট",
    'pages.pointManage.createTime': "তৈরি সময়",
    'pages.pointManage.editSuccess': "সম্পাদনা সফল হয়েছে",
    'pages.pointManage.monitorRule': "পর্যবেক্ষণ নিয়ম",
    'pages.pointManage.selectEventType': "অনুগ্রহ করে ইভেন্টের ধরন নির্বাচন করুন",
    'pages.pointManage.inputPrompt': "অনুগ্রহ করে প্রম্পট লিখুন",
    'pages.pointManage.eventRecord': "ইভেন্ট রেকর্ড",
    'pages.pointManage.paginationInfo': "{total}টির মধ্যে {range0} - {range1} আইটেম",
    'pages.pointManage.detail': "বিস্তারিত",
    'pages.pointManage.eventImage': "ইভেন্টের ছবি",
    'pages.pointManage.pointInfo': "পয়েন্ট তথ্য",
    'pages.pointManage.eventTime': "ইভেন্টের সময়",
    'pages.config.name': "নাম",
    'pages.config.creationDate': "তৈরির তারিখ",
    'pages.config.expirationTime': "মেয়াদ শেষের সময়",
    'pages.config.editApiKey': "API কী সম্পাদনা করুন",
    'pages.config.editSuccess': "সম্পাদনা সফল, স্বয়ংক্রিয় রিফ্রেশ",
    'pages.config.createSuccess': "তৈরি সফল, স্বয়ংক্রিয় রিফ্রেশ",
    'pages.config.editFailure': "সম্পাদনা ব্যর্থ হয়েছে, অনুগ্রহ করে পুনরায় চেষ্টা করুন",
    'pages.config.createFailure': "তৈরি ব্যর্থ হয়েছে, অনুগ্রহ করে পুনরায় চেষ্টা করুন",
    'pages.config.createApiKey': "API কী তৈরি করুন",
    'pages.config.authManagement': "অনুমোদন ব্যবস্থাপনা",
    'pages.config.eventCode': "ইভেন্ট কোড",
    'pages.config.paramConfigFailure': "পরামিতি কনফিগারেশন প্রাপ্ত করতে ব্যর্থ",
    'pages.config.saveFailure': "সংরক্ষণ ব্যর্থ হয়েছে",
    'pages.config.saveSuccess': "সংরক্ষণ সফল হয়েছে",
    'pages.config.save': "সংরক্ষণ করুন",
    'pages.config.reset': "রিসেট করুন",
    'pages.config.streamConfig': "স্ট্রিমিং কনফিগারেশন",
    'pages.config.streamServiceUrl': "স্ট্রিমিং পরিষেবা ঠিকানা",
    'pages.config.secretKey': "সিক্রেট কী",
    'pages.config.configuration': "কনফিগারেশন",
    'pages.config.workspaceId': "ওয়ার্কস্পেস আইডি",
    'pages.config.multiSearchStrategy': "মাল্টি-ডাইমেনশনাল সার্চ স্ট্র্যাটেজি",
    'pages.config.dataCleanStrategy': "ডেটা ক্লিনিং স্ট্র্যাটেজি",
    'pages.config.objectDetection': "অবজেক্ট ডিটেকশন",
    'pages.config.enableObjectDetection': "অবজেক্ট ডিটেকশন সক্রিয় করবেন?",
    'pages.config.allowObjectWhitelist': "অবজেক্ট ডিটেকশন হোয়াইটলিস্ট অনুমতি দিন",
    'pages.config.sedan': "সেডান",
    'pages.config.bus': "বাস",
    'pages.config.truck': "ট্রাক",
    'pages.config.bicycle': "সাইকেল",
    'pages.config.motorcycle': "মোটরসাইকেল",
    'pages.config.enableImageDupCheck': "চিত্রের ডুপ্লিকেট সনাক্তকরণ সক্রিয় করবেন?",
    'pages.config.intervalSeconds': "ব্যবধান সময় (সেকেন্ড)",
    'pages.config.minScoreLimitError': "সর্বনিম্ন স্কোর মান 1 এর বেশি হতে পারে না",
    'pages.config.initialSearchStrategy': "প্রাথমিক স্ক্রিনিং অনুসন্ধান কৌশল",
    'pages.config.enhancedSearchStrategy': "বহু-মাত্রিক উন্নত অনুসন্ধান কৌশল",
    'pages.config.multiInitialSearch': "বহু-মাত্রিক প্রাথমিক স্ক্রিনিং",
    'pages.config.minScore': "সর্বনিম্ন স্কোর মান",
    'pages.config.maxResultSet': "সর্বাধিক ফলাফল সেট",
    'pages.config.topValue': "শীর্ষ মান",
    'pages.config.reRanking': "বহু-মাত্রিক পুনরায় র‍্যাঙ্কিং",
    'pages.config.batchSize': "প্রতি ব্যাচের আকার",
    'pages.config.fineSearch': "বহু-মাত্রিক সূক্ষ্ম স্ক্রিনিং",
    'pages.config.fineSearchStrategy': "সূক্ষ্ম স্ক্রিনিং অনুসন্ধান কৌশল",
    'pages.config.enableReId': "ReId সক্রিয় করবেন?",
    'pages.config.objectMinScore': "অবজেক্ট ডিটেকশন সর্বনিম্ন স্কোর",
    'pages.config.vectorMinScore': "ভেক্টর সাদৃশ্য সর্বনিম্ন স্কোর",
    'pages.config.maxResultsPerSearch': "প্রতিটি অনুসন্ধানে সর্বাধিক ফলাফল",
    'pages.common.logout': "লগআউট",
    'pages.agent.apiCallFailed': "ইন্টারফেস কল করতে ব্যর্থ, দয়া করে পরে আবার চেষ্টা করুন",
    'pages.agent.hello': "হ্যালো, আমি",
    'pages.agent.agent': "এজেন্ট",
    'pages.agent.attachment': "সংযুক্তি",
    'pages.agent.dropFilesHere': "ফাইলটি এখানে রাখুন",
    'pages.agent.uploadFile': "ফাইল আপলোড করুন",
    'pages.agent.clickOrDragToUpload': "আপলোড করার জন্য ফাইলটি এই জায়গায় ক্লিক করুন বা টেনে আনুন",
    'pages.agent.shiftEnterNewline': "লাইন পরিবর্তন করতে Shift+Enter",
    'pages.agent.basicConfig': "মৌলিক কনফিগারেশন",
    'pages.agent.llmModel': "ব্যবহৃত LLM মডেল",
    'pages.agent.doubaoModel': "বিন ব্যাগ মডেল",
    'pages.agent.selectAnOption': "দয়া করে একটি বিকল্প নির্বাচন করুন",
    'pages.agent.memoryMessageCount': "মনে রাখা বার্তার সংখ্যা",
    'pages.agent.skillConfig': "দক্ষতা কনফিগারেশন",
    'pages.agent.toolSet': "টুলসেট",
    'pages.agent.toolSetDescription': "টুলসেট এজেন্টকে বহিরাগত সরঞ্জামগুলিতে কল করতে এবং এজেন্টের ক্ষমতা সীমা প্রসারিত করতে সক্ষম করে।",
    'pages.agent.knowledgeBase': "নলেজ বেস",
    'pages.agent.knowledgeBaseDescription': "যখন ব্যবহারকারী একটি বার্তা পাঠান, তখন এজেন্ট ব্যবহারকারীর প্রশ্নের উত্তর দেওয়ার জন্য জ্ঞান বেসের বিষয়বস্তু উল্লেখ করতে পারেন।",
    'pages.agent.workflow': "ওয়ার্কফ্লো",
    'pages.agent.workflowDescription': "জটিল যুক্তি এবং অনেক ধাপ সহ টাস্ক ফ্লো পরিচালনা করতে ব্যবহৃত হয়।",
    'pages.agent.describePersona': "অনুগ্রহ করে ব্যক্তিত্ব এবং ফাংশন বর্ণনা করুন",
    'pages.agent.publishSuccess': "প্রকাশ সফলভাবে",
    'pages.agent.publishFailed': "প্রকাশ ব্যর্থ হয়েছে",
    'pages.agent.publishNotAllowed': "দুঃখিত, এজেন্ট প্রকাশ করা যাচ্ছে না",
    'pages.agent.config': "এজেন্ট কনফিগারেশন",
    'pages.agent.publish': "প্রকাশ",
    'pages.agent.modelCapabilityConfig': "মডেল ক্ষমতা কনফিগারেশন",
    'pages.agent.promptDev': "প্রম্পট শব্দ উন্নয়ন",
    'pages.agent.debug': "এজেন্ট ডিবাগিং",
    'pages.agent.create': "এজেন্ট তৈরি করুন",
    'pages.agent.submitFailed': "জমা ব্যর্থ হয়েছে, অনুগ্রহ করে ফর্ম ডেটা পরীক্ষা করুন",
    'pages.agent.name': "এজেন্টের নাম",
    'pages.agent.nameLimit': "৬৪ টি অক্ষর পর্যন্ত প্রবেশ করানো যেতে পারে",
    'pages.agent.description': "এজেন্ট ফাংশন ভূমিকা",
    'pages.agent.descriptionTip': "এজেন্টের ফাংশন পরিচয় করিয়ে দিন এবং এজেন্টের ব্যবহারকারীর কাছে প্রদর্শিত হবে",
    'pages.agent.icon': "আইকন",
    'pages.agent.imageOnly': "শুধুমাত্র ইমেজ ফাইল আপলোড করা যাবে",
    'pages.agent.imageSizeLimit': "ছবির আকার ২ মেগাবাইটের বেশি হতে পারে না",
    'pages.agent.imageFormatLimit': "jpg/png ফর্ম্যাট সমর্থন করে, আকার ২ মেগাবাইটের বেশি নয়",
    'pages.agent.flagship': "ফ্ল্যাগশিপ",
    'pages.agent.highSpeed': "উচ্চ গতি",
    'pages.agent.toolInvocation': "টুল কল",
    'pages.agent.rolePlay': "ভূমিকা পালন",
    'pages.agent.longText': "দীর্ঘ টেক্সট",
    'pages.agent.imageUnderstanding': "ছবির বোঝাপড়া",
    'pages.agent.reasoning': "যুক্তি করার ক্ষমতা",
    'pages.agent.videoUnderstanding': "ভিডিও বোঝাপড়া",
    'pages.agent.costPerformance': "ব্যয়-কার্যকারিতা",
    'pages.agent.codeExpert': "কোড দক্ষতা",
    'pages.agent.audioUnderstanding': "অডিও বোঝাপড়া",
    'pages.agent.visualAnalysis': "ভিজ্যুয়াল বিশ্লেষণ",
    'pages.agent.running': "চালানো",
    'pages.agent.queuing': "সারিবদ্ধকরণ",
    'pages.agent.training': "প্রশিক্ষণ",
    'pages.agent.trainingFailed': "প্রশিক্ষণ ব্যর্থ হয়েছে",
    'pages.agent.text': "টেক্সট",
    'pages.agent.multimodal': "মাল্টিমোডাল",
    'pages.agent.landongModel': "অলস গর্ত মডেল",
    'pages.agent.searchModelName': "মডেলের নাম অনুসন্ধান করুন",
    'pages.agent.quotaTrial': "সীমা অভিজ্ঞতা",
    'pages.agent.comingOffline': "শীঘ্রই অফলাইনে আসছে",
    'pages.agent.newModelExperience': "নতুন মডেল অভিজ্ঞতা",
    'pages.agent.advancedModel': "উন্নত মডেল",
    'pages.agent.generalModel': "সাধারণ মডেল",
    'pages.agent.modelType': "মডেলের ধরণ",
    'pages.agent.modelFeature': "মডেল বৈশিষ্ট্য",
    'pages.agent.modelProvider': "মডেল প্রস্তুতকারক",
    'pages.agent.modelSupportedFunctions': "মডেল সমর্থন ফাংশন",
    'pages.agent.contextLength': "প্রসঙ্গ দৈর্ঘ্য",
    'pages.agent.userRights': "ব্যবহারকারীর অধিকার",
    'pages.agent.creator': "স্রষ্টা",
    'pages.agent.creationTime': "সৃষ্টির সময়",
    'pages.agent.describeFunction': "অনুগ্রহ করে চরিত্র এবং ফাংশন বর্ণনা করুন",
    'pages.agent.orchestration': "অর্কেস্ট্রেশন",
    'pages.agent.functionIntroduction': "ফাংশন ভূমিকা",
    'pages.agent.publishStatus': "রিলিজ স্ট্যাটাস",
    'pages.agent.agentDisplay': "এজেন্ট ডিসপ্লে",
    'pages.agent.modelStatus': "মডেল স্ট্যাটাস",
    'pages.search.expandir': "সম্প্রসারণ করুন",
    'pages.search.retirar': "সঙ্কুচিত করুন",
    'pages.search.deleteConfirmWarning': "একবার মুছে ফেলা হলে, এটি পুনরুদ্ধার করা যাবে না। আপনি কি নিশ্চিত যে আপনি এটি মুছে ফেলতে চান?",
    'pages.config.applicationId': "অ্যাপ্লিকেশন আইডি",
    'pages.config.imageDeduplication': "ছবির প্রতিলিপি",
    'pages.pointManage.loadingMessage': "লোড হচ্ছে, অনুগ্রহ করে পৃষ্ঠাটি রিফ্রেশ করবেন না",
    'pages.pointManage.fetchError': "পয়েন্ট পাওয়ার সময়সীমা শেষ, অনুগ্রহ করে ডিভাইসটি অনলাইনে আছে কিনা তা পরীক্ষা করুন",
    'pages.pointManage.deviceTimeout': "ডিভাইসের অনুরোধের সময়সীমা শেষ",
    'pages.pointManage.streamConnectFailed': "স্ট্রিমিং পরিষেবা সংযোগ ব্যর্থ",
    'pages.pointManage.serviceException': "পরিষেবা অস্বাভাবিকতা, অনুগ্রহ করে পরে আবার চেষ্টা করুন",
    'pages.pointManage.deleteHasControlRule': "নির্বাচিত পয়েন্টটি পর্যবেক্ষণ করা হচ্ছে এবং মুছে ফেলা যাচ্ছে না",
    'pages.pointManage.online': "অনলাইন",
    'pages.pointManage.offline': "অফলাইন",
    'pages.pointManage.confirmDeleteEventType': "আপনি কি এই ইভেন্টের ধরণটি মুছে ফেলার বিষয়ে নিশ্চিত?",
    'pages.pointManage.captureIntervalRange': "ক্যাপচার ব্যবধান ১ থেকে ৩৬০০ সেকেন্ডের মধ্যে",
    'pages.pointManage.status': "অবস্থা",
    'pages.login.terms.title': "পরিষেবা ব্যবহারের শর্তাবলি",
    'pages.login.terms.check': "অনুগ্রহ করে প্রথমে পরিষেবা ব্যবহারের শর্তাবলি পড়ে সম্মত হন।",
    'pages.pointManage.confirmDeletePoint': "আপনি কি নিশ্চিতভাবে পয়েন্ট তথ্য রেকর্ড মুছে ফেলতে চান?",
    'pages.pointManage.pointNameRequired': "কিছু পয়েন্টের নাম লেখা হয়নি। জমা দেওয়ার আগে অনুগ্রহ করে পূরণ করুন।",
    'pages.pointManage.refresh': "রিফ্রেশ",
    'pages.account.updateSuc': "সফলভাবে সংশোধন করা হয়েছে",
    'pages.account.updatePwd': "পাসওয়ার্ড পরিবর্তন করুন",
    'pages.account.oldPassword': "পুরানো পাসওয়ার্ড",
    'pages.account.newPassword': "নতুন পাসওয়ার্ড",
    'pages.account.confirmPwd': "পাসওয়ার্ড নিশ্চিত করুন",
    'pages.account.passwordmatch': "আপনার দেওয়া নতুন পাসওয়ার্ড মেলে না",
    'pages.password.reset.fail': "পাসওয়ার্ড রিসেট ব্যর্থ হয়েছে",
    'pages.password.reset.success': "পাসওয়ার্ড সফলভাবে রিসেট হয়েছে",
    'pages.password.update': "পাসওয়ার্ড পরিবর্তন করুন",
    'pages.register.success': "রেজিস্ট্রেশন সফল হয়েছে, অনুগ্রহ করে লগইন করুন",
    'pages.register.fail': "রেজিস্ট্রেশন ব্যর্থ হয়েছে",
    'pages.login.fail': "ব্যবহারকারীর নাম বা পাসওয়ার্ড ভুল",
    'pages.login.needRegister': "অনুগ্রহ করে আগে অ্যাকাউন্ট রেজিস্টার করুন",
    'pages.system.check.fail': "পরিষেবা যাচাইকরণ ব্যর্থ হয়েছে",
    'pages.account.maxlength': "পাসওয়ার্ড সর্বাধিক ১৮ অক্ষর হতে পারে",
    'pages.login.login': "লগইন",
    'pages.login.register': "নিবন্ধন",
    'pages.login.registerTitle': "ব্যবহারকারী নিবন্ধন",
    'pages.search.similarity': "সাদৃশ্যতা",
    'pages.common.sessionExpired': "সেশনের মেয়াদ শেষ, অনুগ্রহ করে আবার লগইন করুন.",
    'pages.primaryKey.id': "প্রাথমিক কী আইডি",
    'pages.agent.type': "ধরন",
    'pages.agent.type.placeholder': "স্মার্টবডি টাইপ নির্বাচন করুন",
    'pages.agent.type.required': "ধরন নির্বাচন করুন",
    'pages.agent.id': "স্মার্ট আইডি",
    'pages.agent.id.placeholder': "অনুগ্রহ করে স্মার্টবডি আইডি লিখুন",
    'pages.agent.id.required': "অনুগ্রহ করে স্মার্টবডি আইডি লিখুন",
    'pages.agent.suggestedQuestions': "আপনি আমাকে জিজ্ঞেস করতে পারেন:",
    'pages.agent.botId.tip': "প্ল্যাটফর্ম (যেমন Coze, Dify) এ যান এবং স্মার্টবডি তৈরি করুন এবং তার আইডি কপি করুন এবং এখানে পেস্ট করুন",
    'pages.agent.apiKey.tip': "Dify প্ল্যাটফর্মে যান এবং তার API কী কপি করুন এবং এখানে পেস্ট করুন",
    'pages.agent.apiKey.required': "API Key প্রয়োজনীয়",
}
