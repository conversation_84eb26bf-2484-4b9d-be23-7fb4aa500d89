package com.zkteco.mars.usc.init;

import com.zkteco.mars.usc.dao.EventTypeDao;
import com.zkteco.mars.usc.model.EventType;
import com.zkteco.mars.usc.service.TextControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 事件类型数据初始化
 *
 * <AUTHOR>
 * @date 2025-04-09 16:35
 * @since 1.0.0
 */
@Component
@Order(value = 21)
@Slf4j
public class EventTypeInit implements CommandLineRunner {

    @Autowired
    private EventTypeDao eventTypeDao;

    @Autowired
    private TextControlService textControlService;


    @Override
    public void run(String... args) throws Exception {
        long count = eventTypeDao.count();
        if (count == 0) {
            initEventTypeData();
        }
    }

    /**
     * 默认数据写入
     *
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2025-04-09 16:38
     * @since 1.0.0
     */
    private void initEventTypeData() {
        List<EventType> eventTypeList = new ArrayList<>();
        EventType eventType = new EventType("00001", "人员打架");
        eventTypeList.add(eventType);
        eventType = new EventType("00002", "打电话");
        eventTypeList.add(eventType);
        eventType = new EventType("00003", "人员摔倒");
        eventTypeList.add(eventType);
        eventType = new EventType("00004", "抽烟");
        eventTypeList.add(eventType);
        eventType = new EventType("00005", "火焰检测");
        eventTypeList.add(eventType);
        eventType = new EventType("00006", "烟雾检测");
        eventTypeList.add(eventType);
        eventType = new EventType("00007", "睡觉的人");
        eventTypeList.add(eventType);
        eventType = new EventType("00008", "玩手机");
        eventTypeList.add(eventType);
        eventType = new EventType("00009", "打扫卫生");
        eventTypeList.add(eventType);
        eventType = new EventType("00010", "手提行李箱");
        eventTypeList.add(eventType);
        eventType = new EventType("00011", "抱小孩");
        eventTypeList.add(eventType);
        eventType = new EventType("00012", "穿粉色衣服的人");
        eventTypeList.add(eventType);
        eventType = new EventType("00013", "戴安全帽的人");
        eventTypeList.add(eventType);
        eventType = new EventType("00014", "遛狗的人");
        eventTypeList.add(eventType);
        eventType = new EventType("00015", "垃圾堆放");
        eventTypeList.add(eventType);
        textControlService.insertEventTypeData(eventTypeList);
    }

}
