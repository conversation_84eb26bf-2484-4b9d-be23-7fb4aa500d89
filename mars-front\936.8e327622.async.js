!(function(){var yn=(he,Y)=>(Y=Symbol[he])?Y:Symbol.for("Symbol."+he),Di=he=>{throw TypeError(he)};var Ai=function(he,Y){this[0]=he,this[1]=Y};var xn=he=>{var Y=he[yn("asyncIterator")],p=!1,l,G={};return Y==null?(Y=he[yn("iterator")](),l=K=>G[K]=V=>Y[K](V)):(Y=Y.call(he),l=K=>G[K]=V=>{if(p){if(p=!1,K==="throw")throw V;return V}return p=!0,{done:!1,value:new Ai(new Promise(z=>{var d=Y[K](V);d instanceof Object||Di("Object expected"),z(d)}),1)}}),G[yn("iterator")]=()=>G,l("next"),"throw"in Y?l("throw"):G.throw=K=>{throw K},"return"in Y&&l("return"),G};(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[936],{80994:function(he,Y,p){"use strict";p.d(Y,{f:function(){return yi}});var l=p(4942),G=p(74165),K=p(15861),V=p(45987),z=p(97685),d=p(1413),ee=p(10915),ue=p(21770),v=p(67294);function ve(r){var e=typeof window=="undefined",t=(0,v.useState)(function(){return e?!1:window.matchMedia(r).matches}),n=(0,z.Z)(t,2),a=n[0],i=n[1];return(0,v.useLayoutEffect)(function(){if(!e){var o=window.matchMedia(r),u=function(f){return i(f.matches)};return o.addListener(u),function(){return o.removeListener(u)}}},[r]),a}var se={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},J=function(){var e=void 0;if(typeof window=="undefined")return e;var t=Object.keys(se).find(function(n){var a=se[n].matchMedia;return!!window.matchMedia(a).matches});return e=t,e},ce=function(){var e=ve(se.md.matchMedia),t=ve(se.lg.matchMedia),n=ve(se.xxl.matchMedia),a=ve(se.xl.matchMedia),i=ve(se.sm.matchMedia),o=ve(se.xs.matchMedia),u=(0,v.useState)(J()),s=(0,z.Z)(u,2),f=s[0],m=s[1];return(0,v.useEffect)(function(){if(n){m("xxl");return}if(a){m("xl");return}if(t){m("lg");return}if(e){m("md");return}if(i){m("sm");return}if(o){m("xs");return}m("md")},[e,t,n,a,i,o]),f},oe=p(12044);function w(r,e){var t=typeof r.pageName=="string"?r.title:e;(0,v.useEffect)(function(){(0,oe.j)()&&t&&(document.title=t)},[r.title,t])}var D=p(1977),R=p(73177);function X(r){if((0,D.n)((0,R.b)(),"5.6.0")<0)return r;var e={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},t=(0,d.Z)({},r);return Object.keys(e).forEach(function(n){t[n]!==void 0&&(t[e[n]]=t[n],delete t[n])}),t}var me=p(90743);function U(r,e){return e>>>r|e<<32-r}function te(r,e,t){return r&e^~r&t}function ie(r,e,t){return r&e^r&t^e&t}function de(r){return U(2,r)^U(13,r)^U(22,r)}function Z(r){return U(6,r)^U(11,r)^U(25,r)}function C(r){return U(7,r)^U(18,r)^r>>>3}function y(r){return U(17,r)^U(19,r)^r>>>10}function E(r,e){return r[e&15]+=y(r[e+14&15])+r[e+9&15]+C(r[e+1&15])}var I=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],S,W,M,ye="0123456789abcdef";function Ie(r,e){var t=(r&65535)+(e&65535),n=(r>>16)+(e>>16)+(t>>16);return n<<16|t&65535}function Be(){S=new Array(8),W=new Array(2),M=new Array(64),W[0]=W[1]=0,S[0]=1779033703,S[1]=3144134277,S[2]=1013904242,S[3]=2773480762,S[4]=1359893119,S[5]=2600822924,S[6]=528734635,S[7]=1541459225}function Me(){var r,e,t,n,a,i,o,u,s,f,m=new Array(16);r=S[0],e=S[1],t=S[2],n=S[3],a=S[4],i=S[5],o=S[6],u=S[7];for(var g=0;g<16;g++)m[g]=M[(g<<2)+3]|M[(g<<2)+2]<<8|M[(g<<2)+1]<<16|M[g<<2]<<24;for(var h=0;h<64;h++)s=u+Z(a)+te(a,i,o)+I[h],h<16?s+=m[h]:s+=E(m,h),f=de(r)+ie(r,e,t),u=o,o=i,i=a,a=Ie(n,s),n=t,t=e,e=r,r=Ie(s,f);S[0]+=r,S[1]+=e,S[2]+=t,S[3]+=n,S[4]+=a,S[5]+=i,S[6]+=o,S[7]+=u}function De(r,e){var t,n,a=0;n=W[0]>>3&63;var i=e&63;for((W[0]+=e<<3)<e<<3&&W[1]++,W[1]+=e>>29,t=0;t+63<e;t+=64){for(var o=n;o<64;o++)M[o]=r.charCodeAt(a++);Me(),n=0}for(var u=0;u<i;u++)M[u]=r.charCodeAt(a++)}function je(){var r=W[0]>>3&63;if(M[r++]=128,r<=56)for(var e=r;e<56;e++)M[e]=0;else{for(var t=r;t<64;t++)M[t]=0;Me();for(var n=0;n<56;n++)M[n]=0}M[56]=W[1]>>>24&255,M[57]=W[1]>>>16&255,M[58]=W[1]>>>8&255,M[59]=W[1]&255,M[60]=W[0]>>>24&255,M[61]=W[0]>>>16&255,M[62]=W[0]>>>8&255,M[63]=W[0]&255,Me()}function Ze(){for(var r=0,e=new Array(32),t=0;t<8;t++)e[r++]=S[t]>>>24&255,e[r++]=S[t]>>>16&255,e[r++]=S[t]>>>8&255,e[r++]=S[t]&255;return e}function _e(){for(var r=new String,e=0;e<8;e++)for(var t=28;t>=0;t-=4)r+=ye.charAt(S[e]>>>t&15);return r}function ze(r){return Be(),De(r,r.length),je(),_e()}var Ae=ze;function We(r){"@babel/helpers - typeof";return We=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(r)}var Qe=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function ut(r,e){return Wt(r)||st(r,e)||Vt(r,e)||ct()}function ct(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function st(r,e){var t=r==null?null:typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,o,u;try{for(t=t.call(r);!(a=(o=t.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,u=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw u}}return n}}function Wt(r){if(Array.isArray(r))return r}function le(r,e){var t=typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=Vt(r))||e&&r&&typeof r.length=="number"){t&&(r=t);var n=0,a=function(){};return{s:a,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(f){throw f},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,o=!1,u;return{s:function(){t=t.call(r)},n:function(){var f=t.next();return i=f.done,f},e:function(f){o=!0,u=f},f:function(){try{!i&&t.return!=null&&t.return()}finally{if(o)throw u}}}}function xe(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Se(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}function ne(r,e,t){return e&&Se(r.prototype,e),t&&Se(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}function $e(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&at(r,e)}function Ct(r){var e=bt();return function(){var n=ot(r),a;if(e){var i=ot(this).constructor;a=Reflect.construct(n,arguments,i)}else a=n.apply(this,arguments);return Kt(this,a)}}function Kt(r,e){if(e&&(We(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return nt(r)}function nt(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function rt(r){var e=typeof Map=="function"?new Map:void 0;return rt=function(n){if(n===null||!Ut(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(n))return e.get(n);e.set(n,a)}function a(){return dt(n,arguments,ot(this).constructor)}return a.prototype=Object.create(n.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),at(a,n)},rt(r)}function dt(r,e,t){return bt()?dt=Reflect.construct.bind():dt=function(a,i,o){var u=[null];u.push.apply(u,i);var s=Function.bind.apply(a,u),f=new s;return o&&at(f,o.prototype),f},dt.apply(null,arguments)}function bt(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(r){return!1}}function Ut(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function at(r,e){return at=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},at(r,e)}function ot(r){return ot=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ot(r)}function Cn(r){return Zr(r)||Sr(r)||Vt(r)||br()}function br(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Vt(r,e){if(r){if(typeof r=="string")return Gt(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Gt(r,e)}}function Sr(r){if(typeof Symbol!="undefined"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function Zr(r){if(Array.isArray(r))return Gt(r)}function Gt(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Mr(r,e){if(r==null)return{};var t=Ir(r,e),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(a=0;a<i.length;a++)n=i[a],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(r,n)&&(t[n]=r[n])}return t}function Ir(r,e){if(r==null)return{};var t={},n=Object.keys(r),a,i;for(i=0;i<n.length;i++)a=n[i],!(e.indexOf(a)>=0)&&(t[a]=r[a]);return t}function bn(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable})),t.push.apply(t,n)}return t}function we(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?bn(Object(t),!0).forEach(function(n){wr(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):bn(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function wr(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}var ke="routes";function St(r){return r.split("?")[0].split("#")[0]}var Xt=function(e){if(!e.startsWith("http"))return!1;try{var t=new URL(e);return!!t}catch(n){return!1}},Tr=function(e){var t=e.path;if(!t||t==="/")try{return"/".concat(Ae(JSON.stringify(e)))}catch(n){}return t&&St(t)},Rr=function(e,t){var n=e.name,a=e.locale;return"locale"in e&&a===!1||!n?!1:e.locale||"".concat(t,".").concat(n)},Sn=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||t).startsWith("/")||Xt(e)?e:"/".concat(t,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},Pr=function(e,t){var n=e.menu,a=n===void 0?{}:n,i=e.indexRoute,o=e.path,u=o===void 0?"":o,s=e.children||[],f=a.name,m=f===void 0?e.name:f,g=a.icon,h=g===void 0?e.icon:g,B=a.hideChildren,A=B===void 0?e.hideChildren:B,P=a.flatMenu,T=P===void 0?e.flatMenu:P,k=i&&Object.keys(i).join(",")!=="redirect"?[we({path:u,menu:a},i)].concat(s||[]):s,H=we({},e);if(m&&(H.name=m),h&&(H.icon=h),k&&k.length){if(A)return delete H.children,H;var F=Yt(we(we({},t),{},{data:k}),e);if(T)return F;delete H[ke]}return H},it=function(e){return Array.isArray(e)&&e.length>0};function Yt(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},t=r.data,n=r.formatMessage,a=r.parentName,i=r.locale;return!t||!Array.isArray(t)?[]:t.filter(function(o){return o?it(o.children)||o.path||o.originPath||o.layout?!0:(o.redirect||o.unaccessible,!1):!1}).filter(function(o){var u,s;return!(o==null||(u=o.menu)===null||u===void 0)&&u.name||o!=null&&o.flatMenu||!(o==null||(s=o.menu)===null||s===void 0)&&s.flatMenu?!0:o.menu!==!1}).map(function(o){var u=we(we({},o),{},{path:o.path||o.originPath});return!u.children&&u[ke]&&(u.children=u[ke],delete u[ke]),u.unaccessible&&delete u.name,u.path==="*"&&(u.path="."),u.path==="/*"&&(u.path="."),!u.path&&u.originPath&&(u.path=u.originPath),u}).map(function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},u=o.children||o[ke]||[],s=Sn(o.path,e?e.path:"/"),f=o.name,m=Rr(o,a||"menu"),g=m!==!1&&i!==!1&&n&&m?n({id:m,defaultMessage:f}):f,h=e.pro_layout_parentKeys,B=h===void 0?[]:h,A=e.children,P=e.icon,T=e.flatMenu,k=e.indexRoute,H=e.routes,F=Mr(e,Qe),L=new Set([].concat(Cn(B),Cn(o.parentKeys||[])));e.key&&L.add(e.key);var O=we(we(we({},F),{},{menu:void 0},o),{},{path:s,locale:m,key:o.key||Tr(we(we({},o),{},{path:s})),pro_layout_parentKeys:Array.from(L).filter(function(j){return j&&j!=="/"})});if(g?O.name=g:delete O.name,O.menu===void 0&&delete O.menu,it(u)){var x=Yt(we(we({},r),{},{data:u,parentName:m||""}),O);it(x)&&(O.children=x)}return Pr(O,r)}).flat(1)}var Br=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(t){return t&&(t.name||it(t.children))&&!t.hideInMenu&&!t.redirect}).map(function(t){var n=we({},t),a=n.children||t[ke]||[];if(delete n[ke],it(a)&&!n.hideChildrenInMenu&&a.some(function(o){return o&&!!o.name})){var i=r(a);if(i.length)return we(we({},n),{},{children:i})}return we({},t)}).filter(function(t){return t})},jr=function(r){$e(t,r);var e=Ct(t);function t(){return xe(this,t),e.apply(this,arguments)}return ne(t,[{key:"get",value:function(a){var i;try{var o=le(this.entries()),u;try{for(o.s();!(u=o.n()).done;){var s=ut(u.value,2),f=s[0],m=s[1],g=St(f);if(!Xt(f)&&(0,me.Bo)(g,[]).test(a)){i=m;break}}}catch(h){o.e(h)}finally{o.f()}}catch(h){i=void 0}return i}}]),t}(rt(Map)),Nr=function(e){var t=new jr,n=function a(i,o){i.forEach(function(u){var s=u.children||u[ke]||[];it(s)&&a(s,u);var f=Sn(u.path,o?o.path:"/");t.set(St(f),u)})};return n(e),t},Er=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(t){var n=t.children||t[ke];if(it(n)){var a=r(n);if(a.length)return we({},t)}var i=we({},t);return delete i[ke],delete i.children,i}).filter(function(t){return t})},Lr=function(e,t,n,a){var i=Yt({data:e,formatMessage:n,locale:t}),o=a?Er(i):Br(i),u=Nr(i);return{breadcrumb:u,menuData:o}},Dr=Lr;function Zn(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable})),t.push.apply(t,n)}return t}function Zt(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Zn(Object(t),!0).forEach(function(n){Ar(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):Zn(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function Ar(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}var Hr=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t={};return e.forEach(function(n){var a=Zt({},n);if(!(!a||!a.key)){!a.children&&a[ke]&&(a.children=a[ke],delete a[ke]);var i=a.children||[];t[St(a.path||a.key||"/")]=Zt({},a),t[a.key||a.path||"/"]=Zt({},a),i&&(t=Zt(Zt({},t),r(i)))}}),t},_r=Hr,Or=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return e.filter(function(a){if(a==="/"&&t==="/")return!0;if(a!=="/"&&a!=="/*"&&a&&!Xt(a)){var i=St(a);try{if(n&&(0,me.Bo)("".concat(i)).test(t)||(0,me.Bo)("".concat(i),[]).test(t)||(0,me.Bo)("".concat(i,"/(.*)")).test(t))return!0}catch(o){}}return!1}).sort(function(a,i){return a===t?10:i===t?-10:a.substr(1).split("/").length-i.substr(1).split("/").length})},$r=function(e,t,n,a){var i=_r(t),o=Object.keys(i),u=Or(o,e||"/",a);return!u||u.length<1?[]:(n||(u=[u[u.length-1]]),u.map(function(s){var f=i[s]||{pro_layout_parentKeys:"",key:""},m=new Map,g=(f.pro_layout_parentKeys||[]).map(function(h){return m.has(h)?null:(m.set(h,!0),i[h])}).filter(function(h){return h});return f.key&&g.push(f),g}).flat(1))},kr=$r,Ve=p(21532),Mt=p(74902),Fr=p(93967),re=p.n(Fr),zr=p(98423),Qt=p(53124),Wr=p(82401),Kr=p(50344),Jt=p(56627);function Ur(r,e,t){return typeof t=="boolean"?t:r.length?!0:(0,Kr.Z)(e).some(a=>a.type===Jt.Z)}var Mn=p(24793),In=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(r);a<n.length;a++)e.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(r,n[a])&&(t[n[a]]=r[n[a]]);return t};function Et(r){let{suffixCls:e,tagName:t,displayName:n}=r;return a=>v.forwardRef((o,u)=>v.createElement(a,Object.assign({ref:u,suffixCls:e,tagName:t},o)))}const qt=v.forwardRef((r,e)=>{const{prefixCls:t,suffixCls:n,className:a,tagName:i}=r,o=In(r,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=v.useContext(Qt.E_),s=u("layout",t),[f,m,g]=(0,Mn.ZP)(s),h=n?`${s}-${n}`:s;return f(v.createElement(i,Object.assign({className:re()(t||h,a,m,g),ref:e},o)))}),Vr=v.forwardRef((r,e)=>{const{direction:t}=v.useContext(Qt.E_),[n,a]=v.useState([]),{prefixCls:i,className:o,rootClassName:u,children:s,hasSider:f,tagName:m,style:g}=r,h=In(r,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),B=(0,zr.Z)(h,["suffixCls"]),{getPrefixCls:A,layout:P}=v.useContext(Qt.E_),T=A("layout",i),k=Ur(n,s,f),[H,F,L]=(0,Mn.ZP)(T),O=re()(T,{[`${T}-has-sider`]:k,[`${T}-rtl`]:t==="rtl"},P==null?void 0:P.className,o,u,F,L),x=v.useMemo(()=>({siderHook:{addSider:j=>{a(Q=>[].concat((0,Mt.Z)(Q),[j]))},removeSider:j=>{a(Q=>Q.filter($=>$!==j))}}}),[]);return H(v.createElement(Wr.V.Provider,{value:x},v.createElement(m,Object.assign({ref:e,className:O,style:Object.assign(Object.assign({},P==null?void 0:P.style),g)},B),s)))}),Gr=Et({tagName:"div",displayName:"Layout"})(Vr),Xr=Et({suffixCls:"header",tagName:"header",displayName:"Header"})(qt),Yr=Et({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(qt),Qr=Et({suffixCls:"content",tagName:"main",displayName:"Content"})(qt);var Jr=Gr;const ft=Jr;ft.Header=Xr,ft.Footer=Yr,ft.Content=Qr,ft.Sider=Jt.Z,ft._InternalSiderContext=Jt.D;var lt=ft,wn=p(97435),Tn=p(80334),qr=p(5068),ea=p(10046),ta=p(78164),c=p(85893),na=function(e){var t=(0,v.useContext)(ee.L_),n=t.hashId,a=e.style,i=e.prefixCls,o=e.children,u=e.hasPageContainer,s=u===void 0?0:u,f=re()("".concat(i,"-content"),n,(0,l.Z)((0,l.Z)({},"".concat(i,"-has-header"),e.hasHeader),"".concat(i,"-content-has-page-container"),s>0)),m=e.ErrorBoundary||ta.S;return e.ErrorBoundary===!1?(0,c.jsx)(lt.Content,{className:f,style:a,children:o}):(0,c.jsx)(m,{children:(0,c.jsx)(lt.Content,{className:f,style:a,children:o})})},ra=function(){return(0,c.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,c.jsxs)("defs",{children:[(0,c.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,c.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,c.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,c.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,c.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,c.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,c.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,c.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,c.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,c.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,c.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,c.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,c.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,c.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,c.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,c.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,c.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,c.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,c.jsxs)("g",{children:[(0,c.jsxs)("g",{fillRule:"nonzero",children:[(0,c.jsxs)("g",{children:[(0,c.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,c.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,c.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,c.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},vt=p(87462),aa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},oa=aa,Rn=p(87646),ia=(0,v.createContext)({}),en=ia,tn=p(71002),la=p(44958),ua=p(27571);function ca(r){return r.replace(/-(.)/g,function(e,t){return t.toUpperCase()})}function nn(r,e){(0,Tn.ZP)(r,"[@ant-design/icons] ".concat(e))}function Pn(r){return(0,tn.Z)(r)==="object"&&typeof r.name=="string"&&typeof r.theme=="string"&&((0,tn.Z)(r.icon)==="object"||typeof r.icon=="function")}function Bn(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(r).reduce(function(e,t){var n=r[t];switch(t){case"class":e.className=n,delete e.class;break;default:delete e[t],e[ca(t)]=n}return e},{})}function rn(r,e,t){return t?v.createElement(r.tag,(0,d.Z)((0,d.Z)({key:e},Bn(r.attrs)),t),(r.children||[]).map(function(n,a){return rn(n,"".concat(e,"-").concat(r.tag,"-").concat(a))})):v.createElement(r.tag,(0,d.Z)({key:e},Bn(r.attrs)),(r.children||[]).map(function(n,a){return rn(n,"".concat(e,"-").concat(r.tag,"-").concat(a))}))}function jn(r){return(0,Rn.generate)(r)[0]}function Nn(r){return r?Array.isArray(r)?r:[r]:[]}var sa={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},da=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,En=function(e){var t=(0,v.useContext)(en),n=t.csp,a=t.prefixCls,i=da;a&&(i=i.replace(/anticon/g,a)),(0,v.useEffect)(function(){var o=e.current,u=(0,ua.A)(o);(0,la.hq)(i,"@ant-design-icons",{prepend:!0,csp:n,attachTo:u})},[])},fa=["icon","className","onClick","style","primaryColor","secondaryColor"],It={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function va(r){var e=r.primaryColor,t=r.secondaryColor;It.primaryColor=e,It.secondaryColor=t||jn(e),It.calculated=!!t}function ma(){return(0,d.Z)({},It)}var Lt=function(e){var t=e.icon,n=e.className,a=e.onClick,i=e.style,o=e.primaryColor,u=e.secondaryColor,s=(0,V.Z)(e,fa),f=v.useRef(),m=It;if(o&&(m={primaryColor:o,secondaryColor:u||jn(o)}),En(f),nn(Pn(t),"icon should be icon definiton, but got ".concat(t)),!Pn(t))return null;var g=t;return g&&typeof g.icon=="function"&&(g=(0,d.Z)((0,d.Z)({},g),{},{icon:g.icon(m.primaryColor,m.secondaryColor)})),rn(g.icon,"svg-".concat(g.name),(0,d.Z)((0,d.Z)({className:n,onClick:a,style:i,"data-icon":g.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},s),{},{ref:f}))};Lt.displayName="IconReact",Lt.getTwoToneColors=ma,Lt.setTwoToneColors=va;var an=Lt;function Ln(r){var e=Nn(r),t=(0,z.Z)(e,2),n=t[0],a=t[1];return an.setTwoToneColors({primaryColor:n,secondaryColor:a})}function pa(){var r=an.getTwoToneColors();return r.calculated?[r.primaryColor,r.secondaryColor]:r.primaryColor}var ga=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];Ln(Rn.blue.primary);var Dt=v.forwardRef(function(r,e){var t=r.className,n=r.icon,a=r.spin,i=r.rotate,o=r.tabIndex,u=r.onClick,s=r.twoToneColor,f=(0,V.Z)(r,ga),m=v.useContext(en),g=m.prefixCls,h=g===void 0?"anticon":g,B=m.rootClassName,A=re()(B,h,(0,l.Z)((0,l.Z)({},"".concat(h,"-").concat(n.name),!!n.name),"".concat(h,"-spin"),!!a||n.name==="loading"),t),P=o;P===void 0&&u&&(P=-1);var T=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,k=Nn(s),H=(0,z.Z)(k,2),F=H[0],L=H[1];return v.createElement("span",(0,vt.Z)({role:"img","aria-label":n.name},f,{ref:e,tabIndex:P,onClick:u,className:A}),v.createElement(an,{icon:n,primaryColor:F,secondaryColor:L,style:T}))});Dt.displayName="AntdIcon",Dt.getTwoToneColor=pa,Dt.setTwoToneColor=Ln;var Dn=Dt,ha=function(e,t){return v.createElement(Dn,(0,vt.Z)({},e,{ref:t,icon:oa}))},ya=v.forwardRef(ha),xa=ya,He=p(98082),Ca=function(e){return(0,l.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary,textDecoration:e.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:e.colorPrimary}},"&-copyright":{fontSize:"14px",color:e.colorText}})};function ba(r){return(0,He.Xj)("ProLayoutFooter",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Ca(t)]})}var Sa=function(e){var t=e.className,n=e.prefixCls,a=e.links,i=e.copyright,o=e.style,u=(0,v.useContext)(Ve.ZP.ConfigContext),s=u.getPrefixCls(n||"pro-global-footer"),f=ba(s),m=f.wrapSSR,g=f.hashId;return(a==null||a===!1||Array.isArray(a)&&a.length===0)&&(i==null||i===!1)?null:m((0,c.jsxs)("div",{className:re()(s,g,t),style:o,children:[a&&(0,c.jsx)("div",{className:"".concat(s,"-list ").concat(g).trim(),children:a.map(function(h){return(0,c.jsx)("a",{className:"".concat(s,"-list-link ").concat(g).trim(),title:h.key,target:h.blankTarget?"_blank":"_self",href:h.href,rel:"noreferrer",children:h.title},h.key)})}),i&&(0,c.jsx)("div",{className:"".concat(s,"-copyright ").concat(g).trim(),children:i})]}))},Za=lt.Footer,Ma=function(e){var t=e.links,n=e.copyright,a=e.style,i=e.className,o=e.prefixCls;return(0,c.jsx)(Za,{className:i,style:(0,d.Z)({padding:0},a),children:(0,c.jsx)(Sa,{links:t,prefixCls:o,copyright:n===!1?null:(0,c.jsxs)(v.Fragment,{children:[(0,c.jsx)(xa,{})," ",n]})})})},An=function r(e){return(e||[]).reduce(function(t,n){if(n.key&&t.push(n.key),n.children||n.routes){var a=t.concat(r(n.children||n.routes)||[]);return a}return t},[])},Hn={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function Hi(r){return r&&Hn[r]?Hn[r]:r||""}function At(r){return r.map(function(e){var t=e.children||[],n=(0,d.Z)({},e);if(!n.children&&n.routes&&(n.children=n.routes),!n.name||n.hideInMenu)return null;if(n&&n!==null&&n!==void 0&&n.children){if(!n.hideChildrenInMenu&&t.some(function(a){return a&&a.name&&!a.hideInMenu}))return(0,d.Z)((0,d.Z)({},e),{},{children:At(t)});delete n.children}return delete n.routes,n}).filter(function(e){return e})}var Ia={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},wa=Ia,Ta=function(e,t){return v.createElement(Dn,(0,vt.Z)({},e,{ref:t,icon:wa}))},Ra=v.forwardRef(Ta),Pa=Ra,Ba=p(55241),ja=function(){return(0,c.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,c.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},Na=function r(e){var t=e.appList,n=e.baseClassName,a=e.hashId,i=e.itemClick;return(0,c.jsx)("div",{className:"".concat(n,"-content ").concat(a).trim(),children:(0,c.jsx)("ul",{className:"".concat(n,"-content-list ").concat(a).trim(),children:t==null?void 0:t.map(function(o,u){var s;return o!=null&&(s=o.children)!==null&&s!==void 0&&s.length?(0,c.jsxs)("div",{className:"".concat(n,"-content-list-item-group ").concat(a).trim(),children:[(0,c.jsx)("div",{className:"".concat(n,"-content-list-item-group-title ").concat(a).trim(),children:o.title}),(0,c.jsx)(r,{hashId:a,itemClick:i,appList:o==null?void 0:o.children,baseClassName:n})]},u):(0,c.jsx)("li",{className:"".concat(n,"-content-list-item ").concat(a).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,c.jsxs)("a",{href:i?void 0:o.url,target:o.target,rel:"noreferrer",children:[ln(o.icon),(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{children:o.title}),o.desc?(0,c.jsx)("span",{children:o.desc}):null]})]})},u)})})})},on=function(e){if(!e||!e.startsWith("http"))return!1;try{var t=new URL(e);return!!t}catch(n){return!1}},Ea=function(e,t){if(e&&typeof e=="string"&&on(e))return(0,c.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,c.jsx)("div",{id:"avatarLogo",children:e});if(!e&&t&&typeof t=="string"){var n=t.substring(0,1);return(0,c.jsx)("div",{id:"avatarLogo",children:n})}return e},La=function r(e){var t=e.appList,n=e.baseClassName,a=e.hashId,i=e.itemClick;return(0,c.jsx)("div",{className:"".concat(n,"-content ").concat(a).trim(),children:(0,c.jsx)("ul",{className:"".concat(n,"-content-list ").concat(a).trim(),children:t==null?void 0:t.map(function(o,u){var s;return o!=null&&(s=o.children)!==null&&s!==void 0&&s.length?(0,c.jsxs)("div",{className:"".concat(n,"-content-list-item-group ").concat(a).trim(),children:[(0,c.jsx)("div",{className:"".concat(n,"-content-list-item-group-title ").concat(a).trim(),children:o.title}),(0,c.jsx)(r,{hashId:a,itemClick:i,appList:o==null?void 0:o.children,baseClassName:n})]},u):(0,c.jsx)("li",{className:"".concat(n,"-content-list-item ").concat(a).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,c.jsxs)("a",{href:i?"javascript:;":o.url,target:o.target,rel:"noreferrer",children:[Ea(o.icon,o.title),(0,c.jsx)("div",{children:(0,c.jsx)("div",{children:o.title})})]})},u)})})})},Da=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":He.Wf===null||He.Wf===void 0?void 0:(0,He.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Aa=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Ha=function(e){var t,n,a,i,o;return(0,l.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:(t=e.layout)===null||t===void 0?void 0:t.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:(n=e.layout)===null||n===void 0?void 0:n.colorTextAppListIconHover,backgroundColor:(a=e.layout)===null||a===void 0?void 0:a.colorBgAppListIconHover},"&-active":{color:(i=e.layout)===null||i===void 0?void 0:i.colorTextAppListIconHover,backgroundColor:(o=e.layout)===null||o===void 0?void 0:o.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,l.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":Aa(e),"&-default":Da(e)})};function _a(r){return(0,He.Xj)("AppsLogoComponents",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Ha(t)]})}var ln=function(e){return typeof e=="string"?(0,c.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):typeof e=="function"?e():e},un=function(e){var t,n=e.appList,a=e.appListRender,i=e.prefixCls,o=i===void 0?"ant-pro":i,u=e.onItemClick,s=v.useRef(null),f=v.useRef(null),m="".concat(o,"-layout-apps"),g=_a(m),h=g.wrapSSR,B=g.hashId,A=(0,v.useState)(!1),P=(0,z.Z)(A,2),T=P[0],k=P[1],H=function(j){u==null||u(j,f)},F=(0,v.useMemo)(function(){var x=n==null?void 0:n.some(function(j){return!(j!=null&&j.desc)});return x?(0,c.jsx)(La,{hashId:B,appList:n,itemClick:u?H:void 0,baseClassName:"".concat(m,"-simple")}):(0,c.jsx)(Na,{hashId:B,appList:n,itemClick:u?H:void 0,baseClassName:"".concat(m,"-default")})},[n,m,B]);if(!(e!=null&&(t=e.appList)!==null&&t!==void 0&&t.length))return null;var L=a?a(e==null?void 0:e.appList,F):F,O=(0,R.X)(void 0,function(x){return k(x)});return h((0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{ref:s,onClick:function(j){j.stopPropagation(),j.preventDefault()}}),(0,c.jsx)(Ba.Z,(0,d.Z)((0,d.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},O),{},{overlayClassName:"".concat(m,"-popover ").concat(B).trim(),content:L,getPopupContainer:function(){return s.current||document.body},children:(0,c.jsx)("span",{ref:f,onClick:function(j){j.stopPropagation()},className:re()("".concat(m,"-icon"),B,(0,l.Z)({},"".concat(m,"-icon-active"),T)),children:(0,c.jsx)(ja,{})})}))]}))},_n=p(7134),Oa=p(42075),On=p(50136);function $a(){return(0,c.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,c.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var ka=function(e){var t,n,a;return(0,l.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorTextCollapsedButton,backgroundColor:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function Fa(r){return(0,He.Xj)("SiderMenuCollapsedIcon",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ka(t)]})}var za=["isMobile","collapsed"],Wa=function(e){var t=e.isMobile,n=e.collapsed,a=(0,V.Z)(e,za),i=Fa(e.className),o=i.wrapSSR,u=i.hashId;return t&&n?null:o((0,c.jsx)("div",(0,d.Z)((0,d.Z)({},a),{},{className:re()(e.className,u,(0,l.Z)((0,l.Z)({},"".concat(e.className,"-collapsed"),n),"".concat(e.className,"-is-mobile"),t)),children:(0,c.jsx)($a,{})})))},Ka=p(43144),Ua=p(15671),Va=p(42550),Ga=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],$n=v.forwardRef(function(r,e){var t=r.className,n=r.component,a=r.viewBox,i=r.spin,o=r.rotate,u=r.tabIndex,s=r.onClick,f=r.children,m=(0,V.Z)(r,Ga),g=v.useRef(),h=(0,Va.x1)(g,e);nn(!!(n||f),"Should have `component` prop or `children`."),En(g);var B=v.useContext(en),A=B.prefixCls,P=A===void 0?"anticon":A,T=B.rootClassName,k=re()(T,P,(0,l.Z)({},"".concat(P,"-spin"),!!i&&!!n),t),H=re()((0,l.Z)({},"".concat(P,"-spin"),!!i)),F=o?{msTransform:"rotate(".concat(o,"deg)"),transform:"rotate(".concat(o,"deg)")}:void 0,L=(0,d.Z)((0,d.Z)({},sa),{},{className:H,style:F,viewBox:a});a||delete L.viewBox;var O=function(){return n?v.createElement(n,L,f):f?(nn(!!a||v.Children.count(f)===1&&v.isValidElement(f)&&v.Children.only(f).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),v.createElement("svg",(0,vt.Z)({},L,{viewBox:a}),f)):null},x=u;return x===void 0&&s&&(x=-1),v.createElement("span",(0,vt.Z)({role:"img"},m,{ref:h,tabIndex:x,onClick:s,className:k}),O())});$n.displayName="AntdIcon";var Xa=$n,Ya=["type","children"],kn=new Set;function Qa(r){return!!(typeof r=="string"&&r.length&&!kn.has(r))}function Ht(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,t=r[e];if(Qa(t)){var n=document.createElement("script");n.setAttribute("src",t),n.setAttribute("data-namespace",t),r.length>e+1&&(n.onload=function(){Ht(r,e+1)},n.onerror=function(){Ht(r,e+1)}),kn.add(t),document.body.appendChild(n)}}function Fn(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=r.scriptUrl,t=r.extraCommonProps,n=t===void 0?{}:t;e&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(e)?Ht(e.reverse()):Ht([e]));var a=v.forwardRef(function(i,o){var u=i.type,s=i.children,f=(0,V.Z)(i,Ya),m=null;return i.type&&(m=v.createElement("use",{xlinkHref:"#".concat(u)})),s&&(m=s),v.createElement(Xa,(0,vt.Z)({},n,f,{ref:o}),m)});return a.displayName="Iconfont",a}function Ja(r){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(r)}var qa=p(83062),eo=p(48054),zn={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},to=function(e,t){var n,a,i=t.includes("horizontal")?(n=e.layout)===null||n===void 0?void 0:n.header:(a=e.layout)===null||a===void 0?void 0:a.sider;return(0,d.Z)((0,d.Z)((0,l.Z)({},"".concat(e.componentCls),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({background:"transparent",color:i==null?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,l.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:i==null?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,l.Z)((0,l.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,l.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,l.Z)((0,l.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),t.includes("horizontal")?{}:(0,l.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};function no(r,e){return(0,He.Xj)("ProLayoutBaseMenu"+e,function(t){var n=(0,d.Z)((0,d.Z)({},t),{},{componentCls:".".concat(r)});return[to(n,e||"inline")]})}var Wn=function(e){var t=(0,v.useState)(e.collapsed),n=(0,z.Z)(t,2),a=n[0],i=n[1],o=(0,v.useState)(!1),u=(0,z.Z)(o,2),s=u[0],f=u[1];return(0,v.useEffect)(function(){f(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable?e.children:(0,c.jsx)(qa.Z,{title:e.title,open:a&&e.collapsed?s:!1,placement:"right",onOpenChange:f,children:e.children})},Kn=Fn({scriptUrl:zn.iconfontUrl}),Un=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",n=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if(on(e)||Ja(e))return(0,c.jsx)("img",{width:16,src:e,alt:"icon",className:n},e);if(e.startsWith(t))return(0,c.jsx)(Kn,{type:e})}return e},Vn=function(e){if(e&&typeof e=="string"){var t=e.substring(0,1).toUpperCase();return t}return null},ro=(0,Ka.Z)(function r(e){var t=this;(0,Ua.Z)(this,r),(0,l.Z)(this,"props",void 0),(0,l.Z)(this,"getNavMenuItems",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return n.map(function(o){return t.getSubMenuOrItem(o,a,i)}).filter(function(o){return o}).flat(1)}),(0,l.Z)(this,"getSubMenuOrItem",function(n,a,i){var o=t.props,u=o.subMenuItemRender,s=o.baseClassName,f=o.prefixCls,m=o.collapsed,g=o.menu,h=o.iconPrefixes,B=o.layout,A=(g==null?void 0:g.type)==="group"&&B!=="top",P=t.props.token,T=t.getIntlName(n),k=(n==null?void 0:n.children)||(n==null?void 0:n.routes),H=A&&a===0?"group":void 0;if(Array.isArray(k)&&k.length>0){var F,L,O,x,j,Q=a===0||A&&a===1,$=Un(n.icon,h,"".concat(s,"-icon ").concat((F=t.props)===null||F===void 0?void 0:F.hashId)),_=m&&Q?Vn(T):null,fe=(0,c.jsxs)("div",{className:re()("".concat(s,"-item-title"),(L=t.props)===null||L===void 0?void 0:L.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(s,"-item-title-collapsed"),m),"".concat(s,"-item-title-collapsed-level-").concat(i),m),"".concat(s,"-group-item-title"),H==="group"),"".concat(s,"-item-collapsed-show-title"),(g==null?void 0:g.collapsedShowTitle)&&m)),children:[H==="group"&&m?null:Q&&$?(0,c.jsx)("span",{className:"".concat(s,"-item-icon ").concat((O=t.props)===null||O===void 0?void 0:O.hashId).trim(),children:$}):_,(0,c.jsx)("span",{className:re()("".concat(s,"-item-text"),(x=t.props)===null||x===void 0?void 0:x.hashId,(0,l.Z)({},"".concat(s,"-item-text-has-icon"),H!=="group"&&Q&&($||_))),children:T})]}),Ce=u?u((0,d.Z)((0,d.Z)({},n),{},{isUrl:!1}),fe,t.props):fe;if(A&&a===0&&t.props.collapsed&&!g.collapsedShowGroupTitle)return t.getNavMenuItems(k,a+1,a);var b=t.getNavMenuItems(k,a+1,A&&a===0&&t.props.collapsed?a:a+1);return[{type:H,key:n.key||n.path,label:Ce,onClick:A?void 0:n.onTitleClick,children:b,className:re()((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(s,"-group"),H==="group"),"".concat(s,"-submenu"),H!=="group"),"".concat(s,"-submenu-has-icon"),H!=="group"&&Q&&$))},A&&a===0?{type:"divider",prefixCls:f,className:"".concat(s,"-divider"),key:(n.key||n.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:t.props.collapsed?"4px":"6px 16px",marginBlockStart:t.props.collapsed?4:8,borderColor:P==null||(j=P.layout)===null||j===void 0||(j=j.sider)===null||j===void 0?void 0:j.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(s,"-menu-item"),disabled:n.disabled,key:n.key||n.path,onClick:n.onTitleClick,label:t.getMenuItemPath(n,a,i)}}),(0,l.Z)(this,"getIntlName",function(n){var a=n.name,i=n.locale,o=t.props,u=o.menu,s=o.formatMessage,f=a;return i&&(u==null?void 0:u.locale)!==!1&&(f=s==null?void 0:s({id:i,defaultMessage:a})),t.props.menuTextRender?t.props.menuTextRender(n,f,t.props):f}),(0,l.Z)(this,"getMenuItemPath",function(n,a,i){var o,u,s,f,m=t.conversionPath(n.path||"/"),g=t.props,h=g.location,B=h===void 0?{pathname:"/"}:h,A=g.isMobile,P=g.onCollapse,T=g.menuItemRender,k=g.iconPrefixes,H=t.getIntlName(n),F=t.props,L=F.baseClassName,O=F.menu,x=F.collapsed,j=(O==null?void 0:O.type)==="group",Q=a===0||j&&a===1,$=Q?Un(n.icon,k,"".concat(L,"-icon ").concat((o=t.props)===null||o===void 0?void 0:o.hashId)):null,_=x&&Q?Vn(H):null,fe=(0,c.jsxs)("div",{className:re()("".concat(L,"-item-title"),(u=t.props)===null||u===void 0?void 0:u.hashId,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(L,"-item-title-collapsed"),x),"".concat(L,"-item-title-collapsed-level-").concat(i),x),"".concat(L,"-item-collapsed-show-title"),(O==null?void 0:O.collapsedShowTitle)&&x)),children:[(0,c.jsx)("span",{className:"".concat(L,"-item-icon ").concat((s=t.props)===null||s===void 0?void 0:s.hashId).trim(),style:{display:_===null&&!$?"none":""},children:$||(0,c.jsx)("span",{className:"anticon",children:_})}),(0,c.jsx)("span",{className:re()("".concat(L,"-item-text"),(f=t.props)===null||f===void 0?void 0:f.hashId,(0,l.Z)({},"".concat(L,"-item-text-has-icon"),Q&&($||_))),children:H})]},m),Ce=on(m);if(Ce){var b,be,N;fe=(0,c.jsxs)("span",{onClick:function(){var Te,ge;(Te=window)===null||Te===void 0||(ge=Te.open)===null||ge===void 0||ge.call(Te,m,"_blank")},className:re()("".concat(L,"-item-title"),(b=t.props)===null||b===void 0?void 0:b.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(L,"-item-title-collapsed"),x),"".concat(L,"-item-title-collapsed-level-").concat(i),x),"".concat(L,"-item-link"),!0),"".concat(L,"-item-collapsed-show-title"),(O==null?void 0:O.collapsedShowTitle)&&x)),children:[(0,c.jsx)("span",{className:"".concat(L,"-item-icon ").concat((be=t.props)===null||be===void 0?void 0:be.hashId).trim(),style:{display:_===null&&!$?"none":""},children:$||(0,c.jsx)("span",{className:"anticon",children:_})}),(0,c.jsx)("span",{className:re()("".concat(L,"-item-text"),(N=t.props)===null||N===void 0?void 0:N.hashId,(0,l.Z)({},"".concat(L,"-item-text-has-icon"),Q&&($||_))),children:H})]},m)}if(T){var ae=(0,d.Z)((0,d.Z)({},n),{},{isUrl:Ce,itemPath:m,isMobile:A,replace:m===B.pathname,onClick:function(){return P&&P(!0)},children:void 0});return a===0?(0,c.jsx)(Wn,{collapsed:x,title:H,disable:n.disabledTooltip,children:T(ae,fe,t.props)}):T(ae,fe,t.props)}return a===0?(0,c.jsx)(Wn,{collapsed:x,title:H,disable:n.disabledTooltip,children:fe}):fe}),(0,l.Z)(this,"conversionPath",function(n){return n&&n.indexOf("http")===0?n:"/".concat(n||"").replace(/\/+/g,"/")}),this.props=e}),ao=function(e,t){var n=t.layout,a=t.collapsed,i={};return e&&!a&&["side","mix"].includes(n||"mix")&&(i={openKeys:e}),i},Gn=function(e){var t=e.mode,n=e.className,a=e.handleOpenChange,i=e.style,o=e.menuData,u=e.prefixCls,s=e.menu,f=e.matchMenuKeys,m=e.iconfontUrl,g=e.selectedKeys,h=e.onSelect,B=e.menuRenderType,A=e.openKeys,P=(0,v.useContext)(ee.L_),T=P.dark,k=P.token,H="".concat(u,"-base-menu-").concat(t),F=(0,v.useRef)([]),L=(0,ue.Z)(s==null?void 0:s.defaultOpenAll),O=(0,z.Z)(L,2),x=O[0],j=O[1],Q=(0,ue.Z)(function(){return s!=null&&s.defaultOpenAll?An(o)||[]:A===!1?!1:[]},{value:A===!1?void 0:A,onChange:a}),$=(0,z.Z)(Q,2),_=$[0],fe=$[1],Ce=(0,ue.Z)([],{value:g,onChange:h?function(Pe){h&&Pe&&h(Pe)}:void 0}),b=(0,z.Z)(Ce,2),be=b[0],N=b[1];(0,v.useEffect)(function(){s!=null&&s.defaultOpenAll||A===!1||f&&(fe(f),N(f))},[f.join("-")]),(0,v.useEffect)(function(){m&&(Kn=Fn({scriptUrl:m}))},[m]),(0,v.useEffect)(function(){if(f.join("-")!==(be||[]).join("-")&&N(f),!x&&A!==!1&&f.join("-")!==(_||[]).join("-")){var Pe=f;(s==null?void 0:s.autoClose)===!1&&(Pe=Array.from(new Set([].concat((0,Mt.Z)(f),(0,Mt.Z)(_||[]))))),fe(Pe)}else s!=null&&s.ignoreFlatMenu&&x?fe(An(o)):j(!1)},[f.join("-")]);var ae=(0,v.useMemo)(function(){return ao(_,e)},[_&&_.join(","),e.layout,e.collapsed]),pe=no(H,t),Te=pe.wrapSSR,ge=pe.hashId,Ne=(0,v.useMemo)(function(){return new ro((0,d.Z)((0,d.Z)({},e),{},{token:k,menuRenderType:B,baseClassName:H,hashId:ge}))},[e,k,B,H,ge]);if(s!=null&&s.loading)return(0,c.jsx)("div",{style:t!=null&&t.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,c.jsx)(eo.Z,{active:!0,title:!1,paragraph:{rows:t!=null&&t.includes("inline")?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(F.current=f);var Re=e.postMenuData?e.postMenuData(o):o;return Re&&(Re==null?void 0:Re.length)<1?null:Te((0,v.createElement)(On.Z,(0,d.Z)((0,d.Z)({},ae),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:t,inlineIndent:16,defaultOpenKeys:F.current,theme:T?"dark":"light",selectedKeys:be,style:(0,d.Z)({backgroundColor:"transparent",border:"none"},i),className:re()(n,ge,H,(0,l.Z)((0,l.Z)({},"".concat(H,"-horizontal"),t==="horizontal"),"".concat(H,"-collapsed"),e.collapsed)),items:Ne.getNavMenuItems(Re,0,0),onOpenChange:function(Ke){e.collapsed||fe(Ke)}},e.menuProps)))};function oo(r,e){var t=e.stylish,n=e.proLayoutCollapsedWidth;return(0,He.Xj)("ProLayoutSiderMenuStylish",function(a){var i=(0,d.Z)((0,d.Z)({},a),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:n});return t?[(0,l.Z)({},"div".concat(a.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),t==null?void 0:t(i)))]:[]})}var io=["title","render"],lo=v.memo(function(r){return(0,c.jsx)(c.Fragment,{children:r.children})}),uo=lt.Sider,Xn=lt._InternalSiderContext,co=Xn===void 0?{Provider:lo}:Xn,cn=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",n=e.logo,a=e.title,i=e.layout,o=e[t];if(o===!1)return null;var u=ln(n),s=(0,c.jsx)("h1",{children:a!=null?a:"Ant Design Pro"});return o?o(u,e.collapsed?null:s,e):e.isMobile?null:i==="mix"&&t==="menuHeaderRender"?!1:e.collapsed?(0,c.jsx)("a",{children:u},"title"):(0,c.jsxs)("a",{children:[u,s]},"title")},Yn=function(e){var t,n=e.collapsed,a=e.originCollapsed,i=e.fixSiderbar,o=e.menuFooterRender,u=e.onCollapse,s=e.theme,f=e.siderWidth,m=e.isMobile,g=e.onMenuHeaderClick,h=e.breakpoint,B=h===void 0?"lg":h,A=e.style,P=e.layout,T=e.menuExtraRender,k=T===void 0?!1:T,H=e.links,F=e.menuContentRender,L=e.collapsedButtonRender,O=e.prefixCls,x=e.avatarProps,j=e.rightContentRender,Q=e.actionsRender,$=e.onOpenChange,_=e.stylish,fe=e.logoStyle,Ce=(0,v.useContext)(ee.L_),b=Ce.hashId,be=(0,v.useMemo)(function(){return!(m||P==="mix")},[m,P]),N="".concat(O,"-sider"),ae=64,pe=oo("".concat(N,".").concat(N,"-stylish"),{stylish:_,proLayoutCollapsedWidth:ae}),Te=re()("".concat(N),b,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(N,"-fixed"),i),"".concat(N,"-fixed-mix"),P==="mix"&&!m&&i),"".concat(N,"-collapsed"),e.collapsed),"".concat(N,"-layout-").concat(P),P&&!m),"".concat(N,"-light"),s!=="dark"),"".concat(N,"-mix"),P==="mix"&&!m),"".concat(N,"-stylish"),!!_)),ge=cn(e),Ne=k&&k(e),Re=(0,v.useMemo)(function(){return F!==!1&&(0,v.createElement)(Gn,(0,d.Z)((0,d.Z)({},e),{},{key:"base-menu",mode:n&&!m?"vertical":"inline",handleOpenChange:$,style:{width:"100%"},className:"".concat(N,"-menu ").concat(b).trim()}))},[N,b,F,$,e]),Pe=(H||[]).map(function(Ee,Ge){return{className:"".concat(N,"-link"),label:Ee,key:Ge}}),Ke=(0,v.useMemo)(function(){return F?F(e,Re):Re},[F,Re,e]),Oe=(0,v.useMemo)(function(){if(!x)return null;var Ee=x.title,Ge=x.render,Ue=(0,V.Z)(x,io),$t=(0,c.jsxs)("div",{className:"".concat(N,"-actions-avatar"),children:[Ue!=null&&Ue.src||Ue!=null&&Ue.srcSet||Ue.icon||Ue.children?(0,c.jsx)(_n.C,(0,d.Z)({size:28},Ue)):null,x.title&&!n&&(0,c.jsx)("span",{children:Ee})]});return Ge?Ge(x,$t,e):$t},[x,N,n]),Fe=(0,v.useMemo)(function(){return Q?(0,c.jsx)(Oa.Z,{align:"center",size:4,direction:n?"vertical":"horizontal",className:re()(["".concat(N,"-actions-list"),n&&"".concat(N,"-actions-list-collapsed"),b]),children:[Q==null?void 0:Q(e)].flat(1).map(function(Ee,Ge){return(0,c.jsx)("div",{className:"".concat(N,"-actions-list-item ").concat(b).trim(),children:Ee},Ge)})}):null},[Q,N,n]),Xe=(0,v.useMemo)(function(){return(0,c.jsx)(un,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.appListRender,e.prefixCls]),Je=(0,v.useMemo)(function(){if(L===!1)return null;var Ee=(0,c.jsx)(Wa,{isMobile:m,collapsed:a,className:"".concat(N,"-collapsed-button"),onClick:function(){u==null||u(!a)}});return L?L(n,Ee):Ee},[L,m,a,N,n,u]),qe=(0,v.useMemo)(function(){return!Oe&&!Fe?null:(0,c.jsxs)("div",{className:re()("".concat(N,"-actions"),b,n&&"".concat(N,"-actions-collapsed")),children:[Oe,Fe]})},[Fe,Oe,N,n,b]),et=(0,v.useMemo)(function(){var Ee;return e!=null&&(Ee=e.menu)!==null&&Ee!==void 0&&Ee.hideMenuWhenCollapsed&&n?"".concat(N,"-hide-menu-collapsed"):null},[N,n,e==null||(t=e.menu)===null||t===void 0?void 0:t.hideMenuWhenCollapsed]),wt=o&&(o==null?void 0:o(e)),Ot=(0,c.jsxs)(c.Fragment,{children:[ge&&(0,c.jsxs)("div",{className:re()([re()("".concat(N,"-logo"),b,(0,l.Z)({},"".concat(N,"-logo-collapsed"),n))]),onClick:be?g:void 0,id:"logo",style:fe,children:[ge,Xe]}),Ne&&(0,c.jsx)("div",{className:re()(["".concat(N,"-extra"),!ge&&"".concat(N,"-extra-no-logo"),b]),children:Ne}),(0,c.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:Ke}),(0,c.jsxs)(co.Provider,{value:{},children:[H?(0,c.jsx)("div",{className:"".concat(N,"-links ").concat(b).trim(),children:(0,c.jsx)(On.Z,{inlineIndent:16,className:"".concat(N,"-link-menu ").concat(b).trim(),selectedKeys:[],openKeys:[],theme:s,mode:"inline",items:Pe})}):null,be&&(0,c.jsxs)(c.Fragment,{children:[qe,!Fe&&j?(0,c.jsx)("div",{className:re()("".concat(N,"-actions"),b,(0,l.Z)({},"".concat(N,"-actions-collapsed"),n)),children:j==null?void 0:j(e)}):null]}),wt&&(0,c.jsx)("div",{className:re()(["".concat(N,"-footer"),b,(0,l.Z)({},"".concat(N,"-footer-collapsed"),n)]),children:wt})]})]});return pe.wrapSSR((0,c.jsxs)(c.Fragment,{children:[i&&!m&&!et&&(0,c.jsx)("div",{style:(0,d.Z)({width:n?ae:f,overflow:"hidden",flex:"0 0 ".concat(n?ae:f,"px"),maxWidth:n?ae:f,minWidth:n?ae:f,transition:"all 0.2s ease 0s"},A)}),(0,c.jsxs)(uo,{collapsible:!0,trigger:null,collapsed:n,breakpoint:B===!1?void 0:B,onCollapse:function(Ge){m||u==null||u(Ge)},collapsedWidth:ae,style:A,theme:s,width:f,className:re()(Te,b,et),children:[et?(0,c.jsx)("div",{className:"".concat(N,"-hide-when-collapsed ").concat(b).trim(),style:{height:"100%",width:"100%",opacity:et?0:1},children:Ot}):Ot,Je]})]}))},so=p(10178),fo=p(9220),vo=function(e){var t,n,a,i,o;return(0,l.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:(a=e.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorTextRightActionsItem,"> div":{height:"44px",color:(i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:(o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgRightActionsItemHover}}}}})};function mo(r){return(0,He.Xj)("ProLayoutRightContent",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[vo(t)]})}var po=["rightContentRender","avatarProps","actionsRender","headerContentRender"],go=["title","render"],Qn=function(e){var t=e.rightContentRender,n=e.avatarProps,a=e.actionsRender,i=e.headerContentRender,o=(0,V.Z)(e,po),u=(0,v.useContext)(Ve.ZP.ConfigContext),s=u.getPrefixCls,f="".concat(s(),"-pro-global-header"),m=mo(f),g=m.wrapSSR,h=m.hashId,B=(0,v.useState)("auto"),A=(0,z.Z)(B,2),P=A[0],T=A[1],k=(0,v.useMemo)(function(){if(!n)return null;var O=n.title,x=n.render,j=(0,V.Z)(n,go),Q=[j!=null&&j.src||j!=null&&j.srcSet||j.icon||j.children?(0,v.createElement)(_n.C,(0,d.Z)((0,d.Z)({},j),{},{size:28,key:"avatar"})):null,O?(0,c.jsx)("span",{style:{marginInlineStart:8},children:O},"name"):void 0];return x?x(n,(0,c.jsx)("div",{children:Q}),o):(0,c.jsx)("div",{children:Q})},[n]),H=a||k?function(O){var x=a&&(a==null?void 0:a(O));return!x&&!k?null:Array.isArray(x)?g((0,c.jsxs)("div",{className:"".concat(f,"-header-actions ").concat(h).trim(),children:[x.filter(Boolean).map(function(j,Q){var $=!1;if(v.isValidElement(j)){var _;$=!!(j!=null&&(_=j.props)!==null&&_!==void 0&&_["aria-hidden"])}return(0,c.jsx)("div",{className:re()("".concat(f,"-header-actions-item ").concat(h),(0,l.Z)({},"".concat(f,"-header-actions-hover"),!$)),children:j},Q)}),k&&(0,c.jsx)("span",{className:"".concat(f,"-header-actions-avatar ").concat(h).trim(),children:k})]})):g((0,c.jsxs)("div",{className:"".concat(f,"-header-actions ").concat(h).trim(),children:[x,k&&(0,c.jsx)("span",{className:"".concat(f,"-header-actions-avatar ").concat(h).trim(),children:k})]}))}:void 0,F=(0,so.D)(function(){var O=(0,K.Z)((0,G.Z)().mark(function x(j){return(0,G.Z)().wrap(function($){for(;;)switch($.prev=$.next){case 0:T(j);case 1:case"end":return $.stop()}},x)}));return function(x){return O.apply(this,arguments)}}(),160),L=H||t;return(0,c.jsx)("div",{className:"".concat(f,"-right-content ").concat(h).trim(),style:{minWidth:P,height:"100%"},children:(0,c.jsx)("div",{style:{height:"100%"},children:(0,c.jsx)(fo.Z,{onResize:function(x){var j=x.width;F.run(j)},children:L?(0,c.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:L((0,d.Z)((0,d.Z)({},o),{},{rightContentSize:P}))}):null})})})},ho=function(e){var t,n;return(0,l.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,l.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max((((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56)-12,40),"px")}})};function yo(r){return(0,He.Xj)("ProLayoutTopNavHeader",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ho(t)]})}var Jn=function(e){var t,n,a,i,o,u,s,f=(0,v.useRef)(null),m=e.onMenuHeaderClick,g=e.contentWidth,h=e.rightContentRender,B=e.className,A=e.style,P=e.headerContentRender,T=e.layout,k=e.actionsRender,H=(0,v.useContext)(Ve.ZP.ConfigContext),F=H.getPrefixCls,L=(0,v.useContext)(ee.L_),O=L.dark,x="".concat(e.prefixCls||F("pro"),"-top-nav-header"),j=yo(x),Q=j.wrapSSR,$=j.hashId,_=void 0;e.menuHeaderRender!==void 0?_="menuHeaderRender":(T==="mix"||T==="top")&&(_="headerTitleRender");var fe=cn((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),_),Ce=(0,v.useContext)(ee.L_),b=Ce.token,be=(0,v.useMemo)(function(){var N,ae,pe,Te,ge,Ne,Re,Pe,Ke,Oe,Fe,Xe,Je,qe=(0,c.jsx)(Ve.ZP,{theme:{hashed:(0,ee.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,d.Z)({},X({colorItemBg:((N=b.layout)===null||N===void 0||(N=N.header)===null||N===void 0?void 0:N.colorBgHeader)||"transparent",colorSubItemBg:((ae=b.layout)===null||ae===void 0||(ae=ae.header)===null||ae===void 0?void 0:ae.colorBgHeader)||"transparent",radiusItem:b.borderRadius,colorItemBgSelected:((pe=b.layout)===null||pe===void 0||(pe=pe.header)===null||pe===void 0?void 0:pe.colorBgMenuItemSelected)||(b==null?void 0:b.colorBgTextHover),itemHoverBg:((Te=b.layout)===null||Te===void 0||(Te=Te.header)===null||Te===void 0?void 0:Te.colorBgMenuItemHover)||(b==null?void 0:b.colorBgTextHover),colorItemBgSelectedHorizontal:((ge=b.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.colorBgMenuItemSelected)||(b==null?void 0:b.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((Ne=b.layout)===null||Ne===void 0||(Ne=Ne.header)===null||Ne===void 0?void 0:Ne.colorTextMenu)||(b==null?void 0:b.colorTextSecondary),colorItemTextHoverHorizontal:((Re=b.layout)===null||Re===void 0||(Re=Re.header)===null||Re===void 0?void 0:Re.colorTextMenuActive)||(b==null?void 0:b.colorText),colorItemTextSelectedHorizontal:((Pe=b.layout)===null||Pe===void 0||(Pe=Pe.header)===null||Pe===void 0?void 0:Pe.colorTextMenuSelected)||(b==null?void 0:b.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:((Ke=b.layout)===null||Ke===void 0||(Ke=Ke.header)===null||Ke===void 0?void 0:Ke.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:((Oe=b.layout)===null||Oe===void 0||(Oe=Oe.header)===null||Oe===void 0?void 0:Oe.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:((Fe=b.layout)===null||Fe===void 0||(Fe=Fe.header)===null||Fe===void 0?void 0:Fe.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:b==null?void 0:b.colorBgElevated,subMenuItemBg:b==null?void 0:b.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:b==null?void 0:b.colorBgElevated}))},token:{colorBgElevated:((Xe=b.layout)===null||Xe===void 0||(Xe=Xe.header)===null||Xe===void 0?void 0:Xe.colorBgHeader)||"transparent"}},children:(0,c.jsx)(Gn,(0,d.Z)((0,d.Z)((0,d.Z)({theme:O?"dark":"light"},e),{},{className:"".concat(x,"-base-menu ").concat($).trim()},e.menuProps),{},{style:(0,d.Z)({width:"100%"},(Je=e.menuProps)===null||Je===void 0?void 0:Je.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return P?P(e,qe):qe},[(t=b.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgHeader,(n=b.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgMenuItemSelected,(a=b.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorBgMenuItemHover,(i=b.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextMenu,(o=b.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorTextMenuActive,(u=b.layout)===null||u===void 0||(u=u.header)===null||u===void 0?void 0:u.colorTextMenuSelected,(s=b.layout)===null||s===void 0||(s=s.header)===null||s===void 0?void 0:s.colorBgMenuElevated,b.borderRadius,b==null?void 0:b.colorBgTextHover,b==null?void 0:b.colorTextSecondary,b==null?void 0:b.colorText,b==null?void 0:b.colorTextBase,b.colorBgElevated,O,e,x,$,P]);return Q((0,c.jsx)("div",{className:re()(x,$,B,(0,l.Z)({},"".concat(x,"-light"),!0)),style:A,children:(0,c.jsxs)("div",{ref:f,className:re()("".concat(x,"-main"),$,(0,l.Z)({},"".concat(x,"-wide"),g==="Fixed"&&T==="top")),children:[fe&&(0,c.jsxs)("div",{className:re()("".concat(x,"-main-left ").concat($)),onClick:m,children:[(0,c.jsx)(un,(0,d.Z)({},e)),(0,c.jsx)("div",{className:"".concat(x,"-logo ").concat($).trim(),id:"logo",children:fe},"logo")]}),(0,c.jsx)("div",{style:{flex:1},className:"".concat(x,"-menu ").concat($).trim(),children:be}),(h||k||e.avatarProps)&&(0,c.jsx)(Qn,(0,d.Z)((0,d.Z)({rightContentRender:h},e),{},{prefixCls:x}))]})}))},xo=function(e){var t,n,a;return(0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:((a=e.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};function Co(r){return(0,He.Xj)("ProLayoutGlobalHeader",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[xo(t)]})}var bo=function(e,t){return e===!1?null:e?e(t,null):t},So=function(e){var t=e.isMobile,n=e.logo,a=e.collapsed,i=e.onCollapse,o=e.rightContentRender,u=e.menuHeaderRender,s=e.onMenuHeaderClick,f=e.className,m=e.style,g=e.layout,h=e.children,B=e.splitMenus,A=e.menuData,P=e.prefixCls,T=(0,v.useContext)(Ve.ZP.ConfigContext),k=T.getPrefixCls,H=T.direction,F="".concat(P||k("pro"),"-global-header"),L=Co(F),O=L.wrapSSR,x=L.hashId,j=re()(f,F,x);if(g==="mix"&&!t&&B){var Q=(A||[]).map(function(Ce){return(0,d.Z)((0,d.Z)({},Ce),{},{children:void 0,routes:void 0})}),$=At(Q);return(0,c.jsx)(Jn,(0,d.Z)((0,d.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:$}))}var _=re()("".concat(F,"-logo"),x,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(F,"-logo-rtl"),H==="rtl"),"".concat(F,"-logo-mix"),g==="mix"),"".concat(F,"-logo-mobile"),t)),fe=(0,c.jsx)("span",{className:_,children:(0,c.jsx)("a",{children:ln(n)})},"logo");return O((0,c.jsxs)("div",{className:j,style:(0,d.Z)({},m),children:[t&&(0,c.jsx)("span",{className:"".concat(F,"-collapsed-button ").concat(x).trim(),onClick:function(){i==null||i(!a)},children:(0,c.jsx)(Pa,{})}),t&&bo(u,fe),g==="mix"&&!t&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(un,(0,d.Z)({},e)),(0,c.jsx)("div",{className:_,onClick:s,children:cn((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,c.jsx)("div",{style:{flex:1},children:h}),(o||e.actionsRender||e.avatarProps)&&(0,c.jsx)(Qn,(0,d.Z)({rightContentRender:o},e))]}))},Zo=function(e){var t,n,a,i;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat(((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:((a=e.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:((i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function Mo(r){return(0,He.Xj)("ProLayoutHeader",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Zo(t)]})}function Io(r,e){var t=e.stylish,n=e.proLayoutCollapsedWidth;return(0,He.Xj)("ProLayoutHeaderStylish",function(a){var i=(0,d.Z)((0,d.Z)({},a),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:n});return t?[(0,l.Z)({},"div".concat(a.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),t==null?void 0:t(i)))]:[]})}var qn=lt.Header,wo=function(e){var t,n,a,i=e.isMobile,o=e.fixedHeader,u=e.className,s=e.style,f=e.collapsed,m=e.prefixCls,g=e.onCollapse,h=e.layout,B=e.headerRender,A=e.headerContentRender,P=(0,v.useContext)(ee.L_),T=P.token,k=(0,v.useContext)(Ve.ZP.ConfigContext),H=(0,v.useState)(!1),F=(0,z.Z)(H,2),L=F[0],O=F[1],x=o||h==="mix",j=(0,v.useCallback)(function(){var N=h==="top",ae=At(e.menuData||[]),pe=(0,c.jsx)(So,(0,d.Z)((0,d.Z)({onCollapse:g},e),{},{menuData:ae,children:A&&A(e,null)}));return N&&!i&&(pe=(0,c.jsx)(Jn,(0,d.Z)((0,d.Z)({mode:"horizontal",onCollapse:g},e),{},{menuData:ae}))),B&&typeof B=="function"?B(e,pe):pe},[A,B,i,h,g,e]);(0,v.useEffect)(function(){var N,ae=(k==null||(N=k.getTargetContainer)===null||N===void 0?void 0:N.call(k))||document.body,pe=function(){var ge,Ne=ae.scrollTop;return Ne>(((ge=T.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.heightLayoutHeader)||56)&&!L?(O(!0),!0):(L&&O(!1),!1)};if(x&&typeof window!="undefined")return ae.addEventListener("scroll",pe,{passive:!0}),function(){ae.removeEventListener("scroll",pe)}},[(t=T.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader,x,L]);var Q=h==="top",$="".concat(m,"-layout-header"),_=Mo($),fe=_.wrapSSR,Ce=_.hashId,b=Io("".concat($,".").concat($,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),be=re()(u,Ce,$,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat($,"-fixed-header"),x),"".concat($,"-fixed-header-scroll"),L),"".concat($,"-mix"),h==="mix"),"".concat($,"-fixed-header-action"),!f),"".concat($,"-top-menu"),Q),"".concat($,"-header"),!0),"".concat($,"-stylish"),!!e.stylish));return h==="side"&&!i?null:b.wrapSSR(fe((0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(Ve.ZP,{theme:{hashed:(0,ee.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[x&&(0,c.jsx)(qn,{style:(0,d.Z)({height:((n=T.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat(((a=T.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},s)}),(0,c.jsx)(qn,{className:be,style:s,children:j()})]})})))},To=p(74330),Ro=["isLoading","pastDelay","timedOut","error","retry"],Po=function(e){var t=e.isLoading,n=e.pastDelay,a=e.timedOut,i=e.error,o=e.retry,u=(0,V.Z)(e,Ro);return(0,c.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,c.jsx)(To.Z,(0,d.Z)({size:"large"},u))})},Bo=p(85265),jo=p(11568),er=new jo.E4("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),No=function(e){var t,n,a,i,o,u,s,f,m,g,h,B;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:((t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorMenuBackground)||"transparent"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.paddingInlineLayoutMenu,paddingBlock:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat((u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:(s=e.layout)===null||s===void 0||(s=s.sider)===null||s===void 0?void 0:s.colorTextMenuTitle,animationName:er,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,l.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:(m=e.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:(g=e.layout)===null||g===void 0||(g=g.sider)===null||g===void 0?void 0:g.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:er,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat(((h=e.layout)===null||h===void 0||(h=h.header)===null||h===void 0?void 0:h.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat(((B=e.layout)===null||B===void 0||(B=B.header)===null||B===void 0?void 0:B.heightLayoutHeader)||56,"px")}}))};function Eo(r,e){var t=e.proLayoutCollapsedWidth;return(0,He.Xj)("ProLayoutSiderMenu",function(n){var a=(0,d.Z)((0,d.Z)({},n),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:t});return[No(a)]})}var tr=function(e){var t,n=e.isMobile,a=e.siderWidth,i=e.collapsed,o=e.onCollapse,u=e.style,s=e.className,f=e.hide,m=e.prefixCls,g=e.getContainer,h=(0,v.useContext)(ee.L_),B=h.token;(0,v.useEffect)(function(){n===!0&&(o==null||o(!0))},[n]);var A=(0,wn.Z)(e,["className","style"]),P=v.useContext(Ve.ZP.ConfigContext),T=P.direction,k=Eo("".concat(m,"-sider"),{proLayoutCollapsedWidth:64}),H=k.wrapSSR,F=k.hashId,L=re()("".concat(m,"-sider"),s,F);if(f)return null;var O=(0,R.X)(!i,function(){return o==null?void 0:o(!0)});return H(n?(0,c.jsx)(Bo.Z,(0,d.Z)((0,d.Z)({placement:T==="rtl"?"right":"left",className:re()("".concat(m,"-drawer-sider"),s)},O),{},{style:(0,d.Z)({padding:0,height:"100vh"},u),onClose:function(){o==null||o(!0)},maskClosable:!0,closable:!1,getContainer:g||!1,width:a,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:(t=B.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorMenuBackground}},children:(0,c.jsx)(Yn,(0,d.Z)((0,d.Z)({},A),{},{isMobile:!0,className:L,collapsed:n?!1:i,splitMenus:!1,originCollapsed:i}))})):(0,c.jsx)(Yn,(0,d.Z)((0,d.Z)({className:L,originCollapsed:i},A),{},{style:u})))},nr=p(76509),sn=p(16305),Lo=function(e,t,n){if(n){var a=(0,Mt.Z)(n.keys()).find(function(o){try{return o.startsWith("http")?!1:(0,sn.match)(o)(e)}catch(u){return console.log("key",o,u),!1}});if(a)return n.get(a)}if(t){var i=Object.keys(t).find(function(o){try{return o!=null&&o.startsWith("http")?!1:(0,sn.match)(o)(e)}catch(u){return console.log("key",o,u),!1}});if(i)return t[i]}return{path:""}},dn=function(e,t){var n=e.pathname,a=n===void 0?"/":n,i=e.breadcrumb,o=e.breadcrumbMap,u=e.formatMessage,s=e.title,f=e.menu,m=f===void 0?{locale:!1}:f,g=t?"":s||"",h=Lo(a,i,o);if(!h)return{title:g,id:"",pageName:g};var B=h.name;return m.locale!==!1&&h.locale&&u&&(B=u({id:h.locale||"",defaultMessage:h.name})),B?t||!s?{title:B,id:h.locale||"",pageName:B}:{title:"".concat(B," - ").concat(s),id:h.locale||"",pageName:B}:{title:g,id:h.locale||"",pageName:g}},_i=function(e,t){return dn(e,t).title},Do={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},Ao=(0,d.Z)({},Do),Ho={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},_o=(0,d.Z)({},Ho),Oo={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},$o=(0,d.Z)({},Oo),ko={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Fo=(0,d.Z)({},ko),zo={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Wo=(0,d.Z)({},zo),rr={"zh-CN":Fo,"zh-TW":Wo,"en-US":Ao,"it-IT":_o,"ko-KR":$o},Ko=function(){if(!(0,oe.j)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},Uo=function(){var e=Ko();return rr[e]||rr["zh-CN"]},_t=p(67159),mt=p(34155),Vo=function(){var e;return typeof mt=="undefined"?_t.Z:((e=mt)===null||mt===void 0||(mt={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||mt===void 0?void 0:mt.ANTD_VERSION)||_t.Z},Go=function(e){var t,n,a,i,o,u,s,f,m,g,h,B,A,P,T,k,H,F,L,O,x,j,Q,$,_,fe,Ce,b,be,N,ae,pe;return(t=Vo())!==null&&t!==void 0&&t.startsWith("5")?{}:(0,l.Z)((0,l.Z)((0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(x={color:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorTextMenu},(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(x,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,l.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:(s=e.layout)===null||s===void 0||(s=s.sider)===null||s===void 0?void 0:s.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat((m=e.layout)===null||m===void 0||(m=m.header)===null||m===void 0?void 0:m.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(g=e.layout)===null||g===void 0||(g=g.sider)===null||g===void 0?void 0:g.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:(h=e.layout)===null||h===void 0||(h=h.sider)===null||h===void 0?void 0:h.colorTextMenuSelected}),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(x,"".concat(e.antCls,"-menu-submenu-selected"),{color:(B=e.layout)===null||B===void 0||(B=B.sider)===null||B===void 0?void 0:B.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:(A=e.layout)===null||A===void 0||(A=A.sider)===null||A===void 0?void 0:A.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:(P=e.layout)===null||P===void 0||(P=P.sider)===null||P===void 0?void 0:P.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:(T=e.layout)===null||T===void 0||(T=T.sider)===null||T===void 0?void 0:T.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:(k=e.layout)===null||k===void 0||(k=k.header)===null||k===void 0?void 0:k.colorTextMenuActive,backgroundColor:"".concat((H=e.layout)===null||H===void 0||(H=H.header)===null||H===void 0?void 0:H.colorBgMenuItemHover," !important")}),"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,l.Z)({backgroundColor:(F=e.layout)===null||F===void 0||(F=F.header)===null||F===void 0?void 0:F.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat((L=e.layout)===null||L===void 0||(L=L.header)===null||L===void 0?void 0:L.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat((O=e.layout)===null||O===void 0||(O=O.header)===null||O===void 0?void 0:O.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,l.Z)((0,l.Z)({},"&".concat(e.antCls,"-menu"),(0,l.Z)({color:(j=e.layout)===null||j===void 0||(j=j.header)===null||j===void 0?void 0:j.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(Q=e.layout)===null||Q===void 0||(Q=Q.header)===null||Q===void 0?void 0:Q.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:($=e.layout)===null||$===void 0||($=$.header)===null||$===void 0?void 0:$.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(_=e.layout)===null||_===void 0||(_=_.header)===null||_===void 0?void 0:_.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:(fe=e.layout)===null||fe===void 0||(fe=fe.header)===null||fe===void 0?void 0:fe.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:(Ce=e.layout)===null||Ce===void 0||(Ce=Ce.header)===null||Ce===void 0?void 0:Ce.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,l.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:(b=e.layout)===null||b===void 0||(b=b.sider)===null||b===void 0?void 0:b.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:(be=e.layout)===null||be===void 0||(be=be.sider)===null||be===void 0?void 0:be.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:(N=e.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected}),"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(ae=e.layout)===null||ae===void 0||(ae=ae.sider)===null||ae===void 0?void 0:ae.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(pe=e.layout)===null||pe===void 0||(pe=pe.sider)===null||pe===void 0?void 0:pe.colorTextMenuActive}))))},Xo=function(e){var t,n,a,i;return(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:((t=e.layout)===null||t===void 0||(t=t.pageContainer)===null||t===void 0?void 0:t.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:(n=e.layout)===null||n===void 0||(n=n.pageContainer)===null||n===void 0?void 0:n.paddingBlockPageContainerContent,paddingInline:(a=e.layout)===null||a===void 0||(a=a.pageContainer)===null||a===void 0?void 0:a.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:(i=e.layout)===null||i===void 0?void 0:i.bgLayout}))};function Yo(r){return(0,He.Xj)("ProLayout",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Xo(t),Go(t)]})}function Qo(r){if(!r||r==="/")return["/"];var e=r.split("/").filter(function(t){return t});return e.map(function(t,n){return"/".concat(e.slice(0,n+1).join("/"))})}var pt=p(34155),Jo=function(){var e;return typeof pt=="undefined"?_t.Z:((e=pt)===null||pt===void 0||(pt={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||pt===void 0?void 0:pt.ANTD_VERSION)||_t.Z},qo=function(e,t,n){var a=e,i=a.breadcrumbName,o=a.title,u=a.path,s=n.findIndex(function(f){return f.linkPath===e.path})===n.length-1;return s?(0,c.jsx)("span",{children:o||i}):(0,c.jsx)("span",{onClick:u?function(){return location.href=u}:void 0,children:o||i})},ei=function(e,t){var n=t.formatMessage,a=t.menu;return e.locale&&n&&(a==null?void 0:a.locale)!==!1?n({id:e.locale,defaultMessage:e.name}):e.name},ti=function(e,t){var n=e.get(t);if(!n){var a=Array.from(e.keys())||[],i=a.find(function(o){try{return o!=null&&o.startsWith("http")?!1:(0,sn.match)(o.replace("?",""))(t)}catch(u){return console.log("path",o,u),!1}});i&&(n=e.get(i))}return n||{path:""}},ni=function(e){var t=e.location,n=e.breadcrumbMap;return{location:t,breadcrumbMap:n}},ri=function(e,t,n){var a=Qo(e==null?void 0:e.pathname),i=a.map(function(o){var u=ti(t,o),s=ei(u,n),f=u.hideInBreadcrumb;return s&&!f?{linkPath:o,breadcrumbName:s,title:s,component:u.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(o){return o&&o.linkPath});return i},ai=function(e){var t=ni(e),n=t.location,a=t.breadcrumbMap;return n&&n.pathname&&a?ri(n,a,e):[]},oi=function(e,t){var n=e.breadcrumbRender,a=e.itemRender,i=t.breadcrumbProps||{},o=i.minLength,u=o===void 0?2:o,s=ai(e),f=function(h){for(var B=a||qo,A=arguments.length,P=new Array(A>1?A-1:0),T=1;T<A;T++)P[T-1]=arguments[T];return B==null?void 0:B.apply(void 0,[(0,d.Z)((0,d.Z)({},h),{},{path:h.linkPath||h.path})].concat(P))},m=s;return n&&(m=n(m||[])||void 0),(m&&m.length<u||n===!1)&&(m=void 0),(0,D.n)(Jo(),"5.3.0")>-1?{items:m,itemRender:f}:{routes:m,itemRender:f}};function ii(r){return(0,Mt.Z)(r).reduce(function(e,t){var n=(0,z.Z)(t,2),a=n[0],i=n[1];return e[a]=i,e},{})}var li=function r(e,t,n,a){var i=Dr(e,(t==null?void 0:t.locale)||!1,n,!0),o=i.menuData,u=i.breadcrumb;return a?r(a(o),t,n,void 0):{breadcrumb:ii(u),breadcrumbMap:u,menuData:o}},ui=p(51812),ci=function(e){var t=(0,v.useState)({}),n=(0,z.Z)(t,2),a=n[0],i=n[1];return(0,v.useEffect)(function(){i((0,ui.Y)({layout:(0,tn.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),a},si=["id","defaultMessage"],di=["fixSiderbar","navTheme","layout"],ar=0,fi=function(e,t){var n;return e.headerRender===!1||e.pure?null:(0,c.jsx)(wo,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{stylish:(n=e.stylish)===null||n===void 0?void 0:n.header}))},vi=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,d.Z)({},e),(0,c.jsx)(Ma,{})):null},mi=function(e,t){var n,a=e.layout,i=e.isMobile,o=e.selectedKeys,u=e.openKeys,s=e.splitMenus,f=e.suppressSiderWhenMenuEmpty,m=e.menuRender;if(e.menuRender===!1||e.pure)return null;var g=e.menuData;if(s&&(u!==!1||a==="mix")&&!i){var h=o||t,B=(0,z.Z)(h,1),A=B[0];if(A){var P;g=((P=e.menuData)===null||P===void 0||(P=P.find(function(F){return F.key===A}))===null||P===void 0?void 0:P.children)||[]}else g=[]}var T=At(g||[]);if(T&&(T==null?void 0:T.length)<1&&(s||f))return null;if(a==="top"&&!i){var k;return(0,c.jsx)(tr,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{hide:!0,stylish:(k=e.stylish)===null||k===void 0?void 0:k.sider}))}var H=(0,c.jsx)(tr,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{menuData:T,stylish:(n=e.stylish)===null||n===void 0?void 0:n.sider}));return m?m(e,H):H},pi=function(e,t){var n=t.pageTitleRender,a=dn(e);if(n===!1)return{title:t.title||"",id:"",pageName:""};if(n){var i=n(e,a.title,a);if(typeof i=="string")return dn((0,d.Z)((0,d.Z)({},a),{},{title:i}));(0,Tn.ZP)(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return a},gi=function(e,t,n){return e?t?64:n:0},hi=function(e){var t,n,a,i,o,u,s,f,m,g,h,B,A,P,T=e||{},k=T.children,H=T.onCollapse,F=T.location,L=F===void 0?{pathname:"/"}:F,O=T.contentStyle,x=T.route,j=T.defaultCollapsed,Q=T.style,$=T.siderWidth,_=T.menu,fe=T.siderMenuType,Ce=T.isChildrenLayout,b=T.menuDataRender,be=T.actionRef,N=T.bgLayoutImgList,ae=T.formatMessage,pe=T.loading,Te=(0,v.useMemo)(function(){return $||(e.layout==="mix"?215:256)},[e.layout,$]),ge=(0,v.useContext)(Ve.ZP.ConfigContext),Ne=(t=e.prefixCls)!==null&&t!==void 0?t:ge.getPrefixCls("pro"),Re=(0,ue.Z)(!1,{value:_==null?void 0:_.loading,onChange:_==null?void 0:_.onLoadingChange}),Pe=(0,z.Z)(Re,2),Ke=Pe[0],Oe=Pe[1],Fe=(0,v.useState)(function(){return ar+=1,"pro-layout-".concat(ar)}),Xe=(0,z.Z)(Fe,1),Je=Xe[0],qe=(0,v.useCallback)(function(Le){var tt=Le.id,zt=Le.defaultMessage,jt=(0,V.Z)(Le,si);if(ae)return ae((0,d.Z)({id:tt,defaultMessage:zt},jt));var Nt=Uo();return Nt[tt]?Nt[tt]:zt},[ae]),et=(0,qr.ZP)([Je,_==null?void 0:_.params],function(){var Le=(0,K.Z)((0,G.Z)().mark(function tt(zt){var jt,Nt,xr,Cr;return(0,G.Z)().wrap(function(xt){for(;;)switch(xt.prev=xt.next){case 0:return Nt=(0,z.Z)(zt,2),xr=Nt[1],Oe(!0),xt.next=4,_==null||(jt=_.request)===null||jt===void 0?void 0:jt.call(_,xr||{},(x==null?void 0:x.children)||(x==null?void 0:x.routes)||[]);case 4:return Cr=xt.sent,Oe(!1),xt.abrupt("return",Cr);case 7:case"end":return xt.stop()}},tt)}));return function(tt){return Le.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),wt=et.data,Ot=et.mutate,Ee=et.isLoading;(0,v.useEffect)(function(){Oe(Ee)},[Ee]);var Ge=(0,ea.kY)(),Ue=Ge.cache;(0,v.useEffect)(function(){return function(){Ue instanceof Map&&Ue.delete(Je)}},[]);var $t=(0,v.useMemo)(function(){return li(wt||(x==null?void 0:x.children)||(x==null?void 0:x.routes)||[],_,qe,b)},[qe,_,b,wt,x==null?void 0:x.children,x==null?void 0:x.routes]),fn=$t||{},xi=fn.breadcrumb,or=fn.breadcrumbMap,ir=fn.menuData,Tt=ir===void 0?[]:ir;be&&_!==null&&_!==void 0&&_.request&&(be.current={reload:function(){Ot()}});var Rt=(0,v.useMemo)(function(){return kr(L.pathname||"/",Tt||[],!0)},[L.pathname,Tt]),vn=(0,v.useMemo)(function(){return Array.from(new Set(Rt.map(function(Le){return Le.key||Le.path||""})))},[Rt]),lr=Rt[Rt.length-1]||{},ur=ci(lr),kt=(0,d.Z)((0,d.Z)({},e),ur),Ci=kt.fixSiderbar,Oi=kt.navTheme,Pt=kt.layout,bi=(0,V.Z)(kt,di),gt=ce(),ht=(0,v.useMemo)(function(){return(gt==="sm"||gt==="xs")&&!e.disableMobile},[gt,e.disableMobile]),Si=Pt!=="top"&&!ht,Zi=(0,ue.Z)(function(){return j!==void 0?j:!!(ht||gt==="md")},{value:e.collapsed,onChange:H}),cr=(0,z.Z)(Zi,2),Bt=cr[0],sr=cr[1],yt=(0,wn.Z)((0,d.Z)((0,d.Z)((0,d.Z)({prefixCls:Ne},e),{},{siderWidth:Te},ur),{},{formatMessage:qe,breadcrumb:xi,menu:(0,d.Z)((0,d.Z)({},_),{},{type:fe||(_==null?void 0:_.type),loading:Ke}),layout:Pt}),["className","style","breadcrumbRender"]),mn=pi((0,d.Z)((0,d.Z)({pathname:L.pathname},yt),{},{breadcrumbMap:or}),e),Mi=oi((0,d.Z)((0,d.Z)({},yt),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:or}),e),Ft=mi((0,d.Z)((0,d.Z)({},yt),{},{menuData:Tt,onCollapse:sr,isMobile:ht,collapsed:Bt}),vn),pn=fi((0,d.Z)((0,d.Z)({},yt),{},{children:null,hasSiderMenu:!!Ft,menuData:Tt,isMobile:ht,collapsed:Bt,onCollapse:sr}),vn),dr=vi((0,d.Z)({isMobile:ht,collapsed:Bt},yt)),Ii=(0,v.useContext)(nr.X),wi=Ii.isChildrenLayout,gn=Ce!==void 0?Ce:wi,Ye="".concat(Ne,"-layout"),fr=Yo(Ye),Ti=fr.wrapSSR,hn=fr.hashId,Ri=re()(e.className,hn,"ant-design-pro",Ye,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"screen-".concat(gt),gt),"".concat(Ye,"-top-menu"),Pt==="top"),"".concat(Ye,"-is-children"),gn),"".concat(Ye,"-fix-siderbar"),Ci),"".concat(Ye,"-").concat(Pt),Pt)),Pi=gi(!!Si,Bt,Te),vr={position:"relative"};(gn||O&&O.minHeight)&&(vr.minHeight=0),(0,v.useEffect)(function(){var Le;(Le=e.onPageChange)===null||Le===void 0||Le.call(e,e.location)},[L.pathname,(n=L.pathname)===null||n===void 0?void 0:n.search]);var Bi=(0,v.useState)(!1),mr=(0,z.Z)(Bi,2),pr=mr[0],ji=mr[1],Ni=(0,v.useState)(0),gr=(0,z.Z)(Ni,2),hr=gr[0],Ei=gr[1];w(mn,e.title||!1);var Li=(0,v.useContext)(ee.L_),q=Li.token,yr=(0,v.useMemo)(function(){return N&&N.length>0?N==null?void 0:N.map(function(Le,tt){return(0,c.jsx)("img",{src:Le.src,style:(0,d.Z)({position:"absolute"},Le)},tt)}):null},[N]);return Ti((0,c.jsx)(nr.X.Provider,{value:(0,d.Z)((0,d.Z)({},yt),{},{breadcrumb:Mi,menuData:Tt,isMobile:ht,collapsed:Bt,hasPageContainer:hr,setHasPageContainer:Ei,isChildrenLayout:!0,title:mn.pageName,hasSiderMenu:!!Ft,hasHeader:!!pn,siderWidth:Pi,hasFooter:!!dr,hasFooterToolbar:pr,setHasFooterToolbar:ji,pageTitleInfo:mn,matchMenus:Rt,matchMenuKeys:vn,currentMenu:lr}),children:e.pure?(0,c.jsx)(c.Fragment,{children:k}):(0,c.jsxs)("div",{className:Ri,children:[yr||(a=q.layout)!==null&&a!==void 0&&a.bgLayout?(0,c.jsx)("div",{className:re()("".concat(Ye,"-bg-list"),hn),children:yr}):null,(0,c.jsxs)(lt,{style:(0,d.Z)({minHeight:"100%",flexDirection:Ft?"row":void 0},Q),children:[(0,c.jsx)(Ve.ZP,{theme:{hashed:(0,ee.nu)(),token:{controlHeightLG:((i=q.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.menuHeight)||(q==null?void 0:q.controlHeightLG)},components:{Menu:X({colorItemBg:((o=q.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorMenuBackground)||"transparent",colorSubItemBg:((u=q.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorMenuBackground)||"transparent",radiusItem:q.borderRadius,colorItemBgSelected:((s=q.layout)===null||s===void 0||(s=s.sider)===null||s===void 0?void 0:s.colorBgMenuItemSelected)||(q==null?void 0:q.colorBgTextHover),colorItemBgHover:((f=q.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorBgMenuItemHover)||(q==null?void 0:q.colorBgTextHover),colorItemBgActive:((m=q.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorBgMenuItemActive)||(q==null?void 0:q.colorBgTextActive),colorItemBgSelectedHorizontal:((g=q.layout)===null||g===void 0||(g=g.sider)===null||g===void 0?void 0:g.colorBgMenuItemSelected)||(q==null?void 0:q.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((h=q.layout)===null||h===void 0||(h=h.sider)===null||h===void 0?void 0:h.colorTextMenu)||(q==null?void 0:q.colorTextSecondary),colorItemTextHover:((B=q.layout)===null||B===void 0||(B=B.sider)===null||B===void 0?void 0:B.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:((A=q.layout)===null||A===void 0||(A=A.sider)===null||A===void 0?void 0:A.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:q==null?void 0:q.colorBgElevated,subMenuItemBg:q==null?void 0:q.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:q==null?void 0:q.colorBgElevated})}},children:Ft}),(0,c.jsxs)("div",{style:vr,className:"".concat(Ye,"-container ").concat(hn).trim(),children:[pn,(0,c.jsx)(na,(0,d.Z)((0,d.Z)({hasPageContainer:hr,isChildrenLayout:gn},bi),{},{hasHeader:!!pn,prefixCls:Ye,style:O,children:pe?(0,c.jsx)(Po,{}):k})),dr,pr&&(0,c.jsx)("div",{className:"".concat(Ye,"-has-footer"),style:{height:64,marginBlockStart:(P=q.layout)===null||P===void 0||(P=P.pageContainer)===null||P===void 0?void 0:P.paddingBlockPageContainerContent}})]})]})]})}))},yi=function(e){var t=e.colorPrimary,n=e.navTheme!==void 0?{dark:e.navTheme==="realDark"}:{};return(0,c.jsx)(Ve.ZP,{theme:t?{token:{colorPrimary:t}}:void 0,children:(0,c.jsx)(ee._Y,(0,d.Z)((0,d.Z)({},n),{},{token:e.token,prefixCls:e.prefixCls,children:(0,c.jsx)(hi,(0,d.Z)((0,d.Z)({logo:(0,c.jsx)(ra,{})},zn),{},{location:(0,oe.j)()?window.location:void 0},e))}))})}},76509:function(he,Y,p){"use strict";p.d(Y,{X:function(){return G}});var l=p(67294),G=(0,l.createContext)({})},87646:function(he,Y,p){"use strict";p.r(Y),p.d(Y,{blue:function(){return E},blueDark:function(){return We},cyan:function(){return y},cyanDark:function(){return Ae},geekblue:function(){return I},geekblueDark:function(){return Qe},generate:function(){return R},gold:function(){return ie},goldDark:function(){return je},gray:function(){return ye},green:function(){return C},greenDark:function(){return ze},grey:function(){return M},greyDark:function(){return st},lime:function(){return Z},limeDark:function(){return _e},magenta:function(){return W},magentaDark:function(){return ct},orange:function(){return te},orangeDark:function(){return De},presetDarkPalettes:function(){return Wt},presetPalettes:function(){return Ie},presetPrimaryColors:function(){return X},purple:function(){return S},purpleDark:function(){return ut},red:function(){return me},redDark:function(){return Be},volcano:function(){return U},volcanoDark:function(){return Me},yellow:function(){return de},yellowDark:function(){return Ze}});var l=p(86500),G=p(1350),K=2,V=.16,z=.05,d=.05,ee=.15,ue=5,v=4,ve=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function se(le){var xe=le.r,Se=le.g,ne=le.b,$e=(0,l.py)(xe,Se,ne);return{h:$e.h*360,s:$e.s,v:$e.v}}function J(le){var xe=le.r,Se=le.g,ne=le.b;return"#".concat((0,l.vq)(xe,Se,ne,!1))}function ce(le,xe,Se){var ne=Se/100,$e={r:(xe.r-le.r)*ne+le.r,g:(xe.g-le.g)*ne+le.g,b:(xe.b-le.b)*ne+le.b};return $e}function oe(le,xe,Se){var ne;return Math.round(le.h)>=60&&Math.round(le.h)<=240?ne=Se?Math.round(le.h)-K*xe:Math.round(le.h)+K*xe:ne=Se?Math.round(le.h)+K*xe:Math.round(le.h)-K*xe,ne<0?ne+=360:ne>=360&&(ne-=360),ne}function w(le,xe,Se){if(le.h===0&&le.s===0)return le.s;var ne;return Se?ne=le.s-V*xe:xe===v?ne=le.s+V:ne=le.s+z*xe,ne>1&&(ne=1),Se&&xe===ue&&ne>.1&&(ne=.1),ne<.06&&(ne=.06),Number(ne.toFixed(2))}function D(le,xe,Se){var ne;return Se?ne=le.v+d*xe:ne=le.v-ee*xe,ne>1&&(ne=1),Number(ne.toFixed(2))}function R(le){for(var xe=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Se=[],ne=(0,G.uA)(le),$e=ue;$e>0;$e-=1){var Ct=se(ne),Kt=J((0,G.uA)({h:oe(Ct,$e,!0),s:w(Ct,$e,!0),v:D(Ct,$e,!0)}));Se.push(Kt)}Se.push(J(ne));for(var nt=1;nt<=v;nt+=1){var rt=se(ne),dt=J((0,G.uA)({h:oe(rt,nt),s:w(rt,nt),v:D(rt,nt)}));Se.push(dt)}return xe.theme==="dark"?ve.map(function(bt){var Ut=bt.index,at=bt.opacity,ot=J(ce((0,G.uA)(xe.backgroundColor||"#141414"),(0,G.uA)(Se[Ut]),at*100));return ot}):Se}var X={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},me=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];me.primary=me[5];var U=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];U.primary=U[5];var te=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];te.primary=te[5];var ie=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];ie.primary=ie[5];var de=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];de.primary=de[5];var Z=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];Z.primary=Z[5];var C=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];C.primary=C[5];var y=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];y.primary=y[5];var E=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];E.primary=E[5];var I=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];I.primary=I[5];var S=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];S.primary=S[5];var W=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];W.primary=W[5];var M=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];M.primary=M[5];var ye=M,Ie={red:me,volcano:U,orange:te,gold:ie,yellow:de,lime:Z,green:C,cyan:y,blue:E,geekblue:I,purple:S,magenta:W,grey:M},Be=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];Be.primary=Be[5];var Me=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];Me.primary=Me[5];var De=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];De.primary=De[5];var je=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];je.primary=je[5];var Ze=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];Ze.primary=Ze[5];var _e=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];_e.primary=_e[5];var ze=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];ze.primary=ze[5];var Ae=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];Ae.primary=Ae[5];var We=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];We.primary=We[5];var Qe=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];Qe.primary=Qe[5];var ut=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];ut.primary=ut[5];var ct=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];ct.primary=ct[5];var st=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];st.primary=st[5];var Wt={red:Be,volcano:Me,orange:De,gold:je,yellow:Ze,lime:_e,green:ze,cyan:Ae,blue:We,geekblue:Qe,purple:ut,magenta:ct,grey:st}},90743:function(he,Y){var p;function l(w){"@babel/helpers - typeof";return l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},l(w)}p={value:!0},Y.Bo=p=p=p=p=p=p=void 0;function G(w){for(var D=[],R=0;R<w.length;){var X=w[R];if(X==="*"||X==="+"||X==="?"){D.push({type:"MODIFIER",index:R,value:w[R++]});continue}if(X==="\\"){D.push({type:"ESCAPED_CHAR",index:R++,value:w[R++]});continue}if(X==="{"){D.push({type:"OPEN",index:R,value:w[R++]});continue}if(X==="}"){D.push({type:"CLOSE",index:R,value:w[R++]});continue}if(X===":"){for(var me="",U=R+1;U<w.length;){var te=w.charCodeAt(U);if(te>=48&&te<=57||te>=65&&te<=90||te>=97&&te<=122||te===95){me+=w[U++];continue}break}if(!me)throw new TypeError("Missing parameter name at "+R);D.push({type:"NAME",index:R,value:me}),R=U;continue}if(X==="("){var ie=1,de="",U=R+1;if(w[U]==="?")throw new TypeError('Pattern cannot start with "?" at '+U);for(;U<w.length;){if(w[U]==="\\"){de+=w[U++]+w[U++];continue}if(w[U]===")"){if(ie--,ie===0){U++;break}}else if(w[U]==="("&&(ie++,w[U+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+U);de+=w[U++]}if(ie)throw new TypeError("Unbalanced pattern at "+R);if(!de)throw new TypeError("Missing pattern at "+R);D.push({type:"PATTERN",index:R,value:de}),R=U;continue}D.push({type:"CHAR",index:R,value:w[R++]})}return D.push({type:"END",index:R,value:""}),D}function K(w,D){D===void 0&&(D={});for(var R=G(w),X=D.prefixes,me=X===void 0?"./":X,U="[^"+ue(D.delimiter||"/#?")+"]+?",te=[],ie=0,de=0,Z="",C=function(Ze){if(de<R.length&&R[de].type===Ze)return R[de++].value},y=function(Ze){var _e=C(Ze);if(_e!==void 0)return _e;var ze=R[de],Ae=ze.type,We=ze.index;throw new TypeError("Unexpected "+Ae+" at "+We+", expected "+Ze)},E=function(){for(var Ze="",_e;_e=C("CHAR")||C("ESCAPED_CHAR");)Ze+=_e;return Ze};de<R.length;){var I=C("CHAR"),S=C("NAME"),W=C("PATTERN");if(S||W){var M=I||"";me.indexOf(M)===-1&&(Z+=M,M=""),Z&&(te.push(Z),Z=""),te.push({name:S||ie++,prefix:M,suffix:"",pattern:W||U,modifier:C("MODIFIER")||""});continue}var ye=I||C("ESCAPED_CHAR");if(ye){Z+=ye;continue}Z&&(te.push(Z),Z="");var Ie=C("OPEN");if(Ie){var M=E(),Be=C("NAME")||"",Me=C("PATTERN")||"",De=E();y("CLOSE"),te.push({name:Be||(Me?ie++:""),pattern:Be&&!Me?U:Me,prefix:M,suffix:De,modifier:C("MODIFIER")||""});continue}y("END")}return te}p=K;function V(w,D){return z(K(w,D),D)}p=V;function z(w,D){D===void 0&&(D={});var R=v(D),X=D.encode,me=X===void 0?function(de){return de}:X,U=D.validate,te=U===void 0?!0:U,ie=w.map(function(de){if(l(de)==="object")return new RegExp("^(?:"+de.pattern+")$",R)});return function(de){for(var Z="",C=0;C<w.length;C++){var y=w[C];if(typeof y=="string"){Z+=y;continue}var E=de?de[y.name]:void 0,I=y.modifier==="?"||y.modifier==="*",S=y.modifier==="*"||y.modifier==="+";if(Array.isArray(E)){if(!S)throw new TypeError('Expected "'+y.name+'" to not repeat, but got an array');if(E.length===0){if(I)continue;throw new TypeError('Expected "'+y.name+'" to not be empty')}for(var W=0;W<E.length;W++){var M=me(E[W],y);if(te&&!ie[C].test(M))throw new TypeError('Expected all "'+y.name+'" to match "'+y.pattern+'", but got "'+M+'"');Z+=y.prefix+M+y.suffix}continue}if(typeof E=="string"||typeof E=="number"){var M=me(String(E),y);if(te&&!ie[C].test(M))throw new TypeError('Expected "'+y.name+'" to match "'+y.pattern+'", but got "'+M+'"');Z+=y.prefix+M+y.suffix;continue}if(!I){var ye=S?"an array":"a string";throw new TypeError('Expected "'+y.name+'" to be '+ye)}}return Z}}p=z;function d(w,D){var R=[],X=oe(w,R,D);return ee(X,R,D)}p=d;function ee(w,D,R){R===void 0&&(R={});var X=R.decode,me=X===void 0?function(U){return U}:X;return function(U){var te=w.exec(U);if(!te)return!1;for(var ie=te[0],de=te.index,Z=Object.create(null),C=function(I){if(te[I]===void 0)return"continue";var S=D[I-1];S.modifier==="*"||S.modifier==="+"?Z[S.name]=te[I].split(S.prefix+S.suffix).map(function(W){return me(W,S)}):Z[S.name]=me(te[I],S)},y=1;y<te.length;y++)C(y);return{path:ie,index:de,params:Z}}}p=ee;function ue(w){return w.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function v(w){return w&&w.sensitive?"":"i"}function ve(w,D){if(!D)return w;var R=w.source.match(/\((?!\?)/g);if(R)for(var X=0;X<R.length;X++)D.push({name:X,prefix:"",suffix:"",modifier:"",pattern:""});return w}function se(w,D,R){var X=w.map(function(me){return oe(me,D,R).source});return new RegExp("(?:"+X.join("|")+")",v(R))}function J(w,D,R){return ce(K(w,R),D,R)}function ce(w,D,R){R===void 0&&(R={});for(var X=R.strict,me=X===void 0?!1:X,U=R.start,te=U===void 0?!0:U,ie=R.end,de=ie===void 0?!0:ie,Z=R.encode,C=Z===void 0?function(je){return je}:Z,y="["+ue(R.endsWith||"")+"]|$",E="["+ue(R.delimiter||"/#?")+"]",I=te?"^":"",S=0,W=w;S<W.length;S++){var M=W[S];if(typeof M=="string")I+=ue(C(M));else{var ye=ue(C(M.prefix)),Ie=ue(C(M.suffix));if(M.pattern)if(D&&D.push(M),ye||Ie)if(M.modifier==="+"||M.modifier==="*"){var Be=M.modifier==="*"?"?":"";I+="(?:"+ye+"((?:"+M.pattern+")(?:"+Ie+ye+"(?:"+M.pattern+"))*)"+Ie+")"+Be}else I+="(?:"+ye+"("+M.pattern+")"+Ie+")"+M.modifier;else I+="("+M.pattern+")"+M.modifier;else I+="(?:"+ye+Ie+")"+M.modifier}}if(de)me||(I+=E+"?"),I+=R.endsWith?"(?="+y+")":"$";else{var Me=w[w.length-1],De=typeof Me=="string"?E.indexOf(Me[Me.length-1])>-1:Me===void 0;me||(I+="(?:"+E+"(?="+y+"))?"),De||(I+="(?="+E+"|"+y+")")}return new RegExp(I,v(R))}p=ce;function oe(w,D,R){return w instanceof RegExp?ve(w,D):Array.isArray(w)?se(w,D,R):J(w,D,R)}Y.Bo=oe},1977:function(he,Y,p){"use strict";p.d(Y,{n:function(){return ve}});var l=p(97685),G=p(71002),K=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,V=function(J){return J==="*"||J==="x"||J==="X"},z=function(J){var ce=parseInt(J,10);return isNaN(ce)?J:ce},d=function(J,ce){return(0,G.Z)(J)!==(0,G.Z)(ce)?[String(J),String(ce)]:[J,ce]},ee=function(J,ce){if(V(J)||V(ce))return 0;var oe=d(z(J),z(ce)),w=(0,l.Z)(oe,2),D=w[0],R=w[1];return D>R?1:D<R?-1:0},ue=function(J,ce){for(var oe=0;oe<Math.max(J.length,ce.length);oe++){var w=ee(J[oe]||"0",ce[oe]||"0");if(w!==0)return w}return 0},v=function(J){var ce,oe=J.match(K);return oe==null||(ce=oe.shift)===null||ce===void 0||ce.call(oe),oe},ve=function(J,ce){var oe=v(J),w=v(ce),D=oe.pop(),R=w.pop(),X=ue(oe,w);return X!==0?X:D||R?D?-1:1:0}},73177:function(he,Y,p){"use strict";p.d(Y,{X:function(){return d},b:function(){return z}});var l=p(67159),G=p(51812),K=p(1977),V=p(34155),z=function(){var ue;return typeof V=="undefined"?l.Z:((ue=V)===null||V===void 0||(V={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||V===void 0?void 0:V.ANTD_VERSION)||l.Z},d=function(ue,v){var ve=(0,K.n)(z(),"4.23.0")>-1?{open:ue,onOpenChange:v}:{visible:ue,onVisibleChange:v};return(0,G.Y)(ve)}},10178:function(he,Y,p){"use strict";p.d(Y,{D:function(){return z}});var l=p(74165),G=p(15861),K=p(67294),V=p(48171);function z(d,ee){var ue=(0,V.J)(d),v=(0,K.useRef)(),ve=(0,K.useCallback)(function(){v.current&&(clearTimeout(v.current),v.current=null)},[]),se=(0,K.useCallback)((0,G.Z)((0,l.Z)().mark(function J(){var ce,oe,w,D=arguments;return(0,l.Z)().wrap(function(X){for(;;)switch(X.prev=X.next){case 0:for(ce=D.length,oe=new Array(ce),w=0;w<ce;w++)oe[w]=D[w];if(!(ee===0||ee===void 0)){X.next=3;break}return X.abrupt("return",ue.apply(void 0,oe));case 3:return ve(),X.abrupt("return",new Promise(function(me){v.current=setTimeout((0,G.Z)((0,l.Z)().mark(function U(){return(0,l.Z)().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:return ie.t0=me,ie.next=3,ue.apply(void 0,oe);case 3:return ie.t1=ie.sent,(0,ie.t0)(ie.t1),ie.abrupt("return");case 6:case"end":return ie.stop()}},U)})),ee)}));case 5:case"end":return X.stop()}},J)})),[ue,ve,ee]);return(0,K.useEffect)(function(){return ve},[ve]),{run:se,cancel:ve}}},48171:function(he,Y,p){"use strict";p.d(Y,{J:function(){return K}});var l=p(74902),G=p(67294),K=function(z){var d=(0,G.useRef)(null);return d.current=z,(0,G.useCallback)(function(){for(var ee,ue=arguments.length,v=new Array(ue),ve=0;ve<ue;ve++)v[ve]=arguments[ve];return(ee=d.current)===null||ee===void 0?void 0:ee.call.apply(ee,[d].concat((0,l.Z)(v)))},[])}},51812:function(he,Y,p){"use strict";p.d(Y,{Y:function(){return l}});var l=function(K){var V={};if(Object.keys(K||{}).forEach(function(z){K[z]!==void 0&&(V[z]=K[z])}),!(Object.keys(V).length<1))return V}},97435:function(he,Y){"use strict";function p(l,G){for(var K=Object.assign({},l),V=0;V<G.length;V+=1){var z=G[V];delete K[z]}return K}Y.Z=p},16305:function(he,Y){"use strict";var p=this&&this.__classPrivateFieldGet||function(Z,C,y,E){if(y==="a"&&!E)throw new TypeError("Private accessor was defined without a getter");if(typeof C=="function"?Z!==C||!E:!C.has(Z))throw new TypeError("Cannot read private member from an object whose class did not declare it");return y==="m"?E:y==="a"?E.call(Z):E?E.value:C.get(Z)},l=this&&this.__classPrivateFieldSet||function(Z,C,y,E,I){if(E==="m")throw new TypeError("Private method is not writable");if(E==="a"&&!I)throw new TypeError("Private accessor was defined without a setter");if(typeof C=="function"?Z!==C||!I:!C.has(Z))throw new TypeError("Cannot write private member to an object whose class did not declare it");return E==="a"?I.call(Z,y):I?I.value=y:C.set(Z,y),y},G;Object.defineProperty(Y,"__esModule",{value:!0}),Y.TokenData=void 0,Y.parse=oe,Y.compile=D,Y.match=U;const K="/",V=Z=>Z,z=/^[$_\p{ID_Start}]$/u,d=/^[$\u200c\u200d\p{ID_Continue}]$/u,ee="https://git.new/pathToRegexpError",ue={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function v(Z){return Z.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}function ve(Z){return Z.sensitive?"s":"is"}function*se(Z){const C=[...Z];let y=0;function E(){let I="";if(z.test(C[++y]))for(I+=C[y];d.test(C[++y]);)I+=C[y];else if(C[y]==='"'){let S=y;for(;y<C.length;){if(C[++y]==='"'){y++,S=0;break}C[y]==="\\"?I+=C[++y]:I+=C[y]}if(S)throw new TypeError(`Unterminated quote at ${S}: ${ee}`)}if(!I)throw new TypeError(`Missing parameter name at ${y}: ${ee}`);return I}for(;y<C.length;){const I=C[y],S=ue[I];if(S)yield{type:S,index:y++,value:I};else if(I==="\\")yield{type:"ESCAPED",index:y++,value:C[y++]};else if(I===":"){const W=E();yield{type:"PARAM",index:y,value:W}}else if(I==="*"){const W=E();yield{type:"WILDCARD",index:y,value:W}}else yield{type:"CHAR",index:y,value:C[y++]}}return{type:"END",index:y,value:""}}class J{constructor(C){this.tokens=C,G.set(this,void 0)}peek(){if(!p(this,G,"f")){const C=this.tokens.next();l(this,G,C.value,"f")}return p(this,G,"f")}tryConsume(C){const y=this.peek();if(y.type===C)return l(this,G,void 0,"f"),y.value}consume(C){const y=this.tryConsume(C);if(y!==void 0)return y;const{type:E,index:I}=this.peek();throw new TypeError(`Unexpected ${E} at ${I}, expected ${C}: ${ee}`)}text(){let C="",y;for(;y=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)C+=y;return C}}G=new WeakMap;class ce{constructor(C){this.tokens=C}}Y.TokenData=ce;function oe(Z,C={}){const{encodePath:y=V}=C,E=new J(se(Z));function I(W){const M=[];for(;;){const ye=E.text();ye&&M.push({type:"text",value:y(ye)});const Ie=E.tryConsume("PARAM");if(Ie){M.push({type:"param",name:Ie});continue}const Be=E.tryConsume("WILDCARD");if(Be){M.push({type:"wildcard",name:Be});continue}if(E.tryConsume("{")){M.push({type:"group",tokens:I("}")});continue}return E.consume(W),M}}const S=I("END");return new ce(S)}function w(Z,C){const{encode:y=encodeURIComponent,delimiter:E=K}=C,I=R(Z.tokens,E,y);return function(W={}){const[M,...ye]=I(W);if(ye.length)throw new TypeError(`Missing parameters: ${ye.join(", ")}`);return M}}function D(Z,C={}){return w(Z instanceof ce?Z:oe(Z,C),C)}function R(Z,C,y){const E=Z.map(I=>X(I,C,y));return I=>{const S=[""];for(const W of E){const[M,...ye]=W(I);S[0]+=M,S.push(...ye)}return S}}function X(Z,C,y){if(Z.type==="text")return()=>[Z.value];if(Z.type==="group"){const I=R(Z.tokens,C,y);return S=>{const[W,...M]=I(S);return M.length?[""]:[W]}}const E=y||V;return Z.type==="wildcard"&&y!==!1?I=>{const S=I[Z.name];if(S==null)return["",Z.name];if(!Array.isArray(S)||S.length===0)throw new TypeError(`Expected "${Z.name}" to be a non-empty array`);return[S.map((W,M)=>{if(typeof W!="string")throw new TypeError(`Expected "${Z.name}/${M}" to be a string`);return E(W)}).join(C)]}:I=>{const S=I[Z.name];if(S==null)return["",Z.name];if(typeof S!="string")throw new TypeError(`Expected "${Z.name}" to be a string`);return[E(S)]}}function me(Z,C={}){const{decode:y=decodeURIComponent,delimiter:E=K,end:I=!0,trailing:S=!0}=C,W=ve(C),M=[],ye=[];for(const{tokens:De}of Z)for(const je of te(De,0,[])){const Ze=ie(je,E,ye);M.push(Ze)}let Ie=`^(?:${M.join("|")})`;S&&(Ie+=`(?:${v(E)}$)?`),Ie+=I?"$":`(?=${v(E)}|$)`;const Be=new RegExp(Ie,W),Me=ye.map(De=>y===!1?V:De.type==="param"?y:je=>je.split(E).map(y));return Object.assign(function(je){const Ze=Be.exec(je);if(!Ze)return!1;const{0:_e}=Ze,ze=Object.create(null);for(let Ae=1;Ae<Ze.length;Ae++){if(Ze[Ae]===void 0)continue;const We=ye[Ae-1],Qe=Me[Ae-1];ze[We.name]=Qe(Ze[Ae])}return{path:_e,params:ze}},{re:Be})}function U(Z,C={}){const E=(Array.isArray(Z)?Z:[Z]).map(I=>I instanceof ce?I:oe(I,C));return me(E,C)}function*te(Z,C,y){if(C===Z.length)return yield y;const E=Z[C];if(E.type==="group"){const I=y.slice();for(const S of te(E.tokens,0,I))yield*xn(te(Z,C+1,S))}else y.push(E);yield*xn(te(Z,C+1,y))}function ie(Z,C,y){let E="",I="",S=!0;for(let W=0;W<Z.length;W++){const M=Z[W];if(M.type==="text"){E+=v(M.value),I=M.value,S||(S=M.value.includes(C));continue}if(M.type==="param"||M.type==="wildcard"){if(!S&&!I)throw new TypeError(`Missing text after "${M.name}": ${ee}`);M.type==="param"?E+=`(${de(C,S?"":I)}+)`:E+="(.+)",y.push(M),I="",S=!1;continue}}return E}function de(Z,C){const y=[Z,C].filter(Boolean);return y.every(I=>I.length===1)?`[^${v(y.join(""))}]`:`(?:(?!${y.map(v).join("|")}).)`}},64599:function(he,Y,p){var l=p(96263);function G(K,V){var z=typeof Symbol!="undefined"&&K[Symbol.iterator]||K["@@iterator"];if(!z){if(Array.isArray(K)||(z=l(K))||V&&K&&typeof K.length=="number"){z&&(K=z);var d=0,ee=function(){};return{s:ee,n:function(){return d>=K.length?{done:!0}:{done:!1,value:K[d++]}},e:function(J){throw J},f:ee}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ue=!0,v=!1,ve;return{s:function(){z=z.call(K)},n:function(){var J=z.next();return ue=J.done,J},e:function(J){v=!0,ve=J},f:function(){try{!ue&&z.return!=null&&z.return()}finally{if(v)throw ve}}}}he.exports=G,he.exports.__esModule=!0,he.exports.default=he.exports}}]);
}());