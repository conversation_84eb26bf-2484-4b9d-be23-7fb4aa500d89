package com.zkteco.mars.usc.processor;

import com.alibaba.fastjson2.JSON;
import com.zkteco.framework.core.utils.DateUtil;
import com.zkteco.framework.core.utils.I18nUtil;
import com.zkteco.framework.vo.ApiResultMessage;

import com.zkteco.mars.ai.constant.IConstants;
import com.zkteco.mars.ai.dto.RagImageDTO;
import com.zkteco.mars.ai.dto.VisionQqDTO;
import com.zkteco.mars.ai.service.ImageText2TextService;
import com.zkteco.mars.ai.service.VisionQqService;
import com.zkteco.mars.usc.constant.USCConstants;
import com.zkteco.mars.usc.dao.ControlRulesDao;
import com.zkteco.mars.usc.dao.DmcPointDao;
import com.zkteco.mars.usc.dao.EventRecordDao;
import com.zkteco.mars.usc.dao.PointControlRulesDao;
import com.zkteco.mars.usc.model.ControlRules;
import com.zkteco.mars.usc.model.DmcPoint;
import com.zkteco.mars.usc.model.EventRecord;
import com.zkteco.mars.usc.model.PointControlRules;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * 图片转文字绑定图片
 *
 * <AUTHOR>
 * @date 2024-12-31 14:48
 * @since 1.0.0
 */
@Slf4j
@Component
@Order(value = 31)
public class ImageText2TextDataProcessor implements CommandLineRunner {

    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(10, 64, 20, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), new ThreadNameFactory(), new HwRejectedExecutionHandler());


    @Autowired(required = false)
    private ImageText2TextService imageText2TextService;

    @Autowired
    private DmcPointDao dmcPointDao;

    @Autowired
    private PointControlRulesDao pointControlRulesDao;

    @Autowired
    private ControlRulesDao controlRulesDao;


    @Autowired
    private VisionQqService visionQqService;


    @Autowired
    private EventRecordDao eventRecordDao;


    @Override
    public void run(String... args) {
        new ImageAnalyseDataHandlerThread("ImageAnalyseDataHandlerThread").start();
    }

    class ImageAnalyseDataHandlerThread extends Thread {
        public ImageAnalyseDataHandlerThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠10s，降低CPU占用
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                log.error("ImageAnalyseDataHandlerThread Sleep Error", e);
            }

            log.info("ImageAnalyseDataHandlerThread Start ...");

            try {
                // 循环检查文件夹是否存在
                while (true) {
                    // 确保 SNAP_SAVE_FOLDER_PATH 和 SNAP_THUMBNAIL_FOLDER_PATH 存在
                    ensureFolderExists(USCConstants.SNAP_SAVE_FOLDER_PATH);
                    ensureFolderExists(USCConstants.SNAP_THUMBNAIL_FOLDER_PATH);


                    // 检查 SNAP_SNAP_FOLDER_PATH 是否存在
                    if (isFolderExists(USCConstants.SNAP_SNAP_FOLDER_PATH)) {
                        log.info("SNAP_SNAP_FOLDER_PATH exists, ImageAnalyseDataHandlerThread  Start complete...");
                        break;
                    }

                    // 等待 2 秒后重试
                    Thread.sleep(2000);
                }
            } catch (InterruptedException e) {
                log.error("ImageAnalyseDataHandler Thread interrupted.", e);
                Thread.currentThread().interrupt();
            }

            // 启动时扫描已有照片并处理 异步执行文件扫描
            THREAD_POOL.submit(this::processExistingFiles);

            // 开始监听新增照片
            try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
                Path path = Paths.get(USCConstants.SNAP_SNAP_FOLDER_PATH);
                path.register(watchService, StandardWatchEventKinds.ENTRY_CREATE);

                while (true) {
                    WatchKey key = watchService.take();

                    for (WatchEvent<?> event : key.pollEvents()) {
                        WatchEvent.Kind<?> kind = event.kind();

                        if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                            Path filePath = path.resolve((Path) event.context());

                            if (filePath.toString().toLowerCase().endsWith(".png")) {
                                // 提交文件处理任务到线程池
                                THREAD_POOL.submit(() -> handleFile(filePath.toFile()));
                            } else {
                                log.info("Ignored file: " + filePath);
                            }
                        }
                    }

                    boolean valid = key.reset();
                    if (!valid) {
                        break;
                    }
                }
            } catch (IOException | InterruptedException e) {
                log.error("Error in WatchService", e);
            }
        }

        /**
         * 处理启动时文件夹中已有的照片
         */
        private void processExistingFiles() {
            File folder = new File(USCConstants.SNAP_SNAP_FOLDER_PATH);

            File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".png"));
            if (files == null || files.length == 0) {
                log.info("No existing photos found in SNAP_SNAP_FOLDER_PATH.");
                return;
            }

            log.info("Found {} existing photos in SNAP_SNAP_FOLDER_PATH. Processing...", files.length);

            for (File file : files) {
                THREAD_POOL.submit(() -> handleFile(file));
            }
        }
    }

    /**
     * 处理单个文件
     */
    private void handleFile(File file) {
        try {
            // 等待文件写入完成
            if (!waitForFileStable(file)) {
                // 文件未就绪，跳过处理
                return;
            }

            // 构建 DTO
            RagImageDTO ragImageDTO = new RagImageDTO();
            ragImageDTO.setMid(UUID.randomUUID().toString());

            // 获取文件名、解析基础信息
            String fileName = file.getName();
            if (!fileName.endsWith(".png")) return;
            fileName = fileName.substring(0, fileName.lastIndexOf(".png"));
            String[] fileNameArray = fileName.split("_");

            String businessId = fileNameArray[1];
            DmcPoint dmcPoint = dmcPointDao.findByBusinessId(businessId);
            if (Objects.isNull(dmcPoint)) {
                log.info("Point not found, skip file: {}", file.getName());
                deleteFileQuietly(file);
                return;
            }

            Map<String, Object> payload = buildPayload(file, fileNameArray, dmcPoint);
            ragImageDTO.setPayload(payload);

            // 入库 + 移动文件 + 生成缩略图
            File movedFile = null;
            boolean shouldSave = shouldSaveImageAndRecordEvents(file, fileNameArray, dmcPoint, businessId, payload);
            if (shouldSave) {
                movedFile = moveAndThumbnail(file, fileNameArray); // 修改方法让它返回移动后的 File
            }

            // 构造最终文件路径（如果未保存就还用原文件）
            File fileToEmbed = (movedFile != null) ? movedFile : file;


            try (FileInputStream fis = new FileInputStream(fileToEmbed)) {
                log.info("EmbedImage request params: {}", JSON.toJSONString(ragImageDTO));
                ApiResultMessage resp = imageText2TextService.embedImage(fis, ragImageDTO);
                log.info("EmbedImage respond body: {}", JSON.toJSONString(resp));

                if (resp.getCode() != 0 && !shouldSave) {
                    deleteFileQuietly(fileToEmbed);
                } else if (!shouldSave) {
                    // 如果没保存，调用 embed 成功后再补移动（确保移动不会覆盖）
                    moveAndThumbnail(file, fileNameArray);
                }

            } catch (Exception e) {
                log.error("EmbedImage 异常：{}", fileToEmbed.getName(), e);
            }

        } catch (Exception e) {
            log.error("处理文件异常: {}", file.getName(), e);
        }
    }

    /**
     * 文件等待写入
     *
     * @param file:
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2025-04-24 10:30
     * @since 1.0.0
     */
    private boolean waitForFileStable(File file) {
        try {
            for (int i = 0; i < 10; i++) {
                if (Files.exists(file.toPath()) && Files.size(file.toPath()) > 0) {
                    try (RandomAccessFile raf = new RandomAccessFile(file, "r");
                         FileChannel channel = raf.getChannel();
                         FileLock lock = channel.tryLock(0, Long.MAX_VALUE, true)) {
                        if (lock != null) {
                            return true; // 文件已写完
                        }
                    } catch (Exception e) {
                        log.info("文件仍在写入中: {}", file.getAbsolutePath());
                    }
                }
                Thread.sleep(100); // 等待100ms重试
            }
        } catch (Exception e) {
            log.error("等待文件稳定时异常: {}", file.getAbsolutePath(), e);
        }
        log.error("文件在预期时间内未就绪: {}", file.getAbsolutePath());
        return false;
    }


    /**
     * 构建下发参数
     *
     * @param file:
     * @param fileNameArray:
     * @param dmcPoint:
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @throws
     * <AUTHOR>
     * @date 2025-04-24 10:30
     * @since 1.0.0
     */
    private Map<String, Object> buildPayload(File file, String[] fileNameArray, DmcPoint dmcPoint) {
        Map<String, Object> payload = new HashMap<>();
        payload.put(IConstants.META_DATA_NAME, file.getName());
        payload.put(IConstants.META_DATA_POINT_ID, dmcPoint.getBusinessId());
        payload.put(IConstants.META_DATA_POINT_NAME, dmcPoint.getName());

        try {
            Date date = DateUtil.parseDate(fileNameArray[0], USCConstants.yyyyMMddHHmmssSSS);
            payload.put(IConstants.META_DATA_ORIGIN_TIME, date.getTime());
        } catch (Exception e) {
            log.warn("时间解析失败: {}", file.getName(), e);
        }

        return payload;
    }

    /**
     * 移动原图和生成缩略图
     *
     * @param file:
     * @param fileNameArray:
     * @return java.io.File
     * @throws
     * <AUTHOR>
     * @date 2025-04-24 10:31
     * @since 1.0.0
     */
    private File moveAndThumbnail(File file, String[] fileNameArray) {
        try {
            String timestamp = fileNameArray[0];
            String channelId = fileNameArray[1];
            String yearMonth = timestamp.substring(0, 4) + "-" + timestamp.substring(4, 6);
            String day = timestamp.substring(6, 8);

            Path targetPath = Paths.get(USCConstants.SNAP_SAVE_FOLDER_PATH, channelId, yearMonth, day, timestamp + ".png");
            Files.createDirectories(targetPath.getParent());
            Files.move(file.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            // 生成缩略图
            String thumbFolderPath = USCConstants.SNAP_THUMBNAIL_FOLDER_PATH + "/" + channelId + "/" + yearMonth + "/" + day;
            File thumbnailFolder = new File(thumbFolderPath);
            if (!thumbnailFolder.exists()) thumbnailFolder.mkdirs();
            File thumbnailFile = new File(thumbnailFolder, timestamp + ".png");
            Thumbnails.of(targetPath.toFile()).size(200, 200).toFile(thumbnailFile);

            return targetPath.toFile();
        } catch (IOException e) {
            log.error("移动或生成缩略图失败: {}", file.getAbsolutePath(), e);
            return null;
        }
    }

    /**
     * 删除文件
     *
     * <AUTHOR>
     * @date 2025-04-24 10:32
     * @since 1.0.0
     */
    private void deleteFileQuietly(File f) {
        try {
            if (f.exists() && !f.delete()) {
                log.error("删除文件失败: {}", f.getAbsolutePath());
            }
            //log.info("删除文件成功: {}", f.getAbsolutePath());
        } catch (Exception e) {
            log.error("删除文件异常: {}", f.getAbsolutePath(), e);
        }
    }


    /**
     * 判断图片是否应保存并记录事件
     *
     * @param file: 图片文件
     * @param fileNameArray: 文件名称
     * @param dmcPoint:点位信息
     * @param businessId: 点位id
     * @param payload:
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2025-06-26 15:47
     * @since 1.0.0
     */
    private boolean shouldSaveImageAndRecordEvents(File file, String[] fileNameArray, DmcPoint dmcPoint, String businessId, Map<String, Object> payload) {
        boolean shouldSaveImage = false;

        List<PointControlRules> pointRules = pointControlRulesDao.findByPointId(businessId);
        List<String> ruleIds = pointRules.stream()
                .map(PointControlRules::getRuleId)
                .toList();

        if (CollectionUtils.isEmpty(ruleIds)) {
            return false;
        }

        List<ControlRules> controlRules = controlRulesDao.findAllById(ruleIds);
        Map<String, ControlRules> ruleMap = controlRules.stream()
                .collect(Collectors.toMap(ControlRules::getId, Function.identity(), (v1, v2) -> v1));

        List<ControlRules> orderedRules = ruleIds.stream()
                .map(ruleMap::get)
                .filter(Objects::nonNull)
                .toList();

        // 分组：每组最多 10 条规则
        int groupSize = 10;
        List<List<ControlRules>> groupedRules = new ArrayList<>();
        for (int i = 0; i < orderedRules.size(); i += groupSize) {
            int end = Math.min(i + groupSize, orderedRules.size());
            groupedRules.add(orderedRules.subList(i, end));
        }

        List<String> allAnswers = new ArrayList<>();

        long start = System.currentTimeMillis();
        try {
            for (int groupIndex = 0; groupIndex < groupedRules.size(); groupIndex++) {
                List<ControlRules> ruleGroup = groupedRules.get(groupIndex);

                List<String> prompts = IntStream.range(0, ruleGroup.size())
                        .mapToObj(i -> (i + 1) + ". " + ruleGroup.get(i).getPrompt())
                        .toList();

                String promptText = String.join("\n", prompts);
                String prefix =  I18nUtil.i18nCode("usc_prompt_prefix");
                String suffix = I18nUtil.i18nCode("usc_prompt_suffix");
                String finalPrompt = prefix + "\n\n" + promptText + "\n\n" + suffix;

                log.info("点位ID: {} | 点位名称: {} | 文件: {} | 第 {} 组 Prompt ->\n{}",
                        dmcPoint.getId(), dmcPoint.getName(), file.getName(), groupIndex + 1, finalPrompt);

                VisionQqDTO visionQqDTO = new VisionQqDTO();
                visionQqDTO.setPrompt(finalPrompt);
                visionQqDTO.setPayload(payload);

                ApiResultMessage apiResultMessage = visionQqService.vqaImageFile(new FileInputStream(file), visionQqDTO);

                if (apiResultMessage != null && apiResultMessage.getCode() == 0) {
                    String answerString = JSON.parseObject(JSON.toJSONString(apiResultMessage.getData()))
                            .getString("response");

                    List<String> answers = Arrays.stream(answerString.split(","))
                            .map(String::trim)
                            .toList();

                    if (answers.size() != ruleGroup.size()) {
                        log.error("点位ID: {} | 回答数量 ({}) 与问题数量 ({}) 不一致 | 文件: {} | 第 {} 组",
                                dmcPoint.getId(), answers.size(), ruleGroup.size(), file.getName(), groupIndex + 1);
                        return false;
                    }

                    allAnswers.addAll(answers);
                }
            }

            long end = System.currentTimeMillis();
            log.info("点位ID: {} | 模型处理总耗时: {} 秒", dmcPoint.getId(), (end - start) / 1000.0);

            shouldSaveImage = allAnswers.stream().anyMatch(ans -> "YES".equalsIgnoreCase(ans));
            log.info("点位ID: {} | 模型总返回: {}", dmcPoint.getId(), String.join(",", allAnswers));
            log.info("点位ID: {} | 是否保存图片: {}", dmcPoint.getId(), shouldSaveImage);

            List<EventRecord> recordList = new ArrayList<>();
            for (int i = 0; i < allAnswers.size(); i++) {
                String answer = allAnswers.get(i);
                if ("YES".equalsIgnoreCase(answer)) {
                    ControlRules rule = orderedRules.get(i);
                    EventRecord eventRecord = new EventRecord();
                    eventRecord.setEventType(rule.getEventType());
                    eventRecord.setEventCode(rule.getEventCode());
                    eventRecord.setPointId(dmcPoint.getBusinessId());
                    eventRecord.setPointName(dmcPoint.getName());
                    eventRecord.setPrompt(rule.getPrompt());
                    eventRecord.setFileName(file.getName());

                    try {
                        Date date = DateUtil.parseDate(fileNameArray[0], USCConstants.yyyyMMddHHmmssSSS);
                        eventRecord.setEventTime(date);
                    } catch (Exception e) {
                        log.warn("时间解析失败: {}", file.getName(), e);
                    }
                    recordList.add(eventRecord);
                }
            }

            if (!recordList.isEmpty()) {
                log.info("点位ID: {} | 保存事件记录数量: {}", dmcPoint.getId(), recordList.size());
                eventRecordDao.saveAll(recordList);
            }

        } catch (FileNotFoundException e) {
            throw new RuntimeException("文件未找到: " + file.getAbsolutePath(), e);
        }

        return shouldSaveImage;
    }


    /**
     * 确保文件夹存在
     *
     * @param folderPath:
     * <AUTHOR>
     * @date 2025-01-21 13:47
     * @since 1.0.0
     */
    private void ensureFolderExists(String folderPath) {
        File folder = new File(folderPath);
        if (!folder.exists()) {
            boolean created = folder.mkdirs(); // 创建多级目录
            if (created) {
                log.info("Folder created successfully: " + folderPath);
            } else {
                log.error("Failed to create folder: " + folderPath);
            }
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param folderPath:
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2025-01-02 16:38
     * @since 1.0.0
     */
    private boolean isFolderExists(String folderPath) {
        folderPath = folderPath.replace("\\", "/");
        File folder = new File(folderPath);
        return folder.exists() && folder.isDirectory();
    }

    /**
     * 自定义线程名称
     */
    static class ThreadNameFactory implements ThreadFactory {
        private final AtomicInteger count = new AtomicInteger(0);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            String threadName = "mars-ImageText2Text-thread-factory-" + count.incrementAndGet();
            t.setName(threadName);
            return t;
        }
    }

    /**
     * 自定义线程拒绝策略
     */
    static class HwRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            try {
                executor.getQueue().put(r); // 使用阻塞方法避免任务丢失
            } catch (InterruptedException e) {
                log.error("Interrupted while handling rejected execution", e);
            }
        }
    }

}