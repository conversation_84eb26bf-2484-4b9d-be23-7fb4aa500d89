import React, { useState, useEffect } from 'react';
import { Modal, Image, Input } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';

const { TextArea } = Input;

interface EventDetailModalProps {
  isEventInfoVisible: boolean;
  handleRecordClose: () => void;
  videoSrc: string;
  eventDetails: {
    imageUrl?: string;
    thumbUrl?: string;
    time: string;
    address: string;
    score: string;
  } | null;
  smartSearchValue: string;
  visibleThumbnails?: string[];
  thumbnailWidth: number;
}

const EventDetailModal: React.FC<EventDetailModalProps> = ({
  isEventInfoVisible,
  handleRecordClose,
  videoSrc,
  eventDetails,
  smartSearchValue,
  visibleThumbnails = [],
  thumbnailWidth,
}) => {
  const intl = useIntl();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [targetImages, setTargetImages] = useState<string[]>([]);

  // 每次打开详情时重置索引和清空目标列表
  useEffect(() => {
    if (isEventInfoVisible) {
      setCurrentIndex(0);
      // 重新计算和清空目标列表 - 使用传入的 visibleThumbnails
      setTargetImages(visibleThumbnails || []);
    } else {
      // 关闭时清空目标列表
      setTargetImages([]);
      setCurrentIndex(0);
    }
  }, [isEventInfoVisible, visibleThumbnails]);

  // 上一页
  const handlePrevThumb = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  // 下一页
  const handleNextThumb = () => {
    const imagesPerPage = 7;
    if (currentIndex + imagesPerPage < targetImages.length) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  // 缩略图点击
//   const handleThumbnailClick = (index: number) => {
//     setCurrentIndex(index);
//   };

  return (
    <Modal
      open={isEventInfoVisible}
      onCancel={handleRecordClose}
      footer={null}
      destroyOnClose={true}
      width={"67.7vw"}
      centered
      title={intl.formatMessage({ id: 'pages.search.eventDetail', defaultMessage: '事件详情', })}
      closeIcon={<CloseOutlined style={{ fontSize: '16px' }} />}
      className="eventInfo-modal"
      styles={{
        mask: { background: 'rgba(0, 0, 0, 0.5)' },
        content: {
          background: 'transparent',
          boxShadow: 'none',
          border: 'none',
        },
        header: {
          borderRadius: '4px 4px 0px 0px',
          background: 'rgb(238, 242, 246)',
          padding: '9px 16px',
          marginBottom: '0'
        },
      }}
    >
      <div style={{ background: 'rgb(255, 255, 255)', padding: '16px 30px', borderRadius: '0px 0px 4px 4px' }}>

        <div style={{ display: 'flex', gap: '30px' }}>
          {/* 视频区域 */}
          <div style={{ width: '50%', borderRadius: '4px', height: '40vh' }}>
            <div style={{
              fontWeight: 'bold',
              textAlign: 'center',
              lineHeight: '30px',
            }}>{intl.formatMessage({ id: 'pages.search.videoPlayback', defaultMessage: '视频回放', })}</div>
            <video
              id="video-play"
              src={videoSrc}
              controls
              autoPlay
              loop
              poster="/icons/video_logo.svg"
              disablePictureInPicture
              onContextMenu={(e) => e.preventDefault()}
              style={{ width: '100%', height: 'calc(100% - 30px)', objectFit: 'fill', background: '#eeeeee' }}
              controlsList="nodownload noplaybackrate"
            />
          </div>

          {/* 全景图区域（右侧） */}
          <div style={{
            width: '50%',
            borderRadius: '4px',
            height: '40vh',
            display: 'flex',
            flexDirection: 'column'
          }}>

            {/* 标题区域 */}
            <div style={{
              fontWeight: 'bold',
              textAlign: 'center',
              lineHeight: '30px',
            }}>
              {intl.formatMessage({ id: 'pages.search.panorama', defaultMessage: '全景图', })}
            </div>

            {/* 全景图区域 */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              // flex: '0 0 65%', // 65% 高度
              background: '#eeeeee',
            }}>
              <Image
                src={eventDetails?.imageUrl || eventDetails?.thumbUrl || '/icons/search/default-noImage.png'}
                style={{
                  width: '100%',
                  height: '25vh',
                  objectFit: 'contain',
                }}
              />
            </div>

            {/* 缩略图区域 */}
            <div style={{
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'center',
              height: '15vh',
              position: 'relative',
              paddingLeft: '10px',
              overflow: 'hidden',
            }}>

              {/* 左箭头按钮 */}
              <img
                src="/icons/search/left-arrow.svg"
                style={{
                  width: '21px',
                  height: '21px',
                  position: 'absolute',
                  left: '5px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  cursor: 'pointer',
                  zIndex: 10,
                }}
                onClick={handlePrevThumb}
              />

              {/* 缩略图列表 */}
              <div style={{
                display: 'flex',
                transition: 'transform 0.3s ease-in-out',
                transform: `translateX(-${currentIndex * thumbnailWidth}px)`,
              }}>
                {targetImages.slice(currentIndex, currentIndex + 7).map((img, index) => (
                  <div key={index} style={{
                    width: '70px',
                    maxWidth: '100%',
                    marginRight: '2px',
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    background: '#eeeeee',
                  }}>
                    <Image
                      src={img}
                      style={{
                        maxWidth: '100%',
                        maxHeight: 'calc(15vh - 40px)',
                        objectFit: 'contain',
                      }}
                    />
                  </div>
                ))}
              </div>

              {/* 右箭头按钮 */}
              <img
                src="/icons/search/right-arrow.svg"
                style={{
                  width: '21px',
                  height: '21px',
                  position: 'absolute',
                  right: '10px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  cursor: 'pointer',
                  zIndex: 10,
                }}
                onClick={handleNextThumb}
              />
            </div>

          </div>
        </div>

        <div style={{ marginTop: "10px", padding: "10px", backgroundColor: "#fff", borderRadius: "8px", width: "60%" }}>
          <div style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
            <strong style={{ flex: 0.4, textAlign: "right", paddingRight: 8 }}>{intl.formatMessage({ id: 'pages.search.description', defaultMessage: '描述', })}：</strong>
            <Input value={smartSearchValue} readOnly style={{ flex: 2, textAlign: "left", border: "none", borderRadius: "3px", height: "24px", pointerEvents: "none" }} />
          </div>
          <div style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
            <strong style={{ flex: 0.4, textAlign: "right", paddingRight: 8 }}>{intl.formatMessage({ id: 'pages.search.time', defaultMessage: '时间', })}：</strong>
            <Input value={eventDetails?.time || ''} readOnly style={{ flex: 2, textAlign: "left", border: "none", borderRadius: "3px", height: "24px", pointerEvents: "none" }} />
          </div>
          <div style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
            <strong style={{ flex: 0.4, textAlign: "right", paddingRight: 8 }}>{intl.formatMessage({ id: 'pages.search.location', defaultMessage: '地点', })}：</strong>
            <Input value={eventDetails?.address || ''} readOnly style={{ flex: 2, textAlign: "left", border: "none", borderRadius: "3px", height: "24px", pointerEvents: "none" }} />
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <strong style={{ flex: 0.4, textAlign: "right", paddingRight: 8 }}>{intl.formatMessage({ id: 'pages.search.similarity', defaultMessage: '相似度', })}：</strong>
            <Input value={eventDetails?.score || ''} readOnly style={{ flex: 2, textAlign: "left", border: "none", borderRadius: "3px", height: "24px", pointerEvents: "none" }} />
          </div>
        </div>

      </div>
    </Modal>
  );
};

export default EventDetailModal;