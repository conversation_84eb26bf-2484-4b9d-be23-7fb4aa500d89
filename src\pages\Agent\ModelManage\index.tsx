import { getAgentModelList, delAgentModels } from "@/services/ant-design-pro/api";
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
  DrawerForm,
  FooterToolbar,
  ProFormDigit,
  ProFormText,
  ProFormSelect,
  ProTable,
  ProForm,
  ProFormUploadButton,
  ProFormTextArea
} from '@ant-design/pro-components';
import { Button, message, Modal, Table, Select, Tag, Tooltip, Input, Image, Card, List, Avatar } from 'antd';
import type { ReactNode } from 'react';
import { useEffect, useRef, useState } from 'react';
import { connect, useIntl, getDvaApp } from 'umi';
import './index.less'

const ModelManage: React.FC = (props) => {
  const intl = useIntl();

  /** 表格的引用 */
  const actionRef = useRef<ActionType>();

  /** 当前行 */
  const [currentRow, setCurrentRow] = useState<any>();

  /** 表单引用 */
  const formRef = useRef<ProFormInstance>();

  /**智能体数据 */
  const [agentsData, setAgentsData] = useState<any[]>([])

  /** 表单ref */
  const agentsRef = useRef()

  /** 下拉框特色选择 */
  const [optionsFeature, setOptionsFeature] = useState<any[]>([])
  /** 下拉框厂商选择 */
  const [optionsManufacturer, setOptionsManufacturer] = useState<any[]>([])
  /** 下拉框厂商支持功能选择 */
  const [optionsModelFunction, setOptionsModelFunction] = useState<any[]>([])

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    name: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
    type: '',
    feature: '',
    manufacturer: '',
    modelFunction: '',
    modelLength: '',
    modelRights: '',
    status: ''
  })
  /** 智能体特色 */
  const featureToLabelMap = {
    1: intl.formatMessage({ id: 'pages.agent.flagship', defaultMessage: '旗舰' }),
    2: intl.formatMessage({ id: 'pages.agent.highSpeed', defaultMessage: '高速' }),
    3: intl.formatMessage({ id: 'pages.agent.toolInvocation', defaultMessage: '工具调用' }),
    4: intl.formatMessage({ id: 'pages.agent.rolePlay', defaultMessage: '角色扮演' }),
    5: intl.formatMessage({ id: 'pages.agent.longText', defaultMessage: '长文本' }),
    6: intl.formatMessage({ id: 'pages.agent.imageUnderstanding', defaultMessage: '图片理解' }),
    7: intl.formatMessage({ id: 'pages.agent.reasoning', defaultMessage: '推理能力' }),
    8: intl.formatMessage({ id: 'pages.agent.videoUnderstanding', defaultMessage: '视频理解' }),
    9: intl.formatMessage({ id: 'pages.agent.costPerformance', defaultMessage: '性价比' }),
    10: intl.formatMessage({ id: 'pages.agent.codeExpert', defaultMessage: '代码专精' }),
    11: intl.formatMessage({ id: 'pages.agent.audioUnderstanding', defaultMessage: '音频理解' }),
    12: intl.formatMessage({ id: 'pages.agent.visualAnalysis', defaultMessage: '视觉分析' }),
  }

  /** 智能体运行状态 */
  const statusMap = {
    1: intl.formatMessage({ id: 'pages.agent.running', defaultMessage: '运行中' }),
    2: intl.formatMessage({ id: 'pages.agent.queuing', defaultMessage: '排队中' }),
    3: intl.formatMessage({ id: 'pages.agent.training', defaultMessage: '训练中' }),
    4: intl.formatMessage({ id: 'pages.agent.trainingFailed', defaultMessage: '训练失败' }),
  }

  /** 模型类型 */
  const typeMap = {
    1: intl.formatMessage({ id: 'pages.agent.text', defaultMessage: '文本' }),
    2: intl.formatMessage({ id: 'pages.agent.multimodal', defaultMessage: '多模态' }),
    3: intl.formatMessage({ id: 'pages.agent.landongModel', defaultMessage: '懒洞模型' })
  }

  /** 选中行集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  /** 表单引用 */
  const editFormRef = useRef<ProFormInstance>();


  const operateRender = (dom: any, record: any): ReactNode => {
    return <div id="operate">
      <a onClick={() => deleteRecord([record])}>
        <img className="img_del" title={intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除' })} />
      </a>
    </div>;
  }

  /**
   * 分页
   */
  const handleAgentPageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {
    console.log('page change', pagination, filters, sorter, extra)
    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadAgentInfo({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('point search', params)
    setSearchParams({
      ...searchParams,
      name: params.name,
      type: params.type,
      feature: params.feature,
      manufacturer: params.manufacturer,
      modelFunction: params.modelFunction,
      modelLength: params.modelLength,
      modelRights: params.modelRights,
      status: params.status,
    })
    loadAgentInfo({
      current: 1,
      name: params.name ? params.name : 'no&input',
      type: params.type ? params.type : 'no&input',
      feature: params.feature ? params.feature : 'no&input',
      manufacturer: params.manufacturer ? params.manufacturer : 'no&input',
      modelFunction: params.modelFunction ? params.modelFunction : 'no&input',
      modelLength: params.modelLength ? params.modelLength : 'no&input',
      modelRights: params.modelRights ? params.modelRights : 'no&input',
      status: params.status ? params.status : 'no&input',

    })
  }

  /**
   * 删除记录
   */
  const deleteRecord = async (rows: any[]) => {
    console.log('del data:', rows)
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.pointManage.deleteRecord', defaultMessage: '删除记录', }),
      content: intl.formatMessage({ id: 'pages.pointManage.confirmDeleteRecord', defaultMessage: '确定删除记录吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk: async () => {
        const hide = message.loading(intl.formatMessage({ id: 'pages.pointManage.deleting', defaultMessage: '正在删除', }));
        var opSuccess = 0;
        var opFail = 0;
        const data: any = []
        // for (let i = 0; i < rows.length; i++) {
        //   data.push({
        //     id: rows[i].id,
        //     deviceId: rows[i].deviceId,
        //     businessId: rows[i].businessId
        //   })
        // }
        const rs = await delAgentModels(rows);
        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }));
        }
        else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.deleteFailure', defaultMessage: '删除失败，请重试', }));
        }
        setSelectedRows([]);
        //actionRef.current?.reloadAndRest?.();
        loadAgentInfo({});

        hide();

      },
    });
  };


  const statusRender = (dom: any, row: any) => {
    // 假设 status 是存储在 row 对象中的
    const statusValues = row.status;

    // 选项映射
    const options = [
      { label: intl.formatMessage({ id: 'pages.agent.running', defaultMessage: '运行中' }), value: 1 },
      { label: intl.formatMessage({ id: 'pages.agent.queuing', defaultMessage: '排队中' }), value: 2 },
      { label: intl.formatMessage({ id: 'pages.agent.training', defaultMessage: '训练中' }), value: 3 },
      { label: intl.formatMessage({ id: 'pages.agent.trainingFailed', defaultMessage: '训练失败' }), value: 4 },
    ];

    // 如果 status 为空或不存在，直接返回 null
    if (!statusValues) {
      return null;
    }
    // 确保 statusValues 是数组
    // 将值转换为数组，处理单个值和多个值的情况
    const values = typeof statusValues === 'string' ?
      statusValues.split(",").map(Number) :
      [Number(statusValues)];
    // 根据 value 获取对应的 label
    const labels = values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean); // 过滤掉 undefined 或 null 的值

    // 只显示前 4 个类型
    const displayLabels = labels.slice(0, 3);

    return (
      <div style={{ whiteSpace: 'pre-wrap' }}>
        {displayLabels.map((label, index) => (
          <Tag color="rgb(11, 211, 87)" key={index}>{label}</Tag>
        ))}
      </div>
    );
  };

  //模型类型
  const typeRender = (dom: any, row: any) => {
    // 假设 type 是存储在 row 对象中的
    const statusValues = row.type;

    // 选项映射
    const options = [
      { label: intl.formatMessage({ id: 'pages.agent.text', defaultMessage: '文本' }), value: 1 },
      { label: intl.formatMessage({ id: 'pages.agent.multimodal', defaultMessage: '多模态' }), value: 2 },
      { label: intl.formatMessage({ id: 'pages.agent.landongModel', defaultMessage: '懒洞模型' }), value: 3 },
    ];
    // 如果 status 为空或不存在，直接返回 null
    if (!statusValues) {
      return null;
    }
    // 确保 statusValues 是数组
    // 将值转换为数组，处理单个值和多个值的情况
    const values = typeof statusValues === 'string' ?
      statusValues.split(",").map(Number) :
      [Number(statusValues)];
    // 根据 value 获取对应的 label
    const labels = values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean); // 过滤掉 undefined 或 null 的值

    // 只显示前 4 个类型
    const displayLabels = labels.slice(0, 3);

    return (
      <div style={{ whiteSpace: 'pre-wrap' }}>
        {displayLabels.map((label, index) => (
          <Tag color="rgb(11, 211, 87)" key={index}>{label}</Tag>
        ))}

      </div>
    );
  };
  const typeFormItem = (item: any, row: any) => {
    if (item.dataIndex == 'name') {

      return (
        <Input placeholder={intl.formatMessage({ id: 'pages.agent.searchModelName', defaultMessage: '搜索模型名称' })} style={{ width: 200 }}
        />
      )

    } else if (item.dataIndex == 'type') {
      return (
        <Select
          defaultValue=""
          style={{ width: 120 }}
          allowClear
          options={[
            { value: 1, label: intl.formatMessage({ id: 'pages.agent.text', defaultMessage: '文本' }) },
            { value: 2, label: intl.formatMessage({ id: 'pages.agent.multimodal', defaultMessage: '多模态' }) },
            { value: 3, label: intl.formatMessage({ id: 'pages.agent.landongModel', defaultMessage: '懒洞模型' }) }
          ]}
        />
      )
    } else if (item.dataIndex == 'feature') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={optionsFeature}
        />
      )
    } else if (item.dataIndex == 'manufacturer') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={optionsManufacturer}
        />
      )

    } else if (item.dataIndex == 'modelFunction') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={optionsModelFunction}
        />
      )

    } else if (item.dataIndex == 'modelLength') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={[
            { label: '0', value: '0' },
            { label: '32K', value: '32K' },
            { label: '64K', value: '64K' },
            { label: 'max', value: 'max' },
          ]}
        />
      )
    } else if (item.dataIndex == 'modelRights') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={[
            { label: intl.formatMessage({ id: 'pages.agent.quotaTrial', defaultMessage: '限额体验' }), value: intl.formatMessage({ id: 'pages.agent.quotaTrial', defaultMessage: '限额体验' }) },
            { label: intl.formatMessage({ id: 'pages.agent.comingOffline', defaultMessage: '即将下线' }), value: intl.formatMessage({ id: 'pages.agent.comingOffline', defaultMessage: '即将下线' }) },
            { label: intl.formatMessage({ id: 'pages.agent.newModelExperience', defaultMessage: '新模型体验' }), value: intl.formatMessage({ id: 'pages.agent.newModelExperience', defaultMessage: '新模型体验' }) },
            { label: intl.formatMessage({ id: 'pages.agent.advancedModel', defaultMessage: '高级模型' }), value: intl.formatMessage({ id: 'pages.agent.advancedModel', defaultMessage: '高级模型' }) },
            { label: intl.formatMessage({ id: 'pages.agent.generalModel', defaultMessage: '通用模型' }), value: intl.formatMessage({ id: 'pages.agent.generalModel', defaultMessage: '通用模型' }) },
          ]}
        />
      )
    } else if (item.dataIndex == 'status') {
      return (
        <Select
          defaultValue=""
          style={{ width: 150 }}
          allowClear
          options={[
            { label: intl.formatMessage({ id: 'pages.agent.running', defaultMessage: '运行中' }), value: 1 },
            { label: intl.formatMessage({ id: 'pages.agent.queuing', defaultMessage: '排队中' }), value: 2 },
            { label: intl.formatMessage({ id: 'pages.agent.training', defaultMessage: '训练中' }), value: 3 },
            { label: intl.formatMessage({ id: 'pages.agent.trainingFailed', defaultMessage: '训练失败' }), value: 4 },
          ]}
        />
      )
    }

  }
  //模型特色
  const featureRender = (dom: any, row: any) => {
    // 假设 feature 是存储在 row 对象中的
    const statusValues = row.feature;

    // 选项映射
    const options = [
      { label: intl.formatMessage({ id: 'pages.agent.flagship', defaultMessage: '旗舰' }), value: 1 },
      { label: intl.formatMessage({ id: 'pages.agent.highSpeed', defaultMessage: '高速' }), value: 2 },
      { label: intl.formatMessage({ id: 'pages.agent.toolInvocation', defaultMessage: '工具调用' }), value: 3 },
      { label: intl.formatMessage({ id: 'pages.agent.rolePlay', defaultMessage: '角色扮演' }), value: 4 },
      { label: intl.formatMessage({ id: 'pages.agent.longText', defaultMessage: '长文本' }), value: 5 },
      { label: intl.formatMessage({ id: 'pages.agent.imageUnderstanding', defaultMessage: '图片理解' }), value: 6 },
      { label: intl.formatMessage({ id: 'pages.agent.reasoning', defaultMessage: '推理能力' }), value: 7 },
      { label: intl.formatMessage({ id: 'pages.agent.videoUnderstanding', defaultMessage: '视频理解' }), value: 8 },
      { label: intl.formatMessage({ id: 'pages.agent.costPerformance', defaultMessage: '性价比' }), value: 9 },
      { label: intl.formatMessage({ id: 'pages.agent.codeExpert', defaultMessage: '代码专精' }), value: 10 },
      { label: intl.formatMessage({ id: 'pages.agent.audioUnderstanding', defaultMessage: '音频理解' }), value: 11 },
      { label: intl.formatMessage({ id: 'pages.agent.visualAnalysis', defaultMessage: '视觉分析' }), value: 12 },
    ];
    // 如果 status 为空或不存在，直接返回 null
    if (!statusValues) {
      return null;
    }
    // 确保 statusValues 是数组
    // 将值转换为数组，处理单个值和多个值的情况
    const values = typeof statusValues === 'string' ?
      statusValues.split(",").map(Number) :
      [Number(statusValues)];
    // 根据 value 获取对应的 label
    const labels = values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean); // 过滤掉 undefined 或 null 的值

    // 只显示前 4 个类型
    const displayLabels = labels.slice(0, 3);

    return (
      <div style={{ whiteSpace: 'pre-wrap' }}>
        {displayLabels.map((label, index) => (
          <Tag color="rgb(11, 211, 87)" key={index}>{label}</Tag>
        ))}

      </div>
    );
  };
  //logo图片
  const logoImageRender = (dom: any, row: any) => {
    //row 对象中的
    const data = [row]
    return (
      <List
        itemLayout="horizontal"
        dataSource={data}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              avatar={<Avatar src={item.logo} />}
              title={
                <>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ fontSize: '18px', color: 'black' }}> {item.name} · {featureToLabelMap[item.feature]} </div>
                    <div> <Tag className="green-button">{statusMap[item.status]}</Tag></div>
                  </div>
                  <div style={{ marginTop: '3px' }}>
                    <Tag className="green-text" style={{ marginLeft: '2px' }}>{typeMap[item.type]}</Tag> <span style={{ marginLeft: '8px' }}> {item.modelRights} </span>
                    <span style={{ marginLeft: '8px' }}> {item.modelFunction} </span>
                  </div>

                </>
              }
              description={
                <>
                  <div >{item.description}</div>
                  <div style={{ marginTop: '3px', color: 'rgba(204, 204, 204, 0.97)' }}>
                    <span>{item.manufacturer}</span> <span style={{ marginLeft: '10px' }}> {intl.formatMessage({ id: 'pages.agent.contextLength', defaultMessage: '上下文长度' })}: {item.modelLength == '0' ? '0K' : item.modelLength}</span>
                    <span style={{ marginLeft: '10px' }}> {item.createTime}</span>
                  </div>
                </>

              }
            />
          </List.Item>
        )}
      />
    );

  };

  //列表渲染
  const columns: any = [
    {
      title: "id",
      dataIndex: 'id',
      hideInSearch: true,
      ellipsis: true,
      hideInTable: true,
    },
    {
      // title: "模型名称",
      title: "",
      dataIndex: 'name',
      hideInSearch: false,
      ellipsis: true,
      render: logoImageRender,
      renderFormItem: typeFormItem
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.modelType', defaultMessage: '模型类型' }),
      dataIndex: 'type',
      width: 120,
      hideInSearch: false,
      ellipsis: true,
      render: typeRender,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.modelFeature', defaultMessage: '模型特色' }),
      dataIndex: 'feature',
      width: 120,
      hideInSearch: false,
      ellipsis: true,
      render: featureRender,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.modelProvider', defaultMessage: '模型厂商' }),
      dataIndex: 'manufacturer',
      width: 160,
      hideInSearch: false,
      ellipsis: true,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.modelSupportedFunctions', defaultMessage: '模型支持功能' }),
      dataIndex: 'modelFunction',
      hideInSearch: false,
      width: 100,
      ellipsis: true,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.contextLength', defaultMessage: '上下文长度' }),
      dataIndex: 'modelLength',
      width: 80,
      hideInSearch: false,
      ellipsis: true,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.userRights', defaultMessage: '用户权益' }),
      dataIndex: 'modelRights',
      width: 120,
      hideInSearch: false,
      ellipsis: true,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.agent.modelStatus', defaultMessage: '模型状态' }),
      dataIndex: 'status',
      hideInSearch: false,
      width: 100,
      ellipsis: true,
      render: statusRender,
      renderFormItem: typeFormItem,
      hideInTable: true,
    },
    // {
    //   title: intl.formatMessage({ id: 'pages.agent.creator', defaultMessage: '创建者' }),
    //   dataIndex: 'createrName',
    //   width: 80,
    //   hideInSearch: true,
    //   ellipsis: true,
    //   hideInTable: true,
    // },
    {
      title: intl.formatMessage({ id: 'pages.agent.creationTime', defaultMessage: '创建时间' }),
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 160,
      ellipsis: true,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.operation', defaultMessage: '操作' }),
      dataIndex: 'option',
      valueType: 'option',
      render: operateRender,
      hideInTable: true,
    },
  ];

  /**
   * 加载智能体信息
   * @param page 
   */
  const loadAgentInfo = async (page: any) => {
    console.log('load device')
    // console.log('load agents', agentsRef.current, agentsRef.current?.getFieldsValue())
    const searchValue = agentsRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let name = page.name ? page.name : searchValue.name
    if (page.name === 'no&input') {
      name = ''
    }
    let type = page.type ? page.type : searchValue.type
    if (page.type === 'no&input') {
      type = ''
    }
    let feature = page.feature ? page.feature : searchValue.feature
    if (page.feature === 'no&input') {
      feature = ''
    }
    let manufacturer = page.manufacturer ? page.manufacturer : searchValue.manufacturer
    if (page.manufacturer === 'no&input') {
      manufacturer = ''
    }
    let modelFunction = page.modelFunction ? page.modelFunction : searchValue.modelFunction
    if (page.modelFunction === 'no&input') {
      modelFunction = ''
    }
    let modelLength = page.modelLength ? page.modelLength : searchValue.modelLength
    if (page.modelLength === 'no&input') {
      modelLength = ''
    }
    let modelRights = page.modelRights ? page.modelRights : searchValue.modelRights
    if (page.modelRights === 'no&input') {
      modelRights = ''
    }
    let status = page.status ? page.status : searchValue.status
    if (page.status === 'no&input') {
      status = ''
    }
    const rs = await getAgentModelList({ name: name, type: type, feature: feature, manufacturer: manufacturer, modelFunction: modelFunction, modelLength: modelLength, modelRights: modelRights, status: status }, currentPage, pageSize);
    //const rs = await getAgentModelList(searchParams, currentPage, pageSize);
    //console.log("getAgentModelList rs->", rs)
    if (rs.code === 0 && rs.data && rs.data?.data.length >= 0) {
      setAgentsData(rs.data?.data)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
      // 新增针对数据下拉框值变化 
      //setSelectSearchValue(backData);
    }
    // 新增针对数据下拉框值变化
    if (rs.code === 0 && rs.detail) {
      setSelectSearchValue(rs.detail?.feature, rs.detail?.manufacturer, rs.detail?.modelFunction)
    }

  }

  //获取下拉框值数据
  const setSelectSearchValue = async (uniqueFeature: any, uniqueManufacturer: any, uniqueModelFunction: any) => {
    // 构建options数组
    const optionsFeatureArray = uniqueFeature.map(feature => ({
      value: feature,
      label: featureToLabelMap[feature]
    })).filter(item => item.label && typeof item.label === 'string' && item.label.trim() !== '');
    setOptionsFeature(optionsFeatureArray);

    const optionsManufacturerArray = uniqueManufacturer.map(manufacturer => ({
      value: manufacturer,
      label: manufacturer
    })).filter(item => item.label && typeof item.label === 'string' && item.label.trim() !== '');
    setOptionsManufacturer(optionsManufacturerArray);

    const optionsModelFunctionArray = uniqueModelFunction.map(modelFunction => ({
      value: modelFunction,
      label: modelFunction
    })).filter(item => item.label && typeof item.label === 'string' && item.label.trim() !== '');
    setOptionsModelFunction(optionsModelFunctionArray);
  }

  useEffect(() => {
    // 加载智能体模板信息
    loadAgentInfo({});
  }, [])

  return (
    <>
      <ProTable
        rowKey="id"
        headerTitle=""
        actionRef={actionRef}
        formRef={agentsRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,          // 默认每页显示的条数
          showQuickJumper: true, // 显示跳转至某页
          showSizeChanger: true, // 显示 pageSize 切换器
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'], // 指定每页可以显示多少条
          //onChange: (page, size) => setPageSize(size), // 更新 pageSize
          total: searchParams.total,   // 数据总数
        }}
        onChange={handleAgentPageChange}
        rowSelection={false}
        beforeSearchSubmit={handleBeforeSearchDevice}
        columns={columns}
        tableAlertRender={false}
        options={{
          density: false,
          setting: false,
          reload: false
        }}
        dataSource={agentsData}
      />

      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {intl.formatMessage({ id: 'pages.pointManage.selected', defaultMessage: '已选择', })}
              <a style={{ fontWeight: 600, color: 'rgb(11, 211, 87)' }}>{selectedRows.length}</a>
              {intl.formatMessage({ id: 'pages.pointManage.item', defaultMessage: '项', })}
            </div>
          }
        >
          <Button onClick={() => deleteRecord(selectedRows)}>{intl.formatMessage({ id: 'pages.pointManage.batchDelete', defaultMessage: '批量删除', })}</Button>
        </FooterToolbar>
      )}
    </>
  );
};

export default connect()(ModelManage);
