import CryptoJS from 'crypto-js';

const k = 'mars-g7h3c1a9b2z8x5l2626';
const _k = CryptoJS.enc.Utf8.parse(k);

// 加密函数
export function encryptAES(text: string): string {
  const e = CryptoJS.AES.encrypt(text, _k, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return e.toString();
}

// 解密函数
export function decryptAES(enc: string): string {
  const d = CryptoJS.AES.decrypt(enc, _k, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return d.toString(CryptoJS.enc.Utf8);
}
