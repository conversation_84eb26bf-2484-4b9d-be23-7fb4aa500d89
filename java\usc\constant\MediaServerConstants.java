package com.zkteco.mars.usc.constant;

public class MediaServerConstants {

    /**
     * 添加拉流代理
     */
    public static final String ADD_STREAM_PROXY = "/media/api/addStreamProxy";


    /**
     * 添加设备
     */
    public static final String ADD_DEVICE = "/media/api/addDevice";


    /**
     * 查询通道
     */
    public static final String GET_CHANNEL_INFO = "/media/api/getChannelInfo";

    /**
     * 添加流
     */
    public static final String ADD_STREAM = "/media/api/addStream";


    /**
     * 编辑通道
     */
    public static final String EDIT_CHANNEL = "/media/api/editChannel";


    /**
     * 删除拉流代理
     */
    public static final String DEL_STREAM_PROXY_V2= "/media/api/delStreamProxyV2";


    /**
     * 删除设备
     */
    public static final String DEL_DEVICE= "/media/api/delDevice";

    /**
     * 编辑流信息
     */
    public static final String EDIT_STREAM= "/media/api/editStream";





}
