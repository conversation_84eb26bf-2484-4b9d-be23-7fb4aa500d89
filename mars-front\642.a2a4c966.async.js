!(function(){"use strict";var Ue=Object.defineProperty,Ne=Object.defineProperties;var We=Object.getOwnPropertyDescriptors;var ke=Object.getOwnPropertySymbols;var He=Object.prototype.hasOwnProperty,Ke=Object.prototype.propertyIsEnumerable;var je=(se,B,e)=>B in se?Ue(se,B,{enumerable:!0,configurable:!0,writable:!0,value:e}):se[B]=e,Be=(se,B)=>{for(var e in B||(B={}))He.call(B,e)&&je(se,e,B[e]);if(ke)for(var e of ke(B))Ke.call(B,e)&&je(se,e,B[e]);return se},we=(se,B)=>Ne(se,We(B));var Ae=(se,B,e)=>new Promise((n,V)=>{var r=x=>{try{K(e.next(x))}catch(W){V(W)}},c=x=>{try{K(e.throw(x))}catch(W){V(W)}},K=x=>x.done?n(x.value):Promise.resolve(x.value).then(r,c);K((e=e.apply(se,B)).next())});(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[642],{10915:function(se,B,e){e.d(B,{_Y:function(){return Pe},L_:function(){return xe},ZP:function(){return _},nu:function(){return Q},YB:function(){return Te}});var n=e(74902),V=e(97685),r=e(45987),c=e(1413),K=e(11568),x=e(21532),W=e(37029),T=e(67294),X=e(10046),$=e(5068),F=e(51779),h=e(27484),P=e.n(h),d=e(98082),p=function(a,i){var b,u,J,R,k,v=(0,c.Z)({},a);return(0,c.Z)((0,c.Z)({bgLayout:"linear-gradient(".concat(i.colorBgContainer,", ").concat(i.colorBgLayout," 28%)"),colorTextAppListIcon:i.colorTextSecondary,appListIconHoverBgColor:v==null||(b=v.sider)===null||b===void 0?void 0:b.colorBgMenuItemSelected,colorBgAppListIconHover:(0,d.uK)(i.colorTextBase,.04),colorTextAppListIconHover:i.colorTextBase},v),{},{header:(0,c.Z)({colorBgHeader:(0,d.uK)(i.colorBgElevated,.6),colorBgScrollHeader:(0,d.uK)(i.colorBgElevated,.8),colorHeaderTitle:i.colorText,colorBgMenuItemHover:(0,d.uK)(i.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorBgMenuElevated:(v==null||(u=v.header)===null||u===void 0?void 0:u.colorBgHeader)!=="rgba(255, 255, 255, 0.6)"?(J=v.header)===null||J===void 0?void 0:J.colorBgHeader:i.colorBgElevated,colorTextMenuSelected:(0,d.uK)(i.colorTextBase,.95),colorBgRightActionsItemHover:(0,d.uK)(i.colorTextBase,.03),colorTextRightActionsItem:i.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:i.colorTextSecondary,colorTextMenuSecondary:i.colorTextTertiary,colorTextMenuTitle:i.colorText,colorTextMenuActive:i.colorText},v.header),sider:(0,c.Z)({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:0,colorBgCollapsedButton:i.colorBgElevated,colorTextCollapsedButtonHover:i.colorTextSecondary,colorTextCollapsedButton:(0,d.uK)(i.colorTextBase,.25),colorMenuBackground:"transparent",colorMenuItemDivider:(0,d.uK)(i.colorTextBase,.06),colorBgMenuItemHover:(0,d.uK)(i.colorTextBase,.03),colorBgMenuItemSelected:(0,d.uK)(i.colorTextBase,.04),colorTextMenuItemHover:i.colorText,colorTextMenuSelected:(0,d.uK)(i.colorTextBase,.95),colorTextMenuActive:i.colorText,colorTextMenu:i.colorTextSecondary,colorTextMenuSecondary:i.colorTextTertiary,colorTextMenuTitle:i.colorText,colorTextSubMenuSelected:(0,d.uK)(i.colorTextBase,.95)},v.sider),pageContainer:(0,c.Z)({colorBgPageContainer:"transparent",paddingInlinePageContainerContent:((R=v.pageContainer)===null||R===void 0?void 0:R.marginInlinePageContainerContent)||40,paddingBlockPageContainerContent:((k=v.pageContainer)===null||k===void 0?void 0:k.marginBlockPageContainerContent)||32,colorBgPageContainerFixed:i.colorBgElevated},v.pageContainer)})},f=e(67804),O=e(71002),y=function(){for(var a={},i=arguments.length,b=new Array(i),u=0;u<i;u++)b[u]=arguments[u];for(var J=b.length,R,k=0;k<J;k+=1)for(R in b[k])b[k].hasOwnProperty(R)&&((0,O.Z)(a[R])==="object"&&(0,O.Z)(b[k][R])==="object"&&a[R]!==void 0&&a[R]!==null&&!Array.isArray(a[R])&&!Array.isArray(b[k][R])?a[R]=(0,c.Z)((0,c.Z)({},a[R]),b[k][R]):a[R]=b[k][R]);return a},S=e(33852),C=e(85893),o=e(34155),E=["locale","getPrefixCls"],U=["locale","theme"],ee=function(a){var i={};if(Object.keys(a||{}).forEach(function(b){a[b]!==void 0&&(i[b]=a[b])}),!(Object.keys(i).length<1))return i},Q=function(){var a,i;return!(typeof o!="undefined"&&(((a="production")===null||a===void 0?void 0:a.toUpperCase())==="TEST"||((i="production")===null||i===void 0?void 0:i.toUpperCase())==="DEV"))},D=T.createContext({intl:(0,c.Z)((0,c.Z)({},F.Hi),{},{locale:"default"}),valueTypeMap:{},theme:f.emptyTheme,hashed:!0,dark:!1,token:f.defaultToken}),ge=D.Consumer,ye=function(){var a=(0,X.kY)(),i=a.cache;return(0,T.useEffect)(function(){return function(){i.clear()}},[]),null},be=function(a){var i,b=a.children,u=a.dark,J=a.valueTypeMap,R=a.autoClearCache,k=R===void 0?!1:R,v=a.token,ue=a.prefixCls,pe=a.intl,j=(0,T.useContext)(x.ZP.ConfigContext),M=j.locale,q=j.getPrefixCls,re=(0,r.Z)(j,E),de=(i=d.Ow.useToken)===null||i===void 0?void 0:i.call(d.Ow),w=(0,T.useContext)(D),te=ue?".".concat(ue):".".concat(q(),"-pro"),fe="."+q(),Fe="".concat(te),Se=(0,T.useMemo)(function(){return p(v||{},de.token||f.defaultToken)},[v,de.token]),me=(0,T.useMemo)(function(){var ae,le=M==null?void 0:M.locale,ve=(0,F.Vy)(le),I=pe!=null?pe:le&&((ae=w.intl)===null||ae===void 0?void 0:ae.locale)==="default"?F.Go[ve]:w.intl||F.Go[ve];return(0,c.Z)((0,c.Z)({},w),{},{dark:u!=null?u:w.dark,token:y(w.token,de.token,{proComponentsCls:te,antCls:fe,themeId:de.theme.id,layout:Se}),intl:I||F.Hi})},[M==null?void 0:M.locale,w,u,de.token,de.theme.id,te,fe,Se,pe]),Oe=(0,c.Z)((0,c.Z)({},me.token||{}),{},{proComponentsCls:te}),t=(0,K.fp)(de.theme,[de.token,Oe!=null?Oe:{}],{salt:Fe,override:Oe}),s=(0,V.Z)(t,2),m=s[0],z=s[1],L=(0,T.useMemo)(function(){return!(a.hashed===!1||w.hashed===!1)},[w.hashed,a.hashed]),g=(0,T.useMemo)(function(){return a.hashed===!1||w.hashed===!1||Q()===!1?"":de.hashId?de.hashId:z},[z,w.hashed,a.hashed]);(0,T.useEffect)(function(){P().locale((M==null?void 0:M.locale)||"zh-cn")},[M==null?void 0:M.locale]);var N=(0,T.useMemo)(function(){return(0,c.Z)((0,c.Z)({},re.theme),{},{hashId:g,hashed:L&&Q()})},[re.theme,g,L,Q()]),oe=(0,T.useMemo)(function(){return(0,c.Z)((0,c.Z)({},me),{},{valueTypeMap:J||(me==null?void 0:me.valueTypeMap),token:m,theme:de.theme,hashed:L,hashId:g})},[me,J,m,de.theme,L,g]),ne=(0,T.useMemo)(function(){return(0,C.jsx)(x.ZP,(0,c.Z)((0,c.Z)({},re),{},{theme:N,children:(0,C.jsx)(D.Provider,{value:oe,children:(0,C.jsxs)(C.Fragment,{children:[k&&(0,C.jsx)(ye,{}),b]})})}))},[re,N,oe,k,b]);return k?(0,C.jsx)($.J$,{value:{provider:function(){return new Map}},children:ne}):ne},Pe=function(a){var i=a.needDeps,b=a.dark,u=a.token,J=(0,T.useContext)(D),R=(0,T.useContext)(x.ZP.ConfigContext),k=R.locale,v=R.theme,ue=(0,r.Z)(R,U),pe=i&&J.hashId!==void 0&&Object.keys(a).sort().join("-")==="children-needDeps";if(pe)return(0,C.jsx)(C.Fragment,{children:a.children});var j=function(){var re=b!=null?b:J.dark;return re&&!Array.isArray(v==null?void 0:v.algorithm)?[d.Ow.darkAlgorithm,v==null?void 0:v.algorithm].filter(Boolean):re&&Array.isArray(v==null?void 0:v.algorithm)?[d.Ow.darkAlgorithm].concat((0,n.Z)((v==null?void 0:v.algorithm)||[])).filter(Boolean):v==null?void 0:v.algorithm},M=(0,c.Z)((0,c.Z)({},ue),{},{locale:k||W.Z,theme:ee((0,c.Z)((0,c.Z)({},v),{},{algorithm:j()}))});return(0,C.jsx)(x.ZP,(0,c.Z)((0,c.Z)({},M),{},{children:(0,C.jsx)(be,(0,c.Z)((0,c.Z)({},a),{},{token:u}))}))};function Te(){var l=(0,T.useContext)(x.ZP.ConfigContext),a=l.locale,i=(0,T.useContext)(D),b=i.intl;return b&&b.locale!=="default"?b||F.Hi:a!=null&&a.locale&&F.Go[(0,F.Vy)(a.locale)]||F.Hi}D.displayName="ProProvider";var xe=D,_=D},51779:function(se,B,e){e.d(B,{Vy:function(){return I},Go:function(){return le},Hi:function(){return b}});var n=e(56790),V={moneySymbol:"$",form:{lightFilter:{more:"\u0627\u0644\u0645\u0632\u064A\u062F",clear:"\u0646\u0638\u0641",confirm:"\u062A\u0623\u0643\u064A\u062F",itemUnit:"\u0639\u0646\u0627\u0635\u0631"}},tableForm:{search:"\u0627\u0628\u062D\u062B",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",submit:"\u0627\u0631\u0633\u0627\u0644",collapsed:"\u0645\u064F\u0642\u0644\u0635",expand:"\u0645\u064F\u0648\u0633\u0639",inputPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062F\u062E\u0627\u0644",selectPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062E\u062A\u064A\u0627\u0631"},alert:{clear:"\u0646\u0638\u0641",selected:"\u0645\u062D\u062F\u062F",item:"\u0639\u0646\u0635\u0631"},pagination:{total:{range:" ",total:"\u0645\u0646",item:"\u0639\u0646\u0627\u0635\u0631"}},tableToolBar:{leftPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noPin:"\u0627\u0644\u063A\u0627\u0621 \u0627\u0644\u062A\u062B\u0628\u064A\u062A",leftFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noFixedTitle:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0625\u0644\u0635\u0627\u0642",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",columnDisplay:"\u0627\u0644\u0623\u0639\u0645\u062F\u0629 \u0627\u0644\u0645\u0639\u0631\u0648\u0636\u0629",columnSetting:"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A",fullScreen:"\u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",exitFullScreen:"\u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",reload:"\u062A\u062D\u062F\u064A\u062B",density:"\u0627\u0644\u0643\u062B\u0627\u0641\u0629",densityDefault:"\u0627\u0641\u062A\u0631\u0627\u0636\u064A",densityLarger:"\u0623\u0643\u0628\u0631",densityMiddle:"\u0648\u0633\u0637",densitySmall:"\u0645\u062F\u0645\u062C"},stepsForm:{next:"\u0627\u0644\u062A\u0627\u0644\u064A",prev:"\u0627\u0644\u0633\u0627\u0628\u0642",submit:"\u0623\u0646\u0647\u0649"},loginForm:{submitText:"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"},editableTable:{action:{save:"\u0623\u0646\u0642\u0630",cancel:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0623\u0645\u0631",delete:"\u062D\u0630\u0641",add:"\u0625\u0636\u0627\u0641\u0629 \u0635\u0641 \u0645\u0646 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A"}},switch:{open:"\u0645\u0641\u062A\u0648\u062D",close:"\u063A\u0644\u0642"}},r={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE9s",clear:"Netejar",confirm:"Confirmar",itemUnit:"Elements"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col\xB7lapsar",inputPlaceholder:"Introdu\xEFu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitj\xE0",densitySmall:"Compacte"},stepsForm:{next:"Seg\xFCent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Cancel\xB7lar",delete:"Eliminar",add:"afegir una fila de dades"}},switch:{open:"obert",close:"tancat"}},c={moneySymbol:"K\u010D",deleteThisLine:"Smazat tento \u0159\xE1dek",copyThisLine:"Kop\xEDrovat tento \u0159\xE1dek",form:{lightFilter:{more:"V\xEDc",clear:"Vymazat",confirm:"Potvrdit",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Dotaz",reset:"Resetovat",submit:"Odeslat",collapsed:"Zv\u011Bt\u0161it",expand:"Zmen\u0161it",inputPlaceholder:"Zadejte pros\xEDm",selectPlaceholder:"Vyberte pros\xEDm"},alert:{clear:"Vymazat",selected:"Vybran\xFD",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eek"}},tableToolBar:{leftPin:"P\u0159ipnout doleva",rightPin:"P\u0159ipnout doprava",noPin:"Odepnuto",leftFixedTitle:"Fixov\xE1no nalevo",rightFixedTitle:"Fixov\xE1no napravo",noFixedTitle:"Neopraveno",reset:"Resetovat",columnDisplay:"Zobrazen\xED sloupc\u016F",columnSetting:"Nastaven\xED",fullScreen:"Cel\xE1 obrazovka",exitFullScreen:"Ukon\u010Dete celou obrazovku",reload:"Obnovit",density:"Hustota",densityDefault:"V\xFDchoz\xED",densityLarger:"V\u011Bt\u0161\xED",densityMiddle:"St\u0159edn\xED",densitySmall:"Kompaktn\xED"},stepsForm:{next:"Dal\u0161\xED",prev:"P\u0159edchoz\xED",submit:"Dokon\u010Dit"},loginForm:{submitText:"P\u0159ihl\xE1sit se"},editableTable:{onlyOneLineEditor:"Upravit lze pouze jeden \u0159\xE1dek",action:{save:"Ulo\u017Eit",cancel:"Zru\u0161it",delete:"Vymazat",add:"p\u0159idat \u0159\xE1dek dat"}},switch:{open:"otev\u0159\xEDt",close:"zav\u0159\xEDt"}},K={moneySymbol:"\u20AC",form:{lightFilter:{more:"Mehr",clear:"Zur\xFCcksetzen",confirm:"Best\xE4tigen",itemUnit:"Eintr\xE4ge"}},tableForm:{search:"Suchen",reset:"Zur\xFCcksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte ausw\xE4hlen"},alert:{clear:"Zur\xFCcksetzen",selected:"Ausgew\xE4hlt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Eintr\xE4gen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zur\xFCcksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Gr\xF6\xDFer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"L\xF6schen",add:"Hinzuf\xFCgen einer Datenzeile"}},switch:{open:"offen",close:"schlie\xDFen"}},x={moneySymbol:"\xA3",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},W={moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},T={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE1s",clear:"Limpiar",confirm:"Confirmar",itemUnit:"art\xEDculos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"art\xEDculos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3n",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"a\xF1adir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}},X={moneySymbol:"\u062A\u0648\u0645\u0627\u0646",form:{lightFilter:{more:"\u0628\u06CC\u0634\u062A\u0631",clear:"\u067E\u0627\u06A9 \u06A9\u0631\u062F\u0646",confirm:"\u062A\u0627\u06CC\u06CC\u062F",itemUnit:"\u0645\u0648\u0631\u062F"}},tableForm:{search:"\u062C\u0633\u062A\u062C\u0648",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",submit:"\u062A\u0627\u06CC\u06CC\u062F",collapsed:"\u0646\u0645\u0627\u06CC\u0634 \u0628\u06CC\u0634\u062A\u0631",expand:"\u0646\u0645\u0627\u06CC\u0634 \u06A9\u0645\u062A\u0631",inputPlaceholder:"\u067E\u06CC\u062F\u0627 \u06A9\u0646\u06CC\u062F",selectPlaceholder:"\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F"},alert:{clear:"\u067E\u0627\u06A9 \u0633\u0627\u0632\u06CC",selected:"\u0627\u0646\u062A\u062E\u0627\u0628",item:"\u0645\u0648\u0631\u062F"},pagination:{total:{range:" ",total:"\u0627\u0632",item:"\u0645\u0648\u0631\u062F"}},tableToolBar:{leftPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0686\u067E",rightPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0631\u0627\u0633\u062A",noPin:"\u0633\u0646\u062C\u0627\u0642 \u0646\u0634\u062F\u0647",leftFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0686\u067E",rightFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0631\u0627\u0633\u062A",noFixedTitle:"\u0634\u0646\u0627\u0648\u0631",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",columnDisplay:"\u0646\u0645\u0627\u06CC\u0634 \u0647\u0645\u0647",columnSetting:"\u062A\u0646\u0638\u06CC\u0645\u0627\u062A",fullScreen:"\u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",exitFullScreen:"\u062E\u0631\u0648\u062C \u0627\u0632 \u062D\u0627\u0644\u062A \u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",reload:"\u062A\u0627\u0632\u0647 \u0633\u0627\u0632\u06CC",density:"\u062A\u0631\u0627\u06A9\u0645",densityDefault:"\u067E\u06CC\u0634 \u0641\u0631\u0636",densityLarger:"\u0628\u0632\u0631\u06AF",densityMiddle:"\u0645\u062A\u0648\u0633\u0637",densitySmall:"\u06A9\u0648\u0686\u06A9"},stepsForm:{next:"\u0628\u0639\u062F\u06CC",prev:"\u0642\u0628\u0644\u06CC",submit:"\u0627\u062A\u0645\u0627\u0645"},loginForm:{submitText:"\u0648\u0631\u0648\u062F"},editableTable:{action:{save:"\u0630\u062E\u06CC\u0631\u0647",cancel:"\u0644\u063A\u0648",delete:"\u062D\u0630\u0641",add:"\u06CC\u06A9 \u0631\u062F\u06CC\u0641 \u062F\u0627\u062F\u0647 \u0627\u0636\u0627\u0641\u0647 \u06A9\u0646\u06CC\u062F"}},switch:{open:"\u0628\u0627\u0632",close:"\u0646\u0632\u062F\u06CC\u06A9"}},$={moneySymbol:"\u20AC",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"R\xE9initialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"R\xE9duire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"S\xE9lectionner une valeur"},alert:{clear:"R\xE9initialiser",selected:"S\xE9lectionn\xE9",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"\xE9l\xE9ments"}},tableToolBar:{leftPin:"\xC9pingler \xE0 gauche",rightPin:"\xC9pingler \xE0 gauche",noPin:"Sans \xE9pingle",leftFixedTitle:"Fixer \xE0 gauche",rightFixedTitle:"Fixer \xE0 droite",noFixedTitle:"Non fix\xE9",reset:"R\xE9initialiser",columnDisplay:"Affichage colonne",columnSetting:"R\xE9glages",fullScreen:"Plein \xE9cran",exitFullScreen:"Quitter Plein \xE9cran",reload:"Rafraichir",density:"Densit\xE9",densityDefault:"Par d\xE9faut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Pr\xE9c\xE9dente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de donn\xE9es"}},switch:{open:"ouvert",close:"pr\xE8s"}},F={moneySymbol:"\u20AA",deleteThisLine:"\u05DE\u05D7\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",copyThisLine:"\u05D4\u05E2\u05EA\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",form:{lightFilter:{more:"\u05D9\u05D5\u05EA\u05E8",clear:"\u05E0\u05E7\u05D4",confirm:"\u05D0\u05D9\u05E9\u05D5\u05E8",itemUnit:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableForm:{search:"\u05D7\u05D9\u05E4\u05D5\u05E9",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",submit:"\u05E9\u05DC\u05D7",collapsed:"\u05D4\u05E8\u05D7\u05D1",expand:"\u05DB\u05D5\u05D5\u05E5",inputPlaceholder:"\u05D0\u05E0\u05D0 \u05D4\u05DB\u05E0\u05E1",selectPlaceholder:"\u05D0\u05E0\u05D0 \u05D1\u05D7\u05E8"},alert:{clear:"\u05E0\u05E7\u05D4",selected:"\u05E0\u05D1\u05D7\u05E8",item:"\u05E4\u05E8\u05D9\u05D8"},pagination:{total:{range:" ",total:"\u05DE\u05EA\u05D5\u05DA",item:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableToolBar:{leftPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noPin:"\u05DC\u05D0 \u05DE\u05E6\u05D5\u05E8\u05E3",leftFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noFixedTitle:"\u05DC\u05D0 \u05DE\u05D5\u05E6\u05DE\u05D3",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",columnDisplay:"\u05EA\u05E6\u05D5\u05D2\u05EA \u05E2\u05DE\u05D5\u05D3\u05D5\u05EA",columnSetting:"\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA",fullScreen:"\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",exitFullScreen:"\u05E6\u05D0 \u05DE\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",reload:"\u05E8\u05E2\u05E0\u05DF",density:"\u05E8\u05D6\u05D5\u05DC\u05D5\u05E6\u05D9\u05D4",densityDefault:"\u05D1\u05E8\u05D9\u05E8\u05EA \u05DE\u05D7\u05D3\u05DC",densityLarger:"\u05D2\u05D3\u05D5\u05DC",densityMiddle:"\u05D1\u05D9\u05E0\u05D5\u05E0\u05D9",densitySmall:"\u05E7\u05D8\u05DF"},stepsForm:{next:"\u05D4\u05D1\u05D0",prev:"\u05E7\u05D5\u05D3\u05DD",submit:"\u05E1\u05D9\u05D5\u05DD"},loginForm:{submitText:"\u05DB\u05E0\u05D9\u05E1\u05D4"},editableTable:{onlyOneLineEditor:"\u05E0\u05D9\u05EA\u05DF \u05DC\u05E2\u05E8\u05D5\u05DA \u05E8\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D0\u05D7\u05EA",action:{save:"\u05E9\u05DE\u05D5\u05E8",cancel:"\u05D1\u05D9\u05D8\u05D5\u05DC",delete:"\u05DE\u05D7\u05D9\u05E7\u05D4",add:"\u05D4\u05D5\u05E1\u05E3 \u05E9\u05D5\u05E8\u05EA \u05E0\u05EA\u05D5\u05E0\u05D9\u05DD"}},switch:{open:"\u05E4\u05EA\u05D7",close:"\u05E1\u05D2\u05D5\u05E8"}},h={moneySymbol:"kn",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretra\u017Ei",reset:"Poni\u0161ti",submit:"Potvrdi",collapsed:"Ra\u0161iri",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"O\u010Disti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prika\u010Di lijevo",rightPin:"Prika\u010Di desno",noPin:"Bez prika\u010Denja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Iza\u0111i iz punog zaslona",reload:"Ponovno u\u010Ditaj",density:"Veli\u010Dina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljede\u0107i",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}},P={moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}},d={moneySymbol:"\u20AC",form:{lightFilter:{more:"pi\xF9",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalit\xE0 schermo intero",exitFullScreen:"Esci da modalit\xE0 schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}},p={moneySymbol:"\xA5",form:{lightFilter:{more:"\u66F4\u306B",clear:"\u30AF\u30EA\u30A2",confirm:"\u78BA\u8A8D",itemUnit:"\u30A2\u30A4\u30C6\u30E0"}},tableForm:{search:"\u691C\u7D22",reset:"\u30EA\u30BB\u30C3\u30C8",submit:"\u9001\u4FE1",collapsed:"\u62E1\u5927",expand:"\u6298\u7573",inputPlaceholder:"\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",selectPlaceholder:"\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044"},alert:{clear:"\u30AF\u30EA\u30A2",selected:"\u9078\u629E\u3057\u305F",item:"\u30A2\u30A4\u30C6\u30E0"},pagination:{total:{range:"\u30EC\u30B3\u30FC\u30C9",total:"/\u5408\u8A08",item:" "}},tableToolBar:{leftPin:"\u5DE6\u306B\u56FA\u5B9A",rightPin:"\u53F3\u306B\u56FA\u5B9A",noPin:"\u30AD\u30E3\u30F3\u30BB\u30EB",leftFixedTitle:"\u5DE6\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",rightFixedTitle:"\u53F3\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",noFixedTitle:"\u56FA\u5B9A\u3055\u308C\u3066\u306A\u3044\u9805\u76EE",reset:"\u30EA\u30BB\u30C3\u30C8",columnDisplay:"\u8868\u793A\u5217",columnSetting:"\u5217\u8868\u793A\u8A2D\u5B9A",fullScreen:"\u30D5\u30EB\u30B9\u30AF\u30EA\u30FC\u30F3",exitFullScreen:"\u7D42\u4E86",reload:"\u66F4\u65B0",density:"\u884C\u9AD8",densityDefault:"\u30C7\u30D5\u30A9\u30EB\u30C8",densityLarger:"\u5927",densityMiddle:"\u4E2D",densitySmall:"\u5C0F"},stepsForm:{next:"\u6B21\u3078",prev:"\u524D\u3078",submit:"\u9001\u4FE1"},loginForm:{submitText:"\u30ED\u30B0\u30A4\u30F3"},editableTable:{action:{save:"\u4FDD\u5B58",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",delete:"\u524A\u9664",add:"\u8FFD\u52A0"}},switch:{open:"\u958B\u304F",close:"\u9589\u3058\u308B"}},f={moneySymbol:"\u20A9",form:{lightFilter:{more:"\uB354\uBCF4\uAE30",clear:"\uCD08\uAE30\uD654",confirm:"\uD655\uC778",itemUnit:"\uAC74\uC218"}},tableForm:{search:"\uC870\uD68C",reset:"\uCD08\uAE30\uD654",submit:"\uC81C\uCD9C",collapsed:"\uD655\uC7A5",expand:"\uB2EB\uAE30",inputPlaceholder:"\uC785\uB825\uD574 \uC8FC\uC138\uC694",selectPlaceholder:"\uC120\uD0DD\uD574 \uC8FC\uC138\uC694"},alert:{clear:"\uCDE8\uC18C",selected:"\uC120\uD0DD",item:"\uAC74"},pagination:{total:{range:" ",total:"/ \uCD1D",item:"\uAC74"}},tableToolBar:{leftPin:"\uC67C\uCABD\uC73C\uB85C \uD540",rightPin:"\uC624\uB978\uCABD\uC73C\uB85C \uD540",noPin:"\uD540 \uC81C\uAC70",leftFixedTitle:"\uC67C\uCABD\uC73C\uB85C \uACE0\uC815",rightFixedTitle:"\uC624\uB978\uCABD\uC73C\uB85C \uACE0\uC815",noFixedTitle:"\uBE44\uACE0\uC815",reset:"\uCD08\uAE30\uD654",columnDisplay:"\uCEEC\uB7FC \uD45C\uC2DC",columnSetting:"\uC124\uC815",fullScreen:"\uC804\uCCB4 \uD654\uBA74",exitFullScreen:"\uC804\uCCB4 \uD654\uBA74 \uCDE8\uC18C",reload:"\uC0C8\uB85C \uACE0\uCE68",density:"\uC5EC\uBC31",densityDefault:"\uAE30\uBCF8",densityLarger:"\uB9CE\uC740 \uC5EC\uBC31",densityMiddle:"\uC911\uAC04 \uC5EC\uBC31",densitySmall:"\uC881\uC740 \uC5EC\uBC31"},stepsForm:{next:"\uB2E4\uC74C",prev:"\uC774\uC804",submit:"\uC885\uB8CC"},loginForm:{submitText:"\uB85C\uADF8\uC778"},editableTable:{action:{save:"\uC800\uC7A5",cancel:"\uCDE8\uC18C",delete:"\uC0AD\uC81C",add:"\uB370\uC774\uD130 \uD589 \uCD94\uAC00"}},switch:{open:"\uC5F4",close:"\uAC00\uAE4C \uC6B4"}},O={moneySymbol:"\u20AE",form:{lightFilter:{more:"\u0418\u043B\u04AF\u04AF",clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",confirm:"\u0411\u0430\u0442\u0430\u043B\u0433\u0430\u0430\u0436\u0443\u0443\u043B\u0430\u0445",itemUnit:"\u041D\u044D\u0433\u0436\u04AF\u04AF\u0434"}},tableForm:{search:"\u0425\u0430\u0439\u0445",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",submit:"\u0418\u043B\u0433\u044D\u044D\u0445",collapsed:"\u04E8\u0440\u0433\u04E9\u0442\u0433\u04E9\u0445",expand:"\u0425\u0443\u0440\u0430\u0430\u0445",inputPlaceholder:"\u0423\u0442\u0433\u0430 \u043E\u0440\u0443\u0443\u043B\u043D\u0430 \u0443\u0443",selectPlaceholder:"\u0423\u0442\u0433\u0430 \u0441\u043E\u043D\u0433\u043E\u043D\u043E \u0443\u0443"},alert:{clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",selected:"\u0421\u043E\u043D\u0433\u043E\u0433\u0434\u0441\u043E\u043D",item:"\u041D\u044D\u0433\u0436"},pagination:{total:{range:" ",total:"\u041D\u0438\u0439\u0442",item:"\u043C\u04E9\u0440"}},tableToolBar:{leftPin:"\u0417\u04AF\u04AF\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",rightPin:"\u0411\u0430\u0440\u0443\u0443\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",noPin:"\u0411\u044D\u0445\u043B\u044D\u0445\u0433\u04AF\u0439",leftFixedTitle:"\u0417\u04AF\u04AF\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",rightFixedTitle:"\u0411\u0430\u0440\u0443\u0443\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",noFixedTitle:"\u0417\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445\u0433\u04AF\u0439",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",columnDisplay:"\u0411\u0430\u0433\u0430\u043D\u0430\u0430\u0440 \u0445\u0430\u0440\u0443\u0443\u043B\u0430\u0445",columnSetting:"\u0422\u043E\u0445\u0438\u0440\u0433\u043E\u043E",fullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446\u044D\u044D\u0440",exitFullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446 \u0446\u0443\u0446\u043B\u0430\u0445",reload:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",density:"\u0425\u044D\u043C\u0436\u044D\u044D",densityDefault:"\u0425\u044D\u0432\u0438\u0439\u043D",densityLarger:"\u0422\u043E\u043C",densityMiddle:"\u0414\u0443\u043D\u0434",densitySmall:"\u0416\u0438\u0436\u0438\u0433"},stepsForm:{next:"\u0414\u0430\u0440\u0430\u0430\u0445",prev:"\u04E8\u043C\u043D\u04E9\u0445",submit:"\u0414\u0443\u0443\u0441\u0433\u0430\u0445"},loginForm:{submitText:"\u041D\u044D\u0432\u0442\u0440\u044D\u0445"},editableTable:{action:{save:"\u0425\u0430\u0434\u0433\u0430\u043B\u0430\u0445",cancel:"\u0426\u0443\u0446\u043B\u0430\u0445",delete:"\u0423\u0441\u0442\u0433\u0430\u0445",add:"\u041C\u04E9\u0440 \u043D\u044D\u043C\u044D\u0445"}},switch:{open:"\u041D\u044D\u044D\u0445",close:"\u0425\u0430\u0430\u0445"}},y={moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}},S={moneySymbol:"z\u0142",form:{lightFilter:{more:"Wi\u0119cej",clear:"Wyczy\u015B\u0107",confirm:"Potwierd\u017A",itemUnit:"Ilo\u015B\u0107"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierd\u017A",collapsed:"Poka\u017C wiecej",expand:"Poka\u017C mniej",inputPlaceholder:"Prosz\u0119 poda\u0107",selectPlaceholder:"Prosz\u0119 wybra\u0107"},alert:{clear:"Wyczy\u015B\u0107",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpis\xF3w"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypi\u0119te do lewej",rightFixedTitle:"Przypi\u0119te do prawej",noFixedTitle:"Nieprzypi\u0119te",reset:"Reset",columnDisplay:"Wy\u015Bwietlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pe\u0142en ekran",exitFullScreen:"Zamknij pe\u0142en ekran",reload:"Od\u015Bwie\u017C",density:"Odst\u0119p",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Zaloguj si\u0119"},editableTable:{action:{save:"Zapisa\u0107",cancel:"Anuluj",delete:"Usun\u0105\u0107",add:"dodawanie wiersza danych"}},switch:{open:"otwiera\u0107",close:"zamyka\u0107"}},C={moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar \xE0 esquerda",rightPin:"Fixar \xE0 direita",noPin:"Desfixado",leftFixedTitle:"Fixado \xE0 esquerda",rightFixedTitle:"Fixado \xE0 direita",noFixedTitle:"N\xE3o fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configura\xE7\xF5es",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padr\xE3o",densityLarger:"Largo",densityMiddle:"M\xE9dio",densitySmall:"Compacto"},stepsForm:{next:"Pr\xF3ximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}},o={moneySymbol:"\u20BD",form:{lightFilter:{more:"\u0415\u0449\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",confirm:"\u041E\u041A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0438\u0438"}},tableForm:{search:"\u041D\u0430\u0439\u0442\u0438",reset:"\u0421\u0431\u0440\u043E\u0441",submit:"\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C",collapsed:"\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C",expand:"\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435",selectPlaceholder:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",selected:"\u0412\u044B\u0431\u0440\u0430\u043D\u043E",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"},pagination:{total:{range:" ",total:"\u0438\u0437",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043B\u0435\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u041E\u0442\u043A\u0440\u0435\u043F\u0438\u0442\u044C",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043B\u0435\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u0431\u0440\u043E\u0441",columnDisplay:"\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0441\u0442\u043E\u043B\u0431\u0446\u0430",columnSetting:"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",fullScreen:"\u041F\u043E\u043B\u043D\u044B\u0439 \u044D\u043A\u0440\u0430\u043D",exitFullScreen:"\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430",reload:"\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",density:"\u0420\u0430\u0437\u043C\u0435\u0440",densityDefault:"\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",densityLarger:"\u0411\u043E\u043B\u044C\u0448\u043E\u0439",densityMiddle:"\u0421\u0440\u0435\u0434\u043D\u0438\u0439",densitySmall:"\u0421\u0436\u0430\u0442\u044B\u0439"},stepsForm:{next:"\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439",prev:"\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C"},loginForm:{submitText:"\u0412\u0445\u043E\u0434"},editableTable:{action:{save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",add:"\u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0440\u044F\u0434 \u0434\u0430\u043D\u043D\u044B\u0445"}},switch:{open:"\u041E\u0442\u043A\u0440\u044B\u0442\u044B\u0439 \u0447\u0435\u043C\u043F\u0438\u043E\u043D\u0430\u0442 \u043C\u0438\u0440\u0430 \u043F\u043E \u0442\u0435\u043D\u043D\u0438\u0441\u0443",close:"\u041F\u043E \u0430\u0434\u0440\u0435\u0441\u0443:"}},E={moneySymbol:"\u20AC",deleteThisLine:"Odstr\xE1ni\u0165 tento riadok",copyThisLine:"Skop\xEDrujte tento riadok",form:{lightFilter:{more:"Viac",clear:"Vy\u010Disti\u0165",confirm:"Potvr\u010Fte",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Vyhlada\u0165",reset:"Resetova\u0165",submit:"Odosla\u0165",collapsed:"Rozbali\u0165",expand:"Zbali\u0165",inputPlaceholder:"Pros\xEDm, zadajte",selectPlaceholder:"Pros\xEDm, vyberte"},alert:{clear:"Vy\u010Disti\u0165",selected:"Vybran\xFD",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eiek"}},tableToolBar:{leftPin:"Pripn\xFA\u0165 v\u013Eavo",rightPin:"Pripn\xFA\u0165 vpravo",noPin:"Odopnut\xE9",leftFixedTitle:"Fixovan\xE9 na \u013Eavo",rightFixedTitle:"Fixovan\xE9 na pravo",noFixedTitle:"Nefixovan\xE9",reset:"Resetova\u0165",columnDisplay:"Zobrazenie st\u013Apcov",columnSetting:"Nastavenia",fullScreen:"Cel\xE1 obrazovka",exitFullScreen:"Ukon\u010Di\u0165 cel\xFA obrazovku",reload:"Obnovi\u0165",density:"Hustota",densityDefault:"Predvolen\xE9",densityLarger:"V\xE4\u010D\u0161ie",densityMiddle:"Stredn\xE9",densitySmall:"Kompaktn\xE9"},stepsForm:{next:"\u010Eal\u0161ie",prev:"Predch\xE1dzaj\xFAce",submit:"Potvrdi\u0165"},loginForm:{submitText:"Prihl\xE1si\u0165 sa"},editableTable:{onlyOneLineEditor:"Upravova\u0165 mo\u017Eno iba jeden riadok",action:{save:"Ulo\u017Ei\u0165",cancel:"Zru\u0161i\u0165",delete:"Odstr\xE1ni\u0165",add:"prida\u0165 riadok \xFAdajov"}},switch:{open:"otvori\u0165",close:"zavrie\u0165"}},U={moneySymbol:"RSD",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Prona\u0111i",reset:"Resetuj",submit:"Po\u0161alji",collapsed:"Pro\u0161iri",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"O\u010Disti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zaka\u010Di levo",rightPin:"Zaka\u010Di desno",noPin:"Nije zaka\u010Deno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Pode\u0161avanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osve\u017Ei",density:"Veli\u010Dina",densityDefault:"Podrazumevana",densityLarger:"Ve\u0107a",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sa\u010Duvaj",cancel:"Poni\u0161ti",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"\u041E\u0442\u0432\u043E\u0440\u0438\u0442\u0435",close:"\u0417\u0430\u0442\u0432\u043E\u0440\u0438\u0442\u0435"}},ee={moneySymbol:"\u0E3F",deleteThisLine:"\u0E25\u0E1A\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",copyThisLine:"\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",form:{lightFilter:{more:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32",clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",confirm:"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19",itemUnit:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableForm:{search:"\u0E2A\u0E2D\u0E1A\u0E16\u0E32\u0E21",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",submit:"\u0E2A\u0E48\u0E07",collapsed:"\u0E02\u0E22\u0E32\u0E22",expand:"\u0E17\u0E23\u0E38\u0E14",inputPlaceholder:"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E1B\u0E49\u0E2D\u0E19",selectPlaceholder:"\u0E42\u0E1B\u0E23\u0E14\u0E40\u0E25\u0E37\u0E2D\u0E01"},alert:{clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",selected:"\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E41\u0E25\u0E49\u0E27",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"},pagination:{total:{range:" ",total:"\u0E02\u0E2D\u0E07",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableToolBar:{leftPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E0B\u0E49\u0E32\u0E22",rightPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E02\u0E27\u0E32",noPin:"\u0E40\u0E25\u0E34\u0E01\u0E15\u0E23\u0E36\u0E07\u0E41\u0E25\u0E49\u0E27",leftFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E0B\u0E49\u0E32\u0E22",rightFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E02\u0E27\u0E32",noFixedTitle:"\u0E44\u0E21\u0E48\u0E04\u0E07\u0E17\u0E35\u0E48",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",columnDisplay:"\u0E01\u0E32\u0E23\u0E41\u0E2A\u0E14\u0E07\u0E04\u0E2D\u0E25\u0E31\u0E21\u0E19\u0E4C",columnSetting:"\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32",fullScreen:"\u0E40\u0E15\u0E47\u0E21\u0E08\u0E2D",exitFullScreen:"\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E42\u0E2B\u0E21\u0E14\u0E40\u0E15\u0E47\u0E21\u0E2B\u0E19\u0E49\u0E32\u0E08\u0E2D",reload:"\u0E23\u0E35\u0E40\u0E1F\u0E23\u0E0A",density:"\u0E04\u0E27\u0E32\u0E21\u0E2B\u0E19\u0E32\u0E41\u0E19\u0E48\u0E19",densityDefault:"\u0E04\u0E48\u0E32\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19",densityLarger:"\u0E02\u0E19\u0E32\u0E14\u0E43\u0E2B\u0E0D\u0E48\u0E02\u0E36\u0E49\u0E19",densityMiddle:"\u0E01\u0E25\u0E32\u0E07",densitySmall:"\u0E01\u0E30\u0E17\u0E31\u0E14\u0E23\u0E31\u0E14"},stepsForm:{next:"\u0E16\u0E31\u0E14\u0E44\u0E1B",prev:"\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",submit:"\u0E40\u0E2A\u0E23\u0E47\u0E08"},loginForm:{submitText:"\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A"},editableTable:{onlyOneLineEditor:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E44\u0E14\u0E49\u0E40\u0E1E\u0E35\u0E22\u0E07\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E40\u0E14\u0E35\u0E22\u0E27\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",action:{save:"\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01",cancel:"\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",delete:"\u0E25\u0E1A",add:"\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E41\u0E16\u0E27\u0E02\u0E2D\u0E07\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"}},switch:{open:"\u0E40\u0E1B\u0E34\u0E14",close:"\u0E1B\u0E34\u0E14"}},Q={moneySymbol:"\u20BA",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"\xD6\u011Feler"}},tableForm:{search:"Filtrele",reset:"S\u0131f\u0131rla",submit:"G\xF6nder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer girin",selectPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer se\xE7in"},alert:{clear:"Temizle",selected:"Se\xE7ili",item:"\xD6\u011Fe"},pagination:{total:{range:" ",total:"Toplam",item:"\xD6\u011Fe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sa\u011Fa sabitle",noPin:"Sabitlemeyi kald\u0131r",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sa\u011Fa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"S\u0131f\u0131rla",columnDisplay:"Kolon G\xF6r\xFCn\xFCm\xFC",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan \xC7\u0131k",reload:"Yenile",density:"Kal\u0131nl\u0131k",densityDefault:"Varsay\u0131lan",densityLarger:"B\xFCy\xFCk",densityMiddle:"Orta",densitySmall:"K\xFC\xE7\xFCk"},stepsForm:{next:"S\u0131radaki",prev:"\xD6nceki",submit:"G\xF6nder"},loginForm:{submitText:"Giri\u015F Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazge\xE7",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"a\xE7\u0131k",close:"kapatmak"}},D={moneySymbol:"\u20B4",deleteThisLine:"\u0412\u0438\u0434\u0430\u0442\u0438\u043B\u0438 \u0440\u044F\u0434\u043E\u043A",copyThisLine:"\u0421\u043A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A",form:{lightFilter:{more:"\u0429\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",confirm:"\u041E\u043A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0456\u0457"}},tableForm:{search:"\u041F\u043E\u0448\u0443\u043A",reset:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",submit:"\u0412\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",collapsed:"\u0420\u043E\u0437\u0433\u043E\u0440\u043D\u0443\u0442\u0438",expand:"\u0417\u0433\u043E\u0440\u043D\u0443\u0442\u0438",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F",selectPlaceholder:"\u041E\u0431\u0435\u0440\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",selected:"\u041E\u0431\u0440\u0430\u043D\u043E",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"},pagination:{total:{range:" ",total:"\u0437",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0437\u043B\u0456\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u0412\u0456\u0434\u043A\u0440\u0456\u043F\u0438\u0442\u0438",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0437\u043B\u0456\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u043A\u0438\u043D\u0443\u0442\u0438",columnDisplay:"\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0441\u0442\u043E\u0432\u043F\u0446\u0456\u0432",columnSetting:"\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F",fullScreen:"\u041F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u0438\u0439 \u0440\u0435\u0436\u0438\u043C",exitFullScreen:"\u0412\u0438\u0439\u0442\u0438 \u0437 \u043F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0443",reload:"\u041E\u043D\u043E\u0432\u0438\u0442\u0438",density:"\u0420\u043E\u0437\u043C\u0456\u0440",densityDefault:"\u0417\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",densityLarger:"\u0412\u0435\u043B\u0438\u043A\u0438\u0439",densityMiddle:"\u0421\u0435\u0440\u0435\u0434\u043D\u0456\u0439",densitySmall:"\u0421\u0442\u0438\u0441\u043B\u0438\u0439"},stepsForm:{next:"\u041D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439",prev:"\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438"},loginForm:{submitText:"\u0412\u0445\u0456\u0445"},editableTable:{onlyOneLineEditor:"\u0422\u0456\u043B\u044C\u043A\u0438 \u043E\u0434\u0438\u043D \u0440\u044F\u0434\u043E\u043A \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u0440\u0435\u0434\u0430\u0433\u043E\u0432\u0430\u043D\u0438\u0439 \u043E\u0434\u043D\u043E\u0447\u0430\u0441\u043D\u043E",action:{save:"\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438",cancel:"\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438",delete:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",add:"\u0434\u043E\u0434\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A"}},switch:{open:"\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u043E",close:"\u0417\u0430\u043A\u0440\u0438\u0442\u043E"}},ge={moneySymbol:"UZS",form:{lightFilter:{more:"Yana",clear:"Tozalash",confirm:"OK",itemUnit:"Pozitsiyalar"}},tableForm:{search:"Qidirish",reset:"Qayta tiklash",submit:"Yuborish",collapsed:"Yig\u2018ish",expand:"Kengaytirish",inputPlaceholder:"Qiymatni kiriting",selectPlaceholder:"Qiymatni tanlang"},alert:{clear:"Tozalash",selected:"Tanlangan",item:"elementlar"},pagination:{total:{range:" ",total:"dan",item:"elementlar"}},tableToolBar:{leftPin:"Chapga mahkamlash",rightPin:"O\u2018ngga mahkamlash",noPin:"Mahkamlashni olib tashlash",leftFixedTitle:"Chapga mahkamlangan",rightFixedTitle:"O\u2018ngga mahkamlangan",noFixedTitle:"Mahkamlashsiz",reset:"Qayta tiklash",columnDisplay:"Ustunni ko\u2018rsatish",columnSetting:"Sozlamalar",fullScreen:"To\u2018liq ekran",exitFullScreen:"To\u2018liq ekrandan chiqish",reload:"Yangilash",density:"O\u2018lcham",densityDefault:"Standart",densityLarger:"Katta",densityMiddle:"O\u2018rtacha",densitySmall:"Kichik"},stepsForm:{next:"Keyingi",prev:"Oldingi",submit:"Tugatish"},loginForm:{submitText:"Kirish"},editableTable:{action:{save:"Saqlash",cancel:"Bekor qilish",delete:"O\u2018chirish",add:"ma\u02BClumotlar qatorini qo\u2018shish"}},switch:{open:"Ochish",close:"Yopish"}},ye={moneySymbol:"\u20AB",form:{lightFilter:{more:"Nhi\u1EC1u h\u01A1n",clear:"Trong",confirm:"X\xE1c nh\u1EADn",itemUnit:"M\u1EE5c"}},tableForm:{search:"T\xECm ki\u1EBFm",reset:"L\xE0m l\u1EA1i",submit:"G\u1EEDi \u0111i",collapsed:"M\u1EDF r\u1ED9ng",expand:"Thu g\u1ECDn",inputPlaceholder:"nh\u1EADp d\u1EEF li\u1EC7u",selectPlaceholder:"Vui l\xF2ng ch\u1ECDn"},alert:{clear:"X\xF3a",selected:"\u0111\xE3 ch\u1ECDn",item:"m\u1EE5c"},pagination:{total:{range:" ",total:"tr\xEAn",item:"m\u1EB7t h\xE0ng"}},tableToolBar:{leftPin:"Ghim tr\xE1i",rightPin:"Ghim ph\u1EA3i",noPin:"B\u1ECF ghim",leftFixedTitle:"C\u1ED1 \u0111\u1ECBnh tr\xE1i",rightFixedTitle:"C\u1ED1 \u0111\u1ECBnh ph\u1EA3i",noFixedTitle:"Ch\u01B0a c\u1ED1 \u0111\u1ECBnh",reset:"L\xE0m l\u1EA1i",columnDisplay:"C\u1ED9t hi\u1EC3n th\u1ECB",columnSetting:"C\u1EA5u h\xECnh",fullScreen:"Ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",exitFullScreen:"Tho\xE1t ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",reload:"L\xE0m m\u1EDBi",density:"M\u1EADt \u0111\u1ED9 hi\u1EC3n th\u1ECB",densityDefault:"M\u1EB7c \u0111\u1ECBnh",densityLarger:"M\u1EB7c \u0111\u1ECBnh",densityMiddle:"Trung b\xECnh",densitySmall:"Ch\u1EADt"},stepsForm:{next:"Sau",prev:"Tr\u01B0\u1EDBc",submit:"K\u1EBFt th\xFAc"},loginForm:{submitText:"\u0110\u0103ng nh\u1EADp"},editableTable:{action:{save:"C\u1EE9u",cancel:"H\u1EE7y",delete:"X\xF3a",add:"th\xEAm m\u1ED9t h\xE0ng d\u1EEF li\u1EC7u"}},switch:{open:"m\u1EDF",close:"\u0111\xF3ng"}},be={moneySymbol:"\xA5",deleteThisLine:"\u5220\u9664\u6B64\u9879",copyThisLine:"\u590D\u5236\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7B5B\u9009",clear:"\u6E05\u9664",confirm:"\u786E\u8BA4",itemUnit:"\u9879"}},tableForm:{search:"\u67E5\u8BE2",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u5F00",expand:"\u6536\u8D77",inputPlaceholder:"\u8BF7\u8F93\u5165",selectPlaceholder:"\u8BF7\u9009\u62E9"},alert:{clear:"\u53D6\u6D88\u9009\u62E9",selected:"\u5DF2\u9009\u62E9",item:"\u9879"},pagination:{total:{range:"\u7B2C",total:"\u6761/\u603B\u5171",item:"\u6761"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5728\u5217\u9996",rightPin:"\u56FA\u5B9A\u5728\u5217\u5C3E",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u4FA7",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u4FA7",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8BBE\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BBD\u677E",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7D27\u51D1"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u63D0\u4EA4"},loginForm:{submitText:"\u767B\u5F55"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u5220\u9664",add:"\u6DFB\u52A0\u4E00\u884C\u6570\u636E"}},switch:{open:"\u6253\u5F00",close:"\u5173\u95ED"}},Pe={moneySymbol:"NT$",deleteThisLine:"\u522A\u9664\u6B64\u9879",copyThisLine:"\u8907\u88FD\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7BE9\u9078",clear:"\u6E05\u9664",confirm:"\u78BA\u8A8D",itemUnit:"\u9805"}},tableForm:{search:"\u67E5\u8A62",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u958B",expand:"\u6536\u8D77",inputPlaceholder:"\u8ACB\u8F38\u5165",selectPlaceholder:"\u8ACB\u9078\u64C7"},alert:{clear:"\u53D6\u6D88\u9078\u64C7",selected:"\u5DF2\u9078\u64C7",item:"\u9805"},pagination:{total:{range:"\u7B2C",total:"\u689D/\u7E3D\u5171",item:"\u689D"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5230\u5DE6\u908A",rightPin:"\u56FA\u5B9A\u5230\u53F3\u908A",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u5074",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u5074",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8A2D\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BEC\u9B06",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7DCA\u6E4A"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u5B8C\u6210"},loginForm:{submitText:"\u767B\u5165"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u6642\u7DE8\u8F2F\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u522A\u9664",add:"\u65B0\u589E\u4E00\u884C\u8CC7\u6599"}},switch:{open:"\u6253\u958B",close:"\u95DC\u9589"}},Te={moneySymbol:"\u20AC",deleteThisLine:"Verwijder deze regel",copyThisLine:"Kopieer deze regel",form:{lightFilter:{more:"Meer filters",clear:"Wissen",confirm:"Bevestigen",itemUnit:"item"}},tableForm:{search:"Zoeken",reset:"Resetten",submit:"Indienen",collapsed:"Uitvouwen",expand:"Inklappen",inputPlaceholder:"Voer in",selectPlaceholder:"Selecteer"},alert:{clear:"Selectie annuleren",selected:"Geselecteerd",item:"item"},pagination:{total:{range:"Van",total:"items/totaal",item:"items"}},tableToolBar:{leftPin:"Vastzetten aan begin",rightPin:"Vastzetten aan einde",noPin:"Niet vastzetten",leftFixedTitle:"Vastzetten aan de linkerkant",rightFixedTitle:"Vastzetten aan de rechterkant",noFixedTitle:"Niet vastzetten",reset:"Resetten",columnDisplay:"Kolomweergave",columnSetting:"Kolominstellingen",fullScreen:"Volledig scherm",exitFullScreen:"Verlaat volledig scherm",reload:"Vernieuwen",density:"Dichtheid",densityDefault:"Normaal",densityLarger:"Ruim",densityMiddle:"Gemiddeld",densitySmall:"Compact"},stepsForm:{next:"Volgende stap",prev:"Vorige stap",submit:"Indienen"},loginForm:{submitText:"Inloggen"},editableTable:{onlyOneLineEditor:"Slechts \xE9\xE9n regel tegelijk bewerken",action:{save:"Opslaan",cancel:"Annuleren",delete:"Verwijderen",add:"Een regel toevoegen"}},switch:{open:"Openen",close:"Sluiten"}},xe={moneySymbol:"RON",deleteThisLine:"\u0218terge acest r\xE2nd",copyThisLine:"Copiaz\u0103 acest r\xE2nd",form:{lightFilter:{more:"Mai multe filtre",clear:"Cur\u0103\u021B\u0103",confirm:"Confirm\u0103",itemUnit:"elemente"}},tableForm:{search:"Caut\u0103",reset:"Reseteaz\u0103",submit:"Trimite",collapsed:"Extinde",expand:"Restr\xE2nge",inputPlaceholder:"Introduce\u021Bi",selectPlaceholder:"Selecta\u021Bi"},alert:{clear:"Anuleaz\u0103 selec\u021Bia",selected:"Selectat",item:"elemente"},pagination:{total:{range:"De la",total:"elemente/total",item:"elemente"}},tableToolBar:{leftPin:"Fixeaz\u0103 la \xEEnceput",rightPin:"Fixeaz\u0103 la sf\xE2r\u0219it",noPin:"Nu fixa",leftFixedTitle:"Fixeaz\u0103 \xEEn st\xE2nga",rightFixedTitle:"Fixeaz\u0103 \xEEn dreapta",noFixedTitle:"Nu fixa",reset:"Reseteaz\u0103",columnDisplay:"Afi\u0219are coloane",columnSetting:"Set\u0103ri coloane",fullScreen:"Ecran complet",exitFullScreen:"Ie\u0219i din ecran complet",reload:"Re\xEEncarc\u0103",density:"Densitate",densityDefault:"Normal",densityLarger:"Larg",densityMiddle:"Mediu",densitySmall:"Compact"},stepsForm:{next:"Pasul urm\u0103tor",prev:"Pasul anterior",submit:"Trimite"},loginForm:{submitText:"Autentificare"},editableTable:{onlyOneLineEditor:"Se poate edita doar un r\xE2nd simultan",action:{save:"Salveaz\u0103",cancel:"Anuleaz\u0103",delete:"\u0218terge",add:"Adaug\u0103 un r\xE2nd"}},switch:{open:"Deschide",close:"\xCEnchide"}},_={moneySymbol:"SEK",deleteThisLine:"Radera denna rad",copyThisLine:"Kopiera denna rad",form:{lightFilter:{more:"Fler filter",clear:"Rensa",confirm:"Bekr\xE4fta",itemUnit:"objekt"}},tableForm:{search:"S\xF6k",reset:"\xC5terst\xE4ll",submit:"Skicka",collapsed:"Expandera",expand:"F\xE4ll ihop",inputPlaceholder:"V\xE4nligen ange",selectPlaceholder:"V\xE4nligen v\xE4lj"},alert:{clear:"Avbryt val",selected:"Vald",item:"objekt"},pagination:{total:{range:"Fr\xE5n",total:"objekt/totalt",item:"objekt"}},tableToolBar:{leftPin:"F\xE4st till v\xE4nster",rightPin:"F\xE4st till h\xF6ger",noPin:"Inte f\xE4st",leftFixedTitle:"F\xE4st till v\xE4nster",rightFixedTitle:"F\xE4st till h\xF6ger",noFixedTitle:"Inte f\xE4st",reset:"\xC5terst\xE4ll",columnDisplay:"Kolumnvisning",columnSetting:"Kolumninst\xE4llningar",fullScreen:"Fullsk\xE4rm",exitFullScreen:"Avsluta fullsk\xE4rm",reload:"Ladda om",density:"T\xE4thet",densityDefault:"Normal",densityLarger:"L\xF6s",densityMiddle:"Medium",densitySmall:"Kompakt"},stepsForm:{next:"N\xE4sta steg",prev:"F\xF6reg\xE5ende steg",submit:"Skicka"},loginForm:{submitText:"Logga in"},editableTable:{onlyOneLineEditor:"Endast en rad kan redigeras \xE5t g\xE5ngen",action:{save:"Spara",cancel:"Avbryt",delete:"Radera",add:"L\xE4gg till en rad"}},switch:{open:"\xD6ppna",close:"St\xE4ng"}},l=function(G,H){return{getMessage:function(Ee,ce){var ie=(0,n.U2)(H,Ee.replace(/\[(\d+)\]/g,".$1").split("."))||"";if(ie)return ie;var Y=G.replace("_","-");if(Y==="zh-CN")return ce;var Z=le["zh-CN"];return Z?Z.getMessage(Ee,ce):ce},locale:G}},a=l("mn_MN",O),i=l("ar_EG",V),b=l("zh_CN",be),u=l("en_US",W),J=l("en_GB",x),R=l("vi_VN",ye),k=l("it_IT",d),v=l("ja_JP",p),ue=l("es_ES",T),pe=l("ca_ES",r),j=l("ru_RU",o),M=l("sr_RS",U),q=l("ms_MY",y),re=l("zh_TW",Pe),de=l("fr_FR",$),w=l("pt_BR",C),te=l("ko_KR",f),fe=l("id_ID",P),Fe=l("de_DE",K),Se=l("fa_IR",X),me=l("tr_TR",Q),Oe=l("pl_PL",S),t=l("hr_",h),s=l("th_TH",ee),m=l("cs_cz",c),z=l("sk_SK",E),L=l("he_IL",F),g=l("uk_UA",D),N=l("uz_UZ",ge),oe=l("nl_NL",Te),ne=l("ro_RO",xe),ae=l("sv_SE",_),le={"mn-MN":a,"ar-EG":i,"zh-CN":b,"en-US":u,"en-GB":J,"vi-VN":R,"it-IT":k,"ja-JP":v,"es-ES":ue,"ca-ES":pe,"ru-RU":j,"sr-RS":M,"ms-MY":q,"zh-TW":re,"fr-FR":de,"pt-BR":w,"ko-KR":te,"id-ID":fe,"de-DE":Fe,"fa-IR":Se,"tr-TR":me,"pl-PL":Oe,"hr-HR":t,"th-TH":s,"cs-CZ":m,"sk-SK":z,"he-IL":L,"uk-UA":g,"uz-UZ":N,"nl-NL":oe,"ro-RO":ne,"sv-SE":ae},ve=Object.keys(le),I=function(G){var H=(G||"zh-CN").toLocaleLowerCase();return ve.find(function(Ce){var Ee=Ce.toLocaleLowerCase();return Ee.includes(H)})}},98082:function(se,B,e){e.d(B,{Nd:function(){return p},Ow:function(){return h},Wf:function(){return d},Xj:function(){return f},dQ:function(){return P},uK:function(){return X}});var n=e(1413),V=e(11568),r=e(10274),c=e(9361),K=e(21532),x=e(67294),W=e(10915),T=e(67804),X=function(y,S){return new r.C(y).setAlpha(S).toRgbString()},$=function(y,S){var C=new TinyColor(y);return C.lighten(S).toHexString()},F=function(){return typeof c.Z=="undefined"||!c.Z?T:c.Z},h=F(),P=h.useToken,d=function(y){return{boxSizing:"border-box",margin:0,padding:0,color:y.colorText,fontSize:y.fontSize,lineHeight:y.lineHeight,listStyle:"none"}},p=function(y){return{color:y.colorLink,outline:"none",cursor:"pointer",transition:"color ".concat(y.motionDurationSlow),"&:focus, &:hover":{color:y.colorLinkHover},"&:active":{color:y.colorLinkActive}}};function f(O,y){var S,C=(0,x.useContext)(W.L_),o=C.token,E=o===void 0?{}:o,U=(0,x.useContext)(W.L_),ee=U.hashed,Q=P(),D=Q.token,ge=Q.hashId,ye=(0,x.useContext)(W.L_),be=ye.theme,Pe=(0,x.useContext)(K.ZP.ConfigContext),Te=Pe.getPrefixCls,xe=Pe.csp;return E.layout||(E=(0,n.Z)({},D)),E.proComponentsCls=(S=E.proComponentsCls)!==null&&S!==void 0?S:".".concat(Te("pro")),E.antCls=".".concat(Te()),{wrapSSR:(0,V.xy)({theme:be,token:E,path:[O],nonce:xe==null?void 0:xe.nonce},function(){return y(E)}),hashId:ee?ge:""}}},67804:function(se,B,e){e.r(B),e.d(B,{defaultToken:function(){return K},emptyTheme:function(){return W},hashCode:function(){return x},token:function(){return T},useToken:function(){return X}});var n=e(1413),V=e(11568),r=e(9361),c,K={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff7875",colorInfo:"#1677ff",colorTextBase:"#000",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:4,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,"blue-1":"#e6f4ff","blue-2":"#bae0ff","blue-3":"#91caff","blue-4":"#69b1ff","blue-5":"#4096ff","blue-6":"#1677ff","blue-7":"#0958d9","blue-8":"#003eb3","blue-9":"#002c8c","blue-10":"#001d66","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorText:"rgba(0, 0, 0, 0.88)",colorTextSecondary:"rgba(0, 0, 0, 0.65)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorFill:"rgba(0, 0, 0, 0.15)",colorFillSecondary:"rgba(0, 0, 0, 0.06)",colorFillTertiary:"rgba(0, 0, 0, 0.04)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",colorBgLayout:"hsl(220,23%,97%)",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#95de64",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ffa39e",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffd666",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f4ff",colorInfoBgHover:"#bae0ff",colorInfoBorder:"#91caff",colorInfoBorderHover:"#69b1ff",colorInfoHover:"#69b1ff",colorInfoActive:"#0958d9",colorInfoTextHover:"#4096ff",colorInfoText:"#1677ff",colorInfoTextActive:"#0958d9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorWhite:"#fff",sizeXXL:48,sizeXL:32,sizeLG:24,sizeMD:20,sizeMS:16,size:16,sizeSM:12,sizeXS:8,sizeXXS:4,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],lineWidthBold:2,borderRadiusXS:1,borderRadiusSM:4,borderRadiusLG:8,borderRadiusOuter:4,colorLink:"#1677ff",colorLinkHover:"#69b1ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.06)",colorFillContentHover:"rgba(0, 0, 0, 0.15)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.04)",colorBorderBg:"#ffffff",colorSplit:"rgba(5, 5, 5, 0.06)",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.88)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.45)",colorTextLightSolid:"#fff",colorHighlight:"#ff7875",colorBgTextHover:"rgba(0, 0, 0, 0.06)",colorBgTextActive:"rgba(0, 0, 0, 0.15)",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.88)",colorErrorOutline:"rgba(255, 38, 5, 0.06)",colorWarningOutline:"rgba(255, 215, 5, 0.1)",fontSizeSM:12,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.04)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.15)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"rgba(5, 145, 255, 0.1)",fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:4,paddingXS:8,paddingSM:12,padding:16,paddingMD:20,paddingLG:24,paddingXL:32,paddingContentHorizontalLG:24,paddingContentVerticalLG:16,paddingContentHorizontal:16,paddingContentVertical:12,paddingContentHorizontalSM:16,paddingContentVerticalSM:8,marginXXS:4,marginXS:8,marginSM:12,margin:16,marginMD:20,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",boxShadowSecondary:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowCard:"0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",boxShadowDrawerRight:"-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerLeft:"6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerUp:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerDown:"0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"19w80ff",_hashId:"css-dev-only-do-not-override-i2zu9q"},x=function(F){for(var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,P=3735928559^h,d=1103547991^h,p=0,f;p<F.length;p++)f=F.charCodeAt(p),P=Math.imul(P^f,2654435761),d=Math.imul(d^f,1597334677);return P=Math.imul(P^P>>>16,2246822507)^Math.imul(d^d>>>13,3266489909),d=Math.imul(d^d>>>16,2246822507)^Math.imul(P^P>>>13,3266489909),4294967296*(2097151&d)+(P>>>0)},W=(0,V.jG)(function($){return $}),T={theme:W,token:(0,n.Z)((0,n.Z)({},K),r.Z===null||r.Z===void 0||(c=r.Z.defaultAlgorithm)===null||c===void 0?void 0:c.call(r.Z,r.Z===null||r.Z===void 0?void 0:r.Z.defaultSeed)),hashId:"pro-".concat(x(JSON.stringify(K)))},X=function(){return T}},81643:function(se,B,e){e.d(B,{Z:function(){return n}});const n=V=>V?typeof V=="function"?V():V:null},57838:function(se,B,e){e.d(B,{Z:function(){return V}});var n=e(67294);function V(){const[,r]=n.useReducer(c=>c+1,0);return r}},7134:function(se,B,e){e.d(B,{C:function(){return xe}});var n=e(67294),V=e(93967),r=e.n(V),c=e(9220),K=e(42550),x=e(74443),W=e(53124),T=e(35792),X=e(98675),$=e(25378),h=n.createContext({}),P=e(11568),d=e(14747),p=e(83559),f=e(83262);const O=_=>{const{antCls:l,componentCls:a,iconCls:i,avatarBg:b,avatarColor:u,containerSize:J,containerSizeLG:R,containerSizeSM:k,textFontSize:v,textFontSizeLG:ue,textFontSizeSM:pe,borderRadius:j,borderRadiusLG:M,borderRadiusSM:q,lineWidth:re,lineType:de}=_,w=(te,fe,Fe)=>({width:te,height:te,borderRadius:"50%",[`&${a}-square`]:{borderRadius:Fe},[`&${a}-icon`]:{fontSize:fe,[`> ${i}`]:{margin:0}}});return{[a]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,d.Wf)(_)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:u,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:b,border:`${(0,P.bf)(re)} ${de} transparent`,"&-image":{background:"transparent"},[`${l}-image-img`]:{display:"block"}}),w(J,v,j)),{"&-lg":Object.assign({},w(R,ue,M)),"&-sm":Object.assign({},w(k,pe,q)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=_=>{const{componentCls:l,groupBorderColor:a,groupOverlapping:i,groupSpace:b}=_;return{[`${l}-group`]:{display:"inline-flex",[l]:{borderColor:a},"> *:not(:first-child)":{marginInlineStart:i}},[`${l}-group-popover`]:{[`${l} + ${l}`]:{marginInlineStart:b}}}},S=_=>{const{controlHeight:l,controlHeightLG:a,controlHeightSM:i,fontSize:b,fontSizeLG:u,fontSizeXL:J,fontSizeHeading3:R,marginXS:k,marginXXS:v,colorBorderBg:ue}=_;return{containerSize:l,containerSizeLG:a,containerSizeSM:i,textFontSize:Math.round((u+J)/2),textFontSizeLG:R,textFontSizeSM:b,groupSpace:v,groupOverlapping:-k,groupBorderColor:ue}};var C=(0,p.I$)("Avatar",_=>{const{colorTextLightSolid:l,colorTextPlaceholder:a}=_,i=(0,f.IX)(_,{avatarBg:a,avatarColor:l});return[O(i),y(i)]},S),o=function(_,l){var a={};for(var i in _)Object.prototype.hasOwnProperty.call(_,i)&&l.indexOf(i)<0&&(a[i]=_[i]);if(_!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,i=Object.getOwnPropertySymbols(_);b<i.length;b++)l.indexOf(i[b])<0&&Object.prototype.propertyIsEnumerable.call(_,i[b])&&(a[i[b]]=_[i[b]]);return a};const E=(_,l)=>{const[a,i]=n.useState(1),[b,u]=n.useState(!1),[J,R]=n.useState(!0),k=n.useRef(null),v=n.useRef(null),ue=(0,K.sQ)(l,k),{getPrefixCls:pe,avatar:j}=n.useContext(W.E_),M=n.useContext(h),q=()=>{if(!v.current||!k.current)return;const Y=v.current.offsetWidth,Z=k.current.offsetWidth;if(Y!==0&&Z!==0){const{gap:he=4}=_;he*2<Z&&i(Z-he*2<Y?(Z-he*2)/Y:1)}};n.useEffect(()=>{u(!0)},[]),n.useEffect(()=>{R(!0),i(1)},[_.src]),n.useEffect(q,[_.gap]);const re=()=>{const{onError:Y}=_;(Y==null?void 0:Y())!==!1&&R(!1)},{prefixCls:de,shape:w,size:te,src:fe,srcSet:Fe,icon:Se,className:me,rootClassName:Oe,alt:t,draggable:s,children:m,crossOrigin:z}=_,L=o(_,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),g=(0,X.Z)(Y=>{var Z,he;return(he=(Z=te!=null?te:M==null?void 0:M.size)!==null&&Z!==void 0?Z:Y)!==null&&he!==void 0?he:"default"}),N=Object.keys(typeof g=="object"?g||{}:{}).some(Y=>["xs","sm","md","lg","xl","xxl"].includes(Y)),oe=(0,$.Z)(N),ne=n.useMemo(()=>{if(typeof g!="object")return{};const Y=x.c4.find(he=>oe[he]),Z=g[Y];return Z?{width:Z,height:Z,fontSize:Z&&(Se||m)?Z/2:18}:{}},[oe,g]),ae=pe("avatar",de),le=(0,T.Z)(ae),[ve,I,A]=C(ae,le),G=r()({[`${ae}-lg`]:g==="large",[`${ae}-sm`]:g==="small"}),H=n.isValidElement(fe),Ce=w||(M==null?void 0:M.shape)||"circle",Ee=r()(ae,G,j==null?void 0:j.className,`${ae}-${Ce}`,{[`${ae}-image`]:H||fe&&J,[`${ae}-icon`]:!!Se},A,le,me,Oe,I),ce=typeof g=="number"?{width:g,height:g,fontSize:Se?g/2:18}:{};let ie;if(typeof fe=="string"&&J)ie=n.createElement("img",{src:fe,draggable:s,srcSet:Fe,onError:re,alt:t,crossOrigin:z});else if(H)ie=fe;else if(Se)ie=Se;else if(b||a!==1){const Y=`scale(${a})`,Z={msTransform:Y,WebkitTransform:Y,transform:Y};ie=n.createElement(c.Z,{onResize:q},n.createElement("span",{className:`${ae}-string`,ref:v,style:Object.assign({},Z)},m))}else ie=n.createElement("span",{className:`${ae}-string`,style:{opacity:0},ref:v},m);return delete L.onError,delete L.gap,ve(n.createElement("span",Object.assign({},L,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ce),ne),j==null?void 0:j.style),L.style),className:Ee,ref:ue}),ie))};var ee=n.forwardRef(E),Q=e(50344),D=e(96159),ge=e(55241);const ye=_=>{const{size:l,shape:a}=n.useContext(h),i=n.useMemo(()=>({size:_.size||l,shape:_.shape||a}),[_.size,_.shape,l,a]);return n.createElement(h.Provider,{value:i},_.children)};var Pe=_=>{var l,a,i;const{getPrefixCls:b,direction:u}=n.useContext(W.E_),{prefixCls:J,className:R,rootClassName:k,style:v,maxCount:ue,maxStyle:pe,size:j,shape:M,maxPopoverPlacement:q,maxPopoverTrigger:re,children:de,max:w}=_,te=b("avatar",J),fe=`${te}-group`,Fe=(0,T.Z)(te),[Se,me,Oe]=C(te,Fe),t=r()(fe,{[`${fe}-rtl`]:u==="rtl"},Oe,Fe,R,k,me),s=(0,Q.Z)(de).map((L,g)=>(0,D.Tm)(L,{key:`avatar-key-${g}`})),m=(w==null?void 0:w.count)||ue,z=s.length;if(m&&m<z){const L=s.slice(0,m),g=s.slice(m,z),N=(w==null?void 0:w.style)||pe,oe=((l=w==null?void 0:w.popover)===null||l===void 0?void 0:l.trigger)||re||"hover",ne=((a=w==null?void 0:w.popover)===null||a===void 0?void 0:a.placement)||q||"top",ae=Object.assign(Object.assign({content:g},w==null?void 0:w.popover),{overlayClassName:r()(`${fe}-popover`,(i=w==null?void 0:w.popover)===null||i===void 0?void 0:i.overlayClassName),placement:ne,trigger:oe});return L.push(n.createElement(ge.Z,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},ae),n.createElement(ee,{style:N},`+${z-m}`))),Se(n.createElement(ye,{shape:M,size:j},n.createElement("div",{className:t,style:v},L)))}return Se(n.createElement(ye,{shape:M,size:j},n.createElement("div",{className:t,style:v},s)))};const Te=ee;Te.Group=Pe;var xe=Te},25378:function(se,B,e){var n=e(67294),V=e(8410),r=e(57838),c=e(74443);function K(){let x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;const W=(0,n.useRef)({}),T=(0,r.Z)(),X=(0,c.ZP)();return(0,V.Z)(()=>{const $=X.subscribe(F=>{W.current=F,x&&T()});return()=>X.unsubscribe($)},[]),W.current}B.Z=K},66330:function(se,B,e){e.d(B,{aV:function(){return X}});var n=e(67294),V=e(93967),r=e.n(V),c=e(92419),K=e(81643),x=e(53124),W=e(20136),T=function(h,P){var d={};for(var p in h)Object.prototype.hasOwnProperty.call(h,p)&&P.indexOf(p)<0&&(d[p]=h[p]);if(h!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,p=Object.getOwnPropertySymbols(h);f<p.length;f++)P.indexOf(p[f])<0&&Object.prototype.propertyIsEnumerable.call(h,p[f])&&(d[p[f]]=h[p[f]]);return d};const X=h=>{let{title:P,content:d,prefixCls:p}=h;return!P&&!d?null:n.createElement(n.Fragment,null,P&&n.createElement("div",{className:`${p}-title`},P),d&&n.createElement("div",{className:`${p}-inner-content`},d))},$=h=>{const{hashId:P,prefixCls:d,className:p,style:f,placement:O="top",title:y,content:S,children:C}=h,o=(0,K.Z)(y),E=(0,K.Z)(S),U=r()(P,d,`${d}-pure`,`${d}-placement-${O}`,p);return n.createElement("div",{className:U,style:f},n.createElement("div",{className:`${d}-arrow`}),n.createElement(c.G,Object.assign({},h,{className:P,prefixCls:d}),C||n.createElement(X,{prefixCls:d,title:o,content:E})))},F=h=>{const{prefixCls:P,className:d}=h,p=T(h,["prefixCls","className"]),{getPrefixCls:f}=n.useContext(x.E_),O=f("popover",P),[y,S,C]=(0,W.Z)(O);return y(n.createElement($,Object.assign({},p,{prefixCls:O,hashId:S,className:r()(d,C)})))};B.ZP=F},55241:function(se,B,e){var n=e(67294),V=e(93967),r=e.n(V),c=e(21770),K=e(15105),x=e(81643),W=e(33603),T=e(96159),X=e(53124),$=e(83062),F=e(66330),h=e(20136),P=function(f,O){var y={};for(var S in f)Object.prototype.hasOwnProperty.call(f,S)&&O.indexOf(S)<0&&(y[S]=f[S]);if(f!=null&&typeof Object.getOwnPropertySymbols=="function")for(var C=0,S=Object.getOwnPropertySymbols(f);C<S.length;C++)O.indexOf(S[C])<0&&Object.prototype.propertyIsEnumerable.call(f,S[C])&&(y[S[C]]=f[S[C]]);return y};const p=n.forwardRef((f,O)=>{var y,S;const{prefixCls:C,title:o,content:E,overlayClassName:U,placement:ee="top",trigger:Q="hover",children:D,mouseEnterDelay:ge=.1,mouseLeaveDelay:ye=.1,onOpenChange:be,overlayStyle:Pe={}}=f,Te=P(f,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle"]),{getPrefixCls:xe}=n.useContext(X.E_),_=xe("popover",C),[l,a,i]=(0,h.Z)(_),b=xe(),u=r()(U,a,i),[J,R]=(0,c.Z)(!1,{value:(y=f.open)!==null&&y!==void 0?y:f.visible,defaultValue:(S=f.defaultOpen)!==null&&S!==void 0?S:f.defaultVisible}),k=(M,q)=>{R(M,!0),be==null||be(M,q)},v=M=>{M.keyCode===K.Z.ESC&&k(!1,M)},ue=M=>{k(M)},pe=(0,x.Z)(o),j=(0,x.Z)(E);return l(n.createElement($.Z,Object.assign({placement:ee,trigger:Q,mouseEnterDelay:ge,mouseLeaveDelay:ye,overlayStyle:Pe},Te,{prefixCls:_,overlayClassName:u,ref:O,open:J,onOpenChange:ue,overlay:pe||j?n.createElement(F.aV,{prefixCls:_,title:pe,content:j}):null,transitionName:(0,W.m)(b,"zoom-big",Te.transitionName),"data-popover-inject":!0}),(0,T.Tm)(D,{onKeyDown:M=>{var q,re;n.isValidElement(D)&&((re=D==null?void 0:(q=D.props).onKeyDown)===null||re===void 0||re.call(q,M)),v(M)}})))});p._InternalPanelDoNotUseOrYouWillBeFired=F.ZP,B.Z=p},20136:function(se,B,e){var n=e(14747),V=e(50438),r=e(97414),c=e(79511),K=e(8796),x=e(83559),W=e(83262);const T=F=>{const{componentCls:h,popoverColor:P,titleMinWidth:d,fontWeightStrong:p,innerPadding:f,boxShadowSecondary:O,colorTextHeading:y,borderRadiusLG:S,zIndexPopup:C,titleMarginBottom:o,colorBgElevated:E,popoverBg:U,titleBorderBottom:ee,innerContentPadding:Q,titlePadding:D}=F;return[{[h]:Object.assign(Object.assign({},(0,n.Wf)(F)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:C,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":E,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${h}-content`]:{position:"relative"},[`${h}-inner`]:{backgroundColor:U,backgroundClip:"padding-box",borderRadius:S,boxShadow:O,padding:f},[`${h}-title`]:{minWidth:d,marginBottom:o,color:y,fontWeight:p,borderBottom:ee,padding:D},[`${h}-inner-content`]:{color:P,padding:Q}})},(0,r.ZP)(F,"var(--antd-arrow-background-color)"),{[`${h}-pure`]:{position:"relative",maxWidth:"none",margin:F.sizePopupArrow,display:"inline-block",[`${h}-content`]:{display:"inline-block"}}}]},X=F=>{const{componentCls:h}=F;return{[h]:K.i.map(P=>{const d=F[`${P}6`];return{[`&${h}-${P}`]:{"--antd-arrow-background-color":d,[`${h}-inner`]:{backgroundColor:d},[`${h}-arrow`]:{background:"transparent"}}}})}},$=F=>{const{lineWidth:h,controlHeight:P,fontHeight:d,padding:p,wireframe:f,zIndexPopupBase:O,borderRadiusLG:y,marginXS:S,lineType:C,colorSplit:o,paddingSM:E}=F,U=P-d,ee=U/2,Q=U/2-h,D=p;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:O+30},(0,c.w)(F)),(0,r.wZ)({contentRadius:y,limitVerticalRadius:!0})),{innerPadding:f?0:12,titleMarginBottom:f?0:S,titlePadding:f?`${ee}px ${D}px ${Q}px`:0,titleBorderBottom:f?`${h}px ${C} ${o}`:"none",innerContentPadding:f?`${E}px ${D}px`:0})};B.Z=(0,x.I$)("Popover",F=>{const{colorBgElevated:h,colorText:P}=F,d=(0,W.IX)(F,{popoverBg:h,popoverColor:P});return[T(d),X(d),(0,V._y)(d,"zoom-big")]},$,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},58439:function(se,B,e){e.d(B,{B:function(){return F},I:function(){return ye},O:function(){return W},S:function(){return me},U:function(){return x},a:function(){return X},b:function(){return c},c:function(){return te},d:function(){return Fe},e:function(){return T},f:function(){return Se},g:function(){return Oe},h:function(){return j},i:function(){return p},m:function(){return $},o:function(){return M},p:function(){return R},s:function(){return u},t:function(){return be},u:function(){return Pe},v:function(){return Te},z:function(){return y}});var n=e(67294),V=Object.prototype.hasOwnProperty;function r(t,s){var m,z;if(t===s)return!0;if(t&&s&&(m=t.constructor)===s.constructor){if(m===Date)return t.getTime()===s.getTime();if(m===RegExp)return t.toString()===s.toString();if(m===Array){if((z=t.length)===s.length)for(;z--&&r(t[z],s[z]););return z===-1}if(!m||typeof t=="object"){z=0;for(m in t)if(V.call(t,m)&&++z&&!V.call(s,m)||!(m in s)||!r(t[m],s[m]))return!1;return Object.keys(s).length===z}}return t!==t&&s!==s}const c=new WeakMap,K=()=>{},x=K(),W=Object,T=t=>t===x,X=t=>typeof t=="function",$=(t,s)=>Be(Be({},t),s),F=t=>X(t.then),h={},P={},d="undefined",p=typeof window!=d,f=typeof document!=d,O=()=>p&&typeof window.requestAnimationFrame!=d,y=(t,s)=>{const m=c.get(t);return[()=>!T(s)&&t.get(s)||h,z=>{if(!T(s)){const L=t.get(s);s in P||(P[s]=L),m[5](s,$(L,z),L||h)}},m[6],()=>!T(s)&&s in P?P[s]:!T(s)&&t.get(s)||h]};let S=!0;const C=()=>S,[o,E]=p&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[K,K],U=()=>{const t=f&&document.visibilityState;return T(t)||t!=="hidden"},ee=t=>(f&&document.addEventListener("visibilitychange",t),o("focus",t),()=>{f&&document.removeEventListener("visibilitychange",t),E("focus",t)}),Q=t=>{const s=()=>{S=!0,t()},m=()=>{S=!1};return o("online",s),o("offline",m),()=>{E("online",s),E("offline",m)}},D={isOnline:C,isVisible:U},ge={initFocus:ee,initReconnect:Q},ye=!n.useId,be=!p||"Deno"in globalThis,Pe=t=>O()?window.requestAnimationFrame(t):setTimeout(t,1),Te=be?n.useEffect:n.useLayoutEffect,xe=typeof navigator!="undefined"&&navigator.connection,_=!be&&xe&&(["slow-2g","2g"].includes(xe.effectiveType)||xe.saveData),l=new WeakMap,a=(t,s)=>W.prototype.toString.call(t)===`[object ${s}]`;let i=0;const b=t=>{const s=typeof t,m=a(t,"Date"),z=a(t,"RegExp"),L=a(t,"Object");let g,N;if(W(t)===t&&!m&&!z){if(g=l.get(t),g)return g;if(g=++i+"~",l.set(t,g),Array.isArray(t)){for(g="@",N=0;N<t.length;N++)g+=b(t[N])+",";l.set(t,g)}if(L){g="#";const oe=W.keys(t).sort();for(;!T(N=oe.pop());)T(t[N])||(g+=N+":"+b(t[N])+",");l.set(t,g)}}else g=m?t.toJSON():s=="symbol"?t.toString():s=="string"?JSON.stringify(t):""+t;return g},u=t=>{if(X(t))try{t=t()}catch(m){t=""}const s=t;return t=typeof t=="string"?t:(Array.isArray(t)?t.length:t)?b(t):"",[t,s]};let J=0;const R=()=>++J,k=0,v=1,ue=2;var j={__proto__:null,ERROR_REVALIDATE_EVENT:3,FOCUS_EVENT:k,MUTATE_EVENT:ue,RECONNECT_EVENT:v};function M(...t){return Ae(this,null,function*(){const[s,m,z,L]=t,g=$({populateCache:!0,throwOnError:!0},typeof L=="boolean"?{revalidate:L}:L||{});let N=g.populateCache;const oe=g.rollbackOnError;let ne=g.optimisticData;const ae=I=>typeof oe=="function"?oe(I):oe!==!1,le=g.throwOnError;if(X(m)){const I=m,A=[],G=s.keys();for(const H of G)!/^\$(inf|sub)\$/.test(H)&&I(s.get(H)._k)&&A.push(H);return Promise.all(A.map(ve))}return ve(m);function ve(I){return Ae(this,null,function*(){const[A]=u(I);if(!A)return;const[G,H]=y(s,A),[Ce,Ee,ce,ie]=c.get(s),Y=()=>{const Re=Ce[A];return(X(g.revalidate)?g.revalidate(G().data,I):g.revalidate!==!1)&&(delete ce[A],delete ie[A],Re&&Re[0])?Re[0](ue).then(()=>G().data):G().data};if(t.length<3)return Y();let Z=z,he;const Ie=R();Ee[A]=[Ie,0];const De=!T(ne),Me=G(),Le=Me.data,ze=Me._c,_e=T(ze)?Le:ze;if(De&&(ne=X(ne)?ne(_e,Le):ne,H({data:ne,_c:_e})),X(Z))try{Z=Z(_e)}catch(Re){he=Re}if(Z&&F(Z))if(Z=yield Z.catch(Re=>{he=Re}),Ie!==Ee[A][0]){if(he)throw he;return Z}else he&&De&&ae(he)&&(N=!0,H({data:_e,_c:x}));if(N&&!he)if(X(N)){const Re=N(Z,_e);H({data:Re,error:x,_c:x})}else H({data:Z,error:x,_c:x});if(Ee[A][1]=R(),Promise.resolve(Y()).then(()=>{H({_c:x})}),he){if(le)throw he;return}return Z})}})}const q=(t,s)=>{for(const m in t)t[m][0]&&t[m][0](s)},re=(t,s)=>{if(!c.has(t)){const m=$(ge,s),z={},L=M.bind(x,t);let g=K;const N={},oe=(le,ve)=>{const I=N[le]||[];return N[le]=I,I.push(ve),()=>I.splice(I.indexOf(ve),1)},ne=(le,ve,I)=>{t.set(le,ve);const A=N[le];if(A)for(const G of A)G(ve,I)},ae=()=>{if(!c.has(t)&&(c.set(t,[z,{},{},{},L,ne,oe]),!be)){const le=m.initFocus(setTimeout.bind(x,q.bind(x,z,k))),ve=m.initReconnect(setTimeout.bind(x,q.bind(x,z,v)));g=()=>{le&&le(),ve&&ve(),c.delete(t)}}};return ae(),[t,L,ae,g]}return[t,c.get(t)[4]]},de=(t,s,m,z,L)=>{const g=m.errorRetryCount,N=L.retryCount,oe=~~((Math.random()+.5)*(1<<(N<8?N:8)))*m.errorRetryInterval;!T(g)&&N>g||setTimeout(z,oe,L)},w=r,[te,fe]=re(new Map),Fe=$({onLoadingSlow:K,onSuccess:K,onError:K,onErrorRetry:de,onDiscarded:K,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:_?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:_?5e3:3e3,compare:w,isPaused:()=>!1,cache:te,mutate:fe,fallback:{}},D),Se=(t,s)=>{const m=$(t,s);if(s){const{use:z,fallback:L}=t,{use:g,fallback:N}=s;z&&g&&(m.use=z.concat(g)),L&&N&&(m.fallback=$(L,N))}return m},me=(0,n.createContext)({}),Oe=t=>{const{value:s}=t,m=(0,n.useContext)(me),z=X(s),L=(0,n.useMemo)(()=>z?s(m):s,[z,m,s]),g=(0,n.useMemo)(()=>z?L:Se(m,L),[z,m,L]),N=L&&L.provider,oe=(0,n.useRef)(x);N&&!oe.current&&(oe.current=re(N(g.cache||te),L));const ne=oe.current;return ne&&(g.cache=ne[0],g.mutate=ne[1]),Te(()=>{if(ne)return ne[2]&&ne[2](),ne[3]},[]),(0,n.createElement)(me.Provider,$(t,{value:g}))}},10046:function(se,B,e){e.d(B,{kY:function(){return T},ko:function(){return P},s6:function(){return h}});var n=e(58439),V=e(67294);const r="$inf$",c=n.i&&window.__SWR_DEVTOOLS_USE__,K=c?window.__SWR_DEVTOOLS_USE__:[],x=()=>{c&&(window.__SWR_DEVTOOLS_REACT__=V)},W=p=>(0,n.a)(p[1])?[p[0],p[1],p[2]||{}]:[p[0],null,(p[1]===null?p[2]:p[1])||{}],T=()=>(0,n.m)(n.d,(0,V.useContext)(n.S)),X=(p,f)=>{const[O,y]=serialize(p),[,,,S]=SWRGlobalState.get(cache);if(S[O])return S[O];const C=f(y);return S[O]=C,C},$=p=>(f,O,y)=>p(f,O&&((...C)=>{const[o]=(0,n.s)(f),[,,,E]=n.b.get(n.c);if(o.startsWith(r))return O(...C);const U=E[o];return(0,n.e)(U)?O(...C):(delete E[o],U)}),y),F=K.concat($),h=p=>function(...O){const y=T(),[S,C,o]=W(O),E=(0,n.f)(y,o);let U=p;const{use:ee}=E,Q=(ee||[]).concat(F);for(let D=Q.length;D--;)U=Q[D](U);return U(S,C||E.fetcher||null,E)},P=(p,f,O)=>{const y=f[p]||(f[p]=[]);return y.push(O),()=>{const S=y.indexOf(O);S>=0&&(y[S]=y[y.length-1],y.pop())}},d=(p,f)=>(...O)=>{const[y,S,C]=W(O),o=(C.use||[]).concat(f);return p(y,S,we(Be({},C),{use:o}))};x()},5068:function(se,B,e){e.d(B,{J$:function(){return S},ZP:function(){return C}});var n=e(67294),V=e(61688),r=e(58439),c=e(10046);const x=void 0,W=null,T=o=>o===x,X=o=>typeof o=="function",$=new WeakMap,F=(o,E)=>W.prototype.toString.call(o)===`[object ${E}]`;let h=0;const P=o=>{const E=typeof o,U=F(o,"Date"),ee=F(o,"RegExp"),Q=F(o,"Object");let D,ge;if(W(o)===o&&!U&&!ee){if(D=$.get(o),D)return D;if(D=++h+"~",$.set(o,D),Array.isArray(o)){for(D="@",ge=0;ge<o.length;ge++)D+=P(o[ge])+",";$.set(o,D)}if(Q){D="#";const ye=W.keys(o).sort();for(;!T(ge=ye.pop());)T(o[ge])||(D+=ge+":"+P(o[ge])+",");$.set(o,D)}}else D=U?o.toJSON():E=="symbol"?o.toString():E=="string"?JSON.stringify(o):""+o;return D},d=o=>{if(X(o))try{o=o()}catch(U){o=""}const E=o;return o=typeof o=="string"?o:(Array.isArray(o)?o.length:o)?P(o):"",[o,E]},p=o=>d(o)[0],f=n.use||(o=>{switch(o.status){case"pending":throw o;case"fulfilled":return o.value;case"rejected":throw o.reason;default:throw o.status="pending",o.then(E=>{o.status="fulfilled",o.value=E},E=>{o.status="rejected",o.reason=E}),o}}),O={dedupe:!0},y=(o,E,U)=>{const{cache:ee,compare:Q,suspense:D,fallbackData:ge,revalidateOnMount:ye,revalidateIfStale:be,refreshInterval:Pe,refreshWhenHidden:Te,refreshWhenOffline:xe,keepPreviousData:_}=U,[l,a,i,b]=r.b.get(ee),[u,J]=(0,r.s)(o),R=(0,n.useRef)(!1),k=(0,n.useRef)(!1),v=(0,n.useRef)(u),ue=(0,n.useRef)(E),pe=(0,n.useRef)(U),j=()=>pe.current,M=()=>j().isVisible()&&j().isOnline(),[q,re,de,w]=(0,r.z)(ee,u),te=(0,n.useRef)({}).current,fe=(0,r.e)(ge)?(0,r.e)(U.fallback)?r.U:U.fallback[u]:ge,Fe=(I,A)=>{for(const G in te){const H=G;if(H==="data"){if(!Q(I[H],A[H])&&(!(0,r.e)(I[H])||!Q(g,A[H])))return!1}else if(A[H]!==I[H])return!1}return!0},Se=(0,n.useMemo)(()=>{const I=!u||!E?!1:(0,r.e)(ye)?j().isPaused()||D?!1:be!==!1:ye,A=ie=>{const Y=(0,r.m)(ie);return delete Y._k,I?Be({isValidating:!0,isLoading:!0},Y):Y},G=q(),H=w(),Ce=A(G),Ee=G===H?Ce:A(H);let ce=Ce;return[()=>{const ie=A(q());return Fe(ie,ce)?(ce.data=ie.data,ce.isLoading=ie.isLoading,ce.isValidating=ie.isValidating,ce.error=ie.error,ce):(ce=ie,ie)},()=>Ee]},[ee,u]),me=(0,V.useSyncExternalStore)((0,n.useCallback)(I=>de(u,(A,G)=>{Fe(G,A)||I()}),[ee,u]),Se[0],Se[1]),Oe=!R.current,t=l[u]&&l[u].length>0,s=me.data,m=(0,r.e)(s)?fe&&(0,r.B)(fe)?f(fe):fe:s,z=me.error,L=(0,n.useRef)(m),g=_?(0,r.e)(s)?L.current:s:m,N=t&&!(0,r.e)(z)?!1:Oe&&!(0,r.e)(ye)?ye:j().isPaused()?!1:D?(0,r.e)(m)?!1:be:(0,r.e)(m)||be,oe=!!(u&&E&&Oe&&N),ne=(0,r.e)(me.isValidating)?oe:me.isValidating,ae=(0,r.e)(me.isLoading)?oe:me.isLoading,le=(0,n.useCallback)(I=>Ae(this,null,function*(){const A=ue.current;if(!u||!A||k.current||j().isPaused())return!1;let G,H,Ce=!0;const Ee=I||{},ce=!i[u]||!Ee.dedupe,ie=()=>r.I?!k.current&&u===v.current&&R.current:u===v.current,Y={isValidating:!1,isLoading:!1},Z=()=>{re(Y)},he=()=>{const De=i[u];De&&De[1]===H&&delete i[u]},Ie={isValidating:!0};(0,r.e)(q().data)&&(Ie.isLoading=!0);try{if(ce&&(re(Ie),U.loadingTimeout&&(0,r.e)(q().data)&&setTimeout(()=>{Ce&&ie()&&j().onLoadingSlow(u,U)},U.loadingTimeout),i[u]=[A(J),(0,r.p)()]),[G,H]=i[u],G=yield G,ce&&setTimeout(he,U.dedupingInterval),!i[u]||i[u][1]!==H)return ce&&ie()&&j().onDiscarded(u),!1;Y.error=r.U;const De=a[u];if(!(0,r.e)(De)&&(H<=De[0]||H<=De[1]||De[1]===0))return Z(),ce&&ie()&&j().onDiscarded(u),!1;const Me=q().data;Y.data=Q(Me,G)?Me:G,ce&&ie()&&j().onSuccess(G,u,U)}catch(De){he();const Me=j(),{shouldRetryOnError:Le}=Me;Me.isPaused()||(Y.error=De,ce&&ie()&&(Me.onError(De,u,Me),(Le===!0||(0,r.a)(Le)&&Le(De))&&(!j().revalidateOnFocus||!j().revalidateOnReconnect||M())&&Me.onErrorRetry(De,u,Me,ze=>{const _e=l[u];_e&&_e[0]&&_e[0](r.h.ERROR_REVALIDATE_EVENT,ze)},{retryCount:(Ee.retryCount||0)+1,dedupe:!0})))}return Ce=!1,Z(),!0}),[u,ee]),ve=(0,n.useCallback)((...I)=>(0,r.o)(ee,v.current,...I),[]);if((0,r.v)(()=>{ue.current=E,pe.current=U,(0,r.e)(s)||(L.current=s)}),(0,r.v)(()=>{if(!u)return;const I=le.bind(r.U,O);let A=0;const G=(Ce,Ee={})=>{if(Ce==r.h.FOCUS_EVENT){const ce=Date.now();j().revalidateOnFocus&&ce>A&&M()&&(A=ce+j().focusThrottleInterval,I())}else if(Ce==r.h.RECONNECT_EVENT)j().revalidateOnReconnect&&M()&&I();else{if(Ce==r.h.MUTATE_EVENT)return le();if(Ce==r.h.ERROR_REVALIDATE_EVENT)return le(Ee)}},H=(0,c.ko)(u,l,G);return k.current=!1,v.current=u,R.current=!0,re({_k:J}),N&&((0,r.e)(m)||r.t?I():(0,r.u)(I)),()=>{k.current=!0,H()}},[u]),(0,r.v)(()=>{let I;function A(){const H=(0,r.a)(Pe)?Pe(q().data):Pe;H&&I!==-1&&(I=setTimeout(G,H))}function G(){!q().error&&(Te||j().isVisible())&&(xe||j().isOnline())?le(O).then(A):A()}return A(),()=>{I&&(clearTimeout(I),I=-1)}},[Pe,Te,xe,u]),(0,n.useDebugValue)(g),D&&(0,r.e)(m)&&u){if(!r.I&&r.t)throw new Error("Fallback data is required when using Suspense in SSR.");ue.current=E,pe.current=U,k.current=!1;const I=b[u];if(!(0,r.e)(I)){const A=ve(I);f(A)}if((0,r.e)(z)){const A=le(O);(0,r.e)(g)||(A.status="fulfilled",A.value=!0),f(A)}else throw z}return{mutate:ve,get data(){return te.data=!0,g},get error(){return te.error=!0,z},get isValidating(){return te.isValidating=!0,ne},get isLoading(){return te.isLoading=!0,ae}}},S=r.O.defineProperty(r.g,"defaultValue",{value:r.d}),C=(0,c.s6)(y)}}]);
}());