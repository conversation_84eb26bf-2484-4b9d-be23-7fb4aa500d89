import React from 'react';
import { Card, Row, Col, Image, Pagination, Button, Modal, Spin } from 'antd';
import { useState, useEffect, useRef } from 'react';
import { getEventRecordList, getImageFile, getAllEventType, getAllPointName } from "@/services/ant-design-pro/api";
import { PageHeader, ProTable } from '@ant-design/pro-components';
import './index.less';
import { useIntl } from '@umijs/max';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';

const EventRecordBoard: React.FC = (props) => {
  const [eventRecordsData, setEventRecordsData] = useState<any[]>([]);

  const [eventRecordInfo, setEventRecordInfo] = useState({});

  const [eventInfoVisible, setEventInfoVisible] = useState<boolean>(false);

  const [loading, setLoading] = useState(false);

  /**事件类型数据 */
  const [eventTypeData, setEventTypeData] = useState<any[]>([])

  /**点位名称数据 */
  const [pointNameOptions, setPointNameOptions] = useState<string[]>([]);

  const intl = useIntl();

  const [searchParams, setSearchParams] = useState({
    eventType: '',
    pointName: '',
    startTime: '',
    endTime: '',
    pageSize: 24,
    pageNo: 1,
    total: 0,
  })

  const formRef = useRef()

  const infoTextStyle = {
    fontSize: 12,
    color: '#555',
    margin: '4px 0',
    display: 'flex',
    alignItems: 'center',
    gap: 4,
  };

  const iconStyle = {
    width: 14,
    height: 14,
  };


  const handleImageClick = async (record: any) => {
    console.log('点击了图片', record);

    const imageUrl = await fetchImage(record.fileName, 'snap');

    const newRecord = {
      ...record,
      imageUrl,
    };

    setEventRecordInfo(newRecord);
    setEventInfoVisible(true);
  };




  /**
   * 分页
   */
  const handlePageChange = async (pageNo: number, pageSize: number) => {
    setSearchParams(prev => ({
      ...prev,
      pageNo,
      pageSize,
    }));

    const searchValue = formRef.current?.getFieldsValue?.() || {};
    const dateRange = searchValue.eventTime || [];

    const startTime = dateRange[0] ? dayjs(dateRange[0]).startOf('second').format('YYYY-MM-DD HH:mm:ss.SSS') : undefined;
    const endTime = dateRange[1] ? dayjs(dateRange[1]).endOf('second').format('YYYY-MM-DD HH:mm:ss.SSS') : undefined;


    await loadEventRecords({
      current: pageNo,
      size: pageSize,
      eventType: searchValue.eventType || 'no&input',
      pointName: searchValue.pointName || 'no&input',
      startTime: startTime || 'no&input',
      endTime: endTime || 'no&input',
    });
  };

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('point search', params)
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      eventType: params.eventType,
      pointName: params.pointName,
      startTime: params.startTime,
      endTime: params.endTime
    })
    loadEventRecords({
      current: 1,
      eventType: params.eventType ? params.eventType : 'no&input',
      pointName: params.pointName ? params.pointName : 'no&input',
      startTime: params.startTime ? params.startTime : 'no&input',
      endTime: params.endTime ? params.endTime : 'no&input'
    })
  }

  const loadEventRecords = async (page: any) => {
    console.log("page->", page)
    const searchValue = formRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let eventType = page.eventType ? page.eventType : searchValue.eventType
    let pointName = page.pointName ? page.pointName : searchValue.pointName
    let startTime = page.startTime ? page.startTime : searchValue.startTime
    let endTime = page.endTime ? page.endTime : searchValue.endTime
    if (page.eventType === 'no&input') {
      eventType = undefined
    }
    if (page.pointName === 'no&input') {
      pointName = undefined
    }
    if (page.startTime === 'no&input') {
      startTime = undefined
    }
    if (page.endTime === 'no&input') {
      endTime = undefined
    }

    setLoading(true);
    const rs = await getEventRecordList({ eventType: eventType, pointName: pointName, startTime: startTime, endTime: endTime }, currentPage, pageSize);
    console.log("rs->", rs)
    if (rs.code === 0 && rs?.data.data && rs?.data.data.length >= 0) {
      const records = await Promise.all(rs.data.data.map(async (record) => ({
        ...record,
        thumbUrl: await fetchImage(record.fileName, 'thumb')
      })));
      console.log("records->", records);
      setEventRecordsData(records)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
    }
    setLoading(false);

  }

  /**
 * 获取图片（支持缩略图 & 原图）
 * @param imageName 图片名称
 * @param imageType 是否获取缩略图（snap 抓拍 thumb 缩略图 target 目标抠图）
 * @returns 图片的 URL（本地 Blob URL）
 */
  const fetchImage = async (imageName: string, imageType: string): Promise<string | null> => {
    try {
      const response = await getImageFile(imageName, imageType);
      if (!response.ok) {
        console.warn(`图片 ${imageName} 获取失败，状态码：${response.status}`);
        return null;
      }

      const blob = await response.blob();
      return URL.createObjectURL(blob); // 生成 Blob URL 供前端展示
    } catch (error) {
      console.error(`获取图片 ${imageName} 失败:`, error);
      return null;
    }
  };


  const columns: any = [
    {
      title: intl.formatMessage({ id: 'pages.search.time', defaultMessage: '时间', }),
      dataIndex: 'eventTime',
      valueType: 'dateRange',
      hideInSearch: false,
      search: {
        transform: (value) => {
          return {
            startTime: value?.[0] ? dayjs(value[0]).startOf('second').format('YYYY-MM-DD HH:mm:ss.SSS') : undefined,
            endTime: value?.[1] ? dayjs(value[1]).endOf('second').format('YYYY-MM-DD HH:mm:ss.SSS') : undefined,
          };
        },
      },
      fieldProps: {
        showTime: {
          format: 'HH:mm:ss',
        },
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', }),
      dataIndex: 'eventType',
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: eventTypeData.map(item => ({
          label: item.eventType,
          value: item.eventType,
        })),
        filterOption: (input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
      },
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.pointName', defaultMessage: '点位名称', }),
      dataIndex: 'pointName',
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: pointNameOptions.map(name => ({
          label: name,
          value: name,
        })),
        filterOption: (input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
      },
    }
  ];


  /**
   * 加载事件类型
   */
  const loadEventType = async () => {
    const rs = await getAllEventType();
    if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
      setEventTypeData(rs.data)
    }
  }

  /**
 * 加载点位名称
 */
  const loadPointName = async () => {
    const rs = await getAllPointName();
    if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
      setPointNameOptions(rs.data);
    }
  }

  useEffect(() => {
    loadEventType();
    loadPointName();
  }, []);



  return (
    <>
      <div>
        <ProTable
          formRef={formRef}
          columns={columns}
          headerTitle={false}
          toolBarRender={false}
          beforeSearchSubmit={handleBeforeSearchDevice}
          search={{
            // labelWidth: 80,
            // span: 6,
            // collapsed: false,
            optionRender: (searchConfig, formProps, dom) => dom, // 保留默认按钮
          }}
          pagination={false} // 不显示表格自带分页
          rowKey="id"
          options={false}
          tableAlertRender={false}
          tableRender={(_, __, ___, rest) => null} // 不渲染 table
          onSubmit={(params) => {
            //setSearchParams(params); // 提交搜索
          }}
          onReset={() => {
            //setSearchParams({}); // 重置
          }}
        />
      </div>
      <PageHeader
        style={{ backgroundColor: 'white', padding: '0px 24px 24px 24px', position: 'relative', minHeight: 'calc(100vh - 150px)' }}
        onBack={() => null}
        backIcon=""
      // extra={[
      //   <Button key="2" onClick={loadEventRecords}>重置</Button>,
      //   <Button key="1" onClick={loadEventRecords} type="primary">
      //     保存
      //   </Button>,
      // ]}
      >

        <h3 style={{ marginBottom: 16 }}>{intl.formatMessage({ id: 'pages.pointManage.eventRecord', defaultMessage: '事件记录', })}</h3>
        <Spin
          spinning={loading}
          tip={intl.formatMessage({ id: 'pages.search.loading', defaultMessage: '加载中...', })}
          size="large"
          style={{
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            fontSize: '24px',
          }}
        >
          {/* 内容区域 */}
          <div
            style={{
              height: 'calc(100vh - 300px)',
              maxHeight: 'calc(100vh - 300px)',
              display: eventRecordsData.length > 0 ? 'grid' : 'flex',
              gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
              gap: '20px',
              padding: '12px',
              overflowY: 'auto',

              /** 内容在高度不足时仍居中对齐 **/
              alignItems: 'center',
              justifyContent: 'center',
              alignContent: eventRecordsData.length > 0 ? 'start' : 'center',
            }}
          >
            {eventRecordsData.length > 0 ? (
              eventRecordsData.map((item, index) => (
                <div
                  key={index}
                  style={{
                    height: 176,
                    background: '#fff',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    overflow: 'hidden',
                    transition: 'transform 0.2s',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                >
                  {/* 图片区域 */}
                  <div className="search-Image">
                    <Image
                      src={item.thumbUrl || '/icons/search/default-noImage.png'}
                      preview={false}
                      alt={item.eventType}
                      style={{
                        width: '100%',
                        height: 105,
                        objectFit: 'cover',
                        cursor: 'pointer',
                      }}
                      onClick={() => handleImageClick(item)}
                    />
                  </div>

                  {/* 信息区域 */}
                  <div style={{ padding: '0 12px 12px 12px' }}>
                    <p style={infoTextStyle}>
                      <img src="/icons/pointManage/img_eventType.svg" style={iconStyle} />
                      {item.eventType}
                    </p>
                    <p style={infoTextStyle}>
                      <img src="/icons/search/local.svg" style={iconStyle} />
                      {item.pointName || ''}
                    </p>
                    <p style={infoTextStyle}>
                      <img src="/icons/search/date.svg" style={iconStyle} />
                      {item.eventTime || ''}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div style={{ textAlign: 'center' }}>
                <img src="/icons/pointManage/img_no_data.png" alt="No Data" style={{ width: '150px' }} />
                {/* <p style={{ color: '#999', marginTop: '10px' }}>
                  {intl.formatMessage({
                    id: 'pages.common.noData',
                    defaultMessage: '暂无数据',
                  })}
                </p> */}
              </div>
            )}
          </div>
        </Spin>
        {/* 分页器固定到底部 */}
        <div
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            background: 'white',
            padding: '12px 0',
            display: 'flex',
            justifyContent: 'right',
            borderTop: '1px solid #f0f0f0',
            paddingRight: '5%',
          }}
        >
          <Pagination
            current={searchParams.pageNo}
            pageSize={searchParams.pageSize}
            total={searchParams.total}
            onChange={(page, pageSize) => {
              handlePageChange(page, pageSize);
            }}
            showSizeChanger
            pageSizeOptions={['24', '48', '72', '96']}
            showTotal={(total, range) => intl.formatMessage({ id: 'pages.pointManage.paginationInfo', defaultMessage: '第 {range0} - {range1} 条/总共 {total} 条', }, { range0: range[0], range1: range[1], total: total })}
          />
        </div>
      </PageHeader>



      <Modal
        width="70vw"
        destroyOnClose
        title={intl.formatMessage({ id: 'pages.search.eventDetail', defaultMessage: '事件详情', })}
        open={eventInfoVisible}
        onCancel={() => setEventInfoVisible(false)}
        className="eventRecord-modal"
        styles={{
          mask: { background: 'rgba(0, 0, 0, 0.5)' },
          header: {
            background: 'rgb(238, 242, 246)',
            padding: '9px 16px',
            marginBottom: '0',
          },
          body: {
            background: 'rgb(255, 255, 255)',
            padding: '24px',
            height: '60vh',
          },
        }}
        footer={false}
      >
        <div className="event-detail-container">
          <div className="event-image-wrapper">
            <Image
              src={eventRecordInfo?.imageUrl || eventRecordInfo?.thumbUrl || '/icons/search/default-noImage.png'}
              alt={intl.formatMessage({ id: 'pages.pointManage.eventImage', defaultMessage: '事件图片', })}
              width="100%"
              height="100%"
              style={{ objectFit: 'cover', borderRadius: 4 }}
              placeholder
            />
          </div>

          {/* 信息区域 */}
          <div className="event-info">
            <div className="event-info-title">{intl.formatMessage({ id: 'pages.pointManage.detail', defaultMessage: '详情', })}</div>
            <div className="event-info-item">{intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', })}：{eventRecordInfo?.eventType || ''}</div>
            <div className="event-info-item">{intl.formatMessage({ id: 'pages.pointManage.pointInfo', defaultMessage: '点位信息', })}：{eventRecordInfo?.pointName || ''}</div>
            <div className="event-info-item">{intl.formatMessage({ id: 'pages.pointManage.eventTime', defaultMessage: '事件时间', })}：{eventRecordInfo?.eventTime || ''}</div>
          </div>
        </div>
      </Modal>


    </>
  );
};

export default EventRecordBoard;