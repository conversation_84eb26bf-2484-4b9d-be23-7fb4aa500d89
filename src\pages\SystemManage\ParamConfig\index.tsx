import { ProForm, ProFormText, PageHeader } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useState, useEffect, useRef } from 'react'
import { useIntl } from "umi";
import { getParamConfig, editParamConfig } from '@/services/ant-design-pro/api';
import './index.less'


const ParamConfig: React.FC = () => {

  /** 基础设置表单引用 */
  const basicSettinFormRef = useRef()

  /** 基础设置表单 */
  const [basicSettingForm] = Form.useForm()

  /** 当前表单数据 */
  const [currentFormData, setCurrentFormData] = useState<any>();

  const intl = useIntl();

  /**
   * 加载参数配置
   */
  const loadParamConfig = async () => {
    const rs = await getParamConfig()
    if (rs.code !== 0 || !rs.data) {
      message.error(intl.formatMessage({ id: 'pages.config.paramConfigFailure', defaultMessage: '获取参数配置失败', }))
      return
    }
    /*const defData = {
                dsUrl:'https://api.deepseek.com',
                dsKey:'***********************************',
                czUrl:'https://api.coze.cn',
                czKey:'pat_Tr989qRMlCXZ1pfKGqcqrzJjIkzaUh2gRzMQcFGVtjm8SjkwUBQn1NoBmtzikKlw',

            }
    */
    setCurrentFormData(rs.data)
    basicSettingForm.setFieldsValue(rs.data)
  }

  /**
  * 保存数据
  * @returns
  */
  const handleSaveSetting = async () => {
    let formData
    try {
      formData = await basicSettingForm.validateFields();
      console.log(formData); // 这里会打印出所有表单项的值，包括ue_start和ue_end
    } catch (errorInfo) {
      console.error('save setting error:', errorInfo);
      return
    }


    const rs = await editParamConfig(
      formData
    )
    if (rs.code !== 0) {
      // if (rs.data) {
      //   message.error(intl.formatMessage({ id: 'pages.serverCode.' + rs.data[0]?.error_code }));
      // } else {
      //   message.error(intl.formatMessage({ id: 'pages.serverCode.' + rs.code }))
      // }
      message.error(intl.formatMessage({ id: 'pages.config.saveFailure', defaultMessage: '保存失败', }))
      return
    } else {
      message.success(intl.formatMessage({ id: 'pages.config.saveSuccess', defaultMessage: '保存成功', }))
    }
  };


  useEffect(() => {
    loadParamConfig();
  }, []);



  return (
    <PageHeader
      style={{ 'backgroundColor': 'white', padding: '24px' }}
      onBack={() => null}
      title=''
      //subTitle="This is a subtitle"
      backIcon=""
      extra={[
        <Button key="2" onClick={loadParamConfig}>{intl.formatMessage({ id: 'pages.config.reset', defaultMessage: '重置', })}</Button>,
        <Button key="1" onClick={handleSaveSetting} type="primary">
          {intl.formatMessage({ id: 'pages.config.save', defaultMessage: '保存', })}
        </Button>,
      ]}
    >
      <div className="form-container"> {/* 应用容器样式 */}
        <ProForm
          labelCol={{ flex: '185px' }}// 根据效果可以自己具体去控制大小
          labelAlign="left"
          labelWrap
          wrapperCol={{ flex: 1 }}
          formRef={basicSettinFormRef}
          layout={'horizontal'}
          form={basicSettingForm}
          submitter={{
            resetButtonProps: {
              hidden: true,
            },
            submitButtonProps: {
              hidden: true,
            },
            searchConfig: {
            }
          }}
        >

          <div className="pbx_cfc_title">
            <span>{intl.formatMessage({ id: 'pages.config.streamConfig', defaultMessage: '流媒体配置', })}</span>
          </div>
          <ProFormText
            rules={[]}
            label={intl.formatMessage({ id: 'pages.config.streamServiceUrl', defaultMessage: '流媒体服务地址', })}
            width="md"
            initialValue={currentFormData?.url}
            name="url"
          />

          <ProFormText
            rules={[]}
            label={intl.formatMessage({ id: 'pages.config.secretKey', defaultMessage: '密钥', })}
            width="md"
            initialValue={currentFormData?.secret}
            name="secret"
          />

          <div className="pbx_cfc_title">
            <span>CZ {intl.formatMessage({ id: 'pages.config.configuration', defaultMessage: '配置', })}</span>
          </div>
          <ProFormText
            rules={[]}
            label='url'
            width="md"
            initialValue={currentFormData?.czurl}
            name="czUrl"
          />

          <ProFormText
            rules={[]}
            label={intl.formatMessage({ id: 'pages.config.secretKey', defaultMessage: '密钥', })}
            width="md"
            initialValue={currentFormData?.czkey}
            name="czKey"
          />
          <ProFormText
            rules={[]}
            label={intl.formatMessage({ id: 'pages.config.applicationId', defaultMessage: '应用 ID', })}
            width="md"
            initialValue={currentFormData?.czApplicationId}
            name="czApplicationId"
          />
          <ProFormText
            rules={[]}
            label={intl.formatMessage({ id: 'pages.config.workspaceId', defaultMessage: '工作空间id', })}
            width="md"
            initialValue={currentFormData?.czkey}
            name="czWorkspaceId"
          />
        </ProForm>
      </div>
    </PageHeader >
  );
};

export default ParamConfig;
