@echo off

echo Start to copy
echo ==========================================

set absolute_path=%cd%

echo %absolute_path%

set src_dir=.\locales
set src_filename=bioplayer.ts
set dst_dir=..\BioPlayer\src\locales

set src_filepath="%src_dir%\en-US\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\en-US.js"

set src_filepath="%src_dir%\fa-IR\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\fa-IR.js"

set src_filepath="%src_dir%\id-ID\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\id-ID.js"

set src_filepath="%src_dir%\ja-JP\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\ja-JP.js"

set src_filepath="%src_dir%\pt-BR\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\pt-BR.js"

set src_filepath="%src_dir%\zh-CN\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\zh-CN.js"

set src_filepath="%src_dir%\zh-TW\%src_filename%"
if exist %src_filepath% move "%src_filepath%" "%dst_dir%\zh-TW.js"

xcopy /Y /F /S /D %src_dir% ..\html\src\locales

echo ==========================================
echo End to copy

::pause
