import {
  Attach<PERSON>,
  Bubble,
  Prompts,
  Sender,
  Welcome,
  useXAgent,
  useXChat,
} from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { useEffect } from 'react';

import {
  CloudUploadOutlined,
  PaperClipOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useIntl } from 'umi';
import { Badge, Button, Flex, type GetProp, Space, Spin } from 'antd';
import { createChat, getChatDetails, getChatDetailInfo, createChatStream } from '@/services/ant-design-pro/api';
import DOMPurify from 'dompurify';

interface ChatData {
  logo: string;
  name: string;
  opening_statement: string
  conversation_id: string;
  agent_id: string;
  external_user_id: string;
}

interface ChatDataProps {
  chatData: ChatData;
}


const useStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
        width: 100%;
        min-width: 300px;
        height: 722px;
        border-radius: ${token.borderRadius}px;
        display: flex;
        background: ${token.colorBgContainer};
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;

        .ant-prompts {
          color: ${token.colorText};
        }
      `,

    chat: css`
        height: 100%;
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
    messages: css`
        flex: 1;
      `,
    placeholder: css`
        padding-top: 32px;

      `,
    sender: css`
        box-shadow: ${token.boxShadow};
      `,
    logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;

        img {
          width: 24px;
          height: 24px;
          display: inline-block;
        }

        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
    addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: calc(100% - 24px);
        margin: 0 12px 24px 12px;
      `,
  };
});

// ==================== roles ====================
const roles: GetProp<typeof Bubble.List, 'roles'> = {
  ai: {
    placement: 'start',
    typing: true,
    header: 'benben',
    avatar: { icon: <UserOutlined />, style: { background: '#fde3cf' } },
    styles: {
      footer: {
        width: '100%',
      },
    },

    loadingRender: () => (
      <Space>
        <Spin size="small" />
        Custom loading...
      </Space>
    ),
  },
  user: {
    placement: 'end',
    header: 'admin',
    avatar: { icon: <UserOutlined />, style: { background: '#87d068' } },
    styles: {

    }
  },
  suggestion: {
    placement: 'start',
    avatar: { icon: <UserOutlined />, style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (items) => <Prompts vertical items={items as any} />,
  },
  file: {
    placement: 'start',
    avatar: { icon: <UserOutlined />, style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (items: any) => (
      <Flex vertical gap="middle">
        {(items as any[]).map((item) => (
          <Attachments.FileCard key={item.uid} item={item} />
        ))}
      </Flex>
    ),
  },
};

const Chat: React.FC<ChatDataProps> = (props) => {
  // ==================== Style ====================
  const { styles } = useStyle();

  // ==================== State ====================
  const [headerOpen, setHeaderOpen] = React.useState(false);

  const [content, setContent] = React.useState('');

  const [attachedFiles, setAttachedFiles] = React.useState<GetProp<typeof Attachments, 'items'>>(
    [],
  );

  /** 国际化 */
  const intl = useIntl();


  // ==================== Runtime ====================
  const [agent] = useXAgent({

    request: async ({ message }, { onSuccess }) => {
      try {
        const params = {
          agent_id: props.chatData.agent_id,
          conversation_id: props.chatData.conversation_id,
          external_user_id: props.chatData.external_user_id,
          stream: true,
          auto_save_history: false,
          chat_messages: [
            {
              content: message,
              content_type: 'text',
              role: 'user',
              type: 'question',

            },
          ],
        };

        let result = '';

        await createChatStream(
          params,
          (chunk) => {
            const lines = chunk.split('\n');
            for (const line of lines) {
              if (!line.startsWith('data:')) continue;

              const jsonStr = line.replace(/^data:\s*/, '').trim();
              if (!jsonStr) continue;

              try {
                const data = JSON.parse(jsonStr);
                if (data?.event === 'message' && data?.message?.answer) {
                  result += data.message.answer;
                }
              } catch (e) {
                console.warn('JSON 解析失败:', jsonStr);
              }
            }
          },
          () => {
            onSuccess(result);
          },
          (error) => {
            console.error('流式请求错误:', error);
            onSuccess('调用接口失败，请稍后重试');
          }
        );

      } catch (error) {
        console.error('调用接口失败:', error);
        onSuccess('调用接口失败，请稍后重试');
      }
    },
  });



  const { onRequest, messages, setMessages } = useXChat({
    agent,
  });

  // ==================== Event ====================
  const onSubmit = (nextContent: string) => {
    if (!nextContent) return;
    onRequest(nextContent);
    setContent('');
  };

  const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info) =>
    setAttachedFiles(info.fileList);

  // ==================== Nodes ====================
  const placeholderNode = (
    <Space direction="vertical" size={16} className={styles.placeholder}>
      <Welcome
        variant="filled"
        title={
          props.chatData.opening_statement ||
          intl.formatMessage({ id: 'pages.agent.hello', defaultMessage: '哈喽，我是' }) +
          intl.formatMessage({ id: 'pages.agent.agent', defaultMessage: '智能体' })
        }
        icon={props.chatData.logo}
      />

      {Array.isArray(props.chatData.suggested_questions) && props.chatData.suggested_questions.length > 0 && (
        <div style={{ marginTop: 8 }}>
          <div style={{ fontWeight: 500, marginBottom: 4, color: '#888' }}>
            {intl.formatMessage({ id: 'pages.agent.suggestedQuestions', defaultMessage: '你可以这样问我：' })}
          </div>
          <Space wrap size={[8, 12]}>
            {props.chatData.suggested_questions.map((q, index) => (
              <div
                key={index}
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '6px 12px',
                  borderRadius: 16,
                  cursor: 'pointer',
                  fontSize: 14,
                  lineHeight: 1.5,
                  transition: 'background-color 0.2s',
                }}
                onClick={() => setContent(q)}
                onMouseEnter={e => (e.currentTarget.style.backgroundColor = '#e6f7ff')}
                onMouseLeave={e => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
                dangerouslySetInnerHTML={{ __html: q }}
              />
            ))}
          </Space>
        </div>
      )}
    </Space>

  );

  const items: GetProp<typeof Bubble.List, 'items'> = messages.map(({ id, message, status }) => ({
    key: id,
    loading: status === 'loading',
    role: status === 'local' ? 'user' : 'ai',
    content: message,
  }));

  const attachmentsNode = (
    <Badge dot={attachedFiles.length > 0 && !headerOpen}>
      <Button type="text" icon={<PaperClipOutlined />} onClick={() => setHeaderOpen(!headerOpen)} />
    </Badge>
  );

  const senderHeader = (
    <Sender.Header
      title={intl.formatMessage({ id: 'pages.agent.attachment', defaultMessage: '附件' })}
      open={headerOpen}
      onOpenChange={setHeaderOpen}
      styles={{
        content: {
          padding: 0,
        },
      }}
    >
      <Attachments
        beforeUpload={() => false}
        items={attachedFiles}
        onChange={handleFileChange}
        placeholder={(type) =>
          type === 'drop'
            ? { title: intl.formatMessage({ id: 'pages.agent.dropFilesHere', defaultMessage: '将文件放到此处' }) }
            : {
              icon: <CloudUploadOutlined />,
              title: intl.formatMessage({ id: 'pages.agent.uploadFile', defaultMessage: '上传文件' }),
              description: intl.formatMessage({ id: 'pages.agent.clickOrDragToUpload', defaultMessage: '单击或拖动文件到此区域进行上传' }),
            }
        }
      />
    </Sender.Header>
  );

  // ==================== Render =================
  return (
    <div className={styles.layout}>

      <div className={styles.chat}>
        {/*  消息列表 */}
        <Bubble.List
          items={items.length > 0 ? items : [{ content: placeholderNode, variant: 'borderless' }]}
          roles={roles}
          className={styles.messages}
          style={{
            overflow: 'auto',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'  // 兼容 IE/Edge
          }}
        />
        {/*  输入框 */}
        <Sender
          value={content}
          header={senderHeader}
          onSubmit={onSubmit}
          onChange={setContent}
          // prefix={attachmentsNode}
          loading={agent.isRequesting()}
          className={styles.sender}
          // allowSpeech
          placeholder={intl.formatMessage({ id: 'pages.agent.shiftEnterNewline', defaultMessage: 'Shift+Enter换行' })}
        />
      </div>
    </div>
  );
};

export default Chat;
