package com.zkteco.mars.usc.service.impl;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zkteco.framework.base.bean.BaseItem;
import com.zkteco.framework.base.bean.Pager;
import com.zkteco.framework.core.utils.CollectionUtil;
import com.zkteco.framework.core.utils.SQLUtil;
import com.zkteco.framework.vo.ApiResultMessage;
import com.zkteco.mars.sys.config.ParamsConfig;
import com.zkteco.mars.sys.service.SysParamService;
import com.zkteco.mars.usc.constant.MediaServerConstants;
import com.zkteco.mars.usc.constant.USCConstants;
import com.zkteco.mars.usc.dao.DmcPointDao;
import com.zkteco.mars.usc.dao.PointControlRulesDao;
import com.zkteco.mars.usc.model.DmcPoint;
import com.zkteco.mars.usc.service.DmcPointService;
import com.zkteco.mars.usc.util.HttpRequestUtil;
import com.zkteco.mars.usc.vo.DmcPointItem;
import com.zkteco.mars.usc.vo.GetChannelItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 点位管理
 *
 * <AUTHOR>
 * @date 2024-12-31 14:36
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class DmcPointServiceImpl implements DmcPointService {

    @Autowired
    private DmcPointDao dmcPointDao;

    @Autowired
    private SysParamService sysParamService;

    @Autowired
    private ParamsConfig paramsConfig;

    @Autowired
    private HttpRequestUtil httpRequestUtil;

    @Autowired
    PointControlRulesDao pointControlRulesDao;


    @Override
    public ApiResultMessage editPointInfo() {
        return null;
    }

    @Override
    public ApiResultMessage addPointInfo(List<DmcPointItem> dmcPointList) {
        JSONArray paramsArray = new JSONArray();
        for (DmcPointItem dmcPointItem : dmcPointList) {
            JSONObject paramObject = buildPointInfoObject(dmcPointItem);
            paramsArray.add(paramObject);
        }
        JSONObject dataObject = new JSONObject();
        dataObject.put("stream", paramsArray);
        String post = httpRequestUtil.postCommon(MediaServerConstants.ADD_STREAM, dataObject);
        JSONObject jsonObject = JSONObject.parseObject(post);
        if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == USCConstants.SUCCESS) {
            for (DmcPointItem dmcPointItem : dmcPointList) {
                DmcPoint dmcPoint = new DmcPoint();
                BeanUtils.copyProperties(dmcPointItem, dmcPoint);
                dmcPoint.setBusinessId(dmcPointItem.getId());
                dmcPoint.setId(null);
                dmcPointDao.save(dmcPoint);
            }
            return ApiResultMessage.successMessage();
        } else {
            return ApiResultMessage.failedMessage(jsonObject.getInteger("code"));
        }
    }

    /**
     * 构建点位请求参数
     *
     * @param dmcPointItem:
     * @return com.alibaba.fastjson2.JSONObject
     * @throws
     * <AUTHOR>
     * @date 2025-01-21 11:32
     * @since 1.0.0
     */
    private JSONObject buildPointInfoObject(DmcPointItem dmcPointItem) {
        JSONObject paramObject = new JSONObject();
        paramObject.put("channel_id", dmcPointItem.getBusinessId());
        paramObject.put("stream_type", 0);
        paramObject.put("app", "mars");
        paramObject.put("enable_rtsp", 1);
        paramObject.put("enable_rtmp", 1);
        paramObject.put("enable_ts", 1);
        paramObject.put("enable_fmp4", 0);
        paramObject.put("enable_hls", 0);
        paramObject.put("enable_audio", 1);
        paramObject.put("add_mute_audio", 1);
        paramObject.put("modify_stamp", 1);
        paramObject.put("detectType", dmcPointItem.getDetectType());
        paramObject.put("autoSnipaste", Integer.parseInt(dmcPointItem.getCaptureInterval()));
        paramObject.put("width", 1280);
        paramObject.put("height", 720);
        paramObject.put("autoSnipastePath", USCConstants.SNAP_FOLDER_PATH);
        paramObject.put("auto_close", 0);
        paramObject.put("buffTime", 5);
        return paramObject;
    }


    @Override
    public ApiResultMessage addDeviceInfos(List<DmcPointItem> dmcPointItemList) {
        JSONArray deviceArray = new JSONArray();

        for (DmcPointItem dmcPointItem : dmcPointItemList) {
            JSONObject paramObject = new JSONObject();
            paramObject.put("name", dmcPointItem.getDeviceName());
            paramObject.put("protocol", dmcPointItem.getProtocolType());

            if (USCConstants.PROTOCOL_ONVIF.equals(dmcPointItem.getProtocolType())) {
                paramObject.put("dev_username", dmcPointItem.getDeviceUsername());
                paramObject.put("dev_userpass", dmcPointItem.getDeviceUserpass());
                paramObject.put("ip", dmcPointItem.getDeviceIp());
                paramObject.put("manufacturer", "UNKNOWN");
                paramObject.put("port", Integer.parseInt(dmcPointItem.getDevicePort()));
                paramObject.put("url_address", "http://" + dmcPointItem.getDeviceIp() + ":" + dmcPointItem.getDevicePort() + "/onvif/device_service");
            }

            if (USCConstants.PROTOCOL_RTSP.equals(dmcPointItem.getProtocolType())) {
                paramObject.put("url_address", dmcPointItem.getMainStream() + ";" + dmcPointItem.getSecondStream());
            }

            deviceArray.add(paramObject);
        }

        JSONObject deviceObject = new JSONObject();
        deviceObject.put("device", deviceArray);


        String post = httpRequestUtil.postCommon(MediaServerConstants.ADD_DEVICE, deviceObject);
        JSONObject jsonObject = JSONObject.parseObject(post);
        if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == USCConstants.SUCCESS) {
            return ApiResultMessage.successMessage(jsonObject.get("data"));
        } else {
            JSONArray data = jsonObject.getJSONArray("data");
            if (data != null && !data.isEmpty()) {
                JSONObject first = data.getJSONObject(0);
                return ApiResultMessage.failedMessage(new ApiResultMessage.ErrorResult(
                        first.getIntValue("error_code"),
                        first.getString("error_msg")
                ));
            }
            return ApiResultMessage.failedMessage(new ApiResultMessage.ErrorResult(
                    jsonObject.getIntValue("code"),
                    null
            ));
        }

    }

    @Override
    public ApiResultMessage getChannelInfo(GetChannelItem getChannelItem) {
        JSONObject paramObject = buildConditionObject(getChannelItem);
        String post = httpRequestUtil.postCommon(MediaServerConstants.GET_CHANNEL_INFO, paramObject);
        JSONObject jsonObject = JSONObject.parseObject(post);
        if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == USCConstants.SUCCESS) {
            return ApiResultMessage.successMessage(jsonObject.get("data"));
        } else {
            return ApiResultMessage.failedMessage(jsonObject.getInteger("code"));
        }
    }


    /**
     * 通道信息查询参数构建
     *
     * <AUTHOR>
     * @date 2025-01-13 11:25
     * @since 1.0.0
     */
    private JSONObject buildConditionObject(GetChannelItem getChannelItem) {
        JSONObject paramObject = new JSONObject();
        JSONObject condition = new JSONObject();

        if (StringUtils.isNotBlank(getChannelItem.getId())) {
            condition.put("id", getChannelItem.getId());
            paramObject.put("condition", condition);
        } else {
            condition.put("parent_id", getChannelItem.getDeviceId());
            JSONArray order = new JSONArray();
            JSONObject channel = new JSONObject();
            channel.put("channel_number", getChannelItem.getChannelNumber());
            order.add(channel);
            JSONObject page = new JSONObject();
            page.put("current_page", getChannelItem.getPageNo());
            page.put("page_size", getChannelItem.getPageSize());


            paramObject.put("condition", condition);
            paramObject.put("order", order);
            paramObject.put("page", page);
        }
        return paramObject;
    }

    @Override
    public ApiResultMessage delPointInfo() {
        return null;
    }


    @Override
    public ApiResultMessage delPoints(List<DmcPointItem> dmcPointList) {
        Set<String> deviceIds = dmcPointList.stream()
                .map(item -> String.valueOf(item.getDeviceId()))
                .collect(Collectors.toSet());

        for (String deviceId : deviceIds) {
            List<DmcPointItem> groupItem = filterPointsByDeviceId(dmcPointList, deviceId);
            boolean deleteWholeDevice = groupItem.size() == dmcPointDao.countByDeviceId(deviceId);

            JSONObject payload = buildDeletePayload(groupItem, deviceId, deleteWholeDevice);
            String url = deleteWholeDevice ? MediaServerConstants.DEL_DEVICE : MediaServerConstants.DEL_STREAM_PROXY_V2;

            JSONObject response = JSONObject.parseObject(httpRequestUtil.postCommon(url, payload));
            int code = response.getIntValue("code");

            if (code != USCConstants.SUCCESS) {
                JSONArray data = response.getJSONArray("data");
                String errorMsg = (data != null && !data.isEmpty())
                        ? data.getJSONObject(0).getString("error_msg")
                        : null;
                return ApiResultMessage.failedMessage(new ApiResultMessage.ErrorResult(code, errorMsg));
            }

            deleteLocalPoints(groupItem);
            deletePointControlRules(groupItem);
            log.info(" 删除{}成功，设备 ID: {}", deleteWholeDevice ? "设备" : "拉流代理", deviceId);
        }

        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage deleteDevice(String deviceId) {
        JSONObject dataObject = new JSONObject();
        JSONArray deviceArray = new JSONArray();
        deviceArray.add(deviceId);
        dataObject.put("device", deviceArray);

        JSONObject response = JSONObject.parseObject(httpRequestUtil.postCommon(MediaServerConstants.DEL_DEVICE, dataObject));
        int code = response.getIntValue("code");

        if (code != USCConstants.SUCCESS) {
            return ApiResultMessage.failedMessage(response.getInteger("code"));
        }

        return ApiResultMessage.successMessage();
    }


    /**
     * 通过设备id查询点位
     *
     * @param allPoints:
     * @param deviceId:
     * @return java.util.List<com.zkteco.mars.usc.vo.DmcPointItem>
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 9:39
     * @since 1.0.0
     */
    private List<DmcPointItem> filterPointsByDeviceId(List<DmcPointItem> allPoints, String deviceId) {
        return allPoints.stream()
                .filter(item -> deviceId.equals(String.valueOf(item.getDeviceId())))
                .collect(Collectors.toList());
    }

    /**
     *  构建下发设备and 通道参数
     *
     * @param groupItem:
     * @param deviceId:
     * @param deleteWholeDevice:
     * @return com.alibaba.fastjson2.JSONObject
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 9:40
     * @since 1.0.0
     */
    private JSONObject buildDeletePayload(List<DmcPointItem> groupItem, String deviceId, boolean deleteWholeDevice) {
        JSONObject data = new JSONObject();
        if (deleteWholeDevice) {
            JSONArray deviceArray = new JSONArray();
            deviceArray.add(deviceId);
            data.put("device", deviceArray);
        } else {
            JSONArray streamArray = new JSONArray();
            for (DmcPointItem item : groupItem) {
                JSONObject stream = new JSONObject();
                stream.put("channel_id", item.getBusinessId());
                stream.put("stream_type", 0);
                stream.put("app", "mars");
                streamArray.add(stream);
            }
            data.put("stream", streamArray);
        }
        return data;
    }

    /**
     * 删除本地的点位数据
     *
     * @param groupItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 9:40
     * @since 1.0.0
     */
    private void deleteLocalPoints(List<DmcPointItem> groupItem) {
        deleteByIds(groupItem.stream()
                .map(dmcPointItem -> String.valueOf(dmcPointItem.getId()))
                .collect(Collectors.joining(",")));
    }

    /**
     * 删除点位绑定的规则信息
     *
     * @param groupItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date  2025-04-23 9:40
     * @since 1.0.0
     */
    private void deletePointControlRules(List<DmcPointItem> groupItem) {
        for (DmcPointItem item : groupItem) {
            pointControlRulesDao.deleteByPointId(item.getBusinessId());
        }
    }

    @Override
    public ApiResultMessage editPointName(DmcPointItem dmcPointItem) {
        JSONObject paramObject = new JSONObject();
        paramObject.put("channel_type", 0);
        paramObject.put("id", dmcPointItem.getId());
        paramObject.put("name", dmcPointItem.getName());

        String post = httpRequestUtil.postCommon(MediaServerConstants.EDIT_CHANNEL, paramObject);
        JSONObject jsonObject = JSONObject.parseObject(post);
        if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == USCConstants.SUCCESS) {
            return ApiResultMessage.successMessage(jsonObject.get("data"));
        } else {
            return ApiResultMessage.failedMessage(jsonObject.getInteger("code"));
        }
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        return dmcPointDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            list.forEach(id -> {
                dmcPointDao.deleteById(id);
            });
        }
        return false;
    }


    @Override
    public ApiResultMessage editPointInfo(DmcPointItem dmcPointItem) {
        JSONObject dataObject = buildEditPointInfo(dmcPointItem);
        try {
            String post = httpRequestUtil.postCommon(MediaServerConstants.EDIT_STREAM, dataObject);
            JSONObject jsonObject = JSONObject.parseObject(post);
            if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == USCConstants.SUCCESS) {
                DmcPoint dmcPoint = dmcPointDao.findByBusinessId(dmcPointItem.getBusinessId());
                dmcPoint.setName(dmcPointItem.getName());
                dmcPoint.setCaptureInterval(dmcPointItem.getCaptureInterval());
                dmcPoint.setDetectType(dmcPointItem.getDetectType());
                dmcPointDao.save(dmcPoint);
            } else {
                return ApiResultMessage.failedMessage(jsonObject.getInteger("code"));
            }
        } catch (Exception e) {
            log.error("Edit Point Info error : {} ", e.getMessage());
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage getAllPointName() {
        List<String> pointNames = dmcPointDao.getAllPointName();
        return ApiResultMessage.successMessage(pointNames);
    }

    @Override
    public ApiResultMessage getPointIdAndNameList() {
        List<java.util.Map<String, Object>> pointList = dmcPointDao.getPointIdAndNameList();
        return ApiResultMessage.successMessage(pointList);
    }

    /**
     * 构建编辑信息
     *
     * @param dmcPointItem:
     * @return com.alibaba.fastjson2.JSONObject
     * @throws
     * <AUTHOR>
     * @date 2025-02-27 14:54
     * @since 1.0.0
     */
    private JSONObject buildEditPointInfo(DmcPointItem dmcPointItem) {
        JSONArray streamArray = new JSONArray();
        JSONObject paramObject = new JSONObject();
        paramObject.put("channel_id", dmcPointItem.getBusinessId());
        paramObject.put("stream_type", 0);
        paramObject.put("app", "mars");

        JSONObject extObject = new JSONObject();
        extObject.put("autoSnipaste", Integer.parseInt(dmcPointItem.getCaptureInterval()));
        extObject.put("detectType", dmcPointItem.getDetectType());

        paramObject.put("ext", extObject);
        streamArray.add(paramObject);

        JSONObject dataObject = new JSONObject();
        dataObject.put("stream", streamArray);
        return dataObject;
    }

}
