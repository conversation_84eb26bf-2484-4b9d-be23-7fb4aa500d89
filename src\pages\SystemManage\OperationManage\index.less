.operation-manage-container {
  display: flex;
  gap: 0;
  min-height: calc(100vh - 200px);
  background: #f5f5f5;

  .log-files-section {
    width: 280px;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    transition: all 0.3s ease;

    &.collapsed {
      width: 60px;

      .log-menu-header {
        justify-content: center;

        .ant-btn {
          margin: 0;
        }
      }
    }
  }

  .log-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
    }

    .menu-collapse-btn {
        background-color: transparent !important;
        border-color: transparent !important;
        color: rgba(0, 0, 0, 0.45);
        margin-left: auto;
        transition: all 0.3s ease;

        &:hover {
          color: #0BD357 !important;
        }
      }
  }

  .system-menu {
    .menu-item {
      padding: 12px 16px;
      margin: 4px 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);

      &:hover {
        background-color: #f5f5f5;
        transform: translateX(2px);
        color: rgba(0, 0, 0, 0.85);
      }

      &.active {
        background-color: #e6f7ff;
        border-left: 3px solid #1890ff;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        color: #1890ff;
        font-weight: 600;

        .anticon {
          color: #1890ff;
        }
      }

      .anticon {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .log-menu-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .log-file-list {
    padding: 0 12px;
  }

  .log-file-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 12px;
  }

  .log-file-item:hover {
    background-color: #f5f5f5;
    transform: translateX(2px);
  }

  .log-file-item.active {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  }

  .log-file-icon {
    font-size: 20px;
    color: #1890ff;
    flex-shrink: 0;
  }

  .log-file-item.active .log-file-icon {
    color: #1890ff;
  }

  .log-file-info {
    flex: 1;
    min-width: 0;
  }

  .log-file-name {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .log-file-item.active .log-file-name {
    color: #1890ff;
    font-weight: 600;
  }

  .log-file-type {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }

  .log-content-section {
    flex: 1;
    padding: 16px;
    background: #fff;
  }

  .log-controls {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 4px;
  }

  .log-viewer-card {
    .log-content {
      height: 500px;
      overflow-y: auto;
      background: #1e1e1e;
      color: #d4d4d4;
      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      line-height: 1.4;
      padding: 12px;
      border-radius: 4px;
      white-space: pre-wrap;
      word-break: break-all;

      .log-line {
        margin-bottom: 2px;
        display: flex;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .log-line-number {
          color: #858585;
          margin-right: 12px;
          min-width: 50px;
          text-align: right;
          user-select: none;
          border-right: 1px solid #404040;
          padding-right: 8px;
          font-size: 11px;
        }

        .log-line-content {
          flex: 1;
          padding-left: 8px;

          // 日志级别高亮
          &:contains('ERROR'),
          &:contains('error') {
            color: #f56565;
          }

          &:contains('WARN'),
          &:contains('warn') {
            color: #ed8936;
          }

          &:contains('INFO'),
          &:contains('info') {
            color: #4299e1;
          }

          &:contains('DEBUG'),
          &:contains('debug') {
            color: #9f7aea;
          }
        }
      }
    }
  }

  .log-download-card {
    text-align: center;
    padding: 40px 0;
  }



  // 日志控制区域
  .log-controls {
    .ant-btn {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 分页组件样式
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
  }

  .ant-list-item-meta-title {
    margin-bottom: 0;
  }

  .ant-list-item-action {
    margin-left: 12px;
  }
}

///* 响应式设计 */
@media (max-width: 768px) {
  .operation-manage-container {
    flex-direction: column;
  }

  .log-files-section {
    flex: none;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
  }

  .log-menu-header {
    padding: 12px 16px;
  }

  .log-content-section {
    padding: 12px;
  }
}
