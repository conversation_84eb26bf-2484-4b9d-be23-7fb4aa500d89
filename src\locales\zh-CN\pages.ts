export default {
    'pages.login.success': "登录成功！",
    'pages.login.failure': "登录失败！",
    'pages.login.retry': "登录失败，请重试！",
    'pages.login.title': "登录页",
    'pages.login.welcome': "欢迎来到火星慧知",
    'pages.login.agreementPrefix': "您已阅读并同意火星慧知",
    'pages.login.terms': "《用户使用条款》",
    'pages.login.and': " 和",
    'pages.login.privacy': "《隐私政策》",
    'pages.login.submit': "用户登录",
    'pages.login.usernamePlaceholder': "请输入用户名",
    'pages.login.passwordPlaceholder': "请输入密码",
    'pages.search.sortByTime': "按最新时间",
    'pages.search.sortBySimilarity': "按相似度",
    'pages.search.history': "历史记录",
    'pages.search.searchHistory': "搜索历史记录",
    'pages.search.delete': "删除",
    'pages.search.hotSearch': "热门搜索",
    'pages.search.example1': "门口机动车旁边的男生",
    'pages.search.example2': "电梯间红色衣服打电话的人",
    'pages.search.example3': "停车场岗亭戴蓝色帽子的人",
    'pages.search.assistHint': "我可以帮助您搜索信息，比如：在园区一楼外卖柜旁的黑色衣服男生",
    'pages.search.searchButton': "搜索",
    'pages.search.filter': "检索条件",
    'pages.search.loading': "加载中...",
    'pages.search.eventDetail': "事件详情",
    'pages.search.videoPlayback': "视频回放",
    'pages.search.panorama': "全景图",
    'pages.search.description': "描述",
    'pages.search.time': "时间",
    'pages.search.location': "地点",
    'pages.search.similarity': "相似度",
    'pages.imageSearch.sort': '排序方式',
    'pages.imageSearch.point': '点位',
    'pages.imageSearch.timeRange': '时间范围',
    'pages.pointManage.voiceInput': "语音输入",
    'pages.pointManage.speechRecognition': "语音识别",
    'pages.pointManage.stopVoiceInput': "停止语音输入",
    'pages.pointManage.operationSuccess': "操作成功",
    'pages.pointManage.preview': "预览",
    'pages.pointManage.edit': "编辑",
    'pages.pointManage.alreadyMonitored': "已布控",
    'pages.pointManage.monitor': "布控",
    'pages.pointManage.delete': "删除",
    'pages.pointManage.deleteRecord': "删除记录",
    'pages.pointManage.confirmDeleteRecord': "确定删除记录吗？",
    'pages.pointManage.confirm': "确认",
    'pages.pointManage.cancel': "取消",
    'pages.pointManage.deleting': "正在删除",
    'pages.pointManage.deleteSuccess': "删除成功，自动刷新",
    'pages.pointManage.deleteFailure': "删除失败，请重试",
    'pages.pointManage.person': "人",
    'pages.pointManage.motorVehicle': "机动车",
    'pages.pointManage.nonMotorVehicle': "非机动车",
    'pages.pointManage.pointName': "点位名称",
    'pages.pointManage.protocolType': "协议类型",
    'pages.pointManage.captureType': "抓拍类型",
    'pages.pointManage.captureInterval': "抓拍间隔",
    'pages.pointManage.deviceName': "设备名称",
    'pages.pointManage.deviceIP': "设备IP",
    'pages.pointManage.devicePort': "设备端口",
    'pages.pointManage.operation': "操作",
    'pages.pointManage.pointManagement': "点位管理",
    'pages.pointManage.add': "新增",
    'pages.pointManage.selected': "已选择",
    'pages.pointManage.batchDelete': "批量删除",
    'pages.pointManage.item': "项",
    'pages.pointManage.inputCaptureInterval': "请输入抓拍间隔",
    'pages.pointManage.inputPointName': "请输入点位名称",
    'pages.pointManage.selectCaptureType': "请选择抓拍类型",
    'pages.pointManage.addDevice': "添加设备",
    'pages.pointManage.protocol': "协议",
    'pages.pointManage.deviceCode': "设备编码",
    'pages.pointManage.inputDeviceName': "请输入设备名称",
    'pages.pointManage.inputDeviceCode': "请输入设备编码",
    'pages.pointManage.port': "端口",
    'pages.pointManage.username': "用户名",
    'pages.pointManage.password': "密码",
    'pages.pointManage.mainStream': "主码流",
    'pages.pointManage.example': "示例",
    'pages.pointManage.subStream': "子码流",
    'pages.pointManage.addPoint': "添加点位",
    'pages.pointManage.validIP': "请输入有效的 IP 地址",
    'pages.pointManage.noData': "无数据",
    'pages.pointManage.selectPoint': "请选择点位",
    'pages.pointManage.addSuccess': "添加成功",
    'pages.pointManage.prevStep': "上一步",
    'pages.pointManage.nextStep': "下一步",
    'pages.pointManage.monitorSuccess': "布控成功",
    'pages.pointManage.monitorFailure': "布控失败",
    'pages.pointManage.cancelSuccess': "取消成功",
    'pages.pointManage.operationFailure': "操作失败",
    'pages.pointManage.monitor': "布控",
    'pages.pointManage.monitorEvent': "布控事件",
    'pages.pointManage.startMonitor': "开始布控",
    'pages.pointManage.monitorRecord': "布控记录",
    'pages.pointManage.noEventRecord': "没有事件记录",
    'pages.pointManage.charLimitExceeded': "字符长度超出限制，最大长度：",
    'pages.pointManage.eventType': "事件类型",
    'pages.pointManage.prompt': "提示词",
    'pages.pointManage.createTime': "创建时间",
    'pages.pointManage.editSuccess': "编辑成功",
    'pages.pointManage.monitorRule': "布控规则",
    'pages.pointManage.selectEventType': "请选择事件类型",
    'pages.pointManage.inputPrompt': "请输入提示词",
    'pages.pointManage.eventRecord': "事件记录",
    'pages.pointManage.paginationInfo': "第 {range0} - {range1} 条/总共 {total} 条",
    'pages.pointManage.detail': "详情",
    'pages.pointManage.eventImage': "事件图片",
    'pages.pointManage.pointInfo': "点位信息",
    'pages.pointManage.eventTime': "事件时间",
    'pages.config.name': "名称",
    'pages.config.creationDate': "创建日期",
    'pages.config.expirationTime': "过期时间",
    'pages.config.editApiKey': "编辑API KEY",
    'pages.config.editSuccess': "编辑成功，自动刷新",
    'pages.config.createSuccess': "创建成功，自动刷新",
    'pages.config.editFailure': "编辑失败，请重试",
    'pages.config.createFailure': "创建失败，请重试",
    'pages.config.createApiKey': "创建API KEY",
    'pages.config.authManagement': "授权管理",
    'pages.config.eventCode': "事件编码",
    'pages.config.paramConfigFailure': "获取参数配置失败",
    'pages.config.saveFailure': "保存失败",
    'pages.config.saveSuccess': "保存成功",
    'pages.config.save': "保存",
    'pages.config.reset': "重置",
    'pages.config.streamConfig': "流媒体配置",
    'pages.config.streamServiceUrl': "流媒体服务地址",
    'pages.config.secretKey': "密钥",
    'pages.config.configuration': "配置",
    'pages.config.workspaceId': "工作空间id",
    'pages.config.multiSearchStrategy': "多维搜检索策略",
    'pages.config.dataCleanStrategy': "数据清洗策略",
    'pages.config.objectDetection': "目标检测",
    'pages.config.enableObjectDetection': "是否启用目标检测",
    'pages.config.allowObjectWhitelist': "允许目标检测白名单",
    'pages.config.sedan': "轿车",
    'pages.config.bus': "公交车",
    'pages.config.truck': "货车",
    'pages.config.bicycle': "自行车",
    'pages.config.motorcycle': "摩托车",
    'pages.config.enableImageDupCheck': "是否启用图像重复检测",
    'pages.config.intervalSeconds': "间隔时间(秒)",
    'pages.config.minScoreLimitError': "最小分数值不能大于1",
    'pages.config.initialSearchStrategy': "初筛检索策略",
    'pages.config.enhancedSearchStrategy': "多维搜增强检索策略",
    'pages.config.multiInitialSearch': "多维搜初筛",
    'pages.config.minScore': "最小分数值",
    'pages.config.maxResultSet': "最大结果集",
    'pages.config.topValue': "Top值",
    'pages.config.reRanking': "多维搜重排名",
    'pages.config.batchSize': "每批次大小",
    'pages.config.fineSearch': "多维搜精筛",
    'pages.config.fineSearchStrategy': "精筛检索策略",
    'pages.config.enableReId': "是否启用ReId",
    'pages.config.objectMinScore': "目标检测最低分",
    'pages.config.vectorMinScore': "向量相似最低分",
    'pages.config.maxResultsPerSearch': "每次检索最大结果集",
    'pages.common.logout': "退出登录",
    'pages.operation.logFiles': "日志文件",
    'pages.operation.realTimeLog': "实时日志",
    'pages.operation.refreshLog': "刷新日志",
    'pages.operation.view': "查看",
    'pages.operation.download': "下载",
    'pages.operation.logDownload': "日志下载",
    'pages.operation.noLogContent': "暂无日志内容",
    'pages.agent.apiCallFailed': "调用接口失败，请稍后重试",
    'pages.agent.hello': "哈喽，我是",
    'pages.agent.agent': "智能体",
    'pages.agent.attachment': "附件",
    'pages.agent.dropFilesHere': "将文件放到此处",
    'pages.agent.uploadFile': "上传文件",
    'pages.agent.clickOrDragToUpload': "单击或拖动文件到此区域进行上传",
    'pages.agent.shiftEnterNewline': "Shift+Enter换行",
    'pages.agent.basicConfig': "基础配置",
    'pages.agent.llmModel': "使用的LLM模型",
    'pages.agent.doubaoModel': "豆包模型",
    'pages.agent.selectAnOption': "请选择一个选项",
    'pages.agent.memoryMessageCount': "记忆消息数",
    'pages.agent.skillConfig': "技能配置",
    'pages.agent.toolSet': "工具集",
    'pages.agent.toolSetDescription': "工具集能够让智能体调用外部工具，扩展智能体的能力边界。",
    'pages.agent.knowledgeBase': "知识库",
    'pages.agent.knowledgeBaseDescription': "用户发送消息时，智能体能够引用知识库的内容，回答用户的问题。",
    'pages.agent.workflow': "工作流",
    'pages.agent.workflowDescription': "用于处理逻辑复杂且有较多步骤的任务流。",
    'pages.agent.describePersona': "请描述人设、功能",
    'pages.agent.publishSuccess': "发布成功",
    'pages.agent.publishFailed': "发布失败",
    'pages.agent.publishNotAllowed': "抱歉，该智能体不能进行发布",
    'pages.agent.config': "智能体配置",
    'pages.agent.publish': "发布",
    'pages.agent.modelCapabilityConfig': "模型能力配置",
    'pages.agent.promptDev': "提示词开发",
    'pages.agent.debug': "智能体调试",
    'pages.agent.create': "创建智能体",
    'pages.agent.submitFailed': "提交失败，请检查表单数据",
    'pages.agent.name': "智能体名称",
    'pages.agent.nameLimit': "最多可输入64个字符",
    'pages.agent.description': "智能体功能介绍",
    'pages.agent.descriptionTip': "介绍智能体的功能，将会展示给智能体的用户",
    'pages.agent.icon': "图标",
    'pages.agent.imageOnly': "只能上传图片文件",
    'pages.agent.imageSizeLimit': "图片大小不能超过2MB",
    'pages.agent.imageFormatLimit': "支持jpg/png格式，大小不超过2MB",
    'pages.agent.flagship': "旗舰",
    'pages.agent.highSpeed': "高速",
    'pages.agent.toolInvocation': "工具调用",
    'pages.agent.rolePlay': "角色扮演",
    'pages.agent.longText': "长文本",
    'pages.agent.imageUnderstanding': "图片理解",
    'pages.agent.reasoning': "推理能力",
    'pages.agent.videoUnderstanding': "视频理解",
    'pages.agent.costPerformance': "性价比",
    'pages.agent.codeExpert': "代码专精",
    'pages.agent.audioUnderstanding': "音频理解",
    'pages.agent.visualAnalysis': "视觉分析",
    'pages.agent.running': "运行中",
    'pages.agent.queuing': "排队中",
    'pages.agent.training': "训练中",
    'pages.agent.trainingFailed': "训练失败",
    'pages.agent.text': "文本",
    'pages.agent.multimodal': "多模态",
    'pages.agent.landongModel': "懒洞模型",
    'pages.agent.searchModelName': "搜索模型名称",
    'pages.agent.quotaTrial': "限额体验",
    'pages.agent.comingOffline': "即将下线",
    'pages.agent.newModelExperience': "新模型体验",
    'pages.agent.advancedModel': "高级模型",
    'pages.agent.generalModel': "通用模型",
    'pages.agent.modelType': "模型类型",
    'pages.agent.modelFeature': "模型特色",
    'pages.agent.modelProvider': "模型厂商",
    'pages.agent.modelSupportedFunctions': "模型支持功能",
    'pages.agent.contextLength': "上下文长度",
    'pages.agent.userRights': "用户权益",
    'pages.agent.creator': "创建者",
    'pages.agent.creationTime': "创建时间",
    'pages.agent.describeFunction': "请描述人设、功能",
    'pages.agent.orchestration': "编排",
    'pages.agent.functionIntroduction': "功能介绍",
    'pages.agent.publishStatus': "发布状态",
    'pages.agent.agentDisplay': "智能体展示",
    'pages.agent.modelStatus': "模型状态",
    'pages.search.expandir': "展开",
    'pages.search.retirar': "收起",
    'pages.search.deleteConfirmWarning': "删除后将无法恢复，确定要删除吗？",
    'pages.config.applicationId': "应用 ID",
    'pages.config.imageDeduplication': "图像去重",
    'pages.pointManage.loadingMessage': "加载中，请勿刷新页面",
    'pages.pointManage.fetchError': "获取点位超时，请检查设备是否在线",
    'pages.pointManage.deviceTimeout': "设备请求超时",
    'pages.pointManage.streamConnectFailed': "流媒体服务连接失败",
    'pages.pointManage.serviceException': "服务异常，请稍后重试",
    'pages.pointManage.deleteHasControlRule': "选中的点位中正在布控中，无法删除",
    'pages.pointManage.online': "在线",
    'pages.pointManage.offline': "离线",
    'pages.pointManage.confirmDeleteEventType': "确定删除该事件类型吗？",
    'pages.pointManage.captureIntervalRange': "抓拍间隔在 1 到 3600 秒之间",
    'pages.pointManage.status': "状态",
    'pages.login.terms.title': "《产品使用协议》",
    'pages.login.terms.check': "请先阅读并同意《产品使用协议》",
    'pages.pointManage.confirmDeletePoint': "确定删除点位信息记录吗？",
    'pages.pointManage.pointNameRequired': "存在未填写的点位名称，请填写后再提交",
    'pages.pointManage.refresh': "刷新",
    'pages.account.updateSuc': "修改成功",
    'pages.account.updatePwd': "修改密码",
    'pages.account.oldPassword': "原密码",
    'pages.account.newPassword': "新密码",
    'pages.account.confirmPwd': "确认密码",
    'pages.account.passwordmatch': "您输入的新密码不匹配",
    'pages.password.reset.fail': "重置密码失败",
    'pages.password.reset.success': "密码重置成功",
    'pages.password.update': "修改密码",
    'pages.register.success': "注册成功，请登录",
    'pages.register.fail': "注册失败",
    'pages.login.fail': "用户名或密码错误",
    'pages.login.needRegister': "请先注册账号",
    'pages.system.check.fail': "服务检测失败",
    'pages.account.maxlength': "密码最多18位",
    'pages.login.login': "登 录",
    'pages.login.register': "注 册",
    'pages.login.registerTitle': "用户注册",
    'pages.search.similarity': "相似度",
    'pages.common.sessionExpired': "用户已过期，请重新登录",
    'pages.primaryKey.id': "主键 ID",
    'pages.agent.type': "类型",
    'pages.agent.type.placeholder': "请选择智能体类型",
    'pages.agent.type.required': "请选择类型",
    'pages.agent.id': "智能体 ID",
    'pages.agent.id.placeholder': "请输入智能体 ID",
    'pages.agent.id.required': "请输入智能体 ID",
    'pages.agent.suggestedQuestions': "你可以这样问我：",
    'pages.agent.botId.tip': "请前往对应平台（如 Coze、Dify）创建智能体后复制其 ID 粘贴到此处",
    'pages.agent.apiKey.tip': "请前往 Dify 平台，复制其 API Key 粘贴到此处",
    'pages.agent.apiKey.required': "API Key 为必填项",
}
