import { Modal, Button, message, Form, Steps, Spin, Input, Select } from "antd"
import { useState, useRef, useEffect } from "react"
import { useIntl } from 'umi'
import { ProForm, ProFormText, ProFormDigit, ProFormSelect, ProTable } from '@ant-design/pro-components';
import './index.less'
import { addDeviceInfos, getChannelInfo, addPointInfo, editPointName, deleteDevice } from "@/services/ant-design-pro/api";

const contentStyle: React.CSSProperties = {
  maxHeight: '73vh',
  overflowY: 'auto',
  //paddingRight: 8,
};

const ManualEditDevice: React.FC = (props: any, ref: any) => {
  /** 国际化 */
  const intl = useIntl();
  /**RTSP地址正则表达式 */
  const RTSP_URL_REGEXP = /^rtsp:\/\/((?:[^\s\/?#]+@)?[^\s\/?#]+)(?::\d+)?(\/[^\s?#]+)(?:\?[^\s#]+)?$/
  /**协议类型 */
  const [protocol, setProtocol] = useState('ONVIF')
  /** 设备表单 */
  const formRef = useRef()
  /** 设备表单格式 */
  const [form] = Form.useForm()
  /**厂商数据 */
  const [manufacturer, setManufacturer] = useState<any[]>([]);
  /** 当前页 */
  const [currentPage, setCurrentPage] = useState(1);
  /** 每页条数 */
  const [pageSize, setPageSize] = useState(8);
  /** 数据总数 */
  const [total, setTotal] = useState(0);
  /** 全局保存 deviceId */
  const [deviceId, setDeviceId] = useState('');
  /** 全局保存 设备信息 */
  const [deviceInfo, setDeviceInfo] = useState('');
  //当前执行的步骤
  const [current, setCurrent] = useState(0);
  // Loading 状态管理
  const [loading, setLoading] = useState(false);
  /** 点位表单 */
  const channelRef = useRef()
  /** 点位数据 */
  const channelDataRef = useRef()
  /** 通道数据 */
  const [channelData, setChannelData] = useState('')
  /** 选中点位集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  /** 搜索通道的集合 */
  const [searchChannels, setSearchChannels] = useState<any[]>([]);


  const dataColumns = [
    {
      title: "id",
      dataIndex: 'id',
      hideInSearch: true,
      ellipsis: true,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.pointName', defaultMessage: '点位名称', }),
      width: 80,
      dataIndex: 'name',
      hideInSearch: true,
      ellipsis: true,
      render: (text, record, index, action) => (
        <Input
          value={record.name || ''}
          maxLength={64}
          onChange={(e) => {
            const updatedValue = e.currentTarget.value;
            const updatedData = channelData.map((channel) => {
              if (channel.id === record.id) {
                channel.name = updatedValue;
              }
              return channel;
            });
            setSearchChannels([...updatedData]);
          }}
          status={record.name?.trim() === '' ? 'error' : ''}
          placeholder={intl.formatMessage({
            id: 'pages.pointManage.inputPointName',
            defaultMessage: '请输入点位名称',
          })}
        />

      ),
    },

    {
      title: intl.formatMessage({ id: 'pages.pointManage.captureType', defaultMessage: '抓拍类型', }),
      width: 120,
      dataIndex: 'detectType',
      render: (text, record, index) => (
        <Select
          mode="multiple"
          placeholder={intl.formatMessage({ id: 'pages.pointManage.selectCaptureType', defaultMessage: '请选择抓拍类型', })}
          onChange={(value) => {
            const formattedValue = value.join(',');
            const updatedData = channelData.map((channel) => {
              if (channel.id === record.id) {
                channel.detectType = formattedValue;
                console.log("channel.detectType->", channel.detectType);
              }
              return channel;
            });
            setSearchChannels(updatedData);
          }}
          options={[
            { label: intl.formatMessage({ id: 'pages.pointManage.person', defaultMessage: '人', }), value: 1 },
            { label: intl.formatMessage({ id: 'pages.pointManage.motorVehicle', defaultMessage: '机动车', }), value: 2 },
            { label: intl.formatMessage({ id: 'pages.pointManage.nonMotorVehicle', defaultMessage: '非机动车', }), value: 3 },
          ]}
          style={{ width: '100%' }}
        />
      ),
    },

    {
      title: intl.formatMessage({ id: 'pages.pointManage.captureInterval', defaultMessage: '抓拍间隔', }),
      width: 80,
      dataIndex: 'captureInterval',
      hideInSearch: true,
      ellipsis: true,
      editable: true,
      render: (text, record, index, action) => [
        <Input
          type="number"
          min={1}
          max={3600}
          value={record.captureInterval ?? 5}
          onChange={(e) => {
            let inputValue = e.currentTarget.value;
            // 限制输入范围
            if (isNaN(inputValue)) {
              inputValue = 1;
            } else if (inputValue < 1) {
              inputValue = 1;
            } else if (inputValue > 3600) {
              inputValue = 3600;
            }
            record.captureInterval = inputValue;

            const updatedData = channelData.map((channel) => {
              if (channel.id === record.id) {
                channel.captureInterval = inputValue;
              }
              return channel;
            });

            setSearchChannels(updatedData);
          }}
          onBlur={(e) => {
            const inputValue = e.currentTarget.value.trim();

            if (inputValue === '' || isNaN(inputValue)) {
              record.captureInterval = 5;
            } else {
              record.captureInterval = Number(inputValue);
            }


            const updatedData = channelData.map((channel) => {
              if (channel.id === record.id) {
                channel.captureInterval = record.captureInterval;
              }
              return channel;
            });

            setSearchChannels(updatedData);
          }}
        />
      ],
    }


  ];

  const steps = [
    {
      title: intl.formatMessage({ id: 'pages.pointManage.addDevice', defaultMessage: '添加设备', }),
      content: <div style={{ display: 'flex', justifyContent: 'center', padding: '5% 0% 0% 5%' }} className="manualEditDeviceForm">
        <Spin spinning={loading} tip={intl.formatMessage({ id: 'pages.pointManage.loadingMessage', defaultMessage: '加载中，请勿刷新页面', })}>
          <ProForm<{
            name: string;
            ip: string;
            port: number;
            username: string;
            passs: string;
          }>
            formRef={formRef}
            layout={'horizontal'}
            form={form}
            style={{ width: '100%' }}
            labelAlign="left"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            submitter={{
              resetButtonProps: {
                hidden: true,
              },
              submitButtonProps: {
                hidden: true,
              },
              searchConfig: {
              }
            }}
          >
            <ProFormSelect
              width="md"
              name='protocolType'
              allowClear={false}
              rules={[{ required: true }]}
              label={intl.formatMessage({ id: 'pages.pointManage.protocol', defaultMessage: '协议', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.protocol', defaultMessage: '协议', })}
              fieldProps={{
                onChange: (value: any, options: any) => {
                  setProtocol(value)

                }
              }}
              initialValue={props.editData?.protocol || protocol}
              valueEnum={{
                ONVIF: 'ONVIF',
                RTSP: 'RTSP',
                // GB28181: 'GB28181',
                /*SIP: 'SIP',*/
              }}
              disabled={props.editData ? true : false}
            />
            <ProFormText
              width="md"
              name="deviceName"
              initialValue={props.editData?.name}
              rules={[
                {
                  required: true
                },
                {
                  max: 64,
                  message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 64"
                },
              ]}
              fieldProps={{
                maxLength: 64,
              }}
              label={intl.formatMessage({ id: 'pages.pointManage.deviceName', defaultMessage: '设备名称', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.inputDeviceName', defaultMessage: '请输入设备名称', })}
            />
            {protocol === 'GB28181' && <ProFormText
              width="md"
              name="device_id"
              style={{ width: '100px', display: 'inline-block' }}
              rules={[{
                required: true
              },
              {
                pattern: /^\d{10}(132|118|111)\d{7}$/,
                //message: intl.formatMessage({ id: 'pages.device.inputValidDeviceID' }),
              }]}
              label={intl.formatMessage({ id: 'pages.pointManage.deviceCode', defaultMessage: '设备编码', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.inputDeviceName', defaultMessage: '请输入设备编码', })}
              //tooltip={intl.formatMessage({ id: 'pages.device.deviceIDTip' })}
              disabled={props.editData ? true : false}
              initialValue={props.editData?.ext.device_id}
            />}

            {protocol === 'ONVIF' && <ProFormText
              width="md"
              name="deviceIp"
              style={{ width: '100px', display: 'inline-block' }}
              rules={[{ required: true }, { validator: validateIPAddress }]}
              label={'IP'}
              placeholder={'IP'}
              disabled={props.editData ? true : false}
              initialValue={props.editData?.ip}
            />}
            {protocol === 'ONVIF' && <ProFormDigit
              width="md"
              name="devicePort"
              style={{ width: '100px', display: 'inline-block' }}
              rules={[{ required: true }]}
              label={intl.formatMessage({ id: 'pages.pointManage.port', defaultMessage: '端口', })}
              min={1}
              max={65535}
              initialValue={props.editData?.port}
              disabled={props.editData ? true : false}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.port', defaultMessage: '端口', })}
              fieldProps={{
                maxLength: 5,
                precision: 0,
              }}
            />}
            {(protocol === 'ONVIF' || protocol === 'SIP') && <ProFormText
              width="md"
              name="deviceUsername"
              rules={[{ required: true }]}
              label={intl.formatMessage({ id: 'pages.pointManage.username', defaultMessage: '用户名', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.username', defaultMessage: '用户名', })}
              initialValue={props.editData?.dev_username}
            />}
            {(protocol === 'ONVIF' || protocol === 'GB28181' || protocol === 'SIP') && <ProFormText.Password
              width="md"
              name="deviceUserpass"
              rules={[{ required: true }]}
              label={intl.formatMessage({ id: 'pages.pointManage.password', defaultMessage: '密码', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.password', defaultMessage: '密码', })}
              initialValue={props.editData?.dev_userpass}
            />}
            {protocol === 'RTSP' && <ProFormText
              width="md"
              name="mainStream"
              rules={[
                { required: true }]}
              // initialValue={props.editData?.url_address?.split(';')[0] ? props.editData?.url_address?.split(';')[0] : ''}
              label={intl.formatMessage({ id: 'pages.pointManage.mainStream', defaultMessage: '主码流', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.mainStream', defaultMessage: '主码流', })}
              tooltip={intl.formatMessage({ id: 'pages.pointManage.example', defaultMessage: '示例', }) + "rtsp://xxx.xxx.xxx.xxx:554/ch1"}
            />}
            {protocol === 'RTSP' && <ProFormText
              width="md"
              name="secondStream"
              // initialValue={props.editData?.url_address?.split(';')[1] ? props.editData?.url_address?.split(';')[1] : ''}
              rules={[{ required: true }]}
              label={intl.formatMessage({ id: 'pages.pointManage.subStream', defaultMessage: '子码流', })}
              placeholder={intl.formatMessage({ id: 'pages.pointManage.subStream', defaultMessage: '子码流', })}
              tooltip={intl.formatMessage({ id: 'pages.pointManage.example', defaultMessage: '示例', }) + "rtsp://xxx.xxx.xxx.xxx:554/ch1_sub"}
            />}
          </ProForm>
        </Spin>
      </div>,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.addPoint', defaultMessage: '添加点位', }),
      content: <div>
        <ProTable
          rowKey="id"
          dataSource={channelData}
          actionRef={channelDataRef}
          formRef={channelRef}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, size) => {
              setCurrentPage(page); // 更新当前页
              setPageSize(size); // 更新每页条数
              handleChannelInfo(deviceId, page, size); // 加载分页数据
            },
          }}
          columns={dataColumns}
          options={{
            density: false,
            setting: false,
            reload: false,
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            onChange(value) {
              console.log('value: ', value);
            },
          }}
          search={false}
          rowSelection={{
            onChange: (_, selectedRows) => {
              console.log("selectedRows-> ", selectedRows)
              setSelectedRows(selectedRows);
            },
          }}
          tableAlertRender={false}
        />
      </div>,
    },

  ];


  /**
   * items 遍历
   */
  const items = steps.map((item) => ({ key: item.title, title: item.title }));

  /**
   * 函数声明形式，避免作用域问题
   */
  function validateIPAddress(rule: any, value: string): Promise<void> {
    const IP_REGEXP =
      /^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$/;

    if (!value || IP_REGEXP.test(value)) {
      return Promise.resolve();
    }

    return Promise.reject(new Error(intl.formatMessage({ id: 'pages.pointManage.validIP', defaultMessage: '请输入有效的 IP 地址', })));
  }

  /**
   * 上一步
   */
  const handleBackImport = async () => {
    if (current === 1) {

      await deleteDevice(deviceId);

      setCurrent(0);
    }
  };

  /**
   * 下一步
   * @returns
   */
  const handleNextImport = async () => {
    if (current === 0) {
      let values;
      try {
        values = await formRef.current?.validateFieldsReturnFormatValue?.();
      } catch (err) {
        console.log('edit add device error', err);
        return;
      }
      // 开始加载
      setLoading(true);
      const deviceList = [values];
      const rs = await addDeviceInfos(deviceList);

      if (rs.code === 0 && rs.data) {
        const newDeviceId = rs.data[0]?.id || '';
        setDeviceId(newDeviceId); // 更新全局 deviceId
        values.deviceId = newDeviceId
        setDeviceInfo(values)



        const data = await fetchChannelInfoWithTimeout(newDeviceId, 1, 10, 90 * 1000, values);

        // 停止加载
        setLoading(false);

        if (data) {
          setCurrent(1);
        } else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.fetchError', defaultMessage: '获取失败，请检查设备是否在线及填写信息是否正确', }));

          // 无数据就删除当前设备
          await deleteDevice(newDeviceId);
        }
      } else {
        const fallbackMessageMap = {
          'Read timed out': intl.formatMessage({ id: 'pages.pointManage.deviceTimeout', defaultMessage: '设备请求超时', }),
          "Connection refused: no further information": intl.formatMessage({ id: 'pages.pointManage.streamConnectFailed', defaultMessage: '流媒体服务连接失败', }),
          "Connection refused": intl.formatMessage({ id: 'pages.pointManage.streamConnectFailed', defaultMessage: '流媒体服务连接失败', })
        } as const;

        showErrorMessage(intl, rs, fallbackMessageMap);

        setLoading(false);
      }
    }

    if (current === 1) {
      if (selectedRows.length <= 0) {
        message.warning(intl.formatMessage({ id: 'pages.pointManage.selectPoint', defaultMessage: '请选择点位', }))
        return
      }
      const data: any = []

      const ids = searchChannels.map(item => item.id)
      console.log("ids ->", ids)
      for (const channel of selectedRows) {
        console.log("channel.id) ->", channel.id)
        channel.deviceId = deviceInfo?.deviceId
        channel.deviceIp = deviceInfo?.deviceIp
        channel.deviceName = deviceInfo?.deviceName;
        channel.devicePort = deviceInfo?.devicePort;
        channel.protocolType = deviceInfo?.protocolType;
        if (ids.includes(channel.id)) {
          const foundObject = searchChannels.find(item => item.id === channel.id);
          channel.name = foundObject.name;
          channel.detectType = foundObject.detectType ? foundObject.detectType : ""
          channel.captureInterval = foundObject.captureInterval ? foundObject.captureInterval : 5
          channel.businessId = channel.id
          data.push(channel)
        } else {
          console.log("channel ->", channel)
          channel.businessId = channel.id
          channel.detectType = ""
          channel.captureInterval = 5
          data.push(channel)
        }
      }
      console.log("SelectedRows ->", selectedRows)
      setSelectedRows(data)

      // 校验点位名称是否为空
      const hasEmptyName = data.some(item => !item.name || !item.name.trim());
      if (hasEmptyName) {
        message.warning(
          intl.formatMessage({
            id: 'pages.pointManage.pointNameRequired',
            defaultMessage: '存在未填写的点位名称，请填写后再提交',
          })
        );
        return;
      }
      const rs = await addPointInfo(data)
      if (rs.code === 0) {
        props.setManualEditVisiable(false);

        setCurrent(0);
        formRef.current?.resetFields();
        message.success(intl.formatMessage({ id: 'pages.pointManage.addSuccess', defaultMessage: '添加成功', }))

        for (const channel of selectedRows) {
          //添加点位成功后编辑点位名称
          await editPointName({
            "id": channel.id,
            "name": channel.name,
          })
        }
        // 清空选择
        setSelectedRows([])

        props.loadPointInfo({})

      } else {
        const fallbackMessageMap = {
          'Read timed out': intl.formatMessage({ id: 'pages.pointManage.deviceTimeout', defaultMessage: '设备请求超时', }),
          "Connection refused: no further information": intl.formatMessage({ id: 'pages.pointManage.streamConnectFailed', defaultMessage: '流媒体服务连接失败', }),
          "Connection refused": intl.formatMessage({ id: 'pages.pointManage.streamConnectFailed', defaultMessage: '流媒体服务连接失败', })
        } as const;

        showErrorMessage(intl, rs, fallbackMessageMap);
      }
    }
  };

  /**
   * 添加带超时的轮询函数
   * @param deviceId
   * @param pageNo
   * @param pageSize
   * @param timeout
   * @returns
   */
  const fetchChannelInfoWithTimeout = async (
    deviceId: string,
    pageNo: number,
    pageSize: number,
    timeout: number,
    values: Object
  ) => {
    return new Promise((resolve) => {
      let timer: NodeJS.Timeout | null = null;
      let interval: NodeJS.Timeout | null = null;

      // 定义超时行为
      timer = setTimeout(() => {
        if (interval) clearInterval(interval);
        resolve(null); // 超时返回 null
      }, timeout);

      // 定义轮询行为
      interval = setInterval(async () => {
        const rs = await handleChannelInfo(deviceId, pageNo, pageSize, values);
        if (rs && rs?.list.length > 0) {
          if (timer) clearTimeout(timer);
          if (interval) clearInterval(interval);
          resolve(rs); // 如果数据有值，则停止轮询并返回数据
        }
      }, 2000); // 每 2 秒轮询一次
    });
  };

  /**
   * 查询通道信息
   * @param deviceId
   * @param pageNo
   * @param pageSize
   * @returns
   */
  const handleChannelInfo = async (deviceId: string, pageNo: number, pageSize: number, values: Object) => {
    const rs = await getChannelInfo({
      deviceId: deviceId,
      channelNumber: 'ASC',
      pageNo: pageNo,
      pageSize: pageSize,
    });

    if (rs.code === 0 && rs.data) {
      console.log("rs?.data?.list.length ->", rs?.data?.list)
      if (rs?.data?.list.length == 1) {
        rs.data.list[0].name = values.deviceName;
      }
      // else {
      //   for (let i = 0; i < rs?.data?.list.length; i++) {
      //     // rs.data.list[i].name = values.name + "-" + (i + 1)
      //   }
      // }
      setChannelData(rs?.data?.list || []);
      setTotal(rs.data.total || 0); // 更新总数
      return rs.data;
    } else {
      message.error(rs.code);
    }
    return null;
  };

  // 在组件内顶部添加 showErrorMessage 工具函数
  function showErrorMessage(intl: any, rs: { code: string | number; msg?: string }, fallbackMessageMap: Record<string, string>) {
    const codeStr = String(rs.code);
    message.error(
      intl.formatMessage({
        id: 'pages.serverCode.' + codeStr,
        defaultMessage:
          fallbackMessageMap[rs.msg as keyof typeof fallbackMessageMap] ||
          rs.msg ||
          intl.formatMessage({ id: 'pages.pointManage.serviceException', defaultMessage: '服务异常，请稍后重试' }),
      }),
    );
  }



  useEffect(() => {
    setProtocol("ONVIF")
    form.resetFields()
  }, [props.visiable])

  return (
    <>
      <Modal
        style={{ minWidth: '200px', }}
        destroyOnClose
        title={intl.formatMessage({ id: 'pages.pointManage.pointManagement', defaultMessage: '点位管理', })}
        open={props.visiable}
        onCancel={() => { props.setManualEditVisiable(false); setCurrent(0); }}
        maskClosable={false}
        afterClose={() => { form.resetFields() }}
        footer={[
          current > 0 && (
            <Button key="back" onClick={handleBackImport}>
              {intl.formatMessage({
                id: 'pages.pointManage.prevStep',
                defaultMessage: '上一步',
              })}
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={handleNextImport}
            loading={loading}
          >
            {intl.formatMessage({
              id: 'pages.pointManage.nextStep',
              defaultMessage: '下一步',
            })}
          </Button>,
        ].filter(Boolean)}
      >
        <div>
          <Steps type="navigation" current={current} items={items} size="small" />
          <div style={contentStyle}>{steps[current].content}</div>
        </div>
      </Modal >


    </>
  )
}
export default ManualEditDevice
