#operate a {
  margin: 0;
  padding: 0 10px 0 0;
}

.img_del {
  width: 24px;
  content: url(/icons/pointManage/img_del.svg);
}

.img_del:hover {
  width: 24px;
  content: url(/icons/pointManage/img_del_hover.svg);
}

.img_edit {
  width: 24px;
  content: url(/icons/pointManage/img_edit.svg);
}

.img_edit:hover {
  width: 24px;
  content: url(/icons/pointManage/img_edit_hover.svg);
}

.img_preview {
  width: 24px;
  content: url(/icons/pointManage/img_preview.svg);
}

.img_preview:hover {
  width: 24px;
  content: url(/icons/pointManage/img_preview_hover.svg);
}

.search-Image .ant-image {
  display: block !important;
}

.eventRecord-modal .ant-modal-close {
  top: 5px;
}

.eventRecord-modal .ant-modal-content {
  padding: 0 !important;
  border-radius: 4px;
}

.event-detail-container {
  display: flex;
  gap: 20px;
  height: 100%;
}

.event-image-wrapper {
  flex: 2;
  /* 三分之二宽度 */
  border-radius: 4px;
  overflow: hidden;
}

.event-info {
  flex: 1;
  /* 三分之一宽度 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  font-size: 14px;
  color: #1d2129;
  line-height: 24px;
}

.event-info-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #52c41a;
}

.event-info-item {
  margin-bottom: 6px;
}