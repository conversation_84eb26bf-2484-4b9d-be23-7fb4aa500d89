package com.zkteco.mars.usc.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkteco.framework.base.annotation.Column;
import com.zkteco.framework.base.annotation.From;
import com.zkteco.framework.base.annotation.OrderBy;
import com.zkteco.framework.base.bean.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 布控规则 item
 * <AUTHOR>
 * @date  2025-04-09 16:20
 * @since 1.0.0
 */
@From(after = "CONTROL_RULES t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
public class ControlRulesItem extends BaseItem implements Serializable {


    /**
     * id
     */
    @Column(name = "t.ID")
    private String id;


    /**
     * 事件编码
     */
    @Column(name = "t.event_code")
    private String eventCode;

    /**
     * 事件类型
     */
    @Column(name = "t.event_type")
    private String eventType;

    /**
     * 提示词
     */
    @Column(name = "t.prompt")
    private String prompt;


    /**
     * 创建时间
     */
    @Column(name = "t.CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;




}
