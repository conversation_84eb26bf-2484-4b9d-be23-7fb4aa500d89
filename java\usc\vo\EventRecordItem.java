package com.zkteco.mars.usc.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkteco.framework.base.annotation.Column;
import com.zkteco.framework.base.annotation.Condition;
import com.zkteco.framework.base.annotation.From;
import com.zkteco.framework.base.annotation.OrderBy;
import com.zkteco.framework.base.bean.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 事件记录
 * <AUTHOR>
 * @date  2025-04-18 10:21
 * @since 1.0.0
 */
@From(after = "EVENT_RECORD t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
public class EventRecordItem extends BaseItem implements Serializable {


    /**
     * id
     */
    @Column(name = "t.ID")
    private String id;


    /**
     * 事件编码
     */
    @Column(name = "t.event_code")
    private String eventCode;

    /**
     * 事件类型
     */
    @Column(name = "t.event_type")
    private String eventType;

    /**
     * 提示词
     */
    @Column(name = "t.prompt")
    private String prompt;

    /**
     * 点位id
     */
    @Column(name = "t.point_id")
    private String pointId;

    /**
     * 点位名称
     */
    @Column(name = "t.point_name")
    private String pointName;


    /**
     * 文件名称
     */
    @Column(name = "t.file_name")
    private String fileName;


    /**
     * 事件时间
     */
    @Column(name = "t.EVENT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime eventTime;

    @Condition(value = "t.EVENT_TIME", equalTag = ">=")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private LocalDateTime endTime;






}
