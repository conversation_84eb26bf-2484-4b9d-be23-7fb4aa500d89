import { getEventTypeList } from "@/services/ant-design-pro/api";
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { connect, useIntl, } from 'umi';
import './index.less'


const EventType: React.FC = (props) => {
  const intl = useIntl();

  /** 表格的引用 */
  const actionRef = useRef<ActionType>();

  /**时间类型数据 */
  const [eventTypeData, setEventTypeData] = useState<any[]>([])

  /** 表单ref */
  const eventTypeRef = useRef()

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    eventType: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })



  /**
   * 分页
   */
  const handlePageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {
    console.log('page change', pagination, filters, sorter, extra)
    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadListInfo({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('search ', params)
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      eventType: params.eventType,
    })
    loadListInfo({
      current: 1,
      eventType: params.eventType ? params.eventType : 'no&input',
    })
  }


  const columns: any = [
    {
      title: intl.formatMessage({ id: 'pages.config.eventCode', defaultMessage: '事件编码', }),
      dataIndex: 'eventCode',
      hideInSearch: true,
      ellipsis: true,
      width: 20,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', }),
      dataIndex: 'eventType',
      width: 120,
      hideInSearch: false,
      ellipsis: true,
    },
  ];



  /**
   * 加载列表信息
   * @param page 
   */
  const loadListInfo = async (page: any) => {
    console.log('load list', eventTypeRef.current, eventTypeRef.current?.getFieldsValue())
    const searchValue = eventTypeRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let eventType = page.eventType ? page.eventType : searchValue.eventType
    if (page.eventType === 'no&input') {
      eventType = undefined
    }

    const rs = await getEventTypeList({ eventType: eventType }, currentPage, pageSize);
    if (rs.code === 0 && rs.data && rs.data?.data.length >= 0) {
      setEventTypeData(rs.data?.data)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
    }



  }

  useEffect(() => {
    //  loadListInfo({});
  }, [])

  return (
    <>
      <ProTable
        headerTitle={intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', })}
        actionRef={actionRef}
        formRef={eventTypeRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          total: searchParams.total,
        }}
        onChange={handlePageChange}
        beforeSearchSubmit={handleBeforeSearchDevice}
        columns={columns}
        tableAlertRender={false}
        options={{
          density: false,
          setting: false,
        }}
        dataSource={eventTypeData}
      />
    </>

  );
};

export default connect()(EventType);
