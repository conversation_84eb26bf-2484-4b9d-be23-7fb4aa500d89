package com.zkteco.mars.usc.dao;

import com.zkteco.framework.dao.BaseDao;
import com.zkteco.mars.usc.model.ControlRules;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date  2024-12-31 16:09
 * @since 1.0.0
 */
public interface ControlRulesDao  extends BaseDao<ControlRules, String> {

    @Query("SELECT DISTINCT c.eventCode FROM ControlRules c")
    List<String> findUsedEventCodes();
}
