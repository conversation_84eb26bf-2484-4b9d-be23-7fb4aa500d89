"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[27],{51042:function(q,R,a){var E=a(1413),d=a(67294),O=a(42110),i=a(91146),_=function(p,S){return d.createElement(i.Z,(0,E.Z)((0,E.Z)({},p),{},{ref:S,icon:O.Z}))},h=d.forwardRef(_);R.Z=h},5966:function(q,R,a){var E=a(97685),d=a(1413),O=a(45987),i=a(21770),_=a(53025),h=a(55241),w=a(97435),p=a(67294),S=a(27577),v=a(85893),Y=["fieldProps","proFieldProps"],G=["fieldProps","proFieldProps"],A="text",z=function(s){var e=s.fieldProps,x=s.proFieldProps,P=(0,O.Z)(s,Y);return(0,v.jsx)(S.Z,(0,d.Z)({valueType:A,fieldProps:e,filedConfig:{valueType:A},proFieldProps:x},P))},T=function(s){var e=(0,i.Z)(s.open||!1,{value:s.open,onChange:s.onOpenChange}),x=(0,E.Z)(e,2),P=x[0],L=x[1];return(0,v.jsx)(_.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(Z){var y,D=Z.getFieldValue(s.name||[]);return(0,v.jsx)(h.Z,(0,d.Z)((0,d.Z)({getPopupContainer:function(o){return o&&o.parentNode?o.parentNode:o},onOpenChange:function(o){return L(o)},content:(0,v.jsxs)("div",{style:{padding:"4px 0"},children:[(y=s.statusRender)===null||y===void 0?void 0:y.call(s,D),s.strengthText?(0,v.jsx)("div",{style:{marginTop:10},children:(0,v.jsx)("span",{children:s.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},s.popoverProps),{},{open:P,children:s.children}))}})},J=function(s){var e=s.fieldProps,x=s.proFieldProps,P=(0,O.Z)(s,G),L=(0,p.useState)(!1),n=(0,E.Z)(L,2),Z=n[0],y=n[1];return e!=null&&e.statusRender&&P.name?(0,v.jsx)(T,{name:P.name,statusRender:e==null?void 0:e.statusRender,popoverProps:e==null?void 0:e.popoverProps,strengthText:e==null?void 0:e.strengthText,open:Z,onOpenChange:y,children:(0,v.jsx)("div",{children:(0,v.jsx)(S.Z,(0,d.Z)({valueType:"password",fieldProps:(0,d.Z)((0,d.Z)({},(0,w.Z)(e,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(f){var o;e==null||(o=e.onBlur)===null||o===void 0||o.call(e,f),y(!1)},onClick:function(f){var o;e==null||(o=e.onClick)===null||o===void 0||o.call(e,f),y(!0)}}),proFieldProps:x,filedConfig:{valueType:A}},P))})}):(0,v.jsx)(S.Z,(0,d.Z)({valueType:"password",fieldProps:e,proFieldProps:x,filedConfig:{valueType:A}},P))},F=z;F.Password=J,F.displayName="ProFormComponent",R.Z=F},6609:function(q,R,a){a.r(R),a.d(R,{default:function(){return P}});var E=a(97857),d=a.n(E),O=a(15009),i=a.n(O),_=a(99289),h=a.n(_),w=a(5574),p=a.n(w),S=a(2618),v=a(51042),Y=a(53199),G=a(34994),A=a(5966),z=a(17788),T=a(2453),J=a(28036),F=a(42075),c=a(67294),s=a(71551),e=a(85893),x=function(){var n=(0,s.useIntl)(),Z=(0,c.useRef)(),y=(0,c.useState)({name:"",pageSize:10,pageNo:1,total:0}),D=p()(y,2),f=D[0],o=D[1],le=(0,c.useState)([]),ee=p()(le,2),de=ee[0],ue=ee[1],fe=(0,c.useState)(),ae=p()(fe,2),U=ae[0],Q=ae[1],ce=(0,c.useState)([]),te=p()(ce,2),Ae=te[0],$=te[1],ge=(0,c.useState)(!1),ne=p()(ge,2),pe=ne[0],N=ne[1],ve=(0,c.useState)(!1),re=p()(ve,2),X=re[0],H=re[1],me=(0,c.useState)(!1),se=p()(me,2),he=se[0],ie=se[1],Me=(0,c.useState)(n.formatMessage({id:"pages.config.createApiKey",defaultMessage:"\u521B\u5EFAAPI KEY"})),oe=p()(Me,2),Pe=oe[0],b=oe[1],Se=(0,c.useRef)(),xe=function(r,t){return(0,e.jsxs)("div",{id:"operate",children:[(0,e.jsx)("a",{onClick:function(){return Ce(t)},children:(0,e.jsx)("img",{className:"img_edit",title:n.formatMessage({id:"pages.pointManage.edit",defaultMessage:"\u7F16\u8F91"})})}),(0,e.jsx)("a",{onClick:function(){return Te([t])},children:(0,e.jsx)("img",{className:"img_del",title:n.formatMessage({id:"pages.pointManage.delete",defaultMessage:"\u5220\u9664"})})})]})},ye=[{title:n.formatMessage({id:"pages.config.name",defaultMessage:"\u540D\u79F0"}),dataIndex:"name",hideInSearch:!1,width:200,ellipsis:!0},{title:"key",dataIndex:"key",width:400,hideInSearch:!0,ellipsis:!0},{title:n.formatMessage({id:"pages.config.creationDate",defaultMessage:"\u521B\u5EFA\u65E5\u671F"}),dataIndex:"createTime",hideInSearch:!0,valueType:"dateTime",ellipsis:!0},{title:n.formatMessage({id:"pages.config.expirationTime",defaultMessage:"\u8FC7\u671F\u65F6\u95F4"}),dataIndex:"expirationTime",hideInSearch:!0,ellipsis:!0},{title:n.formatMessage({id:"pages.pointManage.operation",defaultMessage:"\u64CD\u4F5C"}),dataIndex:"option",valueType:"option",render:xe}],Ce=function(){var u=h()(i()().mark(function r(t){return i()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:H(!0),b(n.formatMessage({id:"pages.config.editApiKey",defaultMessage:"\u7F16\u8F91API KEY"})),Q(t),N(!0);case 4:case"end":return l.stop()}},r)}));return function(t){return u.apply(this,arguments)}}(),Te=function(){var u=h()(i()().mark(function r(t){return i()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:z.Z.confirm({title:n.formatMessage({id:"pages.pointManage.deleteRecord",defaultMessage:"\u5220\u9664\u8BB0\u5F55"}),content:n.formatMessage({id:"pages.pointManage.confirmDeleteRecord",defaultMessage:"\u786E\u5B9A\u5220\u9664\u8BB0\u5F55\u5417\uFF1F"}),okText:n.formatMessage({id:"pages.pointManage.confirm",defaultMessage:"\u786E\u8BA4"}),cancelText:n.formatMessage({id:"pages.pointManage.cancel",defaultMessage:"\u53D6\u6D88"}),onOk:function(){var m=h()(i()().mark(function C(){var I,M;return i()().wrap(function(K){for(;;)switch(K.prev=K.next){case 0:return I=T.ZP.loading(n.formatMessage({id:"pages.pointManage.deleting",defaultMessage:"\u6B63\u5728\u5220\u9664"})),K.next=3,(0,S.XW)(t);case 3:M=K.sent,M.code===0?T.ZP.success(n.formatMessage({id:"pages.pointManage.deleteSuccess",defaultMessage:"\u5220\u9664\u6210\u529F\uFF0C\u81EA\u52A8\u5237\u65B0"})):T.ZP.error(n.formatMessage({id:"pages.pointManage.deleteFailure",defaultMessage:"\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"})),$([]),B({}),I();case 8:case"end":return K.stop()}},C)}));function j(){return m.apply(this,arguments)}return j}()});case 1:case"end":return l.stop()}},r)}));return function(t){return u.apply(this,arguments)}}(),Ee=function(){var u=h()(i()().mark(function r(t){var g;return i()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:return X&&(t.id=U.id),m.next=3,(0,S.Wo)(t);case 3:g=m.sent,g.code===0?T.ZP.success(X?n.formatMessage({id:"pages.config.editSuccess",defaultMessage:"\u7F16\u8F91\u6210\u529F\uFF0C\u81EA\u52A8\u5237\u65B0"}):n.formatMessage({id:"pages.config.createSuccess",defaultMessage:"\u521B\u5EFA\u6210\u529F\uFF0C\u81EA\u52A8\u5237\u65B0"})):T.ZP.error(X?n.formatMessage({id:"pages.config.editFailure",defaultMessage:"\u7F16\u8F91\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"}):n.formatMessage({id:"pages.config.createFailure",defaultMessage:"\u521B\u5EFA\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"})),N(!1),Q({}),$([]),H(!1),b(n.formatMessage({id:"pages.config.createApiKey",defaultMessage:"\u521B\u5EFAAPI KEY"})),B({});case 11:case"end":return m.stop()}},r)}));return function(t){return u.apply(this,arguments)}}(),Re=function(){var u=h()(i()().mark(function r(t,g,l,m){return i()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:o(d()(d()({},f),{},{pageSize:t.pageSize,pageNo:t.current})),B({current:t.current,size:t.pageSize});case 2:case"end":return C.stop()}},r)}));return function(t,g,l,m){return u.apply(this,arguments)}}(),B=function(){var u=h()(i()().mark(function r(t){var g,l,m,j,C,I,M,V;return i()().wrap(function(W){for(;;)switch(W.prev=W.next){case 0:return ie(!0),m=(g=Z.current)===null||g===void 0?void 0:g.getFieldsValue(),j=t.current?t.current:f.pageNo,C=t.size?t.size:f.pageSize,I=t.name?t.name:m.name,t.name==="no&input"&&(I=void 0),W.next=8,(0,S.bn)({name:I},j,C);case 8:M=W.sent,ie(!1),M.code===0&&M.data&&((l=M.data)===null||l===void 0?void 0:l.data.length)>=0?(ue((V=M.data)===null||V===void 0?void 0:V.data),o(d()(d()({},f),{},{pageSize:C,pageNo:j,total:M.data.total}))):M.code!==0&&T.ZP.error(M.msg);case 11:case"end":return W.stop()}},r)}));return function(t){return u.apply(this,arguments)}}(),Oe=function(){var u=h()(i()().mark(function r(t){return i()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:o(d()(d()({},f),{},{pageNo:1,name:t.name})),B({current:1,name:t.name?t.name:"no&input"});case 2:case"end":return l.stop()}},r)}));return function(t){return u.apply(this,arguments)}}(),k=function(){N(!1),Q({}),$([]),H(!1),b(n.formatMessage({id:"pages.config.createApiKey",defaultMessage:"\u521B\u5EFAAPI KEY"}))};return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(Y.Z,{rowKey:"id",headerTitle:n.formatMessage({id:"pages.config.authManagement",defaultMessage:"\u6388\u6743\u7BA1\u7406"}),formRef:Z,pagination:{current:f.pageNo,pageSize:f.pageSize,showQuickJumper:!0,showSizeChanger:!0,showPrevNextJumpers:!0,showTitle:!0,pageSizeOptions:["10","20","50","100"],total:f.total},onChange:Re,rowSelection:{onChange:function(r,t){$(t)}},beforeSearchSubmit:Oe,columns:ye,loading:he,tableAlertRender:!1,options:{density:!1,setting:!1,reload:B},dataSource:de,toolBarRender:function(){return[(0,e.jsxs)(J.ZP,{type:"primary",onClick:function(){N(!0)},children:[(0,e.jsx)(v.Z,{}),n.formatMessage({id:"pages.config.createApiKey",defaultMessage:"\u521B\u5EFAAPI KEY"})]},"primary")]}}),(0,e.jsx)(z.Z,{width:450,footer:null,destroyOnClose:!0,styles:{body:{padding:"0px",height:"150px",overflow:"hidden"}},title:Pe,open:pe,onOk:k,onCancel:k,children:(0,e.jsx)(G.A,{formRef:Se,layout:"horizontal",style:{width:"100%",height:"100%",display:"flex",flexDirection:"column",justifyContent:"space-between"},labelAlign:"left",labelCol:{span:6},wrapperCol:{span:16},onFinish:Ee,submitter:{searchConfig:{submitText:n.formatMessage({id:"pages.pointManage.confirm",defaultMessage:"\u786E\u8BA4"}),resetText:n.formatMessage({id:"pages.pointManage.cancel",defaultMessage:"\u53D6\u6D88"})},onReset:k,render:function(r,t){return(0,e.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",height:"100%"},children:(0,e.jsxs)(F.Z,{children:[t[0]," ",t[1]," "]})})}},children:(0,e.jsx)(A.Z,{width:"md",name:"name",initialValue:U==null?void 0:U.name,rules:[{required:!0}],label:n.formatMessage({id:"pages.config.name",defaultMessage:"\u540D\u79F0"}),placeholder:n.formatMessage({id:"pages.config.name",defaultMessage:"\u540D\u79F0"})})})})]})},P=x}}]);
