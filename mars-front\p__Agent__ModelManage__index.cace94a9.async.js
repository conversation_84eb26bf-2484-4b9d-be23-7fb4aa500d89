"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[270],{2236:function(st,Ce,r){r.d(Ce,{S:function(){return De}});var b=r(1413),T=r(4942),$e=r(71002),ce=r(45987),Re=r(12044),_=r(21532),He=r(93967),ae=r.n(He),Te=r(97435),ne=r(67294),We=r(73935),Ae=r(76509),ve=r(98082),le=function(I){return(0,T.Z)({},I.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,ve.uK)(I.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(I.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:I.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:I.colorText},"&-right":{color:I.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function Ve(V){return(0,ve.Xj)("ProLayoutFooterToolbar",function(I){var J=(0,b.Z)((0,b.Z)({},I),{},{componentCls:".".concat(V)});return[le(J)]})}function K(V,I){var J=I.stylish;return(0,ve.Xj)("ProLayoutFooterToolbarStylish",function(he){var Ie=(0,b.Z)((0,b.Z)({},he),{},{componentCls:".".concat(V)});return J?[(0,T.Z)({},"".concat(Ie.componentCls),J==null?void 0:J(Ie))]:[]})}var U=r(85893),s=["children","className","extra","portalDom","style","renderContent"],De=function(I){var J=I.children,he=I.className,Ie=I.extra,Fe=I.portalDom,je=Fe===void 0?!0:Fe,Xe=I.style,Le=I.renderContent,_e=(0,ce.Z)(I,s),ye=(0,ne.useContext)(_.ZP.ConfigContext),et=ye.getPrefixCls,t=ye.getTargetContainer,x=I.prefixCls||et("pro"),h="".concat(x,"-footer-bar"),i=Ve(h),y=i.wrapSSR,L=i.hashId,f=(0,ne.useContext)(Ae.X),C=(0,ne.useMemo)(function(){var se=f.hasSiderMenu,Pe=f.isMobile,D=f.siderWidth;if(se)return D?Pe?"100%":"calc(100% - ".concat(D,"px)"):"100%"},[f.collapsed,f.hasSiderMenu,f.isMobile,f.siderWidth]),Q=(0,ne.useMemo)(function(){return(typeof window=="undefined"?"undefined":(0,$e.Z)(window))===void 0||(typeof document=="undefined"?"undefined":(0,$e.Z)(document))===void 0?null:(t==null?void 0:t())||document.body},[]),xe=K("".concat(h,".").concat(h,"-stylish"),{stylish:I.stylish}),Y=(0,U.jsxs)(U.Fragment,{children:[(0,U.jsx)("div",{className:"".concat(h,"-left ").concat(L).trim(),children:Ie}),(0,U.jsx)("div",{className:"".concat(h,"-right ").concat(L).trim(),children:J})]});(0,ne.useEffect)(function(){return!f||!(f!=null&&f.setHasFooterToolbar)?function(){}:(f==null||f.setHasFooterToolbar(!0),function(){var se;f==null||(se=f.setHasFooterToolbar)===null||se===void 0||se.call(f,!1)})},[]);var ue=(0,U.jsx)("div",(0,b.Z)((0,b.Z)({className:ae()(he,L,h,(0,T.Z)({},"".concat(h,"-stylish"),!!I.stylish)),style:(0,b.Z)({width:C},Xe)},(0,Te.Z)(_e,["prefixCls"])),{},{children:Le?Le((0,b.Z)((0,b.Z)((0,b.Z)({},I),f),{},{leftWidth:C}),Y):Y})),re=!(0,Re.j)()||!je||!Q?ue:(0,We.createPortal)(ue,Q,h);return xe.wrapSSR(y((0,U.jsx)(ne.Fragment,{children:re},h)))}},76509:function(st,Ce,r){r.d(Ce,{X:function(){return T}});var b=r(67294),T=(0,b.createContext)({})},77895:function(st,Ce,r){r.r(Ce),r.d(Ce,{default:function(){return dt}});var b=r(15009),T=r.n(b),$e=r(97857),ce=r.n($e),Re=r(99289),_=r.n(Re),He=r(5574),ae=r.n(He),Te=r(2618),ne=r(53199),We=r(2236),Ae=r(17788),ve=r(2453),le=r(66309),Ve=r(26915),K=r(34041),U=r(74902),s=r(67294),De=r(93967),V=r.n(De),I=r(38780),J=r(74443),he=r(53124),Ie=r(88258),Fe=r(98675),je=r(92820),Xe=r(25378),Le=r(78818),_e=r(74330);const ye=s.createContext({}),et=ye.Consumer;var t=r(96159),x=r(21584),h=function(l,a){var e={};for(var d in l)Object.prototype.hasOwnProperty.call(l,d)&&a.indexOf(d)<0&&(e[d]=l[d]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,d=Object.getOwnPropertySymbols(l);m<d.length;m++)a.indexOf(d[m])<0&&Object.prototype.propertyIsEnumerable.call(l,d[m])&&(e[d[m]]=l[d[m]]);return e};const i=l=>{var{prefixCls:a,className:e,avatar:d,title:m,description:j}=l,E=h(l,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:P}=(0,s.useContext)(he.E_),N=P("list",a),O=V()(`${N}-item-meta`,e),W=s.createElement("div",{className:`${N}-item-meta-content`},m&&s.createElement("h4",{className:`${N}-item-meta-title`},m),j&&s.createElement("div",{className:`${N}-item-meta-description`},j));return s.createElement("div",Object.assign({},E,{className:O}),d&&s.createElement("div",{className:`${N}-item-meta-avatar`},d),(m||j)&&W)},L=s.forwardRef((l,a)=>{const{prefixCls:e,children:d,actions:m,extra:j,styles:E,className:P,classNames:N,colStyle:O}=l,W=h(l,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:G,itemLayout:z}=(0,s.useContext)(ye),{getPrefixCls:oe,list:Z}=(0,s.useContext)(he.E_),ee=B=>{var R,ie;return V()((ie=(R=Z==null?void 0:Z.item)===null||R===void 0?void 0:R.classNames)===null||ie===void 0?void 0:ie[B],N==null?void 0:N[B])},ge=B=>{var R,ie;return Object.assign(Object.assign({},(ie=(R=Z==null?void 0:Z.item)===null||R===void 0?void 0:R.styles)===null||ie===void 0?void 0:ie[B]),E==null?void 0:E[B])},Ee=()=>{let B=!1;return s.Children.forEach(d,R=>{typeof R=="string"&&(B=!0)}),B&&s.Children.count(d)>1},fe=()=>z==="vertical"?!!j:!Ee(),A=oe("list",e),me=m&&m.length>0&&s.createElement("ul",{className:V()(`${A}-item-action`,ee("actions")),key:"actions",style:ge("actions")},m.map((B,R)=>s.createElement("li",{key:`${A}-item-action-${R}`},B,R!==m.length-1&&s.createElement("em",{className:`${A}-item-action-split`})))),Ne=G?"div":"li",Me=s.createElement(Ne,Object.assign({},W,G?{}:{ref:a},{className:V()(`${A}-item`,{[`${A}-item-no-flex`]:!fe()},P)}),z==="vertical"&&j?[s.createElement("div",{className:`${A}-item-main`,key:"content"},d,me),s.createElement("div",{className:V()(`${A}-item-extra`,ee("extra")),key:"extra",style:ge("extra")},j)]:[d,me,(0,t.Tm)(j,{key:"extra"})]);return G?s.createElement(x.Z,{ref:a,flex:1,style:O},Me):Me});L.Meta=i;var f=L,C=r(11568),Q=r(14747),xe=r(83559),Y=r(83262);const ue=l=>{const{listBorderedCls:a,componentCls:e,paddingLG:d,margin:m,itemPaddingSM:j,itemPaddingLG:E,marginLG:P,borderRadiusLG:N}=l;return{[a]:{border:`${(0,C.bf)(l.lineWidth)} ${l.lineType} ${l.colorBorder}`,borderRadius:N,[`${e}-header,${e}-footer,${e}-item`]:{paddingInline:d},[`${e}-pagination`]:{margin:`${(0,C.bf)(m)} ${(0,C.bf)(P)}`}},[`${a}${e}-sm`]:{[`${e}-item,${e}-header,${e}-footer`]:{padding:j}},[`${a}${e}-lg`]:{[`${e}-item,${e}-header,${e}-footer`]:{padding:E}}}},re=l=>{const{componentCls:a,screenSM:e,screenMD:d,marginLG:m,marginSM:j,margin:E}=l;return{[`@media screen and (max-width:${d}px)`]:{[a]:{[`${a}-item`]:{[`${a}-item-action`]:{marginInlineStart:m}}},[`${a}-vertical`]:{[`${a}-item`]:{[`${a}-item-extra`]:{marginInlineStart:m}}}},[`@media screen and (max-width: ${e}px)`]:{[a]:{[`${a}-item`]:{flexWrap:"wrap",[`${a}-action`]:{marginInlineStart:j}}},[`${a}-vertical`]:{[`${a}-item`]:{flexWrap:"wrap-reverse",[`${a}-item-main`]:{minWidth:l.contentWidth},[`${a}-item-extra`]:{margin:`auto auto ${(0,C.bf)(E)}`}}}}}},se=l=>{const{componentCls:a,antCls:e,controlHeight:d,minHeight:m,paddingSM:j,marginLG:E,padding:P,itemPadding:N,colorPrimary:O,itemPaddingSM:W,itemPaddingLG:G,paddingXS:z,margin:oe,colorText:Z,colorTextDescription:ee,motionDurationSlow:ge,lineWidth:Ee,headerBg:fe,footerBg:A,emptyTextPadding:me,metaMarginBottom:Ne,avatarMarginRight:Me,titleMarginBottom:B,descriptionFontSize:R}=l;return{[a]:Object.assign(Object.assign({},(0,Q.Wf)(l)),{position:"relative","*":{outline:"none"},[`${a}-header`]:{background:fe},[`${a}-footer`]:{background:A},[`${a}-header, ${a}-footer`]:{paddingBlock:j},[`${a}-pagination`]:{marginBlockStart:E,[`${e}-pagination-options`]:{textAlign:"start"}},[`${a}-spin`]:{minHeight:m,textAlign:"center"},[`${a}-items`]:{margin:0,padding:0,listStyle:"none"},[`${a}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:N,color:Z,[`${a}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${a}-item-meta-avatar`]:{marginInlineEnd:Me},[`${a}-item-meta-content`]:{flex:"1 0",width:0,color:Z},[`${a}-item-meta-title`]:{margin:`0 0 ${(0,C.bf)(l.marginXXS)} 0`,color:Z,fontSize:l.fontSize,lineHeight:l.lineHeight,"> a":{color:Z,transition:`all ${ge}`,"&:hover":{color:O}}},[`${a}-item-meta-description`]:{color:ee,fontSize:R,lineHeight:l.lineHeight}},[`${a}-item-action`]:{flex:"0 0 auto",marginInlineStart:l.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${(0,C.bf)(z)}`,color:ee,fontSize:l.fontSize,lineHeight:l.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${a}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:Ee,height:l.calc(l.fontHeight).sub(l.calc(l.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:l.colorSplit}}},[`${a}-empty`]:{padding:`${(0,C.bf)(P)} 0`,color:ee,fontSize:l.fontSizeSM,textAlign:"center"},[`${a}-empty-text`]:{padding:me,color:l.colorTextDisabled,fontSize:l.fontSize,textAlign:"center"},[`${a}-item-no-flex`]:{display:"block"}}),[`${a}-grid ${e}-col > ${a}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:oe,paddingBlock:0,borderBlockEnd:"none"},[`${a}-vertical ${a}-item`]:{alignItems:"initial",[`${a}-item-main`]:{display:"block",flex:1},[`${a}-item-extra`]:{marginInlineStart:E},[`${a}-item-meta`]:{marginBlockEnd:Ne,[`${a}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:B,color:Z,fontSize:l.fontSizeLG,lineHeight:l.lineHeightLG}},[`${a}-item-action`]:{marginBlockStart:P,marginInlineStart:"auto","> li":{padding:`0 ${(0,C.bf)(P)}`,"&:first-child":{paddingInlineStart:0}}}},[`${a}-split ${a}-item`]:{borderBlockEnd:`${(0,C.bf)(l.lineWidth)} ${l.lineType} ${l.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${a}-split ${a}-header`]:{borderBlockEnd:`${(0,C.bf)(l.lineWidth)} ${l.lineType} ${l.colorSplit}`},[`${a}-split${a}-empty ${a}-footer`]:{borderTop:`${(0,C.bf)(l.lineWidth)} ${l.lineType} ${l.colorSplit}`},[`${a}-loading ${a}-spin-nested-loading`]:{minHeight:d},[`${a}-split${a}-something-after-last-item ${e}-spin-container > ${a}-items > ${a}-item:last-child`]:{borderBlockEnd:`${(0,C.bf)(l.lineWidth)} ${l.lineType} ${l.colorSplit}`},[`${a}-lg ${a}-item`]:{padding:G},[`${a}-sm ${a}-item`]:{padding:W},[`${a}:not(${a}-vertical)`]:{[`${a}-item-no-flex`]:{[`${a}-item-action`]:{float:"right"}}}}},Pe=l=>({contentWidth:220,itemPadding:`${(0,C.bf)(l.paddingContentVertical)} 0`,itemPaddingSM:`${(0,C.bf)(l.paddingContentVerticalSM)} ${(0,C.bf)(l.paddingContentHorizontal)}`,itemPaddingLG:`${(0,C.bf)(l.paddingContentVerticalLG)} ${(0,C.bf)(l.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:l.padding,metaMarginBottom:l.padding,avatarMarginRight:l.padding,titleMarginBottom:l.paddingSM,descriptionFontSize:l.fontSize});var D=(0,xe.I$)("List",l=>{const a=(0,Y.IX)(l,{listBorderedCls:`${l.componentCls}-bordered`,minHeight:l.controlHeightLG});return[se(a),ue(a),re(a)]},Pe),Ge=function(l,a){var e={};for(var d in l)Object.prototype.hasOwnProperty.call(l,d)&&a.indexOf(d)<0&&(e[d]=l[d]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,d=Object.getOwnPropertySymbols(l);m<d.length;m++)a.indexOf(d[m])<0&&Object.prototype.propertyIsEnumerable.call(l,d[m])&&(e[d[m]]=l[d[m]]);return e};function Be(l,a){var{pagination:e=!1,prefixCls:d,bordered:m=!1,split:j=!0,className:E,rootClassName:P,style:N,children:O,itemLayout:W,loadMore:G,grid:z,dataSource:oe=[],size:Z,header:ee,footer:ge,loading:Ee=!1,rowKey:fe,renderItem:A,locale:me}=l,Ne=Ge(l,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]);const Me=e&&typeof e=="object"?e:{},[B,R]=s.useState(Me.defaultCurrent||1),[ie,ct]=s.useState(Me.defaultPageSize||10),{getPrefixCls:tt,renderEmpty:te,direction:Je,list:Se}=s.useContext(he.E_),ut={current:1,total:0},at=g=>(v,u)=>{var p;R(v),ct(u),e&&((p=e==null?void 0:e[g])===null||p===void 0||p.call(e,v,u))},gt=at("onChange"),nt=at("onShowSizeChange"),Oe=(g,v)=>{if(!A)return null;let u;return typeof fe=="function"?u=fe(g):fe?u=g[fe]:u=g.key,u||(u=`list-item-${v}`),s.createElement(s.Fragment,{key:u},A(g,v))},ft=()=>!!(G||e||ge),H=tt("list",d),[mt,pt,vt]=D(H);let pe=Ee;typeof pe=="boolean"&&(pe={spinning:pe});const Qe=!!(pe!=null&&pe.spinning),ht=(0,Fe.Z)(Z);let k="";switch(ht){case"large":k="lg";break;case"small":k="sm";break;default:break}const yt=V()(H,{[`${H}-vertical`]:W==="vertical",[`${H}-${k}`]:k,[`${H}-split`]:j,[`${H}-bordered`]:m,[`${H}-loading`]:Qe,[`${H}-grid`]:!!z,[`${H}-something-after-last-item`]:ft(),[`${H}-rtl`]:Je==="rtl"},Se==null?void 0:Se.className,E,P,pt,vt),q=(0,I.Z)(ut,{total:oe.length,current:B,pageSize:ie},e||{}),lt=Math.ceil(q.total/q.pageSize);q.current>lt&&(q.current=lt);const ze=e&&s.createElement("div",{className:V()(`${H}-pagination`)},s.createElement(Le.Z,Object.assign({align:"end"},q,{onChange:gt,onShowSizeChange:nt})));let Ye=(0,U.Z)(oe);e&&oe.length>(q.current-1)*q.pageSize&&(Ye=(0,U.Z)(oe).splice((q.current-1)*q.pageSize,q.pageSize));const F=Object.keys(z||{}).some(g=>["xs","sm","md","lg","xl","xxl"].includes(g)),M=(0,Xe.Z)(F),n=s.useMemo(()=>{for(let g=0;g<J.c4.length;g+=1){const v=J.c4[g];if(M[v])return v}},[M]),S=s.useMemo(()=>{if(!z)return;const g=n&&z[n]?z[n]:z.column;if(g)return{width:`${100/g}%`,maxWidth:`${100/g}%`}},[JSON.stringify(z),n]);let $=Qe&&s.createElement("div",{style:{minHeight:53}});if(Ye.length>0){const g=Ye.map((v,u)=>Oe(v,u));$=z?s.createElement(je.Z,{gutter:z.gutter},s.Children.map(g,v=>s.createElement("div",{key:v==null?void 0:v.key,style:S},v))):s.createElement("ul",{className:`${H}-items`},g)}else!O&&!Qe&&($=s.createElement("div",{className:`${H}-empty-text`},(me==null?void 0:me.emptyText)||(te==null?void 0:te("List"))||s.createElement(Ie.Z,{componentName:"List"})));const c=q.position||"bottom",w=s.useMemo(()=>({grid:z,itemLayout:W}),[JSON.stringify(z),W]);return mt(s.createElement(ye.Provider,{value:w},s.createElement("div",Object.assign({ref:a,style:Object.assign(Object.assign({},Se==null?void 0:Se.style),N),className:yt},Ne),(c==="top"||c==="both")&&ze,ee&&s.createElement("div",{className:`${H}-header`},ee),s.createElement(_e.Z,Object.assign({},pe),$,O),ge&&s.createElement("div",{className:`${H}-footer`},ge),G||(c==="bottom"||c==="both")&&ze)))}const Ke=s.forwardRef(Be);Ke.Item=f;var we=Ke,Ue=r(7134),ot=r(28036),X=r(71551),o=r(85893),it=function(a){var e=(0,X.useIntl)(),d=(0,s.useRef)(),m=(0,s.useState)(),j=ae()(m,2),E=j[0],P=j[1],N=(0,s.useRef)(),O=(0,s.useState)([]),W=ae()(O,2),G=W[0],z=W[1],oe=(0,s.useRef)(),Z=(0,s.useState)([]),ee=ae()(Z,2),ge=ee[0],Ee=ee[1],fe=(0,s.useState)([]),A=ae()(fe,2),me=A[0],Ne=A[1],Me=(0,s.useState)([]),B=ae()(Me,2),R=B[0],ie=B[1],ct=(0,s.useState)({name:"",pageSize:10,pageNo:1,total:0,type:"",feature:"",manufacturer:"",modelFunction:"",modelLength:"",modelRights:"",status:""}),tt=ae()(ct,2),te=tt[0],Je=tt[1],Se={1:e.formatMessage({id:"pages.agent.flagship",defaultMessage:"\u65D7\u8230"}),2:e.formatMessage({id:"pages.agent.highSpeed",defaultMessage:"\u9AD8\u901F"}),3:e.formatMessage({id:"pages.agent.toolInvocation",defaultMessage:"\u5DE5\u5177\u8C03\u7528"}),4:e.formatMessage({id:"pages.agent.rolePlay",defaultMessage:"\u89D2\u8272\u626E\u6F14"}),5:e.formatMessage({id:"pages.agent.longText",defaultMessage:"\u957F\u6587\u672C"}),6:e.formatMessage({id:"pages.agent.imageUnderstanding",defaultMessage:"\u56FE\u7247\u7406\u89E3"}),7:e.formatMessage({id:"pages.agent.reasoning",defaultMessage:"\u63A8\u7406\u80FD\u529B"}),8:e.formatMessage({id:"pages.agent.videoUnderstanding",defaultMessage:"\u89C6\u9891\u7406\u89E3"}),9:e.formatMessage({id:"pages.agent.costPerformance",defaultMessage:"\u6027\u4EF7\u6BD4"}),10:e.formatMessage({id:"pages.agent.codeExpert",defaultMessage:"\u4EE3\u7801\u4E13\u7CBE"}),11:e.formatMessage({id:"pages.agent.audioUnderstanding",defaultMessage:"\u97F3\u9891\u7406\u89E3"}),12:e.formatMessage({id:"pages.agent.visualAnalysis",defaultMessage:"\u89C6\u89C9\u5206\u6790"})},ut={1:e.formatMessage({id:"pages.agent.running",defaultMessage:"\u8FD0\u884C\u4E2D"}),2:e.formatMessage({id:"pages.agent.queuing",defaultMessage:"\u6392\u961F\u4E2D"}),3:e.formatMessage({id:"pages.agent.training",defaultMessage:"\u8BAD\u7EC3\u4E2D"}),4:e.formatMessage({id:"pages.agent.trainingFailed",defaultMessage:"\u8BAD\u7EC3\u5931\u8D25"})},at={1:e.formatMessage({id:"pages.agent.text",defaultMessage:"\u6587\u672C"}),2:e.formatMessage({id:"pages.agent.multimodal",defaultMessage:"\u591A\u6A21\u6001"}),3:e.formatMessage({id:"pages.agent.landongModel",defaultMessage:"\u61D2\u6D1E\u6A21\u578B"})},gt=(0,s.useState)([]),nt=ae()(gt,2),Oe=nt[0],ft=nt[1],H=(0,s.useRef)(),mt=function(M,n){return(0,o.jsx)("div",{id:"operate",children:(0,o.jsx)("a",{onClick:function(){return pe([n])},children:(0,o.jsx)("img",{className:"img_del",title:e.formatMessage({id:"pages.pointManage.delete",defaultMessage:"\u5220\u9664"})})})})},pt=function(){var F=_()(T()().mark(function M(n,S,$,c){return T()().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:console.log("page change",n,S,$,c),Je(ce()(ce()({},te),{},{pageSize:n.pageSize,pageNo:n.current})),ze({current:n.current,size:n.pageSize});case 3:case"end":return g.stop()}},M)}));return function(n,S,$,c){return F.apply(this,arguments)}}(),vt=function(){var F=_()(T()().mark(function M(n){return T()().wrap(function($){for(;;)switch($.prev=$.next){case 0:console.log("point search",n),Je(ce()(ce()({},te),{},{name:n.name,type:n.type,feature:n.feature,manufacturer:n.manufacturer,modelFunction:n.modelFunction,modelLength:n.modelLength,modelRights:n.modelRights,status:n.status})),ze({current:1,name:n.name?n.name:"no&input",type:n.type?n.type:"no&input",feature:n.feature?n.feature:"no&input",manufacturer:n.manufacturer?n.manufacturer:"no&input",modelFunction:n.modelFunction?n.modelFunction:"no&input",modelLength:n.modelLength?n.modelLength:"no&input",modelRights:n.modelRights?n.modelRights:"no&input",status:n.status?n.status:"no&input"});case 3:case"end":return $.stop()}},M)}));return function(n){return F.apply(this,arguments)}}(),pe=function(){var F=_()(T()().mark(function M(n){return T()().wrap(function($){for(;;)switch($.prev=$.next){case 0:console.log("del data:",n),Ae.Z.confirm({title:e.formatMessage({id:"pages.pointManage.deleteRecord",defaultMessage:"\u5220\u9664\u8BB0\u5F55"}),content:e.formatMessage({id:"pages.pointManage.confirmDeleteRecord",defaultMessage:"\u786E\u5B9A\u5220\u9664\u8BB0\u5F55\u5417\uFF1F"}),okText:e.formatMessage({id:"pages.pointManage.confirm",defaultMessage:"\u786E\u8BA4"}),cancelText:e.formatMessage({id:"pages.pointManage.cancel",defaultMessage:"\u53D6\u6D88"}),onOk:function(){var c=_()(T()().mark(function g(){var v,u,p,ke,Ze;return T()().wrap(function(be){for(;;)switch(be.prev=be.next){case 0:return v=ve.ZP.loading(e.formatMessage({id:"pages.pointManage.deleting",defaultMessage:"\u6B63\u5728\u5220\u9664"})),u=0,p=0,ke=[],be.next=6,(0,Te.GP)(n);case 6:Ze=be.sent,Ze.code===0?ve.ZP.success(e.formatMessage({id:"pages.pointManage.deleteSuccess",defaultMessage:"\u5220\u9664\u6210\u529F\uFF0C\u81EA\u52A8\u5237\u65B0"})):ve.ZP.error(e.formatMessage({id:"pages.pointManage.deleteFailure",defaultMessage:"\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"})),ft([]),ze({}),v();case 11:case"end":return be.stop()}},g)}));function w(){return c.apply(this,arguments)}return w}()});case 2:case"end":return $.stop()}},M)}));return function(n){return F.apply(this,arguments)}}(),Qe=function(M,n){var S=n.status,$=[{label:e.formatMessage({id:"pages.agent.running",defaultMessage:"\u8FD0\u884C\u4E2D"}),value:1},{label:e.formatMessage({id:"pages.agent.queuing",defaultMessage:"\u6392\u961F\u4E2D"}),value:2},{label:e.formatMessage({id:"pages.agent.training",defaultMessage:"\u8BAD\u7EC3\u4E2D"}),value:3},{label:e.formatMessage({id:"pages.agent.trainingFailed",defaultMessage:"\u8BAD\u7EC3\u5931\u8D25"}),value:4}];if(!S)return null;var c=typeof S=="string"?S.split(",").map(Number):[Number(S)],w=c.map(function(v){var u;return(u=$.find(function(p){return p.value===v}))===null||u===void 0?void 0:u.label}).filter(Boolean),g=w.slice(0,3);return(0,o.jsx)("div",{style:{whiteSpace:"pre-wrap"},children:g.map(function(v,u){return(0,o.jsx)(le.Z,{color:"rgb(11, 211, 87)",children:v},u)})})},ht=function(M,n){var S=n.type,$=[{label:e.formatMessage({id:"pages.agent.text",defaultMessage:"\u6587\u672C"}),value:1},{label:e.formatMessage({id:"pages.agent.multimodal",defaultMessage:"\u591A\u6A21\u6001"}),value:2},{label:e.formatMessage({id:"pages.agent.landongModel",defaultMessage:"\u61D2\u6D1E\u6A21\u578B"}),value:3}];if(!S)return null;var c=typeof S=="string"?S.split(",").map(Number):[Number(S)],w=c.map(function(v){var u;return(u=$.find(function(p){return p.value===v}))===null||u===void 0?void 0:u.label}).filter(Boolean),g=w.slice(0,3);return(0,o.jsx)("div",{style:{whiteSpace:"pre-wrap"},children:g.map(function(v,u){return(0,o.jsx)(le.Z,{color:"rgb(11, 211, 87)",children:v},u)})})},k=function(M,n){if(M.dataIndex=="name")return(0,o.jsx)(Ve.Z,{placeholder:e.formatMessage({id:"pages.agent.searchModelName",defaultMessage:"\u641C\u7D22\u6A21\u578B\u540D\u79F0"}),style:{width:200}});if(M.dataIndex=="type")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:120},allowClear:!0,options:[{value:1,label:e.formatMessage({id:"pages.agent.text",defaultMessage:"\u6587\u672C"})},{value:2,label:e.formatMessage({id:"pages.agent.multimodal",defaultMessage:"\u591A\u6A21\u6001"})},{value:3,label:e.formatMessage({id:"pages.agent.landongModel",defaultMessage:"\u61D2\u6D1E\u6A21\u578B"})}]});if(M.dataIndex=="feature")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:ge});if(M.dataIndex=="manufacturer")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:me});if(M.dataIndex=="modelFunction")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:R});if(M.dataIndex=="modelLength")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:[{label:"0",value:"0"},{label:"32K",value:"32K"},{label:"64K",value:"64K"},{label:"max",value:"max"}]});if(M.dataIndex=="modelRights")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:[{label:e.formatMessage({id:"pages.agent.quotaTrial",defaultMessage:"\u9650\u989D\u4F53\u9A8C"}),value:e.formatMessage({id:"pages.agent.quotaTrial",defaultMessage:"\u9650\u989D\u4F53\u9A8C"})},{label:e.formatMessage({id:"pages.agent.comingOffline",defaultMessage:"\u5373\u5C06\u4E0B\u7EBF"}),value:e.formatMessage({id:"pages.agent.comingOffline",defaultMessage:"\u5373\u5C06\u4E0B\u7EBF"})},{label:e.formatMessage({id:"pages.agent.newModelExperience",defaultMessage:"\u65B0\u6A21\u578B\u4F53\u9A8C"}),value:e.formatMessage({id:"pages.agent.newModelExperience",defaultMessage:"\u65B0\u6A21\u578B\u4F53\u9A8C"})},{label:e.formatMessage({id:"pages.agent.advancedModel",defaultMessage:"\u9AD8\u7EA7\u6A21\u578B"}),value:e.formatMessage({id:"pages.agent.advancedModel",defaultMessage:"\u9AD8\u7EA7\u6A21\u578B"})},{label:e.formatMessage({id:"pages.agent.generalModel",defaultMessage:"\u901A\u7528\u6A21\u578B"}),value:e.formatMessage({id:"pages.agent.generalModel",defaultMessage:"\u901A\u7528\u6A21\u578B"})}]});if(M.dataIndex=="status")return(0,o.jsx)(K.Z,{defaultValue:"",style:{width:150},allowClear:!0,options:[{label:e.formatMessage({id:"pages.agent.running",defaultMessage:"\u8FD0\u884C\u4E2D"}),value:1},{label:e.formatMessage({id:"pages.agent.queuing",defaultMessage:"\u6392\u961F\u4E2D"}),value:2},{label:e.formatMessage({id:"pages.agent.training",defaultMessage:"\u8BAD\u7EC3\u4E2D"}),value:3},{label:e.formatMessage({id:"pages.agent.trainingFailed",defaultMessage:"\u8BAD\u7EC3\u5931\u8D25"}),value:4}]})},yt=function(M,n){var S=n.feature,$=[{label:e.formatMessage({id:"pages.agent.flagship",defaultMessage:"\u65D7\u8230"}),value:1},{label:e.formatMessage({id:"pages.agent.highSpeed",defaultMessage:"\u9AD8\u901F"}),value:2},{label:e.formatMessage({id:"pages.agent.toolInvocation",defaultMessage:"\u5DE5\u5177\u8C03\u7528"}),value:3},{label:e.formatMessage({id:"pages.agent.rolePlay",defaultMessage:"\u89D2\u8272\u626E\u6F14"}),value:4},{label:e.formatMessage({id:"pages.agent.longText",defaultMessage:"\u957F\u6587\u672C"}),value:5},{label:e.formatMessage({id:"pages.agent.imageUnderstanding",defaultMessage:"\u56FE\u7247\u7406\u89E3"}),value:6},{label:e.formatMessage({id:"pages.agent.reasoning",defaultMessage:"\u63A8\u7406\u80FD\u529B"}),value:7},{label:e.formatMessage({id:"pages.agent.videoUnderstanding",defaultMessage:"\u89C6\u9891\u7406\u89E3"}),value:8},{label:e.formatMessage({id:"pages.agent.costPerformance",defaultMessage:"\u6027\u4EF7\u6BD4"}),value:9},{label:e.formatMessage({id:"pages.agent.codeExpert",defaultMessage:"\u4EE3\u7801\u4E13\u7CBE"}),value:10},{label:e.formatMessage({id:"pages.agent.audioUnderstanding",defaultMessage:"\u97F3\u9891\u7406\u89E3"}),value:11},{label:e.formatMessage({id:"pages.agent.visualAnalysis",defaultMessage:"\u89C6\u89C9\u5206\u6790"}),value:12}];if(!S)return null;var c=typeof S=="string"?S.split(",").map(Number):[Number(S)],w=c.map(function(v){var u;return(u=$.find(function(p){return p.value===v}))===null||u===void 0?void 0:u.label}).filter(Boolean),g=w.slice(0,3);return(0,o.jsx)("div",{style:{whiteSpace:"pre-wrap"},children:g.map(function(v,u){return(0,o.jsx)(le.Z,{color:"rgb(11, 211, 87)",children:v},u)})})},q=function(M,n){var S=[n];return(0,o.jsx)(we,{itemLayout:"horizontal",dataSource:S,renderItem:function(c){return(0,o.jsx)(we.Item,{children:(0,o.jsx)(we.Item.Meta,{avatar:(0,o.jsx)(Ue.C,{src:c.logo}),title:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsxs)("div",{style:{fontSize:"18px",color:"black"},children:[" ",c.name," \xB7 ",Se[c.feature]," "]}),(0,o.jsxs)("div",{children:[" ",(0,o.jsx)(le.Z,{className:"green-button",children:ut[c.status]})]})]}),(0,o.jsxs)("div",{style:{marginTop:"3px"},children:[(0,o.jsx)(le.Z,{className:"green-text",style:{marginLeft:"2px"},children:at[c.type]})," ",(0,o.jsxs)("span",{style:{marginLeft:"8px"},children:[" ",c.modelRights," "]}),(0,o.jsxs)("span",{style:{marginLeft:"8px"},children:[" ",c.modelFunction," "]})]})]}),description:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{children:c.description}),(0,o.jsxs)("div",{style:{marginTop:"3px",color:"rgba(204, 204, 204, 0.97)"},children:[(0,o.jsx)("span",{children:c.manufacturer})," ",(0,o.jsxs)("span",{style:{marginLeft:"10px"},children:[" ",e.formatMessage({id:"pages.agent.contextLength",defaultMessage:"\u4E0A\u4E0B\u6587\u957F\u5EA6"}),": ",c.modelLength=="0"?"0K":c.modelLength]}),(0,o.jsxs)("span",{style:{marginLeft:"10px"},children:[" ",c.createTime]})]})]})})})}})},lt=[{title:"id",dataIndex:"id",hideInSearch:!0,ellipsis:!0,hideInTable:!0},{title:"",dataIndex:"name",hideInSearch:!1,ellipsis:!0,render:q,renderFormItem:k},{title:e.formatMessage({id:"pages.agent.modelType",defaultMessage:"\u6A21\u578B\u7C7B\u578B"}),dataIndex:"type",width:120,hideInSearch:!1,ellipsis:!0,render:ht,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.modelFeature",defaultMessage:"\u6A21\u578B\u7279\u8272"}),dataIndex:"feature",width:120,hideInSearch:!1,ellipsis:!0,render:yt,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.modelProvider",defaultMessage:"\u6A21\u578B\u5382\u5546"}),dataIndex:"manufacturer",width:160,hideInSearch:!1,ellipsis:!0,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.modelSupportedFunctions",defaultMessage:"\u6A21\u578B\u652F\u6301\u529F\u80FD"}),dataIndex:"modelFunction",hideInSearch:!1,width:100,ellipsis:!0,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.contextLength",defaultMessage:"\u4E0A\u4E0B\u6587\u957F\u5EA6"}),dataIndex:"modelLength",width:80,hideInSearch:!1,ellipsis:!0,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.userRights",defaultMessage:"\u7528\u6237\u6743\u76CA"}),dataIndex:"modelRights",width:120,hideInSearch:!1,ellipsis:!0,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.modelStatus",defaultMessage:"\u6A21\u578B\u72B6\u6001"}),dataIndex:"status",hideInSearch:!1,width:100,ellipsis:!0,render:Qe,renderFormItem:k,hideInTable:!0},{title:e.formatMessage({id:"pages.agent.creationTime",defaultMessage:"\u521B\u5EFA\u65F6\u95F4"}),dataIndex:"createTime",hideInSearch:!0,width:160,ellipsis:!0,hideInTable:!0},{title:e.formatMessage({id:"pages.pointManage.operation",defaultMessage:"\u64CD\u4F5C"}),dataIndex:"option",valueType:"option",render:mt,hideInTable:!0}],ze=function(){var F=_()(T()().mark(function M(n){var S,$,c,w,g,v,u,p,ke,Ze,rt,be,xt,de,Mt,St,bt,Ct;return T()().wrap(function(qe){for(;;)switch(qe.prev=qe.next){case 0:return console.log("load device"),c=(S=oe.current)===null||S===void 0?void 0:S.getFieldsValue(),w=n.current?n.current:te.pageNo,g=n.size?n.size:te.pageSize,v=n.name?n.name:c.name,n.name==="no&input"&&(v=""),u=n.type?n.type:c.type,n.type==="no&input"&&(u=""),p=n.feature?n.feature:c.feature,n.feature==="no&input"&&(p=""),ke=n.manufacturer?n.manufacturer:c.manufacturer,n.manufacturer==="no&input"&&(ke=""),Ze=n.modelFunction?n.modelFunction:c.modelFunction,n.modelFunction==="no&input"&&(Ze=""),rt=n.modelLength?n.modelLength:c.modelLength,n.modelLength==="no&input"&&(rt=""),be=n.modelRights?n.modelRights:c.modelRights,n.modelRights==="no&input"&&(be=""),xt=n.status?n.status:c.status,n.status==="no&input"&&(xt=""),qe.next=22,(0,Te.X5)({name:v,type:u,feature:p,manufacturer:ke,modelFunction:Ze,modelLength:rt,modelRights:be,status:xt},w,g);case 22:de=qe.sent,de.code===0&&de.data&&(($=de.data)===null||$===void 0?void 0:$.data.length)>=0&&(z((Mt=de.data)===null||Mt===void 0?void 0:Mt.data),Je(ce()(ce()({},te),{},{pageSize:g,pageNo:w,total:de.data.total}))),de.code===0&&de.detail&&Ye((St=de.detail)===null||St===void 0?void 0:St.feature,(bt=de.detail)===null||bt===void 0?void 0:bt.manufacturer,(Ct=de.detail)===null||Ct===void 0?void 0:Ct.modelFunction);case 25:case"end":return qe.stop()}},M)}));return function(n){return F.apply(this,arguments)}}(),Ye=function(){var F=_()(T()().mark(function M(n,S,$){var c,w,g;return T()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:c=n.map(function(p){return{value:p,label:Se[p]}}).filter(function(p){return p.label&&typeof p.label=="string"&&p.label.trim()!==""}),Ee(c),w=S.map(function(p){return{value:p,label:p}}).filter(function(p){return p.label&&typeof p.label=="string"&&p.label.trim()!==""}),Ne(w),g=$.map(function(p){return{value:p,label:p}}).filter(function(p){return p.label&&typeof p.label=="string"&&p.label.trim()!==""}),ie(g);case 6:case"end":return u.stop()}},M)}));return function(n,S,$){return F.apply(this,arguments)}}();return(0,s.useEffect)(function(){ze({})},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(ne.Z,{rowKey:"id",headerTitle:"",actionRef:d,formRef:oe,pagination:{current:te.pageNo,pageSize:te.pageSize,showQuickJumper:!0,showSizeChanger:!0,showPrevNextJumpers:!0,showTitle:!0,pageSizeOptions:["10","20","50","100"],total:te.total},onChange:pt,rowSelection:!1,beforeSearchSubmit:vt,columns:lt,tableAlertRender:!1,options:{density:!1,setting:!1,reload:!1},dataSource:G}),(Oe==null?void 0:Oe.length)>0&&(0,o.jsx)(We.S,{extra:(0,o.jsxs)("div",{children:[e.formatMessage({id:"pages.pointManage.selected",defaultMessage:"\u5DF2\u9009\u62E9"}),(0,o.jsx)("a",{style:{fontWeight:600,color:"rgb(11, 211, 87)"},children:Oe.length}),e.formatMessage({id:"pages.pointManage.item",defaultMessage:"\u9879"})]}),children:(0,o.jsx)(ot.ZP,{onClick:function(){return pe(Oe)},children:e.formatMessage({id:"pages.pointManage.batchDelete",defaultMessage:"\u6279\u91CF\u5220\u9664"})})})]})},dt=(0,X.connect)()(it)},66309:function(st,Ce,r){r.d(Ce,{Z:function(){return et}});var b=r(67294),T=r(93967),$e=r.n(T),ce=r(98423),Re=r(98787),_=r(69760),He=r(96159),ae=r(45353),Te=r(53124),ne=r(11568),We=r(10274),Ae=r(14747),ve=r(83262),le=r(83559);const Ve=t=>{const{paddingXXS:x,lineWidth:h,tagPaddingHorizontal:i,componentCls:y,calc:L}=t,f=L(i).sub(h).equal(),C=L(x).sub(h).equal();return{[y]:Object.assign(Object.assign({},(0,Ae.Wf)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:f,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:`${(0,ne.bf)(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:t.borderRadiusSM,opacity:1,transition:`all ${t.motionDurationMid}`,textAlign:"start",position:"relative",[`&${y}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},[`${y}-close-icon`]:{marginInlineStart:C,fontSize:t.tagIconSize,color:t.colorTextDescription,cursor:"pointer",transition:`all ${t.motionDurationMid}`,"&:hover":{color:t.colorTextHeading}},[`&${y}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${t.iconCls}-close, ${t.iconCls}-close:hover`]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${y}-checkable-checked):hover`]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${t.iconCls} + span, > span + ${t.iconCls}`]:{marginInlineStart:f}}),[`${y}-borderless`]:{borderColor:"transparent",background:t.tagBorderlessBg}}},K=t=>{const{lineWidth:x,fontSizeIcon:h,calc:i}=t,y=t.fontSizeSM;return(0,ve.IX)(t,{tagFontSize:y,tagLineHeight:(0,ne.bf)(i(t.lineHeightSM).mul(y).equal()),tagIconSize:i(h).sub(i(x).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},U=t=>({defaultBg:new We.C(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText});var s=(0,le.I$)("Tag",t=>{const x=K(t);return Ve(x)},U),De=function(t,x){var h={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&x.indexOf(i)<0&&(h[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var y=0,i=Object.getOwnPropertySymbols(t);y<i.length;y++)x.indexOf(i[y])<0&&Object.prototype.propertyIsEnumerable.call(t,i[y])&&(h[i[y]]=t[i[y]]);return h},I=b.forwardRef((t,x)=>{const{prefixCls:h,style:i,className:y,checked:L,onChange:f,onClick:C}=t,Q=De(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:xe,tag:Y}=b.useContext(Te.E_),ue=Be=>{f==null||f(!L),C==null||C(Be)},re=xe("tag",h),[se,Pe,D]=s(re),Ge=$e()(re,`${re}-checkable`,{[`${re}-checkable-checked`]:L},Y==null?void 0:Y.className,y,Pe,D);return se(b.createElement("span",Object.assign({},Q,{ref:x,style:Object.assign(Object.assign({},i),Y==null?void 0:Y.style),className:Ge,onClick:ue})))}),J=r(98719);const he=t=>(0,J.Z)(t,(x,h)=>{let{textColor:i,lightBorderColor:y,lightColor:L,darkColor:f}=h;return{[`${t.componentCls}${t.componentCls}-${x}`]:{color:i,background:L,borderColor:y,"&-inverse":{color:t.colorTextLightSolid,background:f,borderColor:f},[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}});var Ie=(0,le.bk)(["Tag","preset"],t=>{const x=K(t);return he(x)},U);function Fe(t){return typeof t!="string"?t:t.charAt(0).toUpperCase()+t.slice(1)}const je=(t,x,h)=>{const i=Fe(h);return{[`${t.componentCls}${t.componentCls}-${x}`]:{color:t[`color${h}`],background:t[`color${i}Bg`],borderColor:t[`color${i}Border`],[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}};var Xe=(0,le.bk)(["Tag","status"],t=>{const x=K(t);return[je(x,"success","Success"),je(x,"processing","Info"),je(x,"error","Error"),je(x,"warning","Warning")]},U),Le=function(t,x){var h={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&x.indexOf(i)<0&&(h[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var y=0,i=Object.getOwnPropertySymbols(t);y<i.length;y++)x.indexOf(i[y])<0&&Object.prototype.propertyIsEnumerable.call(t,i[y])&&(h[i[y]]=t[i[y]]);return h};const ye=b.forwardRef((t,x)=>{const{prefixCls:h,className:i,rootClassName:y,style:L,children:f,icon:C,color:Q,onClose:xe,bordered:Y=!0,visible:ue}=t,re=Le(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:se,direction:Pe,tag:D}=b.useContext(Te.E_),[Ge,Be]=b.useState(!0),$t=(0,ce.Z)(re,["closeIcon","closable"]);b.useEffect(()=>{ue!==void 0&&Be(ue)},[ue]);const Ke=(0,Re.o2)(Q),we=(0,Re.yT)(Q),Ue=Ke||we,ot=Object.assign(Object.assign({backgroundColor:Q&&!Ue?Q:void 0},D==null?void 0:D.style),L),X=se("tag",h),[o,it,dt]=s(X),l=$e()(X,D==null?void 0:D.className,{[`${X}-${Q}`]:Ue,[`${X}-has-color`]:Q&&!Ue,[`${X}-hidden`]:!Ge,[`${X}-rtl`]:Pe==="rtl",[`${X}-borderless`]:!Y},i,y,it,dt),a=P=>{P.stopPropagation(),xe==null||xe(P),!P.defaultPrevented&&Be(!1)},[,e]=(0,_.Z)((0,_.w)(t),(0,_.w)(D),{closable:!1,closeIconRender:P=>{const N=b.createElement("span",{className:`${X}-close-icon`,onClick:a},P);return(0,He.wm)(P,N,O=>({onClick:W=>{var G;(G=O==null?void 0:O.onClick)===null||G===void 0||G.call(O,W),a(W)},className:$e()(O==null?void 0:O.className,`${X}-close-icon`)}))}}),d=typeof re.onClick=="function"||f&&f.type==="a",m=C||null,j=m?b.createElement(b.Fragment,null,m,f&&b.createElement("span",null,f)):f,E=b.createElement("span",Object.assign({},$t,{ref:x,className:l,style:ot}),j,e,Ke&&b.createElement(Ie,{key:"preset",prefixCls:X}),we&&b.createElement(Xe,{key:"status",prefixCls:X}));return o(d?b.createElement(ae.Z,{component:"Tag"},E):E)});ye.CheckableTag=I;var et=ye}}]);
