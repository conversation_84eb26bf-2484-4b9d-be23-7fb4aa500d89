(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[261],{78301:function(le,M,s){"use strict";s.d(M,{Z:function(){return Ft}});var o=s(67294),A=s(87462),_={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},ie=_,Z=s(93771),x=function(t,r){return o.createElement(Z.Z,(0,A.Z)({},t,{ref:r,icon:ie}))},S=o.forwardRef(x),T=S,J=s(93967),ee=s.n(J),te=s(9220),K=s(50344),g=s(8410),ne=s(21770),ue=s(98423),B=s(42550),oe=s(79370),Pe=s(53124),Fe=s(10110),be=s(83062),Ve={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},Xe=Ve,Ge=function(t,r){return o.createElement(Z.Z,(0,A.Z)({},t,{ref:r,icon:Xe}))},Je=o.forwardRef(Ge),Qe=Je,$e=s(15105),Ye=s(96159),qe=s(22913),Me=s(14747),_e=s(83559),et=s(65409),tt=s(11568);const nt=(e,t,r,n)=>{const{titleMarginBottom:l,fontWeightStrong:a}=n;return{marginBottom:l,color:r,fontWeight:a,fontSize:e,lineHeight:t}},ot=e=>{const t=[1,2,3,4,5],r={};return t.forEach(n=>{r[`
      h${n}&,
      div&-h${n},
      div&-h${n} > textarea,
      h${n}
    `]=nt(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)}),r},rt=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,Me.Nd)(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},lt=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:et.EV[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),it=e=>{const{componentCls:t,paddingSM:r}=e,n=r;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:`calc(1em - ${(0,tt.bf)(n)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},st=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),at=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),ct=e=>{const{componentCls:t,titleMarginTop:r}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccess},[`&${t}-warning`]:{color:e.colorWarning},[`&${t}-danger`]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},ot(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:r},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:r}}}),lt(e)),rt(e)),{[`
        ${t}-expand,
        ${t}-collapse,
        ${t}-edit,
        ${t}-copy
      `]:Object.assign(Object.assign({},(0,Me.Nd)(e)),{marginInlineStart:e.marginXXS})}),it(e)),st(e)),at()),{"&-rtl":{direction:"rtl"}})}},dt=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"});var Ae=(0,_e.I$)("Typography",e=>[ct(e)],dt),ut=e=>{const{prefixCls:t,"aria-label":r,className:n,style:l,direction:a,maxLength:y,autoSize:O=!0,value:d,onSave:u,onCancel:f,onEnd:m,component:p,enterIcon:b=o.createElement(Qe,null)}=e,D=o.useRef(null),R=o.useRef(!1),j=o.useRef(null),[v,k]=o.useState(d);o.useEffect(()=>{k(d)},[d]),o.useEffect(()=>{var E;if(!((E=D.current)===null||E===void 0)&&E.resizableTextArea){const{textArea:h}=D.current.resizableTextArea;h.focus();const{length:w}=h.value;h.setSelectionRange(w,w)}},[]);const Q=E=>{let{target:h}=E;k(h.value.replace(/[\n\r]/g,""))},I=()=>{R.current=!0},L=()=>{R.current=!1},H=E=>{let{keyCode:h}=E;R.current||(j.current=h)},C=()=>{u(v.trim())},Y=E=>{let{keyCode:h,ctrlKey:w,altKey:X,metaKey:$,shiftKey:W}=E;j.current!==h||R.current||w||X||$||W||(h===$e.Z.ENTER?(C(),m==null||m()):h===$e.Z.ESC&&f())},ae=()=>{C()},[re,F,ce]=Ae(t),V=ee()(t,`${t}-edit-content`,{[`${t}-rtl`]:a==="rtl",[`${t}-${p}`]:!!p},n,F,ce);return re(o.createElement("div",{className:V,style:l},o.createElement(qe.Z,{ref:D,maxLength:y,value:v,onChange:Q,onKeyDown:H,onKeyUp:Y,onCompositionStart:I,onCompositionEnd:L,onBlur:ae,"aria-label":r,rows:1,autoSize:O}),b!==null?(0,Ye.Tm)(b,{className:`${t}-edit-content-confirm`}):null))},pt=s(20640),ft=s.n(pt),mt=s(66680);function gt(e){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1)&&e==null?[]:Array.isArray(e)?e:[e]}var yt=function(e,t,r,n){function l(a){return a instanceof r?a:new r(function(y){y(a)})}return new(r||(r=Promise))(function(a,y){function O(f){try{u(n.next(f))}catch(m){y(m)}}function d(f){try{u(n.throw(f))}catch(m){y(m)}}function u(f){f.done?a(f.value):l(f.value).then(O,d)}u((n=n.apply(e,t||[])).next())})},vt=e=>{let{copyConfig:t,children:r}=e;const[n,l]=o.useState(!1),[a,y]=o.useState(!1),O=o.useRef(null),d=()=>{O.current&&clearTimeout(O.current)},u={};t.format&&(u.format=t.format),o.useEffect(()=>d,[]);const f=(0,mt.Z)(m=>yt(void 0,void 0,void 0,function*(){var p;m==null||m.preventDefault(),m==null||m.stopPropagation(),y(!0);try{const b=typeof t.text=="function"?yield t.text():t.text;ft()(b||gt(r,!0).join("")||"",u),y(!1),l(!0),d(),O.current=setTimeout(()=>{l(!1)},3e3),(p=t.onCopy)===null||p===void 0||p.call(t,m)}catch(b){throw y(!1),b}}));return{copied:n,copyLoading:a,onClick:f}};function Ee(e,t){return o.useMemo(()=>{const r=!!e;return[r,Object.assign(Object.assign({},t),r&&typeof e=="object"?e:null)]},[e])}var bt=e=>{const t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e}),t.current},Et=(e,t,r)=>(0,o.useMemo)(()=>e===!0?{title:t!=null?t:r}:(0,o.isValidElement)(e)?{title:e}:typeof e=="object"?Object.assign({title:t!=null?t:r},e):{title:e},[e,t,r]),ht=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r},De=o.forwardRef((e,t)=>{const{prefixCls:r,component:n="article",className:l,rootClassName:a,setContentRef:y,children:O,direction:d,style:u}=e,f=ht(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:m,direction:p,typography:b}=o.useContext(Pe.E_),D=d!=null?d:p,R=y?(0,B.sQ)(t,y):t,j=m("typography",r),[v,k,Q]=Ae(j),I=ee()(j,b==null?void 0:b.className,{[`${j}-rtl`]:D==="rtl"},l,a,k,Q),L=Object.assign(Object.assign({},b==null?void 0:b.style),u);return v(o.createElement(n,Object.assign({className:I,style:L,ref:R},f),O))}),xt=s(35918),St=s(48820),Ct=function(t,r){return o.createElement(Z.Z,(0,A.Z)({},t,{ref:r,icon:St.Z}))},Ot=o.forwardRef(Ct),wt=Ot,Rt=s(19267);function Ne(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function he(e,t,r){return e===!0||e===void 0?t:e||r&&t}function Tt(e){const t=document.createElement("em");e.appendChild(t);const r=e.getBoundingClientRect(),n=t.getBoundingClientRect();return e.removeChild(t),r.left>n.left||n.right>r.right||r.top>n.top||n.bottom>r.bottom}const xe=e=>["string","number"].includes(typeof e);var jt=e=>{let{prefixCls:t,copied:r,locale:n,iconOnly:l,tooltips:a,icon:y,tabIndex:O,onCopy:d,loading:u}=e;const f=Ne(a),m=Ne(y),{copied:p,copy:b}=n!=null?n:{},D=r?p:b,R=he(f[r?1:0],D),j=typeof R=="string"?R:D;return o.createElement(be.Z,{title:R},o.createElement("button",{type:"button",className:ee()(`${t}-copy`,{[`${t}-copy-success`]:r,[`${t}-copy-icon-only`]:l}),onClick:d,"aria-label":j,tabIndex:O},r?he(m[1],o.createElement(xt.Z,null),!0):he(m[0],u?o.createElement(Rt.Z,null):o.createElement(wt,null),!0)))},It=s(74902);const pe=o.forwardRef((e,t)=>{let{style:r,children:n}=e;const l=o.useRef(null);return o.useImperativeHandle(t,()=>({isExceed:()=>{const a=l.current;return a.scrollHeight>a.clientHeight},getHeight:()=>l.current.clientHeight})),o.createElement("span",{"aria-hidden":!0,ref:l,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},r)},n)}),Lt=e=>e.reduce((t,r)=>t+(xe(r)?String(r).length:1),0);function Be(e,t){let r=0;const n=[];for(let l=0;l<e.length;l+=1){if(r===t)return n;const a=e[l],O=xe(a)?String(a).length:1,d=r+O;if(d>t){const u=t-r;return n.push(String(a).slice(0,u)),n}n.push(a),r=d}return e}const Se=0,Ce=1,Oe=2,we=3,Ze=4,fe={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function Pt(e){const{enableMeasure:t,width:r,text:n,children:l,rows:a,expanded:y,miscDeps:O,onEllipsis:d}=e,u=o.useMemo(()=>(0,K.Z)(n),[n]),f=o.useMemo(()=>Lt(u),[n]),m=o.useMemo(()=>l(u,!1),[n]),[p,b]=o.useState(null),D=o.useRef(null),R=o.useRef(null),j=o.useRef(null),v=o.useRef(null),k=o.useRef(null),[Q,I]=o.useState(!1),[L,H]=o.useState(Se),[C,Y]=o.useState(0),[ae,re]=o.useState(null);(0,g.Z)(()=>{H(t&&r&&f?Ce:Se)},[r,n,a,t,u]),(0,g.Z)(()=>{var E,h,w,X;if(L===Ce){H(Oe);const $=R.current&&getComputedStyle(R.current).whiteSpace;re($)}else if(L===Oe){const $=!!(!((E=j.current)===null||E===void 0)&&E.isExceed());H($?we:Ze),b($?[0,f]:null),I($);const W=((h=j.current)===null||h===void 0?void 0:h.getHeight())||0,Re=a===1?0:((w=v.current)===null||w===void 0?void 0:w.getHeight())||0,ge=((X=k.current)===null||X===void 0?void 0:X.getHeight())||0,Te=Math.max(W,Re+ge);Y(Te+1),d($)}},[L]);const F=p?Math.ceil((p[0]+p[1])/2):0;(0,g.Z)(()=>{var E;const[h,w]=p||[0,0];if(h!==w){const $=(((E=D.current)===null||E===void 0?void 0:E.getHeight())||0)>C;let W=F;w-h===1&&(W=$?h:w),b($?[h,W]:[W,w])}},[p,F]);const ce=o.useMemo(()=>{if(!t)return l(u,!1);if(L!==we||!p||p[0]!==p[1]){const E=l(u,!1);return[Ze,Se].includes(L)?E:o.createElement("span",{style:Object.assign(Object.assign({},fe),{WebkitLineClamp:a})},E)}return l(y?u:Be(u,p[0]),Q)},[y,L,p,u].concat((0,It.Z)(O))),V={width:r,margin:0,padding:0,whiteSpace:ae==="nowrap"?"normal":"inherit"};return o.createElement(o.Fragment,null,ce,L===Oe&&o.createElement(o.Fragment,null,o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},V),fe),{WebkitLineClamp:a}),ref:j},m),o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},V),fe),{WebkitLineClamp:a-1}),ref:v},m),o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},V),fe),{WebkitLineClamp:1}),ref:k},l([],!0))),L===we&&p&&p[0]!==p[1]&&o.createElement(pe,{style:Object.assign(Object.assign({},V),{top:400}),ref:D},l(Be(u,F),!0)),L===Ce&&o.createElement("span",{style:{whiteSpace:"inherit"},ref:R}))}var $t=e=>{let{enableEllipsis:t,isEllipsis:r,children:n,tooltipProps:l}=e;return!(l!=null&&l.title)||!t?n:o.createElement(be.Z,Object.assign({open:r?void 0:!1},l),n)},Mt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};function At(e,t){let{mark:r,code:n,underline:l,delete:a,strong:y,keyboard:O,italic:d}=e,u=t;function f(m,p){p&&(u=o.createElement(m,{},u))}return f("strong",y),f("u",l),f("del",a),f("code",n),f("mark",r),f("kbd",O),f("i",d),u}const Dt="...";var me=o.forwardRef((e,t)=>{var r;const{prefixCls:n,className:l,style:a,type:y,disabled:O,children:d,ellipsis:u,editable:f,copyable:m,component:p,title:b}=e,D=Mt(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:R,direction:j}=o.useContext(Pe.E_),[v]=(0,Fe.Z)("Text"),k=o.useRef(null),Q=o.useRef(null),I=R("typography",n),L=(0,ue.Z)(D,["mark","code","delete","underline","strong","keyboard","italic"]),[H,C]=Ee(f),[Y,ae]=(0,ne.Z)(!1,{value:C.editing}),{triggerType:re=["icon"]}=C,F=i=>{var c;i&&((c=C.onStart)===null||c===void 0||c.call(C)),ae(i)},ce=bt(Y);(0,g.Z)(()=>{var i;!Y&&ce&&((i=Q.current)===null||i===void 0||i.focus())},[Y]);const V=i=>{i==null||i.preventDefault(),F(!0)},E=i=>{var c;(c=C.onChange)===null||c===void 0||c.call(C,i),F(!1)},h=()=>{var i;(i=C.onCancel)===null||i===void 0||i.call(C),F(!1)},[w,X]=Ee(m),{copied:$,copyLoading:W,onClick:Re}=vt({copyConfig:X,children:d}),[ge,Te]=o.useState(!1),[ke,Vt]=o.useState(!1),[He,Xt]=o.useState(!1),[We,Gt]=o.useState(!1),[Jt,Qt]=o.useState(!0),[q,P]=Ee(u,{expandable:!1,symbol:i=>i?v==null?void 0:v.collapse:v==null?void 0:v.expand}),[G,Yt]=(0,ne.Z)(P.defaultExpanded||!1,{value:P.expanded}),N=q&&(!G||P.expandable==="collapsible"),{rows:de=1}=P,ye=o.useMemo(()=>N&&(P.suffix!==void 0||P.onEllipsis||P.expandable||H||w),[N,P,H,w]);(0,g.Z)(()=>{q&&!ye&&(Te((0,oe.G)("webkitLineClamp")),Vt((0,oe.G)("textOverflow")))},[ye,q]);const[z,qt]=o.useState(N),ze=o.useMemo(()=>ye?!1:de===1?ke:ge,[ye,ke,ge]);(0,g.Z)(()=>{qt(ze&&N)},[ze,N]);const Ue=N&&(z?We:He),_t=N&&de===1&&z,je=N&&de>1&&z,en=(i,c)=>{var U;Yt(c.expanded),(U=P.onExpand)===null||U===void 0||U.call(P,i,c)},[Ke,tn]=o.useState(0),nn=i=>{let{offsetWidth:c}=i;tn(c)},on=i=>{var c;Xt(i),He!==i&&((c=P.onEllipsis)===null||c===void 0||c.call(P,i))};o.useEffect(()=>{const i=k.current;if(q&&z&&i){const c=Tt(i);We!==c&&Gt(c)}},[q,z,d,je,Jt,Ke]),o.useEffect(()=>{const i=k.current;if(typeof IntersectionObserver=="undefined"||!i||!z||!N)return;const c=new IntersectionObserver(()=>{Qt(!!i.offsetParent)});return c.observe(i),()=>{c.disconnect()}},[z,N]);const Ie=Et(P.tooltip,C.text,d),ve=o.useMemo(()=>{if(!(!q||z))return[C.text,d,b,Ie.title].find(xe)},[q,z,b,Ie.title,Ue]);if(Y)return o.createElement(ut,{value:(r=C.text)!==null&&r!==void 0?r:typeof d=="string"?d:"",onSave:E,onCancel:h,onEnd:C.onEnd,prefixCls:I,className:l,style:a,direction:j,component:p,maxLength:C.maxLength,autoSize:C.autoSize,enterIcon:C.enterIcon});const rn=()=>{const{expandable:i,symbol:c}=P;return i?o.createElement("button",{type:"button",key:"expand",className:`${I}-${G?"collapse":"expand"}`,onClick:U=>en(U,{expanded:!G}),"aria-label":G?v.collapse:v==null?void 0:v.expand},typeof c=="function"?c(G):c):null},ln=()=>{if(!H)return;const{icon:i,tooltip:c,tabIndex:U}=C,Le=(0,K.Z)(c)[0]||(v==null?void 0:v.edit),dn=typeof Le=="string"?Le:"";return re.includes("icon")?o.createElement(be.Z,{key:"edit",title:c===!1?"":Le},o.createElement("button",{type:"button",ref:Q,className:`${I}-edit`,onClick:V,"aria-label":dn,tabIndex:U},i||o.createElement(T,{role:"button"}))):null},sn=()=>w?o.createElement(jt,Object.assign({key:"copy"},X,{prefixCls:I,copied:$,locale:v,onCopy:Re,loading:W,iconOnly:d==null})):null,an=i=>[i&&rn(),ln(),sn()],cn=i=>[i&&!G&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Dt),P.suffix,an(i)];return o.createElement(te.Z,{onResize:nn,disabled:!N},i=>o.createElement($t,{tooltipProps:Ie,enableEllipsis:N,isEllipsis:Ue},o.createElement(De,Object.assign({className:ee()({[`${I}-${y}`]:y,[`${I}-disabled`]:O,[`${I}-ellipsis`]:q,[`${I}-ellipsis-single-line`]:_t,[`${I}-ellipsis-multiple-line`]:je},l),prefixCls:n,style:Object.assign(Object.assign({},a),{WebkitLineClamp:je?de:void 0}),component:p,ref:(0,B.sQ)(i,k,t),direction:j,onClick:re.includes("text")?V:void 0,"aria-label":ve==null?void 0:ve.toString(),title:b},L),o.createElement(Pt,{enableMeasure:N&&!z,text:d,rows:de,width:Ke,onEllipsis:on,expanded:G,miscDeps:[$,G,W,H,w,v]},(c,U)=>At(e,o.createElement(o.Fragment,null,c.length>0&&U&&!G&&ve?o.createElement("span",{key:"show-content","aria-hidden":!0},c):c,cn(U)))))))}),Nt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r},Bt=o.forwardRef((e,t)=>{var{ellipsis:r,rel:n}=e,l=Nt(e,["ellipsis","rel"]);const a=Object.assign(Object.assign({},l),{rel:n===void 0&&l.target==="_blank"?"noopener noreferrer":n});return delete a.navigate,o.createElement(me,Object.assign({},a,{ref:t,ellipsis:!!r,component:"a"}))}),Zt=o.forwardRef((e,t)=>o.createElement(me,Object.assign({ref:t},e,{component:"div"}))),kt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};const Ht=(e,t)=>{var{ellipsis:r}=e,n=kt(e,["ellipsis"]);const l=o.useMemo(()=>r&&typeof r=="object"?(0,ue.Z)(r,["expandable","rows"]):r,[r]);return o.createElement(me,Object.assign({ref:t},n,{ellipsis:l,component:"span"}))};var Wt=o.forwardRef(Ht),zt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};const Ut=[1,2,3,4,5];var Kt=o.forwardRef((e,t)=>{const{level:r=1}=e,n=zt(e,["level"]),l=Ut.includes(r)?`h${r}`:"h1";return o.createElement(me,Object.assign({ref:t},n,{component:l}))});const se=De;se.Text=Wt,se.Link=Bt,se.Title=Kt,se.Paragraph=Zt;var Ft=se},20640:function(le,M,s){"use strict";var o=s(11742),A={"text/plain":"Text","text/html":"Url",default:"Text"},_="Copy to clipboard: #{key}, Enter";function ie(x){var S=(/mac os x/i.test(navigator.userAgent)?"\u2318":"Ctrl")+"+C";return x.replace(/#{\s*key\s*}/g,S)}function Z(x,S){var T,J,ee,te,K,g,ne=!1;S||(S={}),T=S.debug||!1;try{ee=o(),te=document.createRange(),K=document.getSelection(),g=document.createElement("span"),g.textContent=x,g.ariaHidden="true",g.style.all="unset",g.style.position="fixed",g.style.top=0,g.style.clip="rect(0, 0, 0, 0)",g.style.whiteSpace="pre",g.style.webkitUserSelect="text",g.style.MozUserSelect="text",g.style.msUserSelect="text",g.style.userSelect="text",g.addEventListener("copy",function(B){if(B.stopPropagation(),S.format)if(B.preventDefault(),typeof B.clipboardData=="undefined"){T&&console.warn("unable to use e.clipboardData"),T&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var oe=A[S.format]||A.default;window.clipboardData.setData(oe,x)}else B.clipboardData.clearData(),B.clipboardData.setData(S.format,x);S.onCopy&&(B.preventDefault(),S.onCopy(B.clipboardData))}),document.body.appendChild(g),te.selectNodeContents(g),K.addRange(te);var ue=document.execCommand("copy");if(!ue)throw new Error("copy command was unsuccessful");ne=!0}catch(B){T&&console.error("unable to copy using execCommand: ",B),T&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(S.format||"text",x),S.onCopy&&S.onCopy(window.clipboardData),ne=!0}catch(oe){T&&console.error("unable to copy using clipboardData: ",oe),T&&console.error("falling back to prompt"),J=ie("message"in S?S.message:_),window.prompt(J,x)}}finally{K&&(typeof K.removeRange=="function"?K.removeRange(te):K.removeAllRanges()),g&&document.body.removeChild(g),ee()}return ne}le.exports=Z},79370:function(le,M,s){"use strict";s.d(M,{G:function(){return ie}});var o=s(98924),A=function(x){if((0,o.Z)()&&window.document.documentElement){var S=Array.isArray(x)?x:[x],T=window.document.documentElement;return S.some(function(J){return J in T.style})}return!1},_=function(x,S){if(!A(x))return!1;var T=document.createElement("div"),J=T.style[x];return T.style[x]=S,T.style[x]!==J};function ie(Z,x){return!Array.isArray(Z)&&x!==void 0?_(Z,x):A(Z)}},11742:function(le){le.exports=function(){var M=document.getSelection();if(!M.rangeCount)return function(){};for(var s=document.activeElement,o=[],A=0;A<M.rangeCount;A++)o.push(M.getRangeAt(A));switch(s.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":s.blur();break;default:s=null;break}return M.removeAllRanges(),function(){M.type==="Caret"&&M.removeAllRanges(),M.rangeCount||o.forEach(function(_){M.addRange(_)}),s&&s.focus()}}}}]);
