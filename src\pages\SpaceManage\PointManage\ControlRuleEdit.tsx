import React from 'react';
import { Modal, Button, message, Form, Steps, Spin, Input, Select, Checkbox, Tooltip, Row, Col, Image } from "antd"
import { useState, useRef, useEffect } from "react"
import { useIntl } from 'umi'
import { ProForm, ProFormText, ProFormDigit, ProFormSelect, ProTable } from '@ant-design/pro-components';
import './index.less'
import { getParamConfig, getAllControlRules, startControl, getPointControls, getEventRecordList, getImageFile } from "@/services/ant-design-pro/api";


const ControlRuleEdit: React.FC = (props: any, ref: any) => {
  /** 国际化 */
  const intl = useIntl();

  /**布控规则数据 */
  const [controlRulesData, setControlRulesData] = useState<any[]>([])

  const [pointIdControlRulesData, setPointIdControlRulesData] = useState<any[]>([])

  const [selectedRuleIds, setSelectedRuleIds] = useState<string>("")

  const [eventRecordsData, setEventRecordsData] = useState<any[]>([])

  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);

  const [currentImageId, setCurrentImageId] = useState<string | null>(null);

  const [currentImageUrl, setCurrentImageUrl] = useState<string | null>(null);

  const [imageMap, setImageMap] = useState<Record<string, string>>({});
  const handleStartControl = async () => {
    if (selectedRuleIds === "") {
      // console.log("selectedRuleIds")
      message.warning(intl.formatMessage({ id: 'pages.pointManage.selectEventType', defaultMessage: '请选择事件类型', }))
      return;
    }
    const rs = await startControl(props.currentRow.businessId, selectedRuleIds)
    if (rs.code === 0) {
      message.success(intl.formatMessage({ id: 'pages.pointManage.monitorSuccess', defaultMessage: '布控成功', }))
      props.updateHasControling(props.currentRow.businessId, true)
    } else {
      message.error(intl.formatMessage({ id: 'pages.pointManage.monitorFailure', defaultMessage: '布控失败', }))
    }
  }

  const handleCancelControl = async () => {
    // if (selectedRuleIds != "") {
    //   message.warning(intl.formatMessage({ id: 'pages.pointManage.selectEventType', defaultMessage: '请选择事件类型', }))
    //   return;
    // }
    const rs = await startControl(props.currentRow.businessId, "")
    if (rs.code === 0) {
      setPointIdControlRulesData([])

      props.updateHasControling(props.currentRow.businessId, false)

      message.success(intl.formatMessage({ id: 'pages.pointManage.cancelSuccess', defaultMessage: '取消成功', }))
      setSelectedRuleIds("")
    } else {
      message.error(intl.formatMessage({ id: 'pages.pointManage.operationFailure', defaultMessage: '操作失败', }))
    }
  }

  const previewRecord = async () => {
    console.log("props->", props.currentRow.businessId,)
    const rs = await getParamConfig()
    if (rs.code !== 0 || !rs.data) {
      return
    }

    // 解析 URL 中的 hostname 作为 server
    let server = '127.0.0.1';
    try {
      // 例如 http://127.0.0.1:58095
      const parsedUrl = new URL(rs.data.url);
      server = parsedUrl.hostname; // 提取 IP 或域名部分
    } catch (error) {
      console.warn('无效的 URL，使用默认值:', rs.data.url);
    }

    setTimeout(() => {
      // 延迟执行，避免和modal同步执行找不到videoPreview 节点
      BioPlayer.init({
        server: server,
        secret: rs.data.secret,
        port: '58095',
        element: document.getElementById('videoPreview'),
      })
      BioPlayer.newPlayer({
        uid: 'mars-single-live-play',
        width: 600,
        height: 350,
      })
      let media_info = {}
      media_info.app = 'mars'
      BioPlayer.playVideo({
        channelId: props.currentRow.businessId,
        streamType: 0,
        channelName: props.currentRow.name,
        media_info: media_info,
      })
    }, 100)
  };


  /**
 * 加载布控规则
 */
  const loadControlRules = async () => {
    const rs = await getAllControlRules();
    if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
      setControlRulesData(rs.data)
    }
  }

  /**
* 加载当前点位id的布控规则
*/
  const loadControlRulesByPointId = async () => {
    const rs = await getPointControls(props.currentRow.businessId);
    if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
      setPointIdControlRulesData(rs.data);
    }
  }


  const loadEventRecords = async () => {
    const rs = await getEventRecordList({ pointId: props.currentRow.businessId }, 1, 10);
    if (rs.code === 0 && rs?.data && rs?.data.data.length > 0) {
      const records = await Promise.all(rs.data.data.map(async (record) => ({
        ...record,
        thumbUrl: await fetchImage(record.fileName, 'thumb')
      })));
      console.log("records->", records);
      setEventRecordsData(records);

      if (!hasLoadedOnce) {
        setHasLoadedOnce(true);
      }

    }
  }

  const handleImageClick = async (record: any) => {
    if (currentImageId === record.id && currentImageUrl) return;

    try {
      const imageUrl = await fetchImage(record.fileName, "snap");

      setCurrentImageId(record.id);
      setCurrentImageUrl(imageUrl);
    } catch (error) {
      console.error("获取原图失败", error);
    }
  };



  /**
 * 获取图片（支持缩略图 & 原图）
 * @param imageName 图片名称
 * @param imageType 是否获取缩略图（snap 抓拍 thumb 缩略图 target 目标抠图）
 * @returns 图片的 URL（本地 Blob URL）
 */
  const fetchImage = async (imageName: string, imageType: string): Promise<string | null> => {
    try {
      const response = await getImageFile(imageName, imageType);
      if (!response.ok) {
        console.warn(`图片 ${imageName} 获取失败，状态码：${response.status}`);
        return null;
      }

      const blob = await response.blob();
      return URL.createObjectURL(blob); // 生成 Blob URL 供前端展示
    } catch (error) {
      console.error(`获取图片 ${imageName} 失败:`, error);
      return null;
    }
  };

  const handleControRuleCancel = () => {
    BioPlayer.closeVideo({
      uid: 'mars-single-live-play'
    })
    BioPlayer.removePlayer({
      uid: 'mars-single-live-play'
    })
    props.handleControlRuleFormVisible(false);
  };


  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (props.visiable) {
      previewRecord?.();
      loadControlRules?.();
      loadControlRulesByPointId?.();

      setEventRecordsData([]);
      loadEventRecords();

      interval = setInterval(() => {
        loadEventRecords();
      }, 5000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [props.visiable]);

  return (
    <>
      <Modal
        width="60vw"
        destroyOnClose
        title={intl.formatMessage({ id: 'pages.pointManage.monitor', defaultMessage: '布控', })}
        open={props.visiable}
        onCancel={handleControRuleCancel}
        className="controRuleEdit-modal"
        styles={{
          mask: { background: 'rgba(0, 0, 0, 0.5)' },
          header: {
            background: 'rgb(238, 242, 246)',
            padding: '9px 16px',
            marginBottom: '0',
          },
          body: {
            background: 'rgb(255, 255, 255)',
            padding: '24px',
            // height: '65vh',
          },
        }}
        footer={false}
      >
        <h3>{props.currentRow?.name}</h3>
        <Row gutter={16}>
          <Col span={14}>
            <div id='videoPreview' style={{ width: '100%', height: '350px', overflow: 'hidden' }}></div>
          </Col>
          <Col span={10}>
            <div style={{ border: '1px solid rgb(242, 242, 242)', borderRadius: '4px', minHeight: '350px' }}>
              <div style={{
                width: '100%',
                height: '51px',
                borderTopLeftRadius: '4px',
                borderTopRightRadius: '4px',
                background: 'rgb(248, 248, 248)',
                display: 'flex',
                alignItems: 'center',
                padding: '0 16px',
                boxSizing: 'border-box'
              }}>
                <h3 style={{ margin: 0, lineHeight: '51px' }}>{intl.formatMessage({ id: 'pages.pointManage.monitorEvent', defaultMessage: '布控事件', })}</h3>
              </div>
              <div style={{ height: 'calc(350px - 51px)' }}>
                <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <div style={{ flex: 1 }}>
                    <Checkbox.Group
                      style={{ width: '100%' }}
                      value={pointIdControlRulesData}
                      onChange={(checkedValues) => {
                        const selectedIds = controlRulesData
                          .filter(item => checkedValues.includes(item.id))
                          .map(item => item.id)
                          .join(",");
                        setSelectedRuleIds(selectedIds);
                        setPointIdControlRulesData(checkedValues);
                      }}
                    >

                      <div
                        style={{
                          display: 'grid',
                          padding: '16px 25px 16px 16px',
                          gap: '16px',
                          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                          maxWidth: '100%',
                        }}
                      >
                        {controlRulesData.map((item) => (
                          <Checkbox
                            key={item.eventCode}
                            value={item.id}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              width: '100%', 
                            }}
                          >
                            <Tooltip title={item.eventType}>
                              <div
                                style={{
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  width: '100%',
                                }}
                              >
                                {item.eventType}
                              </div>
                            </Tooltip>
                          </Checkbox>
                        ))}
                      </div>


                    </Checkbox.Group>
                  </div>
                  <div style={{ marginTop: 'auto' }}>
                    <Row>
                      <Col span={12}>
                        <Button
                          block
                          style={{
                            borderRadius: '0 0 0 4px',
                          }}
                          onClick={handleCancelControl}
                        >
                          {intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', })}
                        </Button>
                      </Col>
                      <Col span={12}>
                        <Button
                          block
                          type="primary"
                          style={{
                            borderRadius: '0 0 0 4px',
                          }}
                          onClick={handleStartControl}
                        >
                          {intl.formatMessage({ id: 'pages.pointManage.startMonitor', defaultMessage: '开始布控', })}
                        </Button>
                      </Col>
                    </Row>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
        <div style={{ marginTop: '20px' }}>
          <h3>{intl.formatMessage({ id: 'pages.pointManage.monitorRecord', defaultMessage: '布控记录', })}</h3>
          <div style={{ minHeight: '160px', display: 'flex', alignItems: 'center' }}>
            {eventRecordsData.length > 0 ? (
              <div
                style={{
                  display: 'flex',
                  overflowX: 'auto',
                  gap: '16px',
                  padding: '4px',
                  width: '100%',
                }}
              >
                {eventRecordsData.map((record, index) => (
                  <div
                    key={index}
                    style={{
                      width: '240px',
                      height: '130px',
                      flex: '0 0 auto',
                      borderRadius: '4px',
                      position: 'relative',
                      overflow: 'hidden',
                    }}
                  >
                    <div className="search-Image">
                      <Image
                        src={
                          currentImageId === record.id
                            ? currentImageUrl
                            : record.thumbUrl || '/icons/search/default-noImage.png'
                        }
                        preview={true}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          cursor: 'pointer',
                          zIndex: '99999',
                        }}
                        onClick={() => handleImageClick(record)}
                      />
                    </div>

                    <div
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        background: 'rgba(0, 0, 0, 0.3)',
                        color: 'white',
                        padding: '12px',
                        zIndex: 1,
                      }}
                    >
                      {record.eventType}
                      <br />
                      {record.eventTime}
                    </div>
                  </div>
                ))}
              </div>
            ) : hasLoadedOnce ? (
              <div style={{ textAlign: 'center', color: 'gray', width: '100%' }}>
                {intl.formatMessage({ id: 'pages.pointManage.noEventRecord', defaultMessage: '没有事件记录', })}
              </div>
            ) : null}
          </div>
        </div>
      </Modal>


    </>
  )
}
export default ControlRuleEdit
