"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[483],{32098:function(Ce,F,o){o.r(F),o.d(F,{default:function(){return se}});var K=o(15009),l=o.n(K),J=o(97857),m=o.n(J),Q=o(99289),h=o.n(Q),W=o(5574),y=o.n(W),g=o(67294),X=o(74330),E=o(55060),q=o(78818),_=o(17788),N=o(2618),ee=o(53199),ne=o(97832),te=o(71551),ae=o(27484),R=o.n(ae),r=o(85893),re=function(De){var ie=(0,g.useState)([]),H=y()(ie,2),b=H[0],oe=H[1],le=(0,g.useState)({}),z=y()(le,2),c=z[0],ue=z[1],de=(0,g.useState)(!1),P=y()(de,2),ce=P[0],$=P[1],fe=(0,g.useState)(!1),O=y()(fe,2),pe=O[0],B=O[1],ve=(0,g.useState)([]),L=y()(ve,2),me=L[0],ge=L[1],he=(0,g.useState)([]),k=y()(he,2),Te=k[0],ye=k[1],p=(0,te.useIntl)(),Se=(0,g.useState)({eventType:"",pointName:"",startTime:"",endTime:"",pageSize:24,pageNo:1,total:0}),A=y()(Se,2),S=A[0],C=A[1],I=(0,g.useRef)(),D={fontSize:12,color:"#555",margin:"4px 0",display:"flex",alignItems:"center",gap:4},Y={width:14,height:14},Me=function(){var t=h()(l()().mark(function n(e){var s,a;return l()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return console.log("\u70B9\u51FB\u4E86\u56FE\u7247",e),f.next=3,V(e.fileName,"snap");case 3:s=f.sent,a=m()(m()({},e),{},{imageUrl:s}),ue(a),$(!0);case 7:case"end":return f.stop()}},n)}));return function(e){return t.apply(this,arguments)}}(),je=function(){var t=h()(l()().mark(function n(e,s){var a,v,f,i,M,j;return l()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return C(function(x){return m()(m()({},x),{},{pageNo:e,pageSize:s})}),f=((a=I.current)===null||a===void 0||(v=a.getFieldsValue)===null||v===void 0?void 0:v.call(a))||{},i=f.eventTime||[],M=i[0]?R()(i[0]).startOf("second").format("YYYY-MM-DD HH:mm:ss.SSS"):void 0,j=i[1]?R()(i[1]).endOf("second").format("YYYY-MM-DD HH:mm:ss.SSS"):void 0,u.next=7,U({current:e,size:s,eventType:f.eventType||"no&input",pointName:f.pointName||"no&input",startTime:M||"no&input",endTime:j||"no&input"});case 7:case"end":return u.stop()}},n)}));return function(e,s){return t.apply(this,arguments)}}(),xe=function(){var t=h()(l()().mark(function n(e){return l()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:console.log("point search",e),C(m()(m()({},S),{},{pageNo:1,eventType:e.eventType,pointName:e.pointName,startTime:e.startTime,endTime:e.endTime})),U({current:1,eventType:e.eventType?e.eventType:"no&input",pointName:e.pointName?e.pointName:"no&input",startTime:e.startTime?e.startTime:"no&input",endTime:e.endTime?e.endTime:"no&input"});case 3:case"end":return a.stop()}},n)}));return function(e){return t.apply(this,arguments)}}(),U=function(){var t=h()(l()().mark(function n(e){var s,a,v,f,i,M,j,w,u,x;return l()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:return console.log("page->",e),a=(s=I.current)===null||s===void 0?void 0:s.getFieldsValue(),v=e.current?e.current:S.pageNo,f=e.size?e.size:S.pageSize,i=e.eventType?e.eventType:a.eventType,M=e.pointName?e.pointName:a.pointName,j=e.startTime?e.startTime:a.startTime,w=e.endTime?e.endTime:a.endTime,e.eventType==="no&input"&&(i=void 0),e.pointName==="no&input"&&(M=void 0),e.startTime==="no&input"&&(j=void 0),e.endTime==="no&input"&&(w=void 0),B(!0),T.next=15,(0,N.ps)({eventType:i,pointName:M,startTime:j,endTime:w},v,f);case 15:if(u=T.sent,console.log("rs->",u),!(u.code===0&&u!==null&&u!==void 0&&u.data.data&&(u==null?void 0:u.data.data.length)>=0)){T.next=24;break}return T.next=20,Promise.all(u.data.data.map(function(){var we=h()(l()().mark(function Z(G){return l()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.t0=m(),d.t1=m()({},G),d.t2={},d.next=5,V(G.fileName,"thumb");case 5:return d.t3=d.sent,d.t4={thumbUrl:d.t3},d.abrupt("return",(0,d.t0)(d.t1,d.t2,d.t4));case 8:case"end":return d.stop()}},Z)}));return function(Z){return we.apply(this,arguments)}}()));case 20:x=T.sent,console.log("records->",x),oe(x),C(m()(m()({},S),{},{pageSize:f,pageNo:v,total:u.data.total}));case 24:B(!1);case 25:case"end":return T.stop()}},n)}));return function(e){return t.apply(this,arguments)}}(),V=function(){var t=h()(l()().mark(function n(e,s){var a,v;return l()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,(0,N.ty)(e,s);case 3:if(a=i.sent,a.ok){i.next=7;break}return console.warn("\u56FE\u7247 ".concat(e," \u83B7\u53D6\u5931\u8D25\uFF0C\u72B6\u6001\u7801\uFF1A").concat(a.status)),i.abrupt("return",null);case 7:return i.next=9,a.blob();case 9:return v=i.sent,i.abrupt("return",URL.createObjectURL(v));case 13:return i.prev=13,i.t0=i.catch(0),console.error("\u83B7\u53D6\u56FE\u7247 ".concat(e," \u5931\u8D25:"),i.t0),i.abrupt("return",null);case 17:case"end":return i.stop()}},n,null,[[0,13]])}));return function(e,s){return t.apply(this,arguments)}}(),Ne=[{title:p.formatMessage({id:"pages.search.time",defaultMessage:"\u65F6\u95F4"}),dataIndex:"eventTime",valueType:"dateRange",hideInSearch:!1,search:{transform:function(n){return{startTime:n!=null&&n[0]?R()(n[0]).startOf("second").format("YYYY-MM-DD HH:mm:ss.SSS"):void 0,endTime:n!=null&&n[1]?R()(n[1]).endOf("second").format("YYYY-MM-DD HH:mm:ss.SSS"):void 0}}},fieldProps:{showTime:{format:"HH:mm:ss"},format:"YYYY-MM-DD HH:mm:ss"},ellipsis:!0},{title:p.formatMessage({id:"pages.pointManage.eventType",defaultMessage:"\u4E8B\u4EF6\u7C7B\u578B"}),dataIndex:"eventType",ellipsis:!0,valueType:"select",fieldProps:{showSearch:!0,options:me.map(function(t){return{label:t.eventType,value:t.eventType}}),filterOption:function(n,e){var s;return((s=e==null?void 0:e.label)!==null&&s!==void 0?s:"").toLowerCase().includes(n.toLowerCase())}}},{title:p.formatMessage({id:"pages.pointManage.pointName",defaultMessage:"\u70B9\u4F4D\u540D\u79F0"}),dataIndex:"pointName",ellipsis:!0,valueType:"select",fieldProps:{showSearch:!0,options:Te.map(function(t){return{label:t,value:t}}),filterOption:function(n,e){var s;return((s=e==null?void 0:e.label)!==null&&s!==void 0?s:"").toLowerCase().includes(n.toLowerCase())}}}],Re=function(){var t=h()(l()().mark(function n(){var e;return l()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,N.hI)();case 2:e=a.sent,e.code===0&&e!==null&&e!==void 0&&e.data&&(e==null?void 0:e.data.length)>=0&&ge(e.data);case 4:case"end":return a.stop()}},n)}));return function(){return t.apply(this,arguments)}}(),be=function(){var t=h()(l()().mark(function n(){var e;return l()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,N.d1)();case 2:e=a.sent,e.code===0&&e!==null&&e!==void 0&&e.data&&(e==null?void 0:e.data.length)>=0&&ye(e.data);case 4:case"end":return a.stop()}},n)}));return function(){return t.apply(this,arguments)}}();return(0,g.useEffect)(function(){Re(),be()},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:(0,r.jsx)(ee.Z,{formRef:I,columns:Ne,headerTitle:!1,toolBarRender:!1,beforeSearchSubmit:xe,search:{optionRender:function(n,e,s){return s}},pagination:!1,rowKey:"id",options:!1,tableAlertRender:!1,tableRender:function(n,e,s,a){return null},onSubmit:function(n){},onReset:function(){}})}),(0,r.jsxs)(ne.m,{style:{backgroundColor:"white",padding:"0px 24px 24px 24px",position:"relative",minHeight:"calc(100vh - 150px)"},onBack:function(){return null},backIcon:"",children:[(0,r.jsx)("h3",{style:{marginBottom:16},children:p.formatMessage({id:"pages.pointManage.eventRecord",defaultMessage:"\u4E8B\u4EF6\u8BB0\u5F55"})}),(0,r.jsx)(X.Z,{spinning:pe,tip:p.formatMessage({id:"pages.search.loading",defaultMessage:"\u52A0\u8F7D\u4E2D..."}),size:"large",style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)",fontSize:"24px"},children:(0,r.jsx)("div",{style:{height:"calc(100vh - 300px)",maxHeight:"calc(100vh - 300px)",display:b.length>0?"grid":"flex",gridTemplateColumns:"repeat(auto-fill, minmax(250px, 1fr))",gap:"20px",padding:"12px",overflowY:"auto",alignItems:"center",justifyContent:"center",alignContent:b.length>0?"start":"center"},children:b.length>0?b.map(function(t,n){return(0,r.jsxs)("div",{style:{height:176,background:"#fff",borderRadius:"12px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",overflow:"hidden",transition:"transform 0.2s"},onMouseEnter:function(s){s.currentTarget.style.transform="scale(1.05)"},onMouseLeave:function(s){s.currentTarget.style.transform="scale(1)"},children:[(0,r.jsx)("div",{className:"search-Image",children:(0,r.jsx)(E.Z,{src:t.thumbUrl||"/icons/search/default-noImage.png",preview:!1,alt:t.eventType,style:{width:"100%",height:105,objectFit:"cover",cursor:"pointer"},onClick:function(){return Me(t)}})}),(0,r.jsxs)("div",{style:{padding:"0 12px 12px 12px"},children:[(0,r.jsxs)("p",{style:D,children:[(0,r.jsx)("img",{src:"/icons/pointManage/img_eventType.svg",style:Y}),t.eventType]}),(0,r.jsxs)("p",{style:D,children:[(0,r.jsx)("img",{src:"/icons/search/local.svg",style:Y}),t.pointName||""]}),(0,r.jsxs)("p",{style:D,children:[(0,r.jsx)("img",{src:"/icons/search/date.svg",style:Y}),t.eventTime||""]})]})]},n)}):(0,r.jsx)("div",{style:{textAlign:"center"},children:(0,r.jsx)("img",{src:"/icons/pointManage/img_no_data.png",alt:"No Data",style:{width:"150px"}})})})}),(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",background:"white",padding:"12px 0",display:"flex",justifyContent:"right",borderTop:"1px solid #f0f0f0",paddingRight:"5%"},children:(0,r.jsx)(q.Z,{current:S.pageNo,pageSize:S.pageSize,total:S.total,onChange:function(n,e){je(n,e)},showSizeChanger:!0,pageSizeOptions:["24","48","72","96"],showTotal:function(n,e){return p.formatMessage({id:"pages.pointManage.paginationInfo",defaultMessage:"\u7B2C {range0} - {range1} \u6761/\u603B\u5171 {total} \u6761"},{range0:e[0],range1:e[1],total:n})}})})]}),(0,r.jsx)(_.Z,{width:"70vw",destroyOnClose:!0,title:p.formatMessage({id:"pages.search.eventDetail",defaultMessage:"\u4E8B\u4EF6\u8BE6\u60C5"}),open:ce,onCancel:function(){return $(!1)},className:"eventRecord-modal",styles:{mask:{background:"rgba(0, 0, 0, 0.5)"},header:{background:"rgb(238, 242, 246)",padding:"9px 16px",marginBottom:"0"},body:{background:"rgb(255, 255, 255)",padding:"24px",height:"60vh"}},footer:!1,children:(0,r.jsxs)("div",{className:"event-detail-container",children:[(0,r.jsx)("div",{className:"event-image-wrapper",children:(0,r.jsx)(E.Z,{src:(c==null?void 0:c.imageUrl)||(c==null?void 0:c.thumbUrl)||"/icons/search/default-noImage.png",alt:p.formatMessage({id:"pages.pointManage.eventImage",defaultMessage:"\u4E8B\u4EF6\u56FE\u7247"}),width:"100%",height:"100%",style:{objectFit:"cover",borderRadius:4},placeholder:!0})}),(0,r.jsxs)("div",{className:"event-info",children:[(0,r.jsx)("div",{className:"event-info-title",children:p.formatMessage({id:"pages.pointManage.detail",defaultMessage:"\u8BE6\u60C5"})}),(0,r.jsxs)("div",{className:"event-info-item",children:[p.formatMessage({id:"pages.pointManage.eventType",defaultMessage:"\u4E8B\u4EF6\u7C7B\u578B"}),"\uFF1A",(c==null?void 0:c.eventType)||""]}),(0,r.jsxs)("div",{className:"event-info-item",children:[p.formatMessage({id:"pages.pointManage.pointInfo",defaultMessage:"\u70B9\u4F4D\u4FE1\u606F"}),"\uFF1A",(c==null?void 0:c.pointName)||""]}),(0,r.jsxs)("div",{className:"event-info-item",children:[p.formatMessage({id:"pages.pointManage.eventTime",defaultMessage:"\u4E8B\u4EF6\u65F6\u95F4"}),"\uFF1A",(c==null?void 0:c.eventTime)||""]})]})]})})]})},se=re}}]);
