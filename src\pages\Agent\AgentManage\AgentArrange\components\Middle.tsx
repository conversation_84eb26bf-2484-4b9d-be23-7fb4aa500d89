import {
    ProCard,
    ProForm,
    ProFormGroup,
    ProFormSelect,
    ProFormInstance,
    ProFormTextArea,
    ProFormText,
} from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { useIntl } from 'umi';


interface MiddleData {
    prompt: string;
}

interface MiddleDataProps {
    middleData: MiddleData;
}
const Middle: React.FC<MiddleDataProps> = (props) => {
    const [responsive, setResponsive] = useState(false);
    /** 国际化 */
    const intl = useIntl();

    return (
        <>
            <div style={{
                height: '100%',
                display: 'flex',
                borderColor: '#666',
                margin: 0,
                padding: 0,

            }}>
                <ProFormTextArea
                    name="prompt"
                    placeholder={intl.formatMessage({ id: 'pages.agent.describeFunction', defaultMessage: '请描述人设、功能' })}
                    initialValue={props?.middleData.prompt}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    style={{
                        flex: 1,

                    }}
                    fieldProps={{
                        style: {
                            height: '76vh',
                            width: '55vh',
                            overflow: 'auto',
                            resize: 'none',

                        },
                        rows: undefined
                    }}

                />
            </div>
        </>
    )
};

export default Middle;