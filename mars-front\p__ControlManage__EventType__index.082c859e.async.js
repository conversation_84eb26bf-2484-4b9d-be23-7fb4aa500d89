"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[521],{33939:function(K,C,t){t.r(C),t.d(C,{default:function(){return V}});var $=t(15009),u=t.n($),j=t(97857),l=t.n(j),E=t(99289),g=t.n(E),w=t(5574),M=t.n(w),D=t(2618),A=t(53199),d=t(67294),x=t(71551),h=t(85893),F=function(W){var y=(0,x.useIntl)(),B=(0,d.useRef)(),G=(0,d.useState)([]),I=M()(G,2),J=I[0],L=I[1],p=(0,d.useRef)(),O=(0,d.useState)({eventType:"",pageSize:10,pageNo:1,total:0}),N=M()(O,2),n=N[0],T=N[1],Q=function(){var c=g()(u()().mark(function r(e,s,a,i){return u()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:console.log("page change",e,s,a,i),T(l()(l()({},n),{},{pageSize:e.pageSize,pageNo:e.current})),P({current:e.current,size:e.pageSize});case 3:case"end":return o.stop()}},r)}));return function(e,s,a,i){return c.apply(this,arguments)}}(),Z=function(){var c=g()(u()().mark(function r(e){return u()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:console.log("search ",e),T(l()(l()({},n),{},{pageNo:1,eventType:e.eventType})),P({current:1,eventType:e.eventType?e.eventType:"no&input"});case 3:case"end":return a.stop()}},r)}));return function(e){return c.apply(this,arguments)}}(),H=[{title:y.formatMessage({id:"pages.config.eventCode",defaultMessage:"\u4E8B\u4EF6\u7F16\u7801"}),dataIndex:"eventCode",hideInSearch:!0,ellipsis:!0,width:20},{title:y.formatMessage({id:"pages.pointManage.eventType",defaultMessage:"\u4E8B\u4EF6\u7C7B\u578B"}),dataIndex:"eventType",width:120,hideInSearch:!1,ellipsis:!0}],P=function(){var c=g()(u()().mark(function r(e){var s,a,i,S,o,m,z,v,R;return u()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return console.log("load list",p.current,(s=p.current)===null||s===void 0?void 0:s.getFieldsValue()),S=(a=p.current)===null||a===void 0?void 0:a.getFieldsValue(),o=e.current?e.current:n.pageNo,m=e.size?e.size:n.pageSize,z=e.eventType?e.eventType:S.eventType,e.eventType==="no&input"&&(z=void 0),f.next=8,(0,D.Pw)({eventType:z},o,m);case 8:v=f.sent,v.code===0&&v.data&&((i=v.data)===null||i===void 0?void 0:i.data.length)>=0&&(L((R=v.data)===null||R===void 0?void 0:R.data),T(l()(l()({},n),{},{pageSize:m,pageNo:o,total:v.data.total})));case 10:case"end":return f.stop()}},r)}));return function(e){return c.apply(this,arguments)}}();return(0,d.useEffect)(function(){},[]),(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(A.Z,{headerTitle:y.formatMessage({id:"pages.pointManage.eventType",defaultMessage:"\u4E8B\u4EF6\u7C7B\u578B"}),actionRef:B,formRef:p,pagination:{current:n.pageNo,pageSize:n.pageSize,showQuickJumper:!0,showSizeChanger:!0,showPrevNextJumpers:!0,showTitle:!0,pageSizeOptions:["10","20","50","100"],total:n.total},onChange:Q,beforeSearchSubmit:Z,columns:H,tableAlertRender:!1,options:{density:!1,setting:!1},dataSource:J})})},V=(0,x.connect)()(F)}}]);
