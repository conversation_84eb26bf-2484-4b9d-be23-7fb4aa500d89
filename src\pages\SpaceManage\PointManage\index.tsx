import { getPointInfoList, delPoints, getParamConfig, editPointInfo, getPointControls, getChannelInfo } from "@/services/ant-design-pro/api";
import { PlusOutlined, ReloadOutlined, UploadOutlined } from '@ant-design/icons';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
  DrawerForm,
  FooterToolbar,
  ProFormDigit,
  ProFormText,
  ProFormSelect,
  ProTable,
  ProForm,
  ProFormCheckbox
} from '@ant-design/pro-components';
import { Button, message, Modal, Table, Select, Tag, Tooltip } from 'antd';
import type { ReactNode } from 'react';
import { useEffect, useRef, useState } from 'react';
import { connect, useIntl, getDvaApp } from 'umi';
import './index.less'
import ManualEditDevice from './ManualEditDevice';
import ControlRuleEdit from './ControlRuleEdit';
import ImportDevice from './ImportDevice';




const PointManage: React.FC = (props) => {
  const intl = useIntl();

  /** 表格的引用 */
  const actionRef = useRef<ActionType>();

  /** 当前行 */
  const [currentRow, setCurrentRow] = useState<any>();

  /** 表单引用 */
  const formRef = useRef<ProFormInstance>();

  /**添加设备框显示隐藏 */
  const [deviceFormVisible, setDeviceFormVisible] = useState<boolean>(false);

  /**设备窗口 */
  const [manualEditVisiable, setManualEditVisiable] = useState(false)

  /**编辑设备信息 */
  const [editDeviceData, setEditDeviceData] = useState<any>()

  /**设备树数据 */
  const [pointsData, setPointsData] = useState<any[]>([])

  /** 表单ref */
  const pointsRef = useRef()

  /** 预览窗口 */
  const [priviewVisible, setPriviewVisible] = useState(false);

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    name: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })

  /** 选中点位集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  /** 编辑点位表单显隐 */
  const [pointEditFormVisible, setPointEditFormVisible] = useState<boolean>(false);

  /** 表单引用 */
  const editFormRef = useRef<ProFormInstance>();


  /** 布控规则表单显隐 */
  const [controlRuleFormVisible, setControlRuleFormVisible] = useState<boolean>(false);

  /** 点位导入 */
  const [importDeviceVisible, setImportDeviceVisible] = useState<boolean>(false);



  const verifyUserRef = useRef(null)
  /**
   * 处理点位信息编辑
   */
  const handlePointInfoEdit = async () => {
    let values;
    try {
      values = await editFormRef.current?.validateFieldsReturnFormatValue?.();
    } catch (error) {
      console.log('edit pointInfo error', error);
      return;
    }
    console.log("values", values)

    // 构造待提交的数据对象
    const data = {
      ...currentRow,
      name: values.name,
      captureInterval: values.captureInterval,
      detectType: Array.isArray(values.detectType) ? values.detectType.join(',') : values.detectType
    };
    const rs = await editPointInfo(data)
    if (rs.code !== 0) {
      message.error(rs.msg)
      return
    } else {
      message.success(intl.formatMessage({ id: 'pages.pointManage.operationSuccess', defaultMessage: '操作成功', }))
      // 加载点位信息
      loadPointInfo({});
    }
    setPointEditFormVisible(false)
  }


  /** 单窗口关闭 */
  const videoClose = () => {
    setPriviewVisible(false);
    BioPlayer.closeVideo({
      uid: 'mars-single-live-play'
    })
    BioPlayer.removePlayer({
      uid: 'mars-single-live-play'
    })
  };

  const previewRecord = async (row: any) => {
    setPriviewVisible(true);
    const rs = await getParamConfig()
    if (rs.code !== 0 || !rs.data) {
      return
    }

    // 解析 URL 中的 hostname 作为 server
    let server = '127.0.0.1';
    try {
      // 例如 http://127.0.0.1:58095
      const parsedUrl = new URL(rs.data.url);
      server = parsedUrl.hostname; // 提取 IP 或域名部分
    } catch (error) {
      console.warn('无效的 URL，使用默认值:', rs.data.url);
    }

    setTimeout(() => {
      // 延迟执行，避免和modal同步执行找不到videoPreview 节点
      BioPlayer.init({
        server: server,
        secret: rs.data.secret,
        port: '58095',
        element: document.getElementById('videoPreview'),
      })
      BioPlayer.newPlayer({
        uid: 'mars-single-live-play',
        width: 600,
        height: 350,
      })
      let media_info = {}
      media_info.app = 'mars'
      BioPlayer.playVideo({
        channelId: row.businessId,
        streamType: 0,
        channelName: row.name,
        media_info: media_info,
      })
    }, 100)
  };


  const operateRender = (dom: any, record: any): ReactNode => {
    const isOnline = record.status === 1;
    return <div id="operate">
      <a onClick={isOnline ? () => previewRecord(record) : undefined}>
        <img
          className={`img_preview ${!isOnline ? 'disabled' : ''}`}
          title={intl.formatMessage({
            id: 'pages.pointManage.preview',
            defaultMessage: '预览',
          })}
          src="/icons/pointManage/img_preview.svg"
        />
      </a>

      <a onClick={isOnline ? () => editRecord(record) : undefined}>
        <img
          className={`img_edit ${!isOnline ? 'disabled' : ''}`}
          title={intl.formatMessage({
            id: 'pages.pointManage.edit',
            defaultMessage: '编辑',
          })}
          src="/icons/pointManage/img_edit.svg"
        />
      </a>

      {/* <a onClick={() => getSnap([record])}>
          <img className="img_snapPhoto" title="查看" />
        </a> */}
      <a onClick={() => getControlRule([record])}>
        <img
          className="img_controlRule"
          title={record.hasControlRule ? intl.formatMessage({ id: 'pages.pointManage.alreadyMonitored', defaultMessage: '已布控', }) : intl.formatMessage({ id: 'pages.pointManage.monitor', defaultMessage: '布控', })}
          src={
            record.hasControlRule
              ? '/icons/pointManage/img_controlRule_hover.svg' // 已布控，默认用 hover 图标
              : '/icons/pointManage/img_controlRule.svg'       // 未布控，正常图标
          }
        />
      </a>
      <a onClick={() => deleteRecord([record])}>
        <img className="img_del" title={intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除', })} />
      </a>
    </div>;
  }

  const updateHasControling = async (businessId: any, hasControlRule: any) => {
    setPointsData(prevData =>
      prevData.map(record =>
        record.businessId === businessId
          ? { ...record, hasControlRule }
          : record
      )
    );
  }

  /**
* 加载当前点位id的布控规则
*/
  const checkControlRules = async (data: any) => {
    const newData = await Promise.all(
      data.map(async (record) => {
        console.log("record->", record)
        const res = await getPointControls(record.businessId);
        console.log("res->", res)
        const hasControlRule = Array.isArray(res.data) && res.data.length > 0;
        return { ...record, hasControlRule };
      })
    );
    setPointsData(newData);
  };


  /**
   * 布控规则表单
   * @param rows 
   */
  const getControlRule = async (rows: any[]) => {
    setControlRuleFormVisible(true)
    setCurrentRow(rows[0])
  }

  /**
   * 查看当前抓拍图片
   * @param rows 
   */
  const getSnap = async (rows: any[]) => {

  }




  /**
   * 加载设备
   */
  const loadDevice = async () => {

  }

  /**
   * 添加点位
   */
  const addPoint = async () => {
    setManualEditVisiable(true)
  }

  /**
   * 取消操作
   */
  const deviceOnCancel = () => {
    setDeviceFormVisible(false)
  }


  /**
   * 分页
   */
  const handlePointPageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {
    console.log('page change', pagination, filters, sorter, extra)
    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadPointInfo({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('point search', params)
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      name: params.name,
    })
    loadPointInfo({
      current: 1,
      name: params.name ? params.name : 'no&input',
    })
  }

  /**
   * 编辑点位
   */
  const editRecord = async (rows: any[]) => {
    setPointEditFormVisible(true)
    setCurrentRow(rows)
  }


  /**
   * 删除记录
   */
  const deleteRecord = async (rows: any[]) => {
    console.log('del data:', rows)
    const controlledItems = rows.filter((item) => item.hasControlRule);

    if (controlledItems.length > 0) {
      message.warning(intl.formatMessage({ id: 'pages.pointManage.deleteHasControlRule', defaultMessage: '选中的点位中正在布控中，无法删除', }));
      return;
    }
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.search.delete', defaultMessage: '删除', }),
      content: intl.formatMessage({ id: 'pages.pointManage.confirmDeletePoint', defaultMessage: '确定删除点位信息记录吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk: async () => {
        const hide = message.loading(intl.formatMessage({ id: 'pages.pointManage.deleting', defaultMessage: '正在删除', }));
        var opSuccess = 0;
        var opFail = 0;
        const data: any = []
        // for (let i = 0; i < rows.length; i++) {
        //   data.push({
        //     id: rows[i].id,
        //     deviceId: rows[i].deviceId,
        //     businessId: rows[i].businessId
        //   })
        // }
        const rs = await delPoints(rows);
        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }));
        }
        else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.deleteFailure', defaultMessage: '删除失败，请重试', }));
        }
        setSelectedRows([]);
        //actionRef.current?.reloadAndRest?.();
        loadPointInfo({});

        hide();

      },
    });
  };


  const detectTypeRender = (dom: any, row: any) => {
    // 假设 detectType 是存储在 row 对象中的
    const detectTypeValues = row.detectType;

    // 抓拍类型的选项映射
    const options = [
      { label: intl.formatMessage({ id: 'pages.pointManage.person', defaultMessage: '人', }), value: 1 },
      { label: intl.formatMessage({ id: 'pages.pointManage.motorVehicle', defaultMessage: '机动车', }), value: 2 },
      { label: intl.formatMessage({ id: 'pages.pointManage.nonMotorVehicle', defaultMessage: '非机动车', }), value: 3 },
    ];

    // 如果 detectType 为空或不存在，直接返回 null
    if (!detectTypeValues) {
      return null;
    }

    // 确保 detectTypeValues 是数组
    const values = detectTypeValues.split(",").map(Number);

    // 根据 value 获取对应的 label
    const labels = values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean); // 过滤掉 undefined 或 null 的值

    // 只显示前 4 个类型
    const displayLabels = labels.slice(0, 3);
    const remainingLabels = labels.slice(3).join(', ');

    return (
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
        {displayLabels.map((label, index) => (
          <Tag
            color="rgb(11, 211, 87)"
            key={index}
            style={{
              maxWidth: '120px',
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal',
            }}
          >
            {label}
          </Tag>
        ))}
        {labels.length > 3 && (
          <Tooltip
            title={
              <div
                style={{
                  maxHeight: '100px',
                  maxWidth: '300px',
                  overflowY: 'auto',
                  padding: '5px',
                }}
              >
                {remainingLabels.split(', ').map((label, index) => (
                  <Tag
                    color="rgb(11, 211, 87)"
                    key={index}
                    style={{
                      maxWidth: '120px',
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      marginBottom: '5px',
                    }}
                  >
                    {label}
                  </Tag>
                ))}
              </div>
            }
          >
            <Tag color="rgb(11, 211, 87)">...</Tag>
          </Tooltip>
        )}
      </div>
    );
  };




  //列表渲染
  const columns: any = [
    {
      title: intl.formatMessage({ id: 'pages.pointManage.pointName', defaultMessage: '点位名称', }),
      dataIndex: 'name',
      hideInSearch: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.protocolType', defaultMessage: '协议类型', }),
      dataIndex: 'protocolType',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.captureType', defaultMessage: '抓拍类型', }),
      dataIndex: 'detectType',
      hideInSearch: true,
      ellipsis: true,
      render: detectTypeRender,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.captureInterval', defaultMessage: '抓拍间隔', }),
      dataIndex: 'captureInterval',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.deviceName', defaultMessage: '设备名称', }),
      dataIndex: 'deviceName',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.deviceIP', defaultMessage: '设备IP', }),
      dataIndex: 'deviceIp',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.devicePort', defaultMessage: '设备端口', }),
      dataIndex: 'devicePort',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.status', defaultMessage: '状态' }),
      dataIndex: 'status',
      render: (status: number) => {
        console.log("status value:", status);

        if (status === 1) {
          return <span style={{ color: 'rgb(11, 211, 87)' }}>
            {intl.formatMessage({ id: 'pages.pointManage.online', defaultMessage: '在线' })}
          </span>;
        } else if (status === 0) {
          return <span style={{ color: 'red' }}>
            {intl.formatMessage({ id: 'pages.pointManage.offline', defaultMessage: '离线' })}
          </span>;
        } else {
          // 兜底逻辑
          return <span style={{ color: 'rgb(11, 211, 87)' }}>
            {intl.formatMessage({ id: 'pages.pointManage.online', defaultMessage: '在线' })}
          </span>;
        }
      },
    }
    ,
    {
      title: intl.formatMessage({ id: 'pages.pointManage.operation', defaultMessage: '操作', }),
      dataIndex: 'option',
      valueType: 'option',
      render: operateRender,
    },
  ];



  /**
   * 加载点位信息
   * @param page 
   */
  const loadPointInfo = async (page: any) => {
    const searchValue = pointsRef.current?.getFieldsValue();
    const currentPage = page.current || searchParams.pageNo;
    const pageSize = page.size || searchParams.pageSize;

    let name = page.name ? page.name : searchValue.name;
    if (page.name === 'no&input') name = undefined;

    const rs = await getPointInfoList({ name }, currentPage, pageSize);

    if (rs.code === 0 && rs.data && Array.isArray(rs.data.data)) {
      const rawData = rs.data.data;

      // 立即设置初始数据（可能没有 status/hasControlRule）
      setPointsData(rawData);

      setSearchParams({
        ...searchParams,
        pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      });

      // 异步补充每项的状态和规则
      const enrichedData = await Promise.all(
        rawData.map(async (item) => {
          let status = 0; // 默认离线
          let hasControlRule = false;

          if (!item.businessId) return { ...item, status, hasControlRule };

          try {
            const [channelInfo, controlRes] = await Promise.all([
              getChannelInfo({ id: item.businessId }),
              getPointControls(item.businessId),
            ]);

            // 检查通道信息是否成功获取
            if (channelInfo?.code === 0 && Array.isArray(channelInfo.data?.list)) {
              const matchedDevice = channelInfo.data.list.find(device => device.id === item.businessId);
              status = matchedDevice?.status === 1 ? 1 : 0;
            } else {
              status = 0; // 接口返回异常，设置为离线
            }

            // 检查控制规则
            hasControlRule = Array.isArray(controlRes?.data) && controlRes.data.length > 0;

          } catch (e) {
            console.warn('获取设备详情失败:', item.businessId, e);
            status = 0; // 发生异常，设置为离线
          }

          return { ...item, status, hasControlRule };
        })
      );

      setPointsData(enrichedData);
    } else {
      // 如果获取列表失败，将当前已显示的设备设置为离线
      const offlineData = pointsData.map(item => ({
        ...item,
        status: 0,
        hasControlRule: false
      }));
      setPointsData(offlineData);
    }
  };



  const handleControlRuleFormVisible = async (flag: boolean) => {
    setControlRuleFormVisible(flag)
    setCurrentRow(undefined)
  }


  const handleImportDeviceVisible = async (flag: boolean) => {
    setImportDeviceVisible(flag)
  }




  useEffect(() => {

    const script = document.createElement('script');
    script.src = "../js/bioplayer/app.js";
    script.async = true;
    script.onload = () => {
      if (window.BioPlayer) {
        //console.info('BioPlayer 已加载');
      } else {
        console.error('BioPlayer 未加载');
      }
    };

    document.body.appendChild(script);

    setTimeout(() => {
      //console.log('language has changed', intl.locale)
      BioPlayer.changeLanguage({ locale: intl.locale })
    }, 100)

    return () => {
      document.body.removeChild(script);
    };

  }, [intl.locale]);

  return (
    <>
      <ProTable
        rowKey="id"
        headerTitle={intl.formatMessage({ id: 'pages.pointManage.pointManagement', defaultMessage: '点位管理', })}
        actionRef={actionRef}
        formRef={pointsRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,          // 默认每页显示的条数
          showQuickJumper: true, // 显示跳转至某页
          showSizeChanger: true, // 显示 pageSize 切换器
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'], // 指定每页可以显示多少条
          //onChange: (page, size) => setPageSize(size), // 更新 pageSize
          total: searchParams.total,   // 数据总数
        }}
        onChange={handlePointPageChange}
        rowSelection={{
          onChange: (_, selectedRows) => {
            console.log("selectedRows-> ", selectedRows)
            setSelectedRows(selectedRows);
          },
        }}
        beforeSearchSubmit={handleBeforeSearchDevice}
        columns={columns}
        tableAlertRender={false}
        options={{
          reload: false,
          density: false,
          setting: false,
        }}
        dataSource={pointsData}
        scroll={{ y: 530 }}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={addPoint}>
            <PlusOutlined />
            {intl.formatMessage({ id: 'pages.pointManage.add', defaultMessage: '新增', })}
          </Button>,
          <Button type="primary"
            key="manualRefresh"
            onClick={async () => {
              await loadPointInfo({});
            }}>
            <ReloadOutlined />
            {intl.formatMessage({ id: 'pages.pointManage.refresh', defaultMessage: '刷新', })}
          </Button>,
          <Button type="primary" key="primary" onClick={() => setImportDeviceVisible(true)}>
            <UploadOutlined />
            {intl.formatMessage({ id: '导入', defaultMessage: '导入', })}
          </Button>,
        ]}
      />

      <div>
        <ManualEditDevice visiable={manualEditVisiable} setManualEditVisiable={setManualEditVisiable} loadPointInfo={loadPointInfo} reload={loadDevice} editData={editDeviceData} />
      </div>
      <div>
        <ControlRuleEdit visiable={controlRuleFormVisible} updateHasControling={updateHasControling} handleControlRuleFormVisible={handleControlRuleFormVisible} currentRow={currentRow} />
      </div>

      <div>
        <ImportDevice visiable={importDeviceVisible} loadPointInfo={loadPointInfo} handleImportDeviceVisibleVisible={handleImportDeviceVisible} />
      </div>



      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {intl.formatMessage({ id: 'pages.pointManage.selected', defaultMessage: '已选择', })}
              <a style={{ fontWeight: 600, color: 'rgb(11, 211, 87)' }}>{selectedRows.length}</a>
              {intl.formatMessage({ id: 'pages.pointManage.item', defaultMessage: '项', })}
            </div>
          }
        >
          <Button onClick={() => deleteRecord(selectedRows)}>{intl.formatMessage({ id: 'pages.pointManage.batchDelete', defaultMessage: '批量删除', })}</Button>
        </FooterToolbar>
      )}

      <Modal
        width={650}
        footer={null}
        destroyOnClose
        styles={{
          body: { padding: '0px', height: '350px', overflow: 'hidden' }
        }}
        title={intl.formatMessage({ id: 'pages.pointManage.preview', defaultMessage: '预览', })}
        open={priviewVisible}
        onOk={videoClose}
        onCancel={videoClose}

      >
        <div id='videoPreview' style={{ width: '600px', height: '350px', overflow: 'hidden' }}></div>
      </Modal>

      <Modal
        width={500}
        destroyOnClose
        styles={{
          body: { padding: '24px', height: '25vh', overflow: 'hidden' }
        }}
        title={intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', })}
        open={pointEditFormVisible}
        onOk={handlePointInfoEdit}
        onCancel={() => setPointEditFormVisible(false)}
      >
        <ProForm<{
          name: string;
        }>
          formRef={editFormRef}
          layout={'horizontal'}
          //form={form}
          style={{ width: '100%' }}
          labelAlign="right" // 设置 label 右对齐
          labelCol={{ span: 6 }} // 设置 label 的宽度
          wrapperCol={{ span: 16 }} // 设置输入框的宽度
          submitter={{
            resetButtonProps: {
              hidden: true,
            },
            submitButtonProps: {
              hidden: true,
            },
            searchConfig: {
            }
          }}
        >
          <ProFormText
            width="md"
            name="name"
            initialValue={currentRow?.name}
            rules={[
              {
                required: true
              },
              {
                max: 64,
                message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 64"
              },
            ]}
            fieldProps={{
              maxLength: 64,
            }}
            label={intl.formatMessage({ id: 'pages.pointManage.pointName', defaultMessage: '点位名称', })}
            placeholder={intl.formatMessage({ id: 'pages.pointManage.pointName', defaultMessage: '点位名称', })}
          />

          <ProFormDigit
            width="md"
            name="captureInterval"
            initialValue={currentRow?.captureInterval}
            rules={[
              { required: true, message: '请输入抓拍间隔' },
              {
                validator: (_, value) => {
                  if (value < 1 || value > 3600) {
                    return Promise.reject(new Error(intl.formatMessage({ id: 'pages.pointManage.captureIntervalRange', defaultMessage: '抓拍间隔在 1 到 3600 秒之间' })));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            label={intl.formatMessage({ id: 'pages.pointManage.captureInterval', defaultMessage: '抓拍间隔' })}
            placeholder={intl.formatMessage({ id: 'pages.pointManage.captureInterval', defaultMessage: '请输入抓拍间隔' })}
          />

          <ProFormCheckbox.Group
            width="md"
            name="detectType"
            label={intl.formatMessage({ id: 'pages.pointManage.captureType', defaultMessage: '抓拍类型', })}
            initialValue={currentRow?.detectType ? currentRow?.detectType.toString().split(',').map(Number) : []}
            options={[
              { label: intl.formatMessage({ id: 'pages.pointManage.person', defaultMessage: '人', }), value: 1 },
              { label: intl.formatMessage({ id: 'pages.pointManage.motorVehicle', defaultMessage: '机动车', }), value: 2 },
              { label: intl.formatMessage({ id: 'pages.pointManage.nonMotorVehicle', defaultMessage: '非机动车', }), value: 3 },
            ]}
          />


        </ProForm>
        <div id='videoPreview' style={{ width: '600px', height: '350px', overflow: 'hidden' }}></div>
      </Modal>
    </>

  );
};

export default connect()(PointManage);
