package com.zkteco.mars.usc.model;

import com.zkteco.framework.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 事件类型
 *
 * <AUTHOR>
 * @date 2025-04-08 18:32
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "EVENT_TYPE", indexes = {@Index(name = "EVENT_TYPE_ID_IDX", columnList = "ID"), @Index(name = "EVENT_TYPE_CRT_IDX", columnList = "CREATE_TIME"), @Index(name = "EVENT_TYPE_UPT_IDX", columnList = "UPDATE_TIME")})
public class EventType extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 事件编码
     */
    @Column(name = "event_code", length = 50)
    private String eventCode;

    /**
     * 事件类型
     */
    @Column(name = "event_type", length = 100)
    private String eventType;


    public EventType() {

    }

    public EventType(String eventCode, String eventType) {
        this.eventCode = eventCode;
        this.eventType = eventType;
    }
}

