import { getControlRulesList, getAllEventType, editControlRule, delControlRule, getUsedEventCodes, checkRuleIsControl } from "@/services/ant-design-pro/api";
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { ProTable, FooterToolbar, ProForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { connect, useIntl, } from 'umi';
import './index.less'
import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Modal } from 'antd';

const ControlRules: React.FC = (props) => {
  const intl = useIntl();

  /** 表格的引用 */
  const actionRef = useRef<ActionType>();

  /** 编辑表单 */
  const editFormRef = useRef();

  /**事件类型数据 */
  const [eventTypeData, setEventTypeData] = useState<any[]>([])

  const [dataSource, setDataSource] = useState<any[]>([])

  /** 表单ref */
  const eventTypeRef = useRef()

  /** 搜索参数 */
  const [searchParams, setSearchParams] = useState({
    eventType: '',
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })

  /** 选中点位集合 */
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  /** 当前行 */
  const [currentRow, setCurrentRow] = useState<any>();

  /** 编辑表单显隐 */
  const [editFormVisible, setEditFormVisible] = useState<boolean>(false);

  /** 已使用的时间类型 */
  const [usedEventCodes, setUsedEventCodes] = useState<string[]>([]);

  /**
   * 分页
   */
  const handlePageChange = async (pagination: any, filters: any, sorter: any, extra: any) => {
    console.log('page change', pagination, filters, sorter, extra)
    setSearchParams({
      ...searchParams,
      pageSize: pagination.pageSize,
      pageNo: pagination.current,
    })
    loadListInfo({
      current: pagination.current,
      size: pagination.pageSize
    })
  }

  /**
  * 根据条件搜索设备
  * @param params 查询条件
  */
  const handleBeforeSearchDevice = async (params: any) => {
    console.log('search', params)
    setSearchParams({
      ...searchParams,
      pageNo: 1,
      eventType: params.eventType,
    })
    loadListInfo({
      current: 1,
      eventType: params.eventType ? params.eventType : 'no&input',
    })
  }


  /**
 * 编辑点位
 */
  const editRecord = async (rows: any[]) => {
    setEditFormVisible(true)
    setCurrentRow(rows)
  }


  const operateRender = (dom: any, record: any): ReactNode => {
    return <div id="operate">
      <a onClick={() => editRecord(record)}>
        <img className="img_edit" title={intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', })} />
      </a>
      <a onClick={() => deleteRecord([record])}>
        <img className="img_del" title={intl.formatMessage({ id: 'pages.pointManage.delete', defaultMessage: '删除', })} />
      </a>
    </div>;
  }
  const columns: any = [
    {
      title: intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', }),
      dataIndex: 'eventType',
      width: 80,
      hideInSearch: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.prompt', defaultMessage: '提示词', }),
      dataIndex: 'prompt',
      hideInSearch: true,
      ellipsis: true,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.createTime', defaultMessage: '创建时间', }),
      dataIndex: 'createTime',
      hideInSearch: true,
      ellipsis: true,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'pages.pointManage.operation', defaultMessage: '操作', }),
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: operateRender,
    },
  ];


  /**
  * 删除记录
  */
  const deleteRecord = async (rows: any[]) => {
    console.log('del data:', rows)

    const blocked: string[] = [];

    for (const row of rows) {
      const res = await checkRuleIsControl(row.id);
      if (res?.code === 0) {
        blocked.push(row.eventType);
      }
    }

    if (blocked.length > 0) {
      message.warning(
        intl.formatMessage({
          id: 'pages.pointManage.cannotDelete',
          defaultMessage: '以下事件类型正在布控，请勿删除',
        })
      );
      return;
    }

    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.search.delete', defaultMessage: '删除', }),
      content: intl.formatMessage({ id: 'pages.pointManage.confirmDeleteEventType', defaultMessage: '确定删除该事件类型吗？', }),
      okText: intl.formatMessage({ id: 'pages.pointManage.confirm', defaultMessage: '确认', }),
      cancelText: intl.formatMessage({ id: 'pages.pointManage.cancel', defaultMessage: '取消', }),
      onOk: async () => {
        const hide = message.loading(intl.formatMessage({ id: 'pages.pointManage.deleting', defaultMessage: '正在删除', }));

        const ids = rows.map(row => row.id);

        const rs = await delControlRule(ids);
        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.deleteSuccess', defaultMessage: '删除成功，自动刷新', }));
        } else {
          message.error(intl.formatMessage({ id: 'pages.pointManage.deleteFailure', defaultMessage: '删除失败，请重试', }));
        }
        setSelectedRows([]);
        loadListInfo({});
        hide();
      },
    });
  };


  /**
   * 加载列表信息
   * @param page 
   */
  const loadListInfo = async (page: any) => {
    console.log('load list', eventTypeRef.current, eventTypeRef.current?.getFieldsValue())
    const searchValue = eventTypeRef.current?.getFieldsValue()
    const currentPage = page.current ? page.current : searchParams.pageNo
    const pageSize = page.size ? page.size : searchParams.pageSize
    let eventType = page.eventType ? page.eventType : searchValue.eventType
    if (page.eventType === 'no&input') {
      eventType = undefined
    }

    const rs = await getControlRulesList({ eventType: eventType }, currentPage, pageSize);
    if (rs.code === 0 && rs.data && rs.data?.data.length >= 0) {
      setDataSource(rs.data?.data)
      setSearchParams({
        ...searchParams,
        pageSize: pageSize,
        pageNo: currentPage,
        total: rs.data.total,
      })
    }

  }

  /**
   * 新增编辑操作
   */
  const handleEventTypeEdit = async () => {
    let formData;
    try {
      formData = await editFormRef.current?.validateFieldsReturnFormatValue?.();

      if (currentRow) {
        formData = {
          ...formData,
          id: currentRow.id,
          eventType: currentRow.eventType,
          eventCode: currentRow.eventCode,
        };
      } else {
        const selectedEventCode = formData.eventCode;
        const eventTypeInfo = eventTypeData.find(item => item.eventCode === selectedEventCode);

        if (eventTypeInfo) {
          formData = {
            ...formData,
            eventType: eventTypeInfo.eventType,
            eventCode: eventTypeInfo.eventCode,
          };
        }
      }

      console.log('Saving data:', formData);

      if (currentRow) {
        const rs = await editControlRule(formData);

        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.editSuccess', defaultMessage: '编辑成功', }))
          loadListInfo({})
        } else {
          message.error(rs.msg)
        }

      } else {
        const rs = await editControlRule(formData);

        if (rs.code === 0) {
          message.success(intl.formatMessage({ id: 'pages.pointManage.addSuccess', defaultMessage: '添加成功', }))
          loadListInfo({})
        } else {
          message.error(rs.msg)
        }

      }

      setEditFormVisible(false);
    } catch (errorInfo) {
      console.error('Save error:', errorInfo);
    }
  };

  /**
   * 添加规则
   */
  const addRuls = async () => {
    setCurrentRow();
    // 加载已添加的事件记录
    const res = await getUsedEventCodes();
    if (res.code === 0 && res?.data) {
      setUsedEventCodes(res.data || []);
    }
    setEditFormVisible(true)
  }

  const filteredUsedCodes = usedEventCodes.filter(code => code !== currentRow?.eventCode);

  const availableEventOptions = eventTypeData
    .filter(item => !filteredUsedCodes.includes(item.eventCode))
    .map(item => ({
      label: item.eventType,
      value: item.eventCode,
    }));

  /**
   * 加载事件类型
   */
  const loadEventType = async () => {
    const rs = await getAllEventType();
    if (rs.code === 0 && rs?.data && rs?.data.length >= 0) {
      setEventTypeData(rs.data)
    }
  }

  /**
   * 窗口取消操作
   */
  const cancelClose = () => {
    setEditFormVisible(false)
    setCurrentRow();
  };

  /** 
   * 初始化
   */
  useEffect(() => {
    loadEventType();
  }, [])

  return (
    <>
      <ProTable
        rowKey="id"
        headerTitle={intl.formatMessage({ id: 'pages.pointManage.monitorRule', defaultMessage: '布控规则', })}
        actionRef={actionRef}
        formRef={eventTypeRef}
        pagination={{
          current: searchParams.pageNo,
          pageSize: searchParams.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          showPrevNextJumpers: true,
          showTitle: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          total: searchParams.total,
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            console.log("selectedRows-> ", selectedRows)
            setSelectedRows(selectedRows);
          },
        }}
        onChange={handlePageChange}
        beforeSearchSubmit={handleBeforeSearchDevice}
        columns={columns}
        tableAlertRender={false}
        options={{
          density: false,
          setting: false,
        }}
        dataSource={dataSource}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={addRuls}>
            <PlusOutlined />
            {intl.formatMessage({ id: 'pages.pointManage.add', defaultMessage: '新增', })}
          </Button>,
        ]}
      />

      <Modal
        width={600}
        destroyOnClose
        title={currentRow ? intl.formatMessage({ id: 'pages.pointManage.edit', defaultMessage: '编辑', }) : intl.formatMessage({ id: 'pages.pointManage.add', defaultMessage: '新增', })}
        open={editFormVisible}
        onOk={handleEventTypeEdit}
        onCancel={cancelClose}
        // closeIcon={<CloseOutlined style={{ fontSize: '16px' }} />}
        className="controlRules-modal"
        styles={{
          mask: { background: 'rgba(0, 0, 0, 0.5)' },
          header: {
            background: 'rgb(238, 242, 246)',
            padding: '9px 16px',
            marginBottom: '0'
          },
          body: { background: 'rgb(255, 255, 255)', padding: '24px', height: '25vh' },
        }}
      >
        <ProForm<{
          eventType: string;
          prompt: string;
        }>
          formRef={editFormRef}
          layout={"vertical"}
          //form={form}
          style={{ width: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}
          // labelCol={{ flex: 8, style: { display: 'flex', alignItems: 'center' } }}
          // wrapperCol={{ flex: 24, style: { display: 'flex', alignItems: 'center' } }}
          submitter={{
            resetButtonProps: {
              hidden: true,
            },
            submitButtonProps: {
              hidden: true,
            },
            searchConfig: {
            }
          }}
        >


          <ProFormSelect
            label={intl.formatMessage({ id: 'pages.pointManage.eventType', defaultMessage: '事件类型', })}
            width="md"
            name="eventCode"
            options={availableEventOptions}
            initialValue={currentRow?.eventCode || ''}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.pointManage.selectEventType', defaultMessage: '请选择事件类型' }) }]}
            allowClear={false}
            disabled={!!currentRow}
          />

          <ProFormText
            width="md"
            name="prompt"
            initialValue={currentRow?.prompt}
            rules={[
              { required: true, message: intl.formatMessage({ id: 'pages.pointManage.inputPrompt', defaultMessage: '请输入提示词' }) },
              { max: 256, message: intl.formatMessage({ id: 'pages.pointManage.charLimitExceeded' }) + " 256" },
            ]}
            label={intl.formatMessage({ id: 'pages.pointManage.prompt', defaultMessage: '提示词' })}
            placeholder={intl.formatMessage({ id: 'pages.pointManage.inputPrompt', defaultMessage: '请输入提示词' })}
          />




        </ProForm>
      </Modal >
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {intl.formatMessage({ id: 'pages.pointManage.selected', defaultMessage: '已选择', })}
              <a style={{ fontWeight: 600, color: 'rgb(11, 211, 87)' }}>{selectedRows.length}</a>
              {intl.formatMessage({ id: 'pages.pointManage.item', defaultMessage: '项', })}
            </div>
          }
        >
          <Button onClick={() => deleteRecord(selectedRows)}>{intl.formatMessage({ id: 'pages.pointManage.batchDelete', defaultMessage: '批量删除', })}</Button>
        </FooterToolbar>
      )
      }
    </>

  );
};

export default connect()(ControlRules);
