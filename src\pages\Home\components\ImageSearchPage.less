/* 轨迹网格容器滚动条样式 */
.trajectory-grid-container {
  /* 确保容器能够正确滚动 */
  position: relative;

  /* WebKit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* 确保网格项目正确布局 */
  & > * {
    min-width: 0; /* 防止内容溢出 */
  }
}

/* 动态高度调整类 */
.trajectory-container-wrapper {
  /* 基础高度计算 */
  --header-height: 68px;      /* 面包屑区域 */
  --padding-height: 48px;     /* 搜索区域padding */
  --title-height: 52px;       /* 标题区域 */
  --pagination-height: 48px;  /* 分页区域 */
  --margin-height: 24px;      /* 其他边距 */
  --total-offset: calc(var(--header-height) + var(--padding-height) + var(--title-height) + var(--pagination-height) + var(--margin-height));

  height: calc(100vh - var(--total-offset));
  max-height: calc(100vh - var(--total-offset));
}

/* 响应式网格布局优化 */
@media (max-width: 1400px) {
  .trajectory-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(min(240px, 100%), 1fr)) !important;
  }
}

@media (max-width: 1200px) {
  .trajectory-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(min(220px, 100%), 1fr)) !important;
  }
}

@media (max-width: 992px) {
  .trajectory-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(min(200px, 100%), 1fr)) !important;
    gap: 12px !important;
  }
}

@media (max-width: 768px) {
  .trajectory-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(min(180px, 100%), 1fr)) !important;
    gap: 10px !important;
  }
}

@media (max-width: 576px) {
  .trajectory-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(min(160px, 100%), 1fr)) !important;
    gap: 8px !important;
  }
}

@media (max-width: 480px) {
  .trajectory-grid-container {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
}

/* 时间选择器优化样式 */
.compact-time-selector {
  .ant-btn {
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.ant-btn-sm {
      height: 28px;
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  .ant-picker {
    border-radius: 6px;

    &:hover, &:focus {
     // border-color: #40a9ff;
     // box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &.ant-picker-small {
      height: 28px;
    }
  }

  /* 响应式优化 */
  @media (max-width: 768px) {
    .time-dropdown-content {
      min-width: 280px !important;
      max-width: 320px !important;
    }
  }

  @media (max-width: 480px) {
    .time-dropdown-content {
      min-width: 260px !important;
      max-width: 300px !important;
      padding: 8px !important;
    }

    .ant-space-item .ant-btn {
      font-size: 11px;
      padding: 2px 6px;
      height: 24px;
    }
  }
}

/* 时间选择下拉框动画 */
.time-dropdown-content {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 检索条件选择器样式 */
.condition-select {
  .ant-select-selector {
    border: 1px solid #d9d9d9 !important;
    background: #ffffff !important;
    border-radius: 80px !important;
    transition: all 0.3s ease !important;
  }
  
}

.condition-select-dropdown {
  .ant-select-item {
    border-radius: 4px;
    margin: 2px 4px;
  }
}
