export default {
    'pages.login.success': "登入成功！",
    'pages.login.failure': "登入失敗！",
    'pages.login.retry': "登入失敗，請重試！",
    'pages.login.title': "登入頁",
    'pages.login.welcome': "歡迎來到火星慧知",
    'pages.login.agreementPrefix': "您已閱讀並同意火星慧知",
    'pages.login.terms': "《使用者使用條款》",
    'pages.login.and': " 和",
    'pages.login.privacy': "《隱私權政策》",
    'pages.login.submit': "使用者登入",
    'pages.login.usernamePlaceholder': "請輸入使用者名稱",
    'pages.login.passwordPlaceholder': "請輸入密碼",
    'pages.search.sortByTime': "按最新時間",
    'pages.search.sortBySimilarity': "按相似度",
    'pages.search.history': "歷史記錄",
    'pages.search.searchHistory': "搜尋紀錄",
    'pages.search.delete': "刪除",
    'pages.search.hotSearch': "熱門搜尋",
    'pages.search.example1': "門口機動車旁的男生",
    'pages.search.example2': "電梯間紅色衣服打電話的人",
    'pages.search.example3': "停車場崗亭戴藍色帽子的人",
    'pages.search.assistHint': "我可以幫助您搜尋訊息，例如：在園區一樓外賣櫃旁的黑色衣服男生",
    'pages.search.searchButton': "搜尋",
    'pages.search.filter': "檢索條件",
    'pages.search.loading': "載入中...",
    'pages.search.eventDetail': "事件詳情",
    'pages.search.videoPlayback': "影片回放",
    'pages.search.panorama': "全景圖",
    'pages.search.description': "描述",
    'pages.search.time': "時間",
    'pages.search.location': "地點",
    'pages.search.similarity': "相似度",
    'pages.pointManage.voiceInput': "語音輸入",
    'pages.pointManage.speechRecognition': "語音辨識",
    'pages.pointManage.stopVoiceInput': "停止語音輸入",
    'pages.pointManage.operationSuccess': "操作成功",
    'pages.pointManage.preview': "預覽",
    'pages.pointManage.edit': "編輯",
    'pages.pointManage.alreadyMonitored': "已布控",
    'pages.pointManage.monitor': "布控",
    'pages.pointManage.delete': "刪除",
    'pages.pointManage.deleteRecord': "刪除記錄",
    'pages.pointManage.confirmDeleteRecord': "確定刪除記錄嗎？",
    'pages.pointManage.confirm': "確認",
    'pages.pointManage.cancel': "取消",
    'pages.pointManage.deleting': "正在刪除",
    'pages.pointManage.deleteSuccess': "刪除成功，自動刷新",
    'pages.pointManage.deleteFailure': "刪除失敗，請重試",
    'pages.pointManage.person': "人",
    'pages.pointManage.motorVehicle': "機車",
    'pages.pointManage.nonMotorVehicle': "非機動車",
    'pages.pointManage.pointName': "點位名稱",
    'pages.pointManage.protocolType': "協定類型",
    'pages.pointManage.captureType': "抓拍類型",
    'pages.pointManage.captureInterval': "抓拍間隔",
    'pages.pointManage.deviceName': "設備名稱",
    'pages.pointManage.deviceIP': "設備IP",
    'pages.pointManage.devicePort': "設備連接埠",
    'pages.pointManage.operation': "操作",
    'pages.pointManage.pointManagement': "點位管理",
    'pages.pointManage.add': "新增",
    'pages.pointManage.selected': "已選擇",
    'pages.pointManage.batchDelete': "大量刪除",
    'pages.pointManage.item': "項",
    'pages.pointManage.inputCaptureInterval': "請輸入抓拍間隔",
    'pages.pointManage.inputPointName': "請輸入點位名稱",
    'pages.pointManage.selectCaptureType': "請選擇抓拍類型",
    'pages.pointManage.addDevice': "新增設備",
    'pages.pointManage.protocol': "協定",
    'pages.pointManage.deviceCode': "設備編碼",
    'pages.pointManage.inputDeviceName': "請輸入設備名稱",
    'pages.pointManage.inputDeviceCode': "請輸入設備編碼",
    'pages.pointManage.port': "連接埠",
    'pages.pointManage.username': "使用者名稱",
    'pages.pointManage.password': "密碼",
    'pages.pointManage.mainStream': "主碼串流",
    'pages.pointManage.example': "範例",
    'pages.pointManage.subStream': "子碼串流",
    'pages.pointManage.addPoint': "新增點位",
    'pages.pointManage.validIP': "請輸入有效的 IP 位址",
    'pages.pointManage.noData': "無數據",
    'pages.pointManage.selectPoint': "請選擇點位",
    'pages.pointManage.addSuccess': "添加成功",
    'pages.pointManage.prevStep': "上一步",
    'pages.pointManage.nextStep': "下一步",
    'pages.pointManage.monitorSuccess': "布控成功",
    'pages.pointManage.monitorFailure': "布控失敗",
    'pages.pointManage.cancelSuccess': "取消成功",
    'pages.pointManage.operationFailure': "操作失敗",
    'pages.pointManage.monitor': "布控",
    'pages.pointManage.monitorEvent': "布控事件",
    'pages.pointManage.startMonitor': "開始布控",
    'pages.pointManage.monitorRecord': "布控記錄",
    'pages.pointManage.noEventRecord': "沒有事件記錄",
    'pages.pointManage.charLimitExceeded': "字元長度超出限制，最大長度：",
    'pages.pointManage.eventType': "事件類型",
    'pages.pointManage.prompt': "提示詞",
    'pages.pointManage.createTime': "建立時間",
    'pages.pointManage.editSuccess': "編輯成功",
    'pages.pointManage.monitorRule': "佈控規則",
    'pages.pointManage.selectEventType': "請選擇事件類型",
    'pages.pointManage.inputPrompt': "請輸入提示詞",
    'pages.pointManage.eventRecord': "事件記錄",
    'pages.pointManage.paginationInfo': "第 {range0} - {range1} 條/總共 {total} 條",
    'pages.pointManage.detail': "詳情",
    'pages.pointManage.eventImage': "事件圖片",
    'pages.pointManage.pointInfo': "點位資訊",
    'pages.pointManage.eventTime': "事件時間",
    'pages.config.name': "名稱",
    'pages.config.creationDate': "建立日期",
    'pages.config.expirationTime': "到期時間",
    'pages.config.editApiKey': "編輯 API 金鑰",
    'pages.config.editSuccess': "編輯成功，自動刷新",
    'pages.config.createSuccess': "建立成功，自動刷新",
    'pages.config.editFailure': "編輯失敗，請重試",
    'pages.config.createFailure': "建立失敗，請重試",
    'pages.config.createApiKey': "建立 API 金鑰",
    'pages.config.authManagement': "授權管理",
    'pages.config.eventCode': "事件編碼",
    'pages.config.paramConfigFailure': "取得參數配置失敗",
    'pages.config.saveFailure': "儲存失敗",
    'pages.config.saveSuccess': "儲存成功",
    'pages.config.save': "儲存",
    'pages.config.reset': "重設",
    'pages.config.streamConfig': "串流媒體配置",
    'pages.config.streamServiceUrl': "串流媒體服務地址",
    'pages.config.secretKey': "金鑰",
    'pages.config.configuration': "配置",
    'pages.config.workspaceId': "工作空間 ID",
    'pages.config.multiSearchStrategy': "多維搜尋檢索策略",
    'pages.config.dataCleanStrategy': "資料清理策略",
    'pages.config.objectDetection': "目標檢測",
    'pages.config.enableObjectDetection': "是否啟用目標檢測",
    'pages.config.allowObjectWhitelist': "允許目標檢測白名單",
    'pages.config.sedan': "轎車",
    'pages.config.bus': "公車",
    'pages.config.truck': "貨車",
    'pages.config.bicycle': "自行車",
    'pages.config.motorcycle': "摩托車",
    'pages.config.enableImageDupCheck': "是否啟用圖像重複檢測",
    'pages.config.intervalSeconds': "間隔時間（秒）",
    'pages.config.minScoreLimitError': "最小分數值不能大於1",
    'pages.config.initialSearchStrategy': "初篩檢索策略",
    'pages.config.enhancedSearchStrategy': "多維搜尋增強檢索策略",
    'pages.config.multiInitialSearch': "多維搜尋初篩",
    'pages.config.minScore': "最小分數值",
    'pages.config.maxResultSet': "最大結果集",
    'pages.config.topValue': "Top值",
    'pages.config.reRanking': "多維搜尋重排名",
    'pages.config.batchSize': "每批次大小",
    'pages.config.fineSearch': "多維搜尋精篩",
    'pages.config.fineSearchStrategy': "精篩檢索策略",
    'pages.config.enableReId': "是否啟用ReId",
    'pages.config.objectMinScore': "目標檢測最低分",
    'pages.config.vectorMinScore': "向量相似最低分",
    'pages.config.maxResultsPerSearch': "每次檢索最大結果集",
    'pages.common.logout': "退出登入",
    'pages.agent.apiCallFailed': "呼叫介面失敗，請稍後重試",
    'pages.agent.hello': "哈嘍，我是",
    'pages.agent.agent': "智慧體",
    'pages.agent.attachment': "附件",
    'pages.agent.dropFilesHere': "將文件放到此處",
    'pages.agent.uploadFile': "上傳文件",
    'pages.agent.clickOrDragToUpload': "點擊或拖曳文件到此區域進行上傳",
    'pages.agent.shiftEnterNewline': "Shift+Enter換行",
    'pages.agent.basicConfig': "基礎配置",
    'pages.agent.llmModel': "使用的LLM模型",
    'pages.agent.doubaoModel': "豆包模型",
    'pages.agent.selectAnOption': "請選擇一個選項",
    'pages.agent.memoryMessageCount': "記憶訊息數",
    'pages.agent.skillConfig': "技能配置",
    'pages.agent.toolSet': "工具集",
    'pages.agent.toolSetDescription': "工具集能夠讓智能體呼叫外部工具，擴展智能體的能力邊界。",
    'pages.agent.knowledgeBase': "知識庫",
    'pages.agent.knowledgeBaseDescription': "當使用者發送訊息時，智能體能夠引用知識庫的內容，回答使用者的問題。",
    'pages.agent.workflow': "工作流程",
    'pages.agent.workflowDescription': "用於處理邏輯複雜且有較多步驟的任務流程。",
    'pages.agent.describePersona': "請描述人設、功能",
    'pages.agent.publishSuccess': "發布成功",
    'pages.agent.publishFailed': "發布失敗",
    'pages.agent.publishNotAllowed': "抱歉，該智能體不能進行發布",
    'pages.agent.config': "智能體配置",
    'pages.agent.publish': "發布",
    'pages.agent.modelCapabilityConfig': "模型能力配置",
    'pages.agent.promptDev': "提示詞開發",
    'pages.agent.debug': "智能體調試",
    'pages.agent.create': "創建智能體",
    'pages.agent.submitFailed': "提交失敗，請檢查表單數據",
    'pages.agent.name': "智能體名稱",
    'pages.agent.nameLimit': "最多可輸入64個字符",
    'pages.agent.description': "智能體功能介紹",
    'pages.agent.descriptionTip': "介紹智能體的功能，將會展示給智能體的用戶",
    'pages.agent.icon': "圖示",
    'pages.agent.imageOnly': "只能上傳圖片文件",
    'pages.agent.imageSizeLimit': "圖片大小不能超過2MB",
    'pages.agent.imageFormatLimit': "支援jpg/png格式，大小不超過2MB",
    'pages.agent.flagship': "旗艦",
    'pages.agent.highSpeed': "高速",
    'pages.agent.toolInvocation': "工具調用",
    'pages.agent.rolePlay': "角色扮演",
    'pages.agent.longText': "長文字",
    'pages.agent.imageUnderstanding': "圖片理解",
    'pages.agent.reasoning': "推理能力",
    'pages.agent.videoUnderstanding': "影片理解",
    'pages.agent.costPerformance': "性價比",
    'pages.agent.codeExpert': "程式碼專精",
    'pages.agent.audioUnderstanding': "音訊理解",
    'pages.agent.visualAnalysis': "視覺分析",
    'pages.agent.running': "運作中",
    'pages.agent.queuing': "排隊中",
    'pages.agent.training': "訓練中",
    'pages.agent.trainingFailed': "訓練失敗",
    'pages.agent.text': "文字",
    'pages.agent.multimodal': "多模態",
    'pages.agent.landongModel': "懶洞模型",
    'pages.agent.searchModelName': "搜尋模型名稱",
    'pages.agent.quotaTrial': "限額體驗",
    'pages.agent.comingOffline': "即將下線",
    'pages.agent.newModelExperience': "新模型體驗",
    'pages.agent.advancedModel': "進階模型",
    'pages.agent.generalModel': "一般模型",
    'pages.agent.modelType': "模型類型",
    'pages.agent.modelFeature': "模型特色",
    'pages.agent.modelProvider': "模型廠商",
    'pages.agent.modelSupportedFunctions': "模型支援功能",
    'pages.agent.contextLength': "上下文長度",
    'pages.agent.userRights': "用戶權益",
    'pages.agent.creator': "創建者",
    'pages.agent.creationTime': "創建時間",
    'pages.agent.describeFunction': "請描述人設、功能",
    'pages.agent.orchestration': "編排",
    'pages.agent.functionIntroduction': "功能介紹",
    'pages.agent.publishStatus': "發布狀態",
    'pages.agent.agentDisplay': "智能體展示",
    'pages.agent.modelStatus': "模型狀態",
    'pages.search.expandir': "展開",
    'pages.search.retirar': "收起",
    'pages.search.deleteConfirmWarning': "刪除後將無法恢復，確定要刪除嗎？",
    'pages.config.applicationId': "應用程式 ID",
    'pages.config.imageDeduplication': "影像去重",
    'pages.pointManage.loadingMessage': "載入中，請勿刷新頁面",
    'pages.pointManage.fetchError': "取得點位超時，請檢查設備是否在線",
    'pages.pointManage.deviceTimeout': "設備請求逾時",
    'pages.pointManage.streamConnectFailed': "串流服務連線失敗",
    'pages.pointManage.serviceException': "服務異常，請稍後重試",
    'pages.pointManage.deleteHasControlRule': "選取的點位中正在布控中，無法刪除",
    'pages.pointManage.online': "網路",
    'pages.pointManage.offline': "離線",
    'pages.pointManage.confirmDeleteEventType': "確定刪除該事件類型嗎？",
    'pages.pointManage.captureIntervalRange': "抓拍間隔在 1 到 3600 秒之間",
    'pages.pointManage.status': "狀態",
    'pages.login.terms.title': "《產品使用協議》",
    'pages.login.terms.check': "請先閱讀並同意《產品使用協議》",
    'pages.pointManage.confirmDeletePoint': "確定要刪除點位資訊記錄嗎？",
    'pages.pointManage.pointNameRequired': "存在未填寫的點位名稱，請填寫後再提交",
    'pages.pointManage.refresh': "刷新",
    'pages.account.updateSuc': "修改成功",
    'pages.account.updatePwd': "修改密碼",
    'pages.account.oldPassword': "原密碼",
    'pages.account.newPassword': "新密碼",
    'pages.account.confirmPwd': "確認密碼",
    'pages.account.passwordmatch': "您輸入的新密碼不匹配",
    'pages.password.reset.fail': "重設密碼失敗",
    'pages.password.reset.success': "密碼重置成功",
    'pages.password.update': "修改密碼",
    'pages.register.success': "註冊成功，請登入",
    'pages.register.fail': "註冊失敗",
    'pages.login.fail': "使用者名稱或密碼錯誤",
    'pages.login.needRegister': "請先註冊帳號",
    'pages.system.check.fail': "服務檢測失敗",
    'pages.account.maxlength': "密碼最多18位",
    'pages.login.login': "登 入",
    'pages.login.register': "註 冊",
    'pages.login.registerTitle': "使用者註冊",
    'pages.search.similarity': "相似度",
    'pages.common.sessionExpired': "用戶已過期，請重新登入",
    'pages.primaryKey.id': "主鍵ID",
    'pages.agent.type': "類型",
    'pages.agent.type.placeholder': "請選擇智慧體類型",
    'pages.agent.type.required': "請選擇類型",
    'pages.agent.id': "智慧體ID",
    'pages.agent.id.placeholder': "請輸入智慧體ID",
    'pages.agent.id.required': "請輸入智慧體ID",
    'pages.agent.suggestedQuestions': "你可以這樣問我：",
    'pages.agent.botId.tip': "請前往對應平臺（如Coze、Dify）創建智慧體後複製其ID粘貼到此處",
    'pages.agent.apiKey.tip': "請前往Dify平臺，複製其API Key粘貼到此處",
    'pages.agent.apiKey.required': "API Key為必填項",
}
