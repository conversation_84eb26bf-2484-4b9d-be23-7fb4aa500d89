package com.zkteco.mars.usc.constant;

import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:02
 * @since 1.0.0
 */
public class USCConstants {

    // 指定流媒体抓拍保存路径
    public static final String SNAP_FOLDER_PATH = Paths.get(System.getProperty("user.dir")) + "/zfs";

    // 流媒体抓拍照片路径
    public static final String SNAP_SNAP_FOLDER_PATH = Paths.get(System.getProperty("user.dir")) + "/zfs/snap";

    // 流媒体目标抠图路径
    public static final String SNAP_TARGET_FOLDER_PATH = Paths.get(System.getProperty("user.dir")) + "/zfs/target";

    // 抓拍照片本地保存路径
    public static final String SNAP_SAVE_FOLDER_PATH = Paths.get(System.getProperty("user.dir")) + "/zfs/localSnap";

    // 抓拍缩略图照片保存路径
    public static final String SNAP_THUMBNAIL_FOLDER_PATH = Paths.get(System.getProperty("user.dir")) + "/zfs/thumb";

    // 接口返回成功
    public static final int SUCCESS = 0;

    public static final int API_PROGRAM_ERROR = -1;
    // pageNo或者pageSize不能设置小于等于0
    public static final int API_WRONG_PAGE = -90;
    // pageSize 大于1000
    public static final int API_PAGE_OVERSIZE = -91;

    // 接口返回失败 删除图像
    public static final int FAIL_CODE_500 = 500;

    // yyyyMMddHHmmssSSS时间格式
    public static final String yyyyMMddHHmmssSSS = "yyyyMMddHHmmssSSS";

    // yyyyMMddHHmmss时间格式
    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";

    // 协议类型  ONVIF
    public static final String PROTOCOL_ONVIF = "ONVIF";

    // 协议类型  RTSP
    public static final String PROTOCOL_RTSP = "RTSP";







}
