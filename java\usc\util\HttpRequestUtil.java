package com.zkteco.mars.usc.util;

import com.alibaba.fastjson2.JSONObject;
import com.zkteco.framework.core.exception.ZKBusinessException;
import com.zkteco.mars.sys.constants.SysConstants;
import com.zkteco.mars.sys.service.SysParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * http请求
 *
 * <AUTHOR>
 * @date 2025-01-10 9:15
 * @since 1.0.0
 */
@Slf4j
@Component
public class HttpRequestUtil {
    private static final CloseableHttpClient httpClient = HttpClients.createDefault();

    @Autowired
    private SysParamService sysParamService;

    /**
     * 封装http 请求和 secret
     *
     * @param url:
     * @param dataObject:
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2025-03-14 13:59
     * @since 1.0.0
     */
    public String postCommon(String url, JSONObject dataObject) {
        dataObject.put("secret", sysParamService.getValByName(SysConstants.MD_KEY));

        String fullUrl = sysParamService.getValByName(SysConstants.MD_URL) + url;

        return post(fullUrl, dataObject.toJSONString());
    }

    /**
     * post 请求
     *
     * @param url:
     * @param body:
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2025-01-09 15:20
     * @since 1.0.0
     */
    private String post(String url, String body) {
        HttpPost request = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000).build();
        request.setConfig(requestConfig);

        if (body != null) {
            request.setEntity(new StringEntity(body, "UTF-8"));
            request.addHeader("Content-Type", "application/json");
        }
        log.info("HttpRequestUtil Post request body : {}", body);
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String res = EntityUtils.toString(response.getEntity());
            log.info("HttpRequestUtil Post respond body : {}", res.toString());
            return res;
        } catch (SocketTimeoutException e) {
            log.error("HttpRequestUtil timeout error: {}", e.getMessage());
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, e.getMessage());
        } catch (Exception e) {
            log.error("HttpRequestUtil error: {}", e.getMessage());
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, e.getCause().getMessage());
        }
    }

    /**
     * get请求
     *
     * @param url:
     * @param params:
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2025-01-10 9:14
     * @since 1.0.0
     */
    public static String get(String url, Map<String, String> params) throws IOException {
        // 拼接 URL 参数
        if (params != null && !params.isEmpty()) {
            StringBuilder urlWithParams = new StringBuilder(url);
            urlWithParams.append("?");
            params.forEach((key, value) -> {
                try {
                    urlWithParams.append(URLEncoder.encode(key, "UTF-8")).append("=").append(URLEncoder.encode(value, "UTF-8")).append("&");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException("URL CREATE Fail", e);
                }
            });
            // 移除最后一个 "&"
            url = urlWithParams.substring(0, urlWithParams.length() - 1);
        }

        HttpGet request = new HttpGet(url);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            return EntityUtils.toString(response.getEntity());
        } catch (SocketTimeoutException e) {
            log.error("HttpRequestUtil timeout error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("HttpRequestUtil error: {}", e.getMessage());
            throw new IOException(e);
        }
    }


}
