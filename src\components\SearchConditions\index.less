/* 搜索条件组件样式 */
.compact-time-selector {
  .ant-btn {
    border-radius: 6px;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.ant-btn-sm {
      height: 28px;
      padding: 4px 8px;
      font-size: 12px;
    }
  }
  
  .ant-picker {
    border-radius: 6px;
    
    
    &.ant-picker-small {
      height: 28px;
    }
  }
  
  /* 响应式优化 */
  @media (max-width: 768px) {
    .time-dropdown-content {
      min-width: 280px !important;
      max-width: 320px !important;
    }
  }
  
  @media (max-width: 480px) {
    .time-dropdown-content {
      min-width: 260px !important;
      max-width: 300px !important;
      padding: 8px !important;
    }
    
    .ant-space-item .ant-btn {
      font-size: 11px;
      padding: 2px 6px;
      height: 24px;
    }
  }
}

/* 时间选择下拉框动画 */
.time-dropdown-content {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
