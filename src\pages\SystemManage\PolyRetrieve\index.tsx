import { ProForm, PageHeader, ProFormDigit, ProFormSelect, ProFormSwitch, ProFormCheckbox } from '@ant-design/pro-components';
import { Button, Form, message, Tabs } from 'antd';
import React, { useState, useEffect, useRef } from 'react'
import { useIntl } from "umi";
import { getVLMInfo, updateVLMInfo } from '@/services/ant-design-pro/api';
import './index.less'


const PolyRetrieve: React.FC = () => {

  /** 初筛检索策略设置表单 */
  const [preFilterForm] = Form.useForm();

  /** 数据清洗策略设置表单 */
  const [dataCleanForm] = Form.useForm();

  /** 精筛检索策略设置表单 */
  const [postFilterForm] = Form.useForm();

  /** 当前表单数据 */
  const [currentFormData, setCurrentFormData] = useState<any>();

  const intl = useIntl();

  const [tabChange<PERSON>ey, setTabChangeKey] = useState("cleaning_strategy");

  const handleTabChange = (activeKey: any) => {
    console.log('Switched to tab:', activeKey);
    setTabChangeKey(activeKey)
    loadParamConfig(activeKey)
  };

  const loadParamConfig = async (key: string) => {
    const rs = await getVLMInfo(key);
    if (rs.code !== 0 || !rs.data) {
      message.error(intl.formatMessage({ id: 'pages.config.paramConfigFailure', defaultMessage: '获取参数配置失败', }))
      return;
    }

    const formData = rs.data;
    setCurrentFormData(formData);
    preFilterForm.setFieldsValue(formData);
    dataCleanForm.setFieldsValue(formData);
    postFilterForm.setFieldsValue(formData);

  };


  /**
  * 保存数据
  * @returns
  */
  const handleSaveSetting = async () => {
    let formData
    try {
      if (tabChangeKey === 'retrieval_strategy') {
        formData = await preFilterForm.validateFields();
      } else if (tabChangeKey === 'cleaning_strategy') {
        formData = await dataCleanForm.validateFields();
      } else if (tabChangeKey === 'reid_strategy') {
        formData = await postFilterForm.validateFields();
      }
      console.log(formData);
    } catch (errorInfo) {
      console.error('save setting error:', errorInfo);
      return
    }


    const rs = await updateVLMInfo(tabChangeKey, formData)
    if (rs.code !== 0) {
      message.error(intl.formatMessage({ id: 'pages.config.saveFailure', defaultMessage: '保存失败', }))
      return
    } else {
      message.success(intl.formatMessage({ id: 'pages.config.saveSuccess', defaultMessage: '保存成功', }))
    }
  };


  useEffect(() => {
    loadParamConfig("cleaning_strategy");
  }, []);



  return (
    <PageHeader
      style={{ 'backgroundColor': 'white', padding: '24px' }}
      onBack={() => null}
      title={intl.formatMessage({ id: 'pages.config.multiSearchStrategy', defaultMessage: '保存成功', })}
      //subTitle="This is a subtitle"
      backIcon=""
      extra={[
        <Button key="2" onClick={() => loadParamConfig(tabChangeKey)}>{intl.formatMessage({ id: 'pages.config.reset', defaultMessage: '重置', })}</Button>,
        <Button key="1" onClick={handleSaveSetting} type="primary">
          {intl.formatMessage({ id: 'pages.config.save', defaultMessage: '保存', })}
        </Button>,
      ]}
    >

      <Tabs defaultActiveKey="cleaning_strategy" onChange={handleTabChange}>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'pages.config.dataCleanStrategy', defaultMessage: '数据清洗策略', })} key="cleaning_strategy">
          <div className="form-container">
            <ProForm
              labelAlign="left"
              layout={'horizontal'}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              form={dataCleanForm}
              submitter={{
                resetButtonProps: { hidden: true },
                submitButtonProps: { hidden: true },
              }}
            >
              <div className="pbx_cfc_title">
                <span>{intl.formatMessage({ id: 'pages.config.objectDetection', defaultMessage: '目标检测', })}</span>
              </div>

              <ProFormSwitch
                name="enable_check_od"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.enableObjectDetection', defaultMessage: '是否启用目标检测', })}
              //initialValue={currentFormData?.enable_check_od ?? false}
              />

              <ProFormCheckbox.Group
                name="allowed_od_white_list"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.allowObjectWhitelist', defaultMessage: '允许目标检测白名单', })}
                options={[
                  { label: intl.formatMessage({ id: 'pages.pointManage.person', defaultMessage: '人', }), value: 'person' },
                  { label: intl.formatMessage({ id: 'pages.config.sedan', defaultMessage: '轿车', }), value: 'car' },
                  { label: intl.formatMessage({ id: 'pages.config.bus', defaultMessage: '公交车', }), value: 'bus' },
                  { label: intl.formatMessage({ id: 'pages.config.truck', defaultMessage: '货车', }), value: 'truck' },
                  { label: intl.formatMessage({ id: 'pages.config.bicycle', defaultMessage: '自行车', }), value: 'bicycle' },
                  { label: intl.formatMessage({ id: 'pages.config.motorcycle', defaultMessage: '摩托车', }), value: 'motorcycle' },
                ]}
              //initialValue={currentFormData?.allowed_od_white_list?.[0]}
              />

              <ProFormDigit
                name="od_min_score"
                rules={[{ required: true }, { type: 'number', max: 1, message: intl.formatMessage({ id: 'pages.config.minScoreLimitError', defaultMessage: '最小分数值不能大于1', }) }]}
                label={intl.formatMessage({ id: 'pages.config.minScore', defaultMessage: '最小分数值', })}
                width="md"
              />

              <div className="pbx_cfc_title">
                <span>{intl.formatMessage({ id: 'pages.config.imageDeduplication', defaultMessage: '图像去重', })}</span>
              </div>

              <ProFormSwitch
                name="enable_check_image_repeat"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.enableImageDupCheck', defaultMessage: '是否启用图像重复检测', })}
              />

              <ProFormDigit
                name="image_repeat_interval"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.intervalSeconds', defaultMessage: '间隔时间(秒)', })}
                width="md"
              />

              <ProFormDigit
                name="image_repeat_min_score"
                rules={[{ required: true }, { type: 'number', max: 1, message: intl.formatMessage({ id: 'pages.config.minScoreLimitError', defaultMessage: '最小分数值不能大于1', }) }]}
                label={intl.formatMessage({ id: 'pages.config.minScore', defaultMessage: '最小分数值', })}
                width="md"
              />
            </ProForm>
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'pages.config.initialSearchStrategy', defaultMessage: '初筛检索策略' })} key="retrieval_strategy">
          <div className="form-container">
            <ProForm
              // labelCol={{ flex: '185px' }}
              labelAlign="left"
              layout={'horizontal'}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              form={preFilterForm}
              submitter={{
                resetButtonProps: { hidden: true },
                submitButtonProps: { hidden: true },
              }}
            >
              <ProFormSelect
                name="rag_mode"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.enhancedSearchStrategy', defaultMessage: '多维搜增强检索策略' })}
                width="md"
                options={[
                  { label: 'rerank', value: 1 },
                  { label: 'llm', value: 2 },
                  { label: 'rerank + llm', value: 3 },
                ]}
                initialValue={currentFormData?.rag_mode}
              />

              <div className="pbx_cfc_title">
                <span>{intl.formatMessage({ id: 'pages.config.multiInitialSearch', defaultMessage: '多维搜初筛' })}</span>
              </div>
              <ProFormDigit
                name="vector_min_score"
                rules={[{ required: true }, { type: 'number', max: 1, message: intl.formatMessage({ id: 'pages.config.minScoreLimitError', defaultMessage: '最小分数值不能大于1', }) }]}
                label={intl.formatMessage({ id: 'pages.config.minScore', defaultMessage: '最小分数值', })}
                width="md"
                initialValue={currentFormData?.vector_min_score}
              />

              <ProFormDigit
                name="vector_max_result"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.maxResultSet', defaultMessage: '最大结果集' })}
                width="md"
                initialValue={currentFormData?.vector_max_result}
              />
              <div className="pbx_cfc_title">
                <span>{intl.formatMessage({ id: 'pages.config.reRanking', defaultMessage: '多维搜重排名' })}</span>
              </div>
              <ProFormDigit
                name="rerank_top"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.topValue', defaultMessage: 'Top值' })}
                width="md"
                initialValue={currentFormData?.rerank_top}
              />

              <ProFormDigit
                name="rerank_min_score"
                rules={[{ required: true }, { type: 'number', max: 1, message: '最小分数值不能大于1' }]}
                label={intl.formatMessage({ id: 'pages.config.minScore', defaultMessage: '最小分数值' })}
                width="md"
                initialValue={currentFormData?.rerank_min_score}
              />

              <ProFormDigit
                name="rerank_batch_size"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.batchSize', defaultMessage: '每批次大小' })}
                width="md"
                initialValue={currentFormData?.rerank_batch_size}
              />

              <div className="pbx_cfc_title">
                <span>{intl.formatMessage({ id: 'pages.config.fineSearch', defaultMessage: '多维搜精筛' })}</span>
              </div>
              <ProFormDigit
                name="llm_batch_size"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.batchSize', defaultMessage: '每批次大小' })}
                width="md"
                initialValue={currentFormData?.llm_batch_size}
              />
            </ProForm>
          </div>
        </Tabs.TabPane>

        <Tabs.TabPane tab={intl.formatMessage({ id: 'pages.config.fineSearchStrategy', defaultMessage: '精筛检索策略' })} key="reid_strategy">
          <div className="form-container">
            <ProForm
              labelAlign="left"
              layout={'horizontal'}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              form={postFilterForm}
              submitter={{
                resetButtonProps: { hidden: true },
                submitButtonProps: { hidden: true },
              }}
            >

              <ProFormSwitch
                name="reid_enable"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.enableReId', defaultMessage: '是否启用ReId' })}
                initialValue={currentFormData?.reid_enable ?? false}
              />

              <ProFormDigit
                name="reid_od_min_score"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.objectMinScore', defaultMessage: '目标检测最低分' })}
                width="md"
                initialValue={currentFormData?.reid_od_min_score}
              />


              <ProFormDigit
                name="reid_vector_min_score"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.vectorMinScore', defaultMessage: '向量相似最低分' })}
                width="md"
                initialValue={currentFormData?.reid_vector_min_score}
              />

              <ProFormDigit
                name="reid_max_result"
                rules={[{ required: true }]}
                label={intl.formatMessage({ id: 'pages.config.maxResultsPerSearch', defaultMessage: '每次检索最大结果集' })}
                width="md"
                initialValue={currentFormData?.reIdMaxResult}
              />

            </ProForm>
          </div>


        </Tabs.TabPane>
      </Tabs>
    </PageHeader >
  );
};

export default PolyRetrieve;
